/*
 * Copyright (c) 2013 Tencent all rights reserved
 *
 * time:   2014-03-26
 * file:   test.cpp
 * desc:   undo
 *
 */

#include <stdio.h>

#include "bigaddr_client_def.h"
#include "bigaddr_listener.h"
#include "dir_service.h"
#include "tdr/tdr.h"
#include "tlog/tlog.h"
#include "tloghelp/tlogload.h"

using namespace apollo_dir;

extern unsigned char g_szMetalib_bigaddr_client_def[];

BigAddrListener g_listener;

int main() {
  LPTDRMETALIB metalib =
      reinterpret_cast<LPTDRMETALIB>(g_szMetalib_bigaddr_client_def);
  LPTDRMETA meta = tdr_get_meta_by_name(metalib, "ClientCfg");
  LPTLOGCTX logctx = tlog_init_from_file("log.xml");
  LPTLOGCATEGORYINST logcat = tlog_get_category(logctx, "text");

  if (NULL == logcat) {
    printf("logcat is null\n");
    return -1;
  }

  if (NULL == meta) {
    printf("meta is null\n");
    return -1;
  }

  CLIENTCFG cfg;
  TDRDATA data;
  data.pszBuff = (char*)&cfg;
  data.iBuff = sizeof(CLIENTCFG);

  int ret = tdr_input_file(meta, &data, "bigaddr_client.xml", 0,
                           TDR_XML_DATA_FORMAT_LIST_ENTRY_NAME);
  if (0 != ret) {
    printf("tdr_input_file fail\n");
    return -1;
  }

  DirServiceApi api;
  for (uint32_t i = 0; i < cfg.dwAddrCount; i++) {
    printf("add url:%s\n", cfg.aszAddr[i]);

    ret = api.AddDirUrl(cfg.aszAddr[i]);
    if (0 != ret) {
      printf("AddDirUrl fail, %d\n", ret);
      return -1;
    }
  }

  ret = api.RegistBigAddrService(cfg.dwBussinessId, cfg.dwRegionId,
                                 cfg.dwServiceId);
  if (ret != 0) {
    printf("RegistBigAddrService fail, %d\n", ret);
    return -1;
  }

  ret = api.Init(cfg.dwTimeout, &g_listener, logcat);
  if (ret != 0) {
    printf("Init fail, %d\n", ret);
    return -1;
  }

  ServiceBigAddrRouteInfo stRouteInfo;
  ReportRouteInfo stReportRouteInfo;

  ret = api.GetBigAddrServiceRoute(&stRouteInfo, &stReportRouteInfo);
  if (ret != 0) {
    printf("GetBigAddrServiceRoute fail, %d\n", ret);
    return -1;
  }

  printf("get addr succ\n");

  printf("businessid:%u, serviceid:%u, regionid:%u, addrlist count:%u\n",
         stRouteInfo.bussiness_id, stRouteInfo.service_id,
         stRouteInfo.region_id, stRouteInfo.service_addr_count);

  for (unsigned int i = 0; i < stRouteInfo.service_addr_count; i++) {
    printf("addr[%u]:%s\n", i, stRouteInfo.service_addr_list[i].url);
  }

  while (1) {
    api.Update();
    sleep(1);
  }

  return 0;
}
