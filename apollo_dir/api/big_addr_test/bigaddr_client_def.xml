<?xml version="1.0" encoding="GBK" standalone="yes" ?>

<metalib tagsetversion="1" name="bigaddr_client_def" version="1">

  <macro name="TDIR_MAX_ADDR_NUM" value="32" desc=""/>
  <macro name="TDIR_URL_LEN" value="64" desc=""/>
  <macro name="TDIR_MAX_REQ_ROUTE_NUM" value="128" desc=""/>


<struct name="ClientCfg" version="1">
    <entry name="BussinessId" type="uint32" desc="" />
    <entry name="ServiceId" type="uint32" desc="" />
    <entry name="RegionId" type="uint32" desc="" />
    
    <entry name="AddrCount" type="uint32" />
    <entry name="Addr" type="string" size="TDIR_URL_LEN" count="TDIR_MAX_ADDR_NUM" refer="AddrCount" />
    <entry name="Timeout" type="uint32" />
</struct>

</metalib>
