Import("buildenv")

import os
from subprocess import *

buildenv.xml2c('bigaddr_client_def.cpp', Split('bigaddr_client_def.xml'))
buildenv.xml2h('bigaddr_client_def.xml')

inc = ['../src/', '../../tdr_files', '../../svr/src', '../../comm']

buildenv.env.Append(LIBPATH=['../src', './'])

api_client = Glob('bigaddr_client.cpp')
api_client += Glob('bigaddr_client_def.cpp')

api_client_objs = Glob('../../lib/libtdirapi.a')

api_client_objs += buildenv.buildStaticObj(api_client, OBJPREFIX='api_client_', CPPPATH=inc)
buildenv.buildExec('bigaddr_client', api_client_objs+Glob('../../lib/libtdirapi.a'), CPPPATH=inc, a_thirdparty_static_lib='tdirapi tsf4g tdr_comm anl')

