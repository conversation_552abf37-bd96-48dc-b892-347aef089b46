<?xml version="1.0" encoding="GBK" standalone="yes" ?>
<TLOGConf version="2">
    <Magic>1548 </Magic>
    <PriorityHigh>NULL </PriorityHigh>
    <PriorityLow>DEBUG</PriorityLow>
    <DelayInit>0 </DelayInit>
    <SuppressError>1 </SuppressError>
    <Count>6 </Count>
    <CategoryList type="TLOGCategory">
        <Name>text</Name>
        <PriorityHigh>INFO </PriorityHigh>
        <PriorityLow>TRACE </PriorityLow>
        <Filter type="TLOGFilterVec">
            <Count>1 </Count>
            <Filters type="TLOGFilter">
                <IDFilter type="IntFilter">
                    <Start>0 </Start>
                    <Count>100000000 </Count>
                    <Mod>0 </Mod>
                    <ModStart>0 </ModStart>
                    <ModCount>0 </ModCount>
                </IDFilter>
                <ClsFilter type="IntFilter">
                    <Start>0 </Start>
                    <Count>100 </Count>
                    <Mod>0 </Mod>
                    <ModStart>0 </ModStart>
                    <ModCount>0 </ModCount>
                </ClsFilter>
            </Filters>
        </Filter>
        <LevelDispatch>0 </LevelDispatch>
        <MustSucc>0 </MustSucc>
        <MaxMsgSize>0 </MaxMsgSize>
        <Format></Format>
        <ForwardCat>texttrace</ForwardCat>
        <Device type="TLOGDevAny">
            <Type>NO </Type>
        </Device>
    </CategoryList>
    <CategoryList type="TLOGCategory">
        <Name>texttrace</Name>
        <PriorityHigh>NULL </PriorityHigh>
        <PriorityLow>TRACE </PriorityLow>
        <Filter type="TLOGFilterVec">
            <Count>0 </Count>
        </Filter>
        <LevelDispatch>0 </LevelDispatch>
        <MustSucc>0 </MustSucc>
        <MaxMsgSize>0 </MaxMsgSize>
        <Format>[%d.%u][%h][(%f:%l) (%F)][%M][%p] %m%n</Format>
        <ForwardCat>texterr</ForwardCat>
        <Device type="TLOGDevAny">
            <Type>FILE </Type>
            <Device type="TLOGDevSelector">
                <File type="TLOGDevFile">
                  <Pattern>../log/test.log</Pattern>
                    <BuffSize>0 </BuffSize>
                    <SizeLimit>10485760 </SizeLimit>
                    <Precision>1 </Precision>
                    <MaxRotate>2 </MaxRotate>
                    <SyncTime>0 </SyncTime>
                    <NoFindLatest>0 </NoFindLatest>
                    <RotateStick>0 </RotateStick>
                    <DropBinaryHead>0 </DropBinaryHead>
                </File>
            </Device>
        </Device>
    </CategoryList>
    <CategoryList type="TLOGCategory">
        <Name>texterr</Name>
        <PriorityHigh>NULL </PriorityHigh>
        <PriorityLow>ERROR </PriorityLow>
        <Filter type="TLOGFilterVec">
            <Count>0 </Count>
        </Filter>
        <LevelDispatch>0 </LevelDispatch>
        <MustSucc>0 </MustSucc>
        <MaxMsgSize>0 </MaxMsgSize>
        <Format>[%d.%u][%h][(%f:%l) (%F)][%M][%p] %m%n</Format>
        <ForwardCat></ForwardCat>
        <Device type="TLOGDevAny">
            <Type>FILE </Type>
            <Device type="TLOGDevSelector">
                <File type="TLOGDevFile">
                  <Pattern>../log/test.error</Pattern>
                    <BuffSize>0 </BuffSize>
                    <SizeLimit>10485760 </SizeLimit>
                    <Precision>1 </Precision>
                    <MaxRotate>2 </MaxRotate>
                    <SyncTime>0 </SyncTime>
                    <NoFindLatest>0 </NoFindLatest>
                    <RotateStick>0 </RotateStick>
                    <DropBinaryHead>0 </DropBinaryHead>
                </File>
            </Device>
        </Device>
    </CategoryList>
    <CategoryList type="TLOGCategory">
        <Name>texttrace.bus</Name>
        <PriorityHigh>NULL </PriorityHigh>
        <PriorityLow>ERROR </PriorityLow>
        <Filter type="TLOGFilterVec">
            <Count>0 </Count>
        </Filter>
        <LevelDispatch>1 </LevelDispatch>
        <MustSucc>0 </MustSucc>
        <MaxMsgSize>0 </MaxMsgSize>
        <Format>[%d%u]%p %m %F:%l%n</Format>
        <ForwardCat></ForwardCat>
        <Device type="TLOGDevAny">
            <Type>FILE </Type>
            <Device type="TLOGDevSelector">
                <File type="TLOGDevFile">
                  <Pattern>../log/test_tbus.log</Pattern>
                    <BuffSize>0 </BuffSize>
                    <SizeLimit>5242880 </SizeLimit>
                    <Precision>1 </Precision>
                    <MaxRotate>3 </MaxRotate>
                    <SyncTime>0 </SyncTime>
                    <NoFindLatest>0 </NoFindLatest>
                    <RotateStick>0 </RotateStick>
                    <DropBinaryHead>0 </DropBinaryHead>
                </File>
            </Device>
        </Device>
    </CategoryList>
    <CategoryList type="TLOGCategory">
        <Name>data</Name>
        <PriorityHigh>NULL </PriorityHigh>
        <PriorityLow>INFO </PriorityLow>
        <Filter type="TLOGFilterVec">
            <Count>0 </Count>
        </Filter>
        <LevelDispatch>0 </LevelDispatch>
        <MustSucc>0 </MustSucc>
        <MaxMsgSize>0 </MaxMsgSize>
        <Format>[%d.%u][%h][(%f:%l) (%F)][%M][%p] %m%n</Format>
        <ForwardCat></ForwardCat>
        <Device type="TLOGDevAny">
            <Type>FILE </Type>
            <Device type="TLOGDevSelector">
                <File type="TLOGDevFile">
                  <Pattern>../log/test_rundata.log</Pattern>
                    <BuffSize>0 </BuffSize>
                    <SizeLimit>20971520 </SizeLimit>
                    <Precision>1 </Precision>
                    <MaxRotate>3 </MaxRotate>
                    <SyncTime>0 </SyncTime>
                    <NoFindLatest>0 </NoFindLatest>
                    <RotateStick>0 </RotateStick>
                    <DropBinaryHead>0 </DropBinaryHead>
                </File>
            </Device>
        </Device>
    </CategoryList>
    <CategoryList type="TLOGCategory">
        <Name>bill</Name>
        <PriorityHigh>NULL </PriorityHigh>
        <PriorityLow>ERROR </PriorityLow>
        <Filter type="TLOGFilterVec">
            <Count>0 </Count>
        </Filter>
        <LevelDispatch>0 </LevelDispatch>
        <MustSucc>0 </MustSucc>
        <MaxMsgSize>0 </MaxMsgSize>
        <Format>[%d.%u]%m%n</Format>
        <ForwardCat></ForwardCat>
        <Device type="TLOGDevAny">
            <Type>FILE </Type>
            <Device type="TLOGDevSelector">
                <File type="TLOGDevFile">
                  <Pattern>../log/test.bill</Pattern>
                    <BuffSize>0 </BuffSize>
                    <SizeLimit>20971520 </SizeLimit>
                    <Precision>1 </Precision>
                    <MaxRotate>0 </MaxRotate>
                    <SyncTime>0 </SyncTime>
                    <NoFindLatest>0 </NoFindLatest>
                    <RotateStick>0 </RotateStick>
                    <DropBinaryHead>0 </DropBinaryHead>
                </File>
            </Device>
        </Device>
    </CategoryList>
</TLOGConf>
