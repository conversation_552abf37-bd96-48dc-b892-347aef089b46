
#include "dir_service.h"

using namespace apollo_dir;

class BigAddrListener : public BaseDirEventListener {
 public:
  BigAddrListener(){};
  ~BigAddrListener(){};

 public:
  virtual void OnServiceBigAddrChange(
      const ServiceBigAddrRouteInfo* route_info) {
    printf("into OnServiceBigAddrChange\n");

    if (!route_info) {
      printf("null input\n");
      return;
    }

    printf("businessid:%u, serviceid:%u, regionid:%u, addrlist count:%u\n",
           route_info->bussiness_id, route_info->service_id,
           route_info->region_id, route_info->service_addr_count);

    for (unsigned int i = 0; i < route_info->service_addr_count; i++) {
      printf("addr[%u]:%s\n", i, route_info->service_addr_list[i].url);
    }
  }

  virtual void OnReportChange(IN const ReportRouteInfo* list,
                              IN uint32_t size) {
    printf("into OnReportChange, size:%u\n", size);
  }
};