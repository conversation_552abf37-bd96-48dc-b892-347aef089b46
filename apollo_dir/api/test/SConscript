Import("buildenv")

import os
from subprocess import *

buildenv.xml2c('client_conf_desc.cpp', Split('client_conf_desc.xml'))
buildenv.xml2h('client_conf_desc.xml')

inc = ['../src/', '../../tdr_files', '../../svr/src', '../../comm']

buildenv.env.Append(LIBPATH=['../src', './'])

#buildenv.buildExec('test_tdirapi', src, a_thirdparty_static_lib='tsf4g')


#api_run tool
api_run = Glob('api_run.cpp')
api_run_objs = Glob('../../lib/libtdirapi.a')
api_run_objs += buildenv.buildStaticObj(api_run, OBJPREFIX='api_run_', CPPPATH=inc)
buildenv.buildExec('api_run', api_run_objs+Glob('../../lib/libtdirapi.a'), a_thirdparty_static_lib='tdirapi tsf4g tdr_comm anl')

#api_client tool
buildenv.xml2c('api_client_def.c', Split('api_client_def.xml'))
buildenv.xml2h('api_client_def.xml')
api_client = Glob('api_client.cpp')
api_client += Glob('api_client_def.c')
api_client_objs = Glob('../../lib/libtdirapi.a')
api_client_objs += buildenv.buildStaticObj(api_client, OBJPREFIX='api_client_', CPPPATH=inc)
buildenv.buildExec('api_client', api_client_objs+Glob('../../lib/libtdirapi.a'), CPPPATH=inc, a_thirdparty_static_lib='tdirapi tsf4g tdr_comm anl')


test_multi = Glob("test_multi.cpp")
test_multi_obj = buildenv.buildStaticObj(test_multi, OBJPREFIX='test_multi_', CPPPATH=["../src/"])
buildenv.buildExec("test_multi", test_multi + Glob('../../lib/libtdirapi.a'), CPPPATH=["../src/"], a_thirdparty_static_lib='tsf4g tdr_comm anl')
