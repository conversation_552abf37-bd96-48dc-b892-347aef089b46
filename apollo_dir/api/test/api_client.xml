<?xml version="1.0" encoding="GBK" standalone="yes" ?>

<api_client_cfg>

    <!-- Mode: 0-service, 1-business -->
    <Mode>1</Mode>

    <DirUrls>
        <Url>tcp://10.123.16.139:6200</Url>
    </DirUrls>
    
    <RegistBussinessService>
        <Service>
            <BussinessId>10000</BussinessId>
            <ServiceId>4 10</ServiceId>
        </Service>
        <Service>
            <BussinessId>12</BussinessId>
            <ServiceId>1</ServiceId>
        </Service>
    </RegistBussinessService>
    
    <SetBussiness>
        <BussinessId>10000</BussinessId>
    </SetBussiness>
    
    <Request>
        <CallInterval>1000</CallInterval>
        <ServiceRequest>
        
            <Service>
                <BussinessId>10000</BussinessId>
                <ServiceId>4</ServiceId>
            </Service>
            
            <AddrCallConfig>

            <AddrCallInfo>
                <Url>tcp://172.25.41.209:897</Url>
                <FailRate>800</FailRate>
            </AddrCallInfo>

             <AddrCallInfo>
                <Url>tcp://172.25.42.209:897</Url>
                <FailRate>800</FailRate>
            </AddrCallInfo>

             <AddrCallInfo>
                <Url>tcp://172.25.43.209:897</Url>
                <FailRate>80</FailRate>
            </AddrCallInfo>

            </AddrCallConfig>
            
        </ServiceRequest>

        <ServiceRequest>
        
            <Service>
                <BussinessId>2</BussinessId>
                <ServiceId>2</ServiceId>
            </Service>
            
            <AddrCallConfig>

            <AddrCallInfo>
                <Url>tcp://192.168.1.1:8888</Url>
                <FailRate>80</FailRate>
            </AddrCallInfo>

             <AddrCallInfo>
                <Url>tcp://192.168.1.1:8988</Url>
                <FailRate>100</FailRate>
            </AddrCallInfo>

            </AddrCallConfig>
            
        </ServiceRequest>

    </Request>
    
</api_client_cfg>
