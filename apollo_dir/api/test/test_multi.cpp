#include "dir_service.h"
#include "tlog/tlog.h"
#include "tloghelp/tlogload.h"

using namespace apollo_dir;

int main(int argc, char** argv) {
  if (argc < 3) {
    printf("usage:%s [url] [log]\n", argv[0]);
    return -1;
  }

  DirServiceApi api;

  int ret = api.AddDirUrl(argv[1]);
  if (ret != 0) {
    printf("add url fail, ret[%d], url[%s]\n", ret, argv[1]);
    return -1;
  }

  LPTLOGCTX logctx = tlog_init_from_file(argv[2]);
  LPTLOGCATEGORYINST logcat = tlog_get_category(logctx, "text");

  MultiBusinessInfo info;
  info.business_list[0].business_id = 2;
  snprintf(info.business_list[0].business_key,
           sizeof(info.business_list[0].business_key), "%s",
           "9103C8C82514F39D8360C7430C4EE557");

  info.business_list[1].business_id = 3;
  snprintf(info.business_list[1].business_key,
           sizeof(info.business_list[1].business_key), "%s",
           "F5DFFC111454B227FBCDF36178DFE6AC");

  info.business_count = 2;

  ret = api.SetMultiBusiness(info);
  if (ret != 0) {
    printf("set multi business fail, ret[%d]\n", ret);
    return -1;
  }

  ret = api.Init(2000, NULL, logcat);
  if (ret != 0) {
    printf("init fail, ret[%d]\n", ret);
    return -1;
  }

  ret = api.SetMultiBusiness(info);
  if (ret != 0) {
    printf("set multi business fail, ret[%d]\n", ret);
    return -1;
  }

  RouteInfo route[1024];
  uint32_t size = sizeof(route);
  ret = api.GetAllBussinessServiceRoute(route, &size);
  if (ret != 0) {
    printf("GetAllBussinessServiceRoute fail, ret[%d]\n", ret);
    return -1;
  }

  for (uint32_t i = 0; i < size; ++i) {
    printf("business_id[%u], service_id[%u], report_interval[%u]\n",
           route[i].bussiness_id, route[i].service_id,
           route[i].report_interval);

    for (uint32_t j = 0; j < route[i].service_addr_count; ++j) {
      printf("svr url[%s]\n", route[i].service_addr_list[j].url);
    }

    for (uint32_t j = 0; j < route[i].report_addr_count; ++j) {
      printf("report url[%s]\n", route[i].report_addr_list[j].url);
    }
  }

  return 0;
}
