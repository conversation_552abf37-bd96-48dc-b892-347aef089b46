<?xml version="1.0" encoding="GBK" standalone="yes" ?>

<metalib tagsetversion="1" name="test_dir" version="1">

  <macro name="TDIR_MAX_ADDR_NUM" value="32" desc="单个模块最大地址数" />
  <macro name="TDIR_URL_LEN" value="64" desc="地址url最大长度" />
  <macro name="TDIR_MAX_REQ_ROUTE_NUM" value="128" desc="最大请求业务模块数" />


  <struct name="BussinessService" version="1">
    <entry name="BussinessId" type="uint32" desc="业务id" />
    <entry name="ServiceId" type="uint32" desc="服务id" />
  </struct>

  <struct name="ClientCfg" version="1">
    <entry name="BussinessServiceCount" type="uint32" />
    <entry name="BussinessService" type="BussinessService" count="TDIR_MAX_REQ_ROUTE_NUM" refer="BussinessServiceCount" />
    <entry name="AddrCount" type="uint32" />
    <entry name="Addr" type="string" size="TDIR_URL_LEN" count="TDIR_MAX_ADDR_NUM" refer="AddrCount" />
    <entry name="Timeout" type="uint32" />
  </struct>

</metalib>
