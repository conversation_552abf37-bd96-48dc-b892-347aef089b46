<?xml version="1.0" encoding="GBK"?>

<metalib tagsetversion="1" name="api_client_def" version="1" >

<struct name="Service" version="1">
    <entry name="BussinessId" type="uint32" />
    <entry name="ServiceId" type="uint32" />
</struct>

<struct name="AddrCallInfo" version="1">
    <entry name="Url" type="string" size="32" />
    <entry name="FailRate" type="uint32" />
</struct>

<struct name="AddrCallConfig" version="1">
    <entry name="AddrCallInfoCount" type="uint32" io="nooutput" />
    <entry name="AddrCallInfo" type="AddrCallInfo" count="32" refer="AddrCallInfoCount" />
</struct>

<struct name="ServiceRequest" version="1">
    <entry name="Service" type="Service" />
    <entry name="AddrCallConfig" type="AddrCallConfig" />
</struct>

<struct name="Request" version="1">
    <entry name="CallInterval" type="uint32" />
    <entry name="ServiceRequestCount" type="uint32" io="nooutput" />
    <entry name="ServiceRequest" type="ServiceRequest" count="128" refer="ServiceRequestCount" />
</struct>


<struct name="ServiceList" version="1">
    <entry name="ServiceCount" type="uint32" io="nooutput" />
    <entry name="Service" type="Service" count="128" refer="ServiceCount" />
</struct>

<struct name="DirUrls" version="1">
    <entry name="UrlCount" type="uint32" io="nooutput" />
    <entry name="Url" type="string" size="32" count="32" refer="UrlCount" />
</struct>

<struct name="SetBussiness" version="1">
    <entry name="BussinessId" type="uint32" />
</struct>

<struct name="api_client_cfg" version="1">
    <entry name="Mode" type="int" desc="" />
    <entry name="DirUrls" type="DirUrls" desc="" />
    <entry name="SetBussiness" type="SetBussiness" />
    <entry name="RegistBussinessService" type="ServiceList" />
    <entry name="Request" type="Request" desc="" />
</struct>

</metalib>
