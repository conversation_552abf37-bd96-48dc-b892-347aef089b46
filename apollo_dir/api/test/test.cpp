/*
 * Copyright (c) 2013 Tencent all rights reserved
 *
 * time:   2014-03-26
 * file:   test.cpp
 * desc:   undo
 *
 */

#include <stdio.h>

#include "client_conf_desc.h"
#include "dir_service.h"
#include "tdr/tdr.h"
#include "tlog/tlog.h"
#include "tloghelp/tlogload.h"

using namespace apollo_dir;

extern unsigned char g_szMetalib_test_dir[];

int main() {
  LPTDRMETALIB metalib = reinterpret_cast<LPTDRMETALIB>(g_szMetalib_test_dir);
  LPTDRMETA meta = tdr_get_meta_by_name(metalib, "ClientCfg");
  LPTLOGCTX logctx = tlog_init_from_file("log.xml");
  LPTLOGCATEGORYINST logcat = tlog_get_category(logctx, "text");

  if (NULL == logcat) {
    printf("logcat us null\n");
    return -1;
  }

  if (NULL == meta) {
    printf("meta is null\n");
    return -1;
  }

  CLIENTCFG cfg;
  TDRDATA data;
  data.pszBuff = (char*)&cfg;
  data.iBuff = sizeof(CLIENTCFG);

  int ret = tdr_input_file(meta, &data, "client_conf.xml", 0,
                           TDR_XML_DATA_FORMAT_LIST_ENTRY_NAME);
  if (0 != ret) {
    printf("tdr_input_file fail\n");
    return -1;
  }

  DirServiceApi api;
  // RouteInfo info;
  Addr addr;

  for (uint32_t i = 0; i < cfg.AddrCount; i++) {
    printf("add url:%s\n", cfg.Addr[i]);

    ret = api.AddDirUrl(cfg.Addr[i]);
    if (0 != ret) {
      printf("AddDirUrl fail, %d\n", ret);
      return -1;
    }
  }

  for (uint32_t i = 0; i < cfg.BussinessServiceCount; i++) {
    printf("regist bussiness service: bussiness_id[%u], service_id[%u]\n",
           cfg.BussinessService[i].BussinessId,
           cfg.BussinessService[i].ServiceId);

    ret = api.RegistBussinessService(cfg.BussinessService[i].BussinessId,
                                     cfg.BussinessService[i].ServiceId);
    if (ret != 0) {
      printf("RegistBussinessService fail, %d\n", ret);
      return -1;
    }
  }

  ret = api.Init(cfg.Timeout, NULL, logcat);
  if (ret != 0) {
    printf("Init fail, %d\n", ret);
    return -1;
  }

  api.ClearDirUrl();
  ret = api.AddDirUrl(cfg.Addr[1]);
  if (0 != ret) {
    printf("AddDirUrl fail, %d\n", ret);
    return -1;
  }

  while (1) {
    for (uint32_t i = 0; i < cfg.BussinessServiceCount; i++) {
      ret = api.GetBussinessServiceRoute(cfg.BussinessService[i].BussinessId,
                                         cfg.BussinessService[i].ServiceId,
                                         &addr);
      if (ret != 0) {
        printf("GetBussinessServiceRoute fail, %d\n", ret);
        return -1;
      }

      printf("get addr succ:%s\n", addr.url);
      printf("\n");
    }

    if (api.Update() < 0) {
      sleep(1);
    }
  }

  return 0;
}
