/*
 **  @file RCSfile
 **  general description of this module
 **  Id
 **  <AUTHOR>
 **  @date Date
 **  @version Revision
 **  @note Editor: Vim 6.1, Gcc 4.0.1, tab=4
 **  @note Platform: Linux
 */
#include "api_client_def.h"
#include "base_macro.h"
#include "dir_service.h"
#include "tapp/tapp.h"
#include "tdr/tdr.h"
#include "tlog/tlog.h"
#include "tloghelp/tlogload.h"

using namespace apollo_dir;

const int PRINT_SERVICE_INTERVAL = 10;
time_t g_last_print_service_time;

struct RunEnv {
  DirServiceApi api;
  API_CLIENT_CFG* cfg;
  TLOGCATEGORYINST* logger;
  struct timeval last_call_time;

  RunEnv() : cfg(NULL), logger(NULL) {
    memset(&last_call_time, 0, sizeof(last_call_time));
    fprintf(stderr, "env construct succ\n");
  }
};

void GetAndPrintRoute(RunEnv* pstEnv);

extern "C" unsigned char g_szMetalib_api_client_def[];

static TAPPCTX gs_stAppCtx;
static RunEnv gs_stEnv;

class TestListener : public apollo_dir::BaseDirEventListener {
 public:
  void OnServiceChange(const apollo_dir::ServiceRouteInfo* list,
                       uint32_t size) {
    fprintf(stderr, "OnServiceChange fired, size=%u\n", size);
    for (uint32_t i = 0; i < size; ++i) {
      fprintf(stderr, "bussiness_id=%u, service_id=%u, addr_count=%u\n",
              list[i].bussiness_id, list[i].service_id,
              list[i].service_addr_count);
      fprintf(stderr, "service_addr_list:\n");
      for (uint32_t j = 0; j < list[i].service_addr_count; ++j) {
        fprintf(stderr, "url:%s\n", list[i].service_addr_list[j].url);
      }
    }

    GetAndPrintRoute(&gs_stEnv);
  }

  void OnReportChange(const apollo_dir::ReportRouteInfo* list, uint32_t size) {
    fprintf(stderr, "OnReportChange fired, size=%u\n", size);
    for (uint32_t i = 0; i < size; ++i) {
      fprintf(
          stderr,
          "bussiness_id=%u, service_id=%u, report_interval=%u, addr_count=%u\n",
          list[i].bussiness_id, list[i].service_id, list[i].report_interval,
          list[i].report_addr_count);
      fprintf(stderr, "report_addr_list:\n");
      for (uint32_t j = 0; j < list[i].report_addr_count; ++j) {
        fprintf(stderr, "url:%s\n", list[i].report_addr_list[j].url);
      }
    }

    GetAndPrintRoute(&gs_stEnv);
  }
};

////////////////////////////////////////////////////////////////////////////////

static TestListener g_listener;

int api_test_init(TAPPCTX* pstAppCtx, RunEnv* pstEnv);
int api_test_proc(TAPPCTX* pstAppCtx, RunEnv* pstEnv);
int api_test_reload(TAPPCTX* pstAppCtx, RunEnv* pstEnv);
int api_test_fini(TAPPCTX* pstAppCtx, RunEnv* pstEnv);
int api_test_tick(TAPPCTX* pstAppCtx, RunEnv* pstEnv);

////////////////////////////////////////////////////////////////////////////////

void PrintRouteList(RouteInfo* route, uint32_t route_count) {
  fprintf(stderr, "route_count=%u\n", route_count);
  if (route && route_count) {
    for (uint32_t i = 0; i < route_count; ++i) {
      fprintf(stderr,
              "bussiness_id=%u, service_id=%u, report_interval=%u, "
              "service_addr_count=%u, report_addr_count=%u\n",
              route[i].bussiness_id, route[i].service_id,
              route[i].report_interval, route[i].service_addr_count,
              route[i].report_addr_count);

      fprintf(stderr, "service addr:\n");
      for (uint32_t j = 0; j < route[i].service_addr_count; ++j) {
        fprintf(stderr, "url:%s\n", route[i].service_addr_list[j].url);
      }

      fprintf(stderr, "report addr:\n");
      for (uint32_t j = 0; j < route[i].report_addr_count; ++j) {
        fprintf(stderr, "url:%s\n", route[i].report_addr_list[j].url);
      }
      fprintf(stderr, "gdata report addr:\n");
      for (uint32_t j = 0; j < route[i].gdata_report_addr_count; ++j) {
        fprintf(stderr, "\turl:%s\n", route[i].gdata_report_addr_list[j].url);
      }
    }
  }
}

int InitApi(RunEnv* pstEnv) {
  int ret = 0;

  API_CLIENT_CFG& cfg = *(pstEnv->cfg);
  DirServiceApi& api = pstEnv->api;

  // add url
  for (uint32_t i = 0; i < cfg.stDirUrls.dwUrlCount; ++i) {
    ret = api.AddDirUrl(cfg.stDirUrls.aszUrl[i]);
    if (ret) {
      fprintf(stderr, "AddDirUrl failed, ret=%d, i=%u, url=%s, err=%s\n", ret,
              i, cfg.stDirUrls.aszUrl[i], api.GetErrorString(ret));
      return -1;
    }
  }

  if (cfg.iMode == 0) {
    // regist service
    for (uint32_t i = 0; i < cfg.stRegistBussinessService.dwServiceCount; ++i) {
      ret = api.RegistBussinessService(
          cfg.stRegistBussinessService.astService[i].dwBussinessId,
          cfg.stRegistBussinessService.astService[i].dwServiceId);
      if (ret) {
        fprintf(stderr,
                "RegistBussinessService failed, ret=%d, i=%u, bussinedd_id=%u, "
                "service_id=%u, err=%s\n",
                ret, i,
                cfg.stRegistBussinessService.astService[i].dwBussinessId,
                cfg.stRegistBussinessService.astService[i].dwServiceId,
                api.GetErrorString(ret));
        return -5;
      }
    }
  } else {
    ret = api.SetBussiness(cfg.stSetBussiness.dwBussinessId, "");
    if (ret) {
      fprintf(stderr, "SetBussiness failed, ret=%d, bussinedd_id=%u, err=%s\n",
              ret, cfg.stSetBussiness.dwBussinessId, api.GetErrorString(ret));
      return -8;
    }
  }

  // init
  ret = api.Init(3000, &g_listener, pstEnv->logger);
  if (ret) {
    fprintf(stderr, "Init failed, ret=%d, err=%s\n", ret,
            api.GetErrorString(ret));
    return -10;
  }

  // repoeat
  ret = api.Init(3000, &g_listener, pstEnv->logger);
  fprintf(stderr, "repeat init ret=%d, err=%s\n", ret, api.GetErrorString(ret));

  return 0;
}

void GetAndPrintServiceInfo(RunEnv* pstEnv) {
  g_last_print_service_time = time(NULL);

  ServiceInfo service_info[128];
  uint32_t service_count = ARRAY_ITEM_COUNT(service_info);
  int ret = pstEnv->api.GetAllServiceInfo(service_info, &service_count);
  if (ret != 0) {
    fprintf(stderr, "GetAllServiceInfo failed, ret=%d, err=%s\n", ret,
            pstEnv->api.GetErrorString(ret));
    return;
  }

  fprintf(stderr, "begin to print all service info\n");

  for (uint32_t i = 0; i < service_count; ++i) {
    ServiceInfo& info = service_info[i];
    fprintf(stderr, "service_id=%u service_name=%s magic_value=%u\n",
            info.service_id, info.service_name, info.magic_value);
  }

  fprintf(stderr, "print all service info end\n");
}

void GetAndPrintRoute(RunEnv* pstEnv) {
  // get all route
  RouteInfo route[1024];
  // uint32_t route_count = 1;
  uint32_t route_count = ARRAY_ITEM_COUNT(route);
  int ret = pstEnv->api.GetAllBussinessServiceRoute(route, &route_count);
  if (ret) {
    fprintf(stderr, "GetAllBussinessServiceRoute failed, ret=%d, err=%s\n", ret,
            pstEnv->api.GetErrorString(ret));
    return;
  }

  fprintf(stderr, "all route info begin\n");

  PrintRouteList(route, route_count);

  fprintf(stderr, "all route info end\n");
}

int api_test_init(TAPPCTX* pstAppCtx, RunEnv* pstEnv) {
  if (NULL == pstAppCtx->stConfData.pszBuff ||
      0 == pstAppCtx->stConfData.iMeta || NULL == pstAppCtx->pszConfFile) {
    return -1;
  }
  pstEnv->cfg = (API_CLIENT_CFG*)pstAppCtx->stConfData.pszBuff;

  if (0 > tapp_get_category(TLOG_DEF_CATEGORY_TEXTROOT, (&pstEnv->logger))) {
    printf("tapp_get_category run fail\n");
    return -1;
  }

  int ret = InitApi(pstEnv);
  if (ret) {
    fprintf(stderr, "InitApi failed, ret=%d\n", ret);
    return -1;
  }

  GetAndPrintRoute(pstEnv);
  GetAndPrintServiceInfo(pstEnv);

  memset(&(pstEnv->last_call_time), 0, sizeof(pstEnv->last_call_time));

  fprintf(stderr, "api test init succ\n");

  return 0;
}

void ReportResult(DirServiceApi& api, SERVICEREQUEST& request,
                  Addr& call_addr) {
  for (uint32_t i = 0; i < request.stAddrCallConfig.dwAddrCallInfoCount; ++i) {
    ADDRCALLINFO& call_info = request.stAddrCallConfig.astAddrCallInfo[i];
    if (strcmp(call_addr.url, call_info.szUrl) == 0) {
      api.ReportBussinessModRouteCallResult(
          request.stService.dwBussinessId, request.stService.dwServiceId,
          &call_addr,
          ((uint32_t)(random() % 1000) < call_info.dwFailRate) ? -1 : 0, 4);
    }
  }
}

void Call(DirServiceApi& api, API_CLIENT_CFG& cfg) {
  for (uint32_t i = 0; i < cfg.stRequest.dwServiceRequestCount; ++i) {
    Addr call_addr;
    SERVICEREQUEST& request = cfg.stRequest.astServiceRequest[i];
    int ret =
        api.GetBussinessServiceRoute(request.stService.dwBussinessId,
                                     request.stService.dwServiceId, &call_addr);
    // int ret = api.GetBussinessServiceRoute( request.stService.dwBussinessId,
    // request.stService.dwServiceId, NULL );
    if (ret) {
      fprintf(stderr,
              "GetBussinessServiceRoute failed, ret=%d, bussiness_id=%u, "
              "service_id=%u\n",
              ret, request.stService.dwBussinessId,
              request.stService.dwServiceId);
      continue;
    }

    fprintf(stderr,
            "GetBussinessServiceRoute ret=%d, bussiness_id=%u, service_id=%u, "
            "addr=%s\n",
            ret, request.stService.dwBussinessId, request.stService.dwServiceId,
            call_addr.url);

    ReportResult(api, request, call_addr);
  }
}

int api_test_proc(TAPPCTX* pstAppCtx, RunEnv* pstEnv) {
  API_CLIENT_CFG& cfg = *(pstEnv->cfg);
  DirServiceApi& api = pstEnv->api;
  int update_ret = api.Update();

  struct timeval now;
  gettimeofday(&now, NULL);
  if (CalTimeValDiffMS(now, pstEnv->last_call_time) >=
      cfg.stRequest.dwCallInterval) {
    Call(api, cfg);
    pstEnv->last_call_time = now;
  }

  if (time(NULL) - g_last_print_service_time > PRINT_SERVICE_INTERVAL) {
    GetAndPrintServiceInfo(pstEnv);
  }

  return update_ret;
}

int api_test_reload(TAPPCTX* pstAppCtx, RunEnv* pstEnv) { return -1; }

int api_test_fini(TAPPCTX* pstAppCtx, RunEnv* pstEnv) { return 0; }

int api_test_tick(TAPPCTX* pstAppCtx, RunEnv* pstEnv) { return 0; }

int main(int argc, char* argv[]) {
  int iRet;
  void* pvArg = &gs_stEnv;

  memset(&gs_stAppCtx, 0, sizeof(gs_stAppCtx));

  gs_stAppCtx.argc = argc;
  gs_stAppCtx.argv = argv;

  gs_stAppCtx.pfnInit = (PFNTAPPFUNC)api_test_init;
  gs_stAppCtx.pfnFini = (PFNTAPPFUNC)api_test_fini;
  gs_stAppCtx.pfnProc = (PFNTAPPFUNC)api_test_proc;
  gs_stAppCtx.pfnTick = (PFNTAPPFUNC)api_test_tick;
  gs_stAppCtx.pfnReload = NULL;

  gs_stAppCtx.iLib = (intptr_t)g_szMetalib_api_client_def;
  gs_stAppCtx.stConfData.pszMetaName = (char*)"api_client_cfg";
  gs_stAppCtx.uiVersion = 0;

  iRet = tapp_def_init(&gs_stAppCtx, pvArg);
  if (iRet < 0) {
    printf("Error: app Initialization failed.\n");
    return iRet;
  }

  iRet = tapp_def_mainloop(&gs_stAppCtx, pvArg);

  tapp_def_fini(&gs_stAppCtx, pvArg);

  return iRet;
}
