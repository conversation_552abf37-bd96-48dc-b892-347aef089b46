#include "dir_service_api.h"

namespace apollo_dir {

static const char* error_string[] =
    {
        "no error",                                          // 0
        "system has no more memery for alloc",               // 1
        "function incoming invalid argument",                // 2
        "the buffer length is not enough to write",          // 3
        "the dir url has been added",                        // 4
        "the bussiness id and service id has been added",    // 5
        "api has at least one dir url",                      // 6
        "api has at least one bussiness id and service id",  // 7
        "create socket fd fail",                             // 8
        "url is invalid, it must like tcp://127.0.0.1:80 or "
        "tcp://www.qq.com:80",                                        // 9
        "connect fail, u can call errno to check what happen",        // 10
        "connect timeout",                                            // 11
        "pack protocal package fail",                                 // 12
        "socket send buffer fail",                                    // 13
        "socket send buffer uncompleted, some data still in buffer",  // 14
        "get route info timeout",                                     // 15
        "there is no available addr for service",                     // 16
        "the bussiness is and service id is not registed",            // 17
        "the addr is not for the service",                            // 18
        "api has inited, can not init again",                         // 19
        "api has not inited",                                         // 20
        "the operation would block",                                  // 21
        "the svr closed the connection",                              // 22
        "unpack msg failed",                                          // 23
        "recv failed",                                                // 24
        "the recv is uncompleted",                                    // 25
        "the socket is in processing",                                // 26
        "system error",                                               // 27
        "api already has a request in transaction",                   // 28
        "protocal error cmd",                                         // 29
        "transaction timeout",                                        // 30
        "bussiness mode and service mode can not used together",      // 31
        "this methond can not be called at current mode",             // 32
        "the business key is invalid"                                 // 33
};

int error_size = (int)(sizeof(error_string) / sizeof(error_string[0]));

const char* DirServiceApi::GetErrorString(int error_code) {
  int idx = -1 * error_code;
  if (idx < 0 || idx >= error_size) {
    return "unknow error";
  }

  return error_string[idx];
}

}  // namespace apollo_dir
