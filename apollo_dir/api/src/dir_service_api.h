/*
 * Copyright (c) 2013 Tencent all rights reserved
 *
 * time:   2014-03-20
 * file:   dir_service_api.h
 * desc:   dir服务化API
 *
 */

#ifndef DIR_SERVICE_API_H_
#define DIR_SERVICE_API_H_

#include <stdint.h>

#include "tlog/tlog.h"

#ifndef IN
#define IN
#endif
#ifndef OUT
#define OUT
#endif
#ifndef INOUT
#define INOUT
#endif

namespace apollo_dir {

//向前引用声明
class DirServiceApiImp;
class BaseDirEventListener;
struct RouteInfo;
struct Addr;
struct ServiceInfo;
struct MultiBusinessInfo;
struct ServiceBigAddrRouteInfo;
struct ReportRouteInfo;

/// @brief Dir API句柄类
/// - 说明: 本类是非线程安全的
class DirServiceApi {
 public:
  DirServiceApi();
  virtual ~DirServiceApi();

 public:
  /// @brief 添加DirSvr的地址， 在调用Init之前必须有调用过本方法
  ///        最大可添加32个DirSvr地址
  ///
  /// @param[in] dir_url  DirSvr的地址，格式为"tcp://127.0.0.1:1234"
  ///
  /// @return 0  成功
  ///         !0 失败
  int AddDirUrl(IN const char* dir_url);

  /// @brief 清空所有url方法
  ///        用来在运行过程中重新设置dirsvr地址，在调用本方法后，要立刻调用AddDirUrl
  void ClearDirUrl();

  /// @brief 判断是否正在用域名
  bool UsingDomain();

  /// @brief 注册需要拉取地址的业务id和服务id, 必须在Init之前调用
  ///
  /// @param[in] bussiness_id   业务id
  /// @param[in] service_id     服务id
  /// @param[in] business_key   服务key
  ///
  /// @return 0  成功
  ///         !0 失败
  int RegistBussinessService(IN uint32_t bussiness_id, IN uint32_t service_id,
                             const char* business_key = NULL);

  /// @brief 注册需要拉取大地址的业务id和服务id, 必须在Init之前调用
  ///
  /// @param[in] bussiness_id   业务id
  /// @param[in] region_id      区域id
  /// @param[in] service_id     服务id
  /// @param[in] business_key   服务key
  ///
  /// @return 0  成功
  ///         !0 失败
  int RegistBigAddrService(IN uint32_t bussiness_id, IN uint32_t region_id,
                           IN uint32_t service_id,
                           const char* business_key = NULL);

  /// @brief 注册需要拉取地址和配置信息的业务id
  ///        使用本接口，会自动拉取该业务所有在apollo上注册过的服务的地址
  ///        本接口和RegistBussinessService接口只能使用其中一个，不能混合使用
  ///
  ///
  int SetBussiness(IN uint32_t bussiness_id, const char* bussiness_key = NULL);

  /// @brief 注册需要拉取地址和配置信息的多个业务id
  ///        使用本接口，会自动拉取该业务所有在apollo上注册过的服务的地址
  ///        本接口和RegistBussinessService接口只能使用其中一个，不能混合使用
  ///
  ///
  int SetMultiBusiness(IN const MultiBusinessInfo& business_info);

  /// @brief 初始化，会连接DirSvr获取初始数据，
  /// 在调用本函数前必须已经调用过AddDirUrl和RegistBussinessService
  ///        本方法是同步调用的，会阻塞，建议在应用进程启动时调用
  ///
  /// @param[in] timeout          从DirSvr拉取数据的超时时间，单位毫秒,
  /// 必须大于1000, 建议稍微大一些
  /// @param[in] event_listener
  /// 事件监听类的指针，当注册的业务服务中，有地址列表发生变化时，会回调该对象的方法,
  /// 如果不关注该变化，可以传NULL
  /// @param[in] logger           日志实例指针， 不记录日志时，可以填NULL,
  /// 但编译文件时必须链接tlog库
  ///
  /// @return 0  成功
  ///         !0 失败
  int Init(IN uint32_t timeout, IN BaseDirEventListener* event_listener,
           IN TLOGCATEGORYINST* logger);

  /// @brief
  /// 清理接口，调用后，本对象回到刚刚创建时的状态(如果要使用，需要重新AddDirUrl，RegistBussinessService,
  /// Init)
  void Fini();

 public:
  /// @brief 获取所有注册过的业务服务的全量地址列表，必须在Init之后调用
  ///        本方法不会阻塞
  ///
  /// @param[out]      list     所有注册过的业务服务地址列表
  /// @param[in|out]   size
  /// 传入list能容纳的业务服务最大数目，传出查询到的业务服务数目(应该和RegistServiceMod注册过的服务数相同)
  ///
  /// @return  0  成功
  ///          !0 失败
  int GetAllBussinessServiceRoute(OUT RouteInfo* list, INOUT uint32_t* size);

  /// @brief 获取指定业务服务的当前调用地址
  ///        可以在每次需要向服务后端发送请求时都使用本接口来获取后端地址，
  ///        并在调用结束后将
  ///        调用结果用ReportBussinessModRouteCallResult反馈给api，
  ///        这样会由本api来负责容灾和负载均衡计算，
  ///        api会计算后台的服务质量，在某个后台地址有故障时自动踢除该地址，
  ///        并在合适的时候自动恢复
  ///        注意：本接口必须和UpdateServiceModRouteCallResult结合起来使用，每次用本接口获取到地址并完成调用后，都应该
  ///             相应的调用UpdateServiceModRouteCallResult
  ///
  /// @param[in]  bussiness_id 业务id
  /// @param[in]  service_id    服务id
  /// @param[out] addr         传出指定业务服务的地址
  ///
  /// @return 0   成功
  ///         !0  失败
  int GetBussinessServiceRoute(IN uint32_t bussiness_id, IN uint32_t service_id,
                               OUT Addr* addr);

  /// @brief 反馈某个业务服务后端地址的调用结果情况
  ///        作用参见GetBussinessServiceRoute的说明
  /// @param[in] bussiness_id 业务id
  /// @param[in] service_id   服务id
  /// @param[in] addr         业务服务地址
  /// @param[in] result       调用结果(大于等于0为成功，小于0为失败)
  /// @param[in] elapsed_ms   调用耗时，单位毫秒
  ///
  /// @return void
  void ReportBussinessModRouteCallResult(IN uint32_t bussiness_id,
                                         IN uint32_t service_id,
                                         IN const Addr* addr, IN int result,
                                         uint32_t elapsed_ms);

  /// @brief 获取业务大地址列表，必须在Init之后调用
  ///        本方法不会阻塞
  ///
  /// @param[out]      list     所有注册过的业务服务地址列表
  ///
  /// @return  0  成功
  ///          !0 失败
  int GetBigAddrServiceRoute(OUT ServiceBigAddrRouteInfo* route_info,
                             OUT ReportRouteInfo* report_route_info);

 public:
  /// @brief 获取所有service的相关信息
  ///        要使用本接口，必须在调用Init前调用了SetBussiness接口
  ///
  /// @param[out]     service_info   查询到的服务信息
  /// @param[inout]   size           传入service_info的最大大小，
  /// 传出查询到的实际大小
  ///
  /// @return 0   成功
  ///         !0  失败
  int GetAllServiceInfo(OUT ServiceInfo* service_info, INOUT uint32_t* size);

 public:
  /// @brief 状态更新
  ///
  /// @return <0  本轮循环空闲
  ///         >=0 本轮循环有进行逻辑处理，需要马上进行下轮循环
  int Update();

 public:
  static const char* GetErrorString(int error_code);

 private:
  DirServiceApiImp* impl_;

  //禁止隐式的拷贝
  DirServiceApi(const DirServiceApi&);
  DirServiceApi& operator=(const DirServiceApi&);
};

} /* namespace apollo_dir */

#endif /* DIR_SERVICE_API_H_ */
