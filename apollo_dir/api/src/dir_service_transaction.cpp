/*
 * Copyright (c) 2013 Tencent all rights reserved
 *
 * time:   2014-04-01
 * file:   dir_service_transaction.cpp
 * desc:   事务管理
 *
 */

#include "dir_service_transaction.h"

#include <sys/time.h>

#include "dir_api_common.h"
#include "dir_service_error.h"

using dir_svr::TDirHead;
using namespace tsf4g_tdr;

namespace apollo_dir {

DirServiceTransaction::DirServiceTransaction()
    : logger_(NULL),
      timeout_ms_(0),
      curr_url_(NULL),
      state_(TRANS_STATE_IDLE),
      async_(0),
      service_count_(0),
      service_list_(NULL),
      bussiness_id_(0) {
  memset(&start_, 0, sizeof(start_));
  send_msg_.construct();
  recv_msg_.construct();
  bussiness_key_[0] = 0;  // lint !e140
  // lint !e1401
  business_info_.business_count = 0;
  last_list_md5_[0] = 0;
}

DirServiceTransaction::~DirServiceTransaction() {
  logger_ = NULL;
  curr_url_ = NULL;
  service_list_ = NULL;
}

int DirServiceTransaction::Start(TDIR_CMD cmd, const char* url) {
  if (NULL == url) {
    return APOLLO_DIR_ERROR_INVALID_ARG;
  }

  if (TRANS_STATE_IDLE != state_) {
    DIR_LOG_DEBUG(logger_, "already has a request in transaction");
    return APOLLO_DIR_ERROR_IN_TRANS;
  }

  DIR_LOG_DEBUG(logger_, "start transaction[%d]", cmd);

  int ret = 0;

  gettimeofday(&start_, NULL);
  curr_url_ = url;
  //重置收发缓冲区，避免出错
  ResetNode();

  ret = DispatchRequstCmd(cmd);
  if (APOLLO_DIR_ERROR_NONE != ret) {
    return ret;
  }

  net_api_.BuffLen(PACKAGE_LEN);
  ret = net_api_.Connect(curr_url_);
  if (APOLLO_DIR_ERROR_NONE == ret) {
    state_ = TRANS_STATE_START;
  }

  return ret;
}

int DirServiceTransaction::Update() {
  if (TRANS_STATE_IDLE != state_ && TRANS_STATE_END != state_ && IsTimeout()) {
    net_api_.Close();
    state_ = TRANS_STATE_IDLE;

    return APOLLO_DIR_ERROR_TRANS_TIMEOUT;
  }

  int ret = 0;

  switch (state_) {
    case TRANS_STATE_START: {
      ret = net_api_.Update();
      if (APOLLO_DIR_ERROR_NONE != ret) {
        state_ = TRANS_STATE_IDLE;
      } else {
        if (net_api_.IsConnected()) {
          state_ = TRANS_STATE_READY;
        } else {
          DIR_LOG_TRACE(logger_, "transaction is connecting svr");
        }
      }

      break;
    }

    case TRANS_STATE_READY: {
      ret = Pack();
      if (APOLLO_DIR_ERROR_NONE == ret) {
        state_ = TRANS_STATE_SEND;
      } else {
        net_api_.Close();
        state_ = TRANS_STATE_IDLE;
      }

      break;
    }

    case TRANS_STATE_SEND: {
      ret = Send();
      if (APOLLO_DIR_ERROR_NONE == ret) {
        state_ = TRANS_STATE_RECV;
      } else {
        if (APOLLO_DIR_ERROR_SEND_FAIL == ret) {
          net_api_.Close();
          state_ = TRANS_STATE_IDLE;
        } else {
          // SEND_UNCOMPLETED状态认为是正常
          ret = APOLLO_DIR_ERROR_NONE;
        }
      }

      break;
    }

    case TRANS_STATE_RECV: {
      ret = RecvAndTryUnpack();

      break;
    }

    default: {
      break;
    }
  }

  return ret;
}

bool DirServiceTransaction::IsTimeout() {
  uint32_t remain = 0;

  remain = RemainTimeMs(&start_, timeout_ms_);

  DIR_LOG_TRACE(logger_,
                "check is timeout, state[%d], remian time[%u], timeout[%u]",
                state_, remain, timeout_ms_);

  return remain > 0 ? false : true;
}

int DirServiceTransaction::Pack() {
  TdrError::ErrorType tdr_ret = TdrError::TDR_NO_ERROR;
  char* begin = NULL;
  size_t capacity = 0;
  size_t used = 0;

  if (send_buff_.offset != send_buff_.data_len) {
    if (send_buff_.buff_len > send_buff_.data_len) {
      capacity = send_buff_.buff_len - send_buff_.data_len;
      if (capacity < PACKAGE_LEN) {
        size_t remain = send_buff_.data_len - send_buff_.offset;
        memmove(send_buff_.buff, (send_buff_.buff + send_buff_.offset), remain);
        send_buff_.offset = 0;
        send_buff_.data_len = remain;
      }
    } else {
      //一种错误的状态
      send_buff_.offset = 0;
      send_buff_.data_len = 0;
    }
  } else {
    send_buff_.offset = 0;
    send_buff_.data_len = 0;
  }

  begin = send_buff_.buff + send_buff_.data_len;
  capacity = send_buff_.buff_len - send_buff_.data_len;

  tdr_ret = send_msg_.pack(begin, capacity, &used);
  if (TdrError::TDR_NO_ERROR != tdr_ret) {
    DIR_LOG_ERROR(logger_, "tdr pack fail, %s",
                  TdrError::getErrorString(tdr_ret));
    return APOLLO_DIR_ERROR_PACK_FAIL;
  }

  send_buff_.data_len += used;

  DIR_LOG_DEBUG(logger_, "after pack, buffer data len[%d]",
                (uint32_t)send_buff_.data_len);

  return APOLLO_DIR_ERROR_NONE;
}

int DirServiceTransaction::Send() {
  int need_send = 0;
  int send_len = 0;

  if (send_buff_.data_len > send_buff_.offset)  // lint !e574
  {
    need_send = send_buff_.data_len - send_buff_.offset;
  }

  if (need_send > 0) {
    DIR_LOG_DEBUG(logger_, "need send data len[%d]", need_send);

    send_len = net_api_.Send((send_buff_.buff + send_buff_.offset), need_send);
    if (send_len >= 0) {
      send_buff_.offset += send_len;
      if (static_cast<uint32_t>(send_len) == send_buff_.data_len) {
        DIR_LOG_DEBUG(logger_, "send all data len[%d] succ", send_len);
        return APOLLO_DIR_ERROR_NONE;
      } else {
        DIR_LOG_DEBUG(logger_, "send uncompleted, data len[%u], send len[%d]",
                      (uint32_t)send_buff_.data_len, send_len);
        return APOLLO_DIR_ERROR_SEND_UNCOMPLETED;
      }
    } else if (APOLLO_DIR_ERROR_WOULD_BLOCK == send_len) {
      DIR_LOG_DEBUG(logger_, "send uncompleted, socket would be block");
      return APOLLO_DIR_ERROR_SEND_UNCOMPLETED;
    } else {
      DIR_LOG_ERROR(logger_, "socket send fail, ret[%d]", send_len);
      return APOLLO_DIR_ERROR_SEND_FAIL;
    }
  }

  return APOLLO_DIR_ERROR_NONE;
}

int DirServiceTransaction::Recv() {
  int ret = 0;
  char* begin = NULL;
  size_t capacity = 0;

  begin = recv_buff_.buff + recv_buff_.data_len;
  capacity = recv_buff_.buff_len - recv_buff_.data_len;

  DIR_LOG_TRACE(logger_, "recv buffer capacity[%u]", (uint32_t)capacity);

  ret = net_api_.Recv(begin, capacity);
  if (ret > 0) {
    DIR_LOG_DEBUG(logger_, "recv data len[%d]", ret);
    recv_buff_.data_len += ret;
    return APOLLO_DIR_ERROR_NONE;
  }

  if (APOLLO_DIR_ERROR_SVR_CLOSE_CONN == ret) {
    DIR_LOG_ERROR(logger_, "dir svr close connection");
    return APOLLO_DIR_ERROR_SVR_CLOSE_CONN;
  }

  if (APOLLO_DIR_ERROR_WOULD_BLOCK == ret) {
    DIR_LOG_TRACE(logger_, "recv uncompleted, socket would be block");
    return APOLLO_DIR_ERROR_RECV_UNCOMPLETED;
  }

  DIR_LOG_ERROR(logger_, "recv fail");
  return APOLLO_DIR_ERROR_RECV_FAIL;
}

#define OFFSETOF(TYPE, MEMBER) ((size_t) & (((TYPE*)0)->MEMBER))
int DirServiceTransaction::Unpack() {
  const size_t body_offset = OFFSETOF(TDirHead, dwBodyLen);  // lint !e578
  const size_t unused_data =
      recv_buff_.data_len - recv_buff_.offset;  // lint !e578 !e18

  if (unused_data >= body_offset + sizeof(uint32_t))  // lint !e574
  {
    uint32_t head_len = 0;
    uint32_t body_len = 0;
    uint32_t pkg_len = 0;
    char* begin = NULL;

    begin = recv_buff_.buff + recv_buff_.offset;
    const TDirHead* head = reinterpret_cast<TDirHead*>(begin);
    tdr_cpp_cast_ntoh32(&head_len, &head->dwHeadLen);
    tdr_cpp_cast_ntoh32(&body_len, &head->dwBodyLen);

    pkg_len = head_len + body_len;

    DIR_LOG_DEBUG(logger_, "unpack data, pkg_len info[%u]", pkg_len);
    DIR_LOG_DEBUG(logger_, "buff has unused data len[%u]",
                  (uint32_t)unused_data);

    if (unused_data >= pkg_len)  // lint !e574
    {
      TdrError::ErrorType tdr_ret = TdrError::TDR_NO_ERROR;
      size_t used = 0;

      tdr_ret = recv_msg_.unpack(begin, unused_data, &used, 0);
      if (TdrError::TDR_NO_ERROR != tdr_ret) {
        DIR_LOG_ERROR(logger_, "tdr unpack fail, %s",
                      TdrError::getErrorString(tdr_ret));
        return APOLLO_DIR_ERROR_UNPACK_FAIL;
      }

      recv_buff_.offset += used;

      if (IsValidRespPkg()) {
        DIR_VISUALIZE(logger_, recv_msg_, TLOG_PRIORITY_DEBUG);
        return APOLLO_DIR_ERROR_NONE;
      }
    } else {
      DIR_LOG_DEBUG(logger_, "recv uncomplted");
      return APOLLO_DIR_ERROR_RECV_UNCOMPLETED;
    }
  }

  return APOLLO_DIR_ERROR_RECV_UNCOMPLETED;
}
#undef OFFSETOF

bool DirServiceTransaction::IsValidRespPkg() {
  if (TDIR_MAGIC_CURRENT != recv_msg_.stHead.wMagic) {
    return false;
  }

  return true;
}

void DirServiceTransaction::End() {
  if (TRANS_STATE_IDLE != state_) {
    net_api_.Close();
    state_ = TRANS_STATE_IDLE;
  }
}

void DirServiceTransaction::BuildGetBigAddrReq() {
  send_msg_.stBody.stTDirGetBigAddrReq.dwBussinessId = bussiness_id_;
  send_msg_.stBody.stTDirGetBigAddrReq.dwServiceId =
      big_addr_service_info_.dwServiceId;
  send_msg_.stBody.stTDirGetBigAddrReq.dwRegionId =
      big_addr_service_info_.dwRegionId;

  STR_N_CPY(send_msg_.stBody.stTDirGetBigAddrReq.szBussinessKey, bussiness_key_,
            TDIR_MAX_KEY_LEN);

  STR_N_CPY(send_msg_.stBody.stTDirGetBigAddrReq.szBigAddrListMd5,
            last_list_md5_, dir_svr::TDIR_MD5_VALUE_LEN);

  DIR_VISUALIZE(logger_, send_msg_, TLOG_PRIORITY_DEBUG);
}

void DirServiceTransaction::BuildGetConfigReq() {
  if (NULL == service_list_) {
    return;
  }

  for (uint32_t i = 0; i < service_count_; i++) {
    send_msg_.stBody.stTDirGetConfigReq.astBussinessService[i].dwBussinessId =
        service_list_[i].dwBussinessId;
    send_msg_.stBody.stTDirGetConfigReq.astBussinessService[i].dwServiceId =
        service_list_[i].dwServiceId;

    STR_N_CPY(send_msg_.stBody.stTDirGetConfigReq.astBussinessService[i]
                  .szBussinessKey,
              service_list_[i].szBussinessKey,
              sizeof(service_list_[i].szBussinessKey));
  }

  send_msg_.stBody.stTDirGetConfigReq.dwCount = service_count_;

  DIR_VISUALIZE(logger_, send_msg_, TLOG_PRIORITY_DEBUG);
}

void DirServiceTransaction::BuildGetMultiConfigReq() {
  for (uint32_t i = 0; i < business_info_.business_count; ++i) {
    send_msg_.stBody.stTDirGetMultiConfigReq.astBusinessReq[i].dwBussinessId =
        business_info_.business_list[i].business_id;
    STR_N_CPY(send_msg_.stBody.stTDirGetMultiConfigReq.astBusinessReq[i]
                  .szBussinessKey,
              business_info_.business_list[i].business_key,
              sizeof(service_list_[i].szBussinessKey));
  }

  send_msg_.stBody.stTDirGetMultiConfigReq.dwBusinessReqCount =
      business_info_.business_count;

  DIR_VISUALIZE(logger_, send_msg_, TLOG_PRIORITY_DEBUG);
}

void DirServiceTransaction::BuildGetConfigByBussinessReq() {
  send_msg_.stBody.stTDirGetConfigByBussinessReq.dwBussinessId = bussiness_id_;
  STR_N_CPY(
      send_msg_.stBody.stTDirGetConfigByBussinessReq.szBussinessKey,
      bussiness_key_,
      sizeof(send_msg_.stBody.stTDirGetConfigByBussinessReq.szBussinessKey));
  DIR_VISUALIZE(logger_, send_msg_, TLOG_PRIORITY_DEBUG);
}

int DirServiceTransaction::DispatchRequstCmd(TDIR_CMD cmd) {
  int ret = APOLLO_DIR_ERROR_NONE;

  switch (cmd) {
    case TDIR_CMD_GET_CONFIG_REQ: {
      BuildHead(cmd);
      BuildGetConfigReq();
      break;
    }
    case TDIR_CMD_GET_BIG_ADDR_REQ: {
      BuildHead(cmd);
      BuildGetBigAddrReq();
      break;
    }
    case TDIR_CMD_GET_CONFIG_BY_BUSSINESS_REQ: {
      BuildHead(cmd);
      BuildGetConfigByBussinessReq();
      break;
    }

    case TDIR_CMD_GET_MULTI_CONFIG_REQ: {
      BuildHead(cmd);
      BuildGetMultiConfigReq();
      break;
    }

    default: {
      DIR_LOG_ERROR(logger_, "error cmd[%d]", cmd);
      ret = APOLLO_DIR_ERROR_ERROR_CMD;
      break;
    }
  }

  return ret;
}

int DirServiceTransaction::RecvAndTryUnpack() {
  int ret = APOLLO_DIR_ERROR_NONE;

  ret = Recv();
  if (APOLLO_DIR_ERROR_NONE == ret) {
    //收到数据，尝试解包
    ret = Unpack();
    if (APOLLO_DIR_ERROR_NONE == ret) {
      //收到一个包
      state_ = TRANS_STATE_END;
      net_api_.Close();
    } else if (APOLLO_DIR_ERROR_UNPACK_FAIL == ret) {
      //解包出现了异常
      net_api_.Close();
      state_ = TRANS_STATE_IDLE;
    } else {
      //由于包未收完，导致的解包失败，认为是正常
      ret = APOLLO_DIR_ERROR_NONE;
    }
  } else {
    //未收到数据
    if (APOLLO_DIR_ERROR_RECV_FAIL == ret ||
        APOLLO_DIR_ERROR_SVR_CLOSE_CONN == ret) {
      //收数据出现了异常
      net_api_.Close();
      state_ = TRANS_STATE_IDLE;

      DIR_LOG_DEBUG(logger_, "recv data fail, ret[%d], transaction end", ret);
    } else {
      // RECV_UNCOMPLETED 状态认为是正常
      ret = APOLLO_DIR_ERROR_NONE;
    }
  }

  return ret;
}

}  // namespace apollo_dir
