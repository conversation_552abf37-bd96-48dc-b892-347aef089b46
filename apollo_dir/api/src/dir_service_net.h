/*
 * Copyright (c) 2013 Tencent all rights reserved
 *
 * time:   2014-03-21
 * file:   dir_service_net.h
 * desc:   dir_service网络管理部分
 *
 */
#ifndef DIR_SERVICE_NET_H_
#define DIR_SERVICE_NET_H_

#include <netinet/in.h>
#include <unistd.h>

#include "base_macro.h"
#include "dir_log.h"

namespace apollo_dir {

struct URLInfo {
  std::string host;
  uint16_t port;
};

class DirServiceNet {
 public:
  enum {
    NET_STATE_CLOSED = 0,
    NET_STATE_CONNECTING,
    NET_STATE_CONNECTED,
  };

  DirServiceNet();
  virtual ~DirServiceNet();

  int Connect(const char* url);
  int Send(const char* buff, size_t len);
  int Recv(char* buff, size_t len);
  int Update();

  inline int Close() {
    int ret = 0;

    if (-1 != fd_) {
      ret = close(fd_);
      fd_ = -1;
      net_state_ = NET_STATE_CLOSED;
      DIR_LOG_DEBUG(logger_, "close succ, change state[NET_STATE_CLOSED]");
    }

    return ret;
  }

  inline void Fini() { Close(); }

  inline void BuffLen(int len) { bufflen_ = len; }

  inline void Timeout(uint32_t timeout) { timeout_ = timeout; }

  inline bool IsConnected() const {
    if (NET_STATE_CONNECTED == net_state_) {
      return true;
    } else {
      return false;
    }
  }

  inline bool IsClosed() const {
    if (NET_STATE_CLOSED == net_state_) {
      return true;
    } else {
      return false;
    }
  }

  inline void Logcat(TLOGCATEGORYINST* logger) { logger_ = logger; }

  int Str2Net(const char* url) {
    struct sockaddr_in addr;
    return Str2Net(url, &addr);
  }

  int GetURLInfo(const char* url, URLInfo* url_info);

  bool IsTCP(const char* url);
  int IsIP(const char* host);
  int GetAddrByDomainSync(const std::string& domain, struct sockaddr_in* addr);

 private:
  int OpenSocket(const char* url);
  int SetNonBlock();
  int Str2Net(const char* url, struct sockaddr_in* addr);

  void AdjustSendAndRecvBuffLen();
  int Connect(const struct sockaddr_in* addr);
  int CheckConnect();
  int EpollCheck(int events);

  static const char* TCP;
  static const char* UDP;
  static const char* PROTO_SEP;
  static const char PORT_SEP;
  static const char* NUMBER;

  int fd_;
  uint32_t timeout_;
  struct timeval start_;

  uint32_t bufflen_;
  int net_state_;

  TLOGCATEGORYINST* logger_;
};

}  // namespace apollo_dir
#endif
