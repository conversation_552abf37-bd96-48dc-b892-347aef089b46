/*
 * Copyright (c) 2013 Tencent all rights reserved
 *
 * date:   2014年6月12日
 * file:   dir_service_api_imp_service_mode.cpp
 * desc:
 *
 */

#include "dir_service_api_imp_service_mode.h"

#include "dir_api_common.h"
#include "dir_common_func.h"
#include "dir_service_api_imp.h"

namespace apollo_dir {

DirServiceApiImpServiceMode::DirServiceApiImpServiceMode()
    : DirServiceApiImpMode(IMP_MODE_SERVICE) {
  svr_resp_.construct();
}

DirServiceApiImpServiceMode::~DirServiceApiImpServiceMode() {
  // TODO Auto-generated destructor stub
}

int DirServiceApiImpServiceMode::Init(uint32_t timeout) {
  if (NULL == this->GetImp()) {
    return -1;
  }

  DirServiceApiImp* imp = this->GetImp();

  //从dirsvr获取所有路由信息
  static TDirMsg svr_resp_msg;
  int ret = imp->svr_accessor_->InitAndGetAllRoute(
      imp->service_, imp->service_count_, timeout, imp->logger_, &svr_resp_msg);
  if (ret) {
    DIR_LOG_ERROR(imp->logger_, "svr_accessor InitAndGetAllRoute failed");
    return ret;
  }

  if (svr_resp_msg.stHead.wCommand != dir_svr::TDIR_CMD_GET_CONFIG_RESP) {
    if (TDIR_CMD_ERROR_RESP == svr_resp_msg.stHead.wCommand) {
      DIR_LOG_ERROR(imp->logger_,
                    "svr response error, cmd=%u, errno=%d, errmsg=%s",
                    svr_resp_msg.stBody.stTDirErrorResp.wReqCmd,
                    svr_resp_msg.stBody.stTDirErrorResp.iErrorCode,
                    svr_resp_msg.stBody.stTDirErrorResp.szErrorMsg);
      return GetApiErrorFromSvrError(
          svr_resp_msg.stBody.stTDirErrorResp.iErrorCode);
    } else {
      DIR_LOG_ERROR(imp->logger_, "the svr response msg cmd is invalid, cmd=%u",
                    svr_resp_msg.stHead.wCommand);
      return APOLLO_DIR_ERROR_SYS;
    }
  }

  ret = imp->route_mgr_->Init(svr_resp_msg.stBody.stTDirGetConfigResp,
                              imp->logger_);
  if (ret) {
    DIR_LOG_ERROR(imp->logger_, "route_mgr_ Init failed");
    return ret;
  }

  imp_->FillRouteInfo(
      svr_resp_msg.stBody.stTDirGetConfigResp.astBussinessServiceConfig,
      svr_resp_msg.stBody.stTDirGetConfigResp.dwBussinessServiceConfigCount,
      svr_resp_msg.stBody.stTDirGetConfigResp.stPolicyInfo);

  DirGetConfigRespCopy(&(svr_resp_), svr_resp_msg.stBody.stTDirGetConfigResp);

  return 0;
}

void DirServiceApiImpServiceMode::Fini() { svr_resp_.construct(); }

/// @brief 在收到新的server应答时，要做的事情
///        1. 看路由信息(包括report间隔)是否有变化， 是否需要触发事件通知
///        2.
///        把收到的策略信息设置给route_mgr_，如果服务地址列表有变化，需要对route_mgr_重新初始化
int DirServiceApiImpServiceMode::ProcessRouteAndPolicyInfo(
    const TDirMsg& resp, ServiceRouteInfo* service_change_route,
    uint32_t* service_change_count, ReportRouteInfo* report_change_route,
    uint32_t* report_change_count) {
  if (NULL == imp_) {
    return APOLLO_DIR_ERROR_SYS;
  }

  if (resp.stHead.wCommand != TDIR_CMD_GET_CONFIG_RESP) {
    //不应该出现
    DIR_LOG_ERROR(imp_->logger_, "resp is mismatch!");
    return APOLLO_DIR_ERROR_SYS;
  }

  (*service_change_count) = 0;
  (*report_change_count) = 0;

  GetChangedRoute(resp.stBody.stTDirGetConfigResp, service_change_route,
                  service_change_count, report_change_route,
                  report_change_count);

  imp_->LogChangedRoute(TLOG_PRIORITY_DEBUG, service_change_route,
                        service_change_count, report_change_route,
                        report_change_count);

  if (*service_change_count) {
    //服务地址有变化，路由要重新进行计算，重新初始化
    int ret = imp_->ResetRouteMgr(resp.stBody.stTDirGetConfigResp, svr_resp_);
    if (ret) {
      //这里失败后，route_mgr应该还是在使用老的地址列表,
      //就认为新的应答是无效的，不进行接下来的通知操作
      DIR_LOG_ERROR(imp_->logger_, "ResetRouteMgr failed");
      return ret;
    }
  }

  IF_NOTNULL_CALL(imp_->route_mgr_,
                  imp_->route_mgr_->SetPolicy(
                      resp.stBody.stTDirGetConfigResp.stPolicyInfo));

  DirGetConfigRespCopy(&svr_resp_, resp.stBody.stTDirGetConfigResp);

  imp_->FillRouteInfo(
      resp.stBody.stTDirGetConfigResp.astBussinessServiceConfig,
      resp.stBody.stTDirGetConfigResp.dwBussinessServiceConfigCount,
      resp.stBody.stTDirGetConfigResp.stPolicyInfo);

  return 0;
}

void DirServiceApiImpServiceMode::CompareRoute(
    const BussinessServiceConfig& old_route,
    const TDirGetConfigResp& new_svr_resp, bool report_policy_change,
    ServiceRouteInfo* service_route, uint32_t* service_change_count,
    ReportRouteInfo* report_route, uint32_t* report_change_count) {
  for (uint32_t i = 0; i < new_svr_resp.dwBussinessServiceConfigCount; ++i) {
    const BussinessServiceConfig& new_config =
        new_svr_resp.astBussinessServiceConfig[i];
    if (old_route.stBussinessService.dwBussinessId ==
            new_config.stBussinessService.dwBussinessId &&
        old_route.stBussinessService.dwServiceId ==
            new_config.stBussinessService.dwServiceId) {
      if (report_policy_change ||
          CompareAddrList(old_route.stReportAddrList,
                          new_config.stReportAddrList)) {
        ReportRouteInfoCopy(&(report_route[*report_change_count]), new_config,
                            new_svr_resp.stPolicyInfo);
        ++(*report_change_count);
      }

      if (CompareAddrList(old_route.stServiceAddrList,
                          new_config.stServiceAddrList)) {
        ServiceRouteInfoCopy(&(service_route[*service_change_count]),
                             new_config);
        ++(*service_change_count);
      }
    }
  }
}

void DirServiceApiImpServiceMode::GetChangedRoute(
    const TDirGetConfigResp& new_svr_resp, ServiceRouteInfo* service_route,
    uint32_t* service_change_count, ReportRouteInfo* report_route,
    uint32_t* report_change_count) {
  if (NULL == imp_) {
    return;
  }

  if (service_route && service_change_count && report_route &&
      report_change_count) {
    (*service_change_count) = 0;
    (*report_change_count) = 0;

    if (new_svr_resp.dwBussinessServiceConfigCount !=
        svr_resp_.dwBussinessServiceConfigCount) {
      //由于不允许在Init后再调用RegistBussinessService,
      //所以不应该出现这种情况，直接返回
      DIR_LOG_ERROR(
          imp_->logger_,
          "suprise!!!new cfg count is not equal to old, new_count=%u, old=%u",
          new_svr_resp.dwBussinessServiceConfigCount,
          svr_resp_.dwBussinessServiceConfigCount);
      return;
    }

    //如果report_interval变化了，那么所有业务的report_list都要进行变化通知
    bool report_policy_change = false;
    if (new_svr_resp.stPolicyInfo.dwReportInterval !=
        svr_resp_.stPolicyInfo.dwReportInterval) {
      report_policy_change = true;
    }

    for (uint32_t i = 0; i < svr_resp_.dwBussinessServiceConfigCount; ++i) {
      CompareRoute(svr_resp_.astBussinessServiceConfig[i], new_svr_resp,
                   report_policy_change, service_route, service_change_count,
                   report_route, report_change_count);
    }
  }
}

void DirServiceApiImpServiceMode::SetSvrResp(const TDirGetConfigResp& resp) {
  DirGetConfigRespCopy(&svr_resp_, resp);
}

}  // namespace apollo_dir
