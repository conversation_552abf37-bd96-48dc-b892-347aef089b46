/*
 * Copyright (c) 2013 Tencent all rights reserved
 *
 * date:   2014年6月12日
 * file:   dir_service_api_this->imp_bussiness_mode.h
 * desc:
 *
 */

#ifndef DIR_SERVICE_API_IMP_BUSSINESS_MODE_H_
#define DIR_SERVICE_API_IMP_BUSSINESS_MODE_H_

#include "dir_api_common.h"
#include "dir_common_func.h"
#include "dir_protocol.h"
#include "dir_service_api_imp.h"
#include "dir_service_api_imp_mode.h"
#include "dir_service_error.h"

using dir_svr::BussinessServiceConfig;
using dir_svr::TDIR_CMD_GET_CONFIG_BY_BUSSINESS_RESP;
using dir_svr::TDirGetConfigByBussinessResp;

namespace apollo_dir {

enum AddrType { ADDR_TYPE_SERVICE = 0, ADDR_TYPE_REPORT = 1 };

template <typename resp_body>
class DirServiceApiImpBussinessMode : public DirServiceApiImpMode {
 public:
  DirServiceApiImpBussinessMode();
  virtual ~DirServiceApiImpBussinessMode();

 public:
  void SetSvrResp(const resp_body& resp);
  virtual int Init(uint32_t timeout);
  void Fini();
  virtual int ProcessRouteAndPolicyInfo(const TDirMsg& resp,
                                        ServiceRouteInfo* service_change_route,
                                        uint32_t* service_change_count,
                                        ReportRouteInfo* report_change_route,
                                        uint32_t* report_change_count);

 private:
  void FillImpService();
  void FillImpServiceInfo();
  template <typename T>
  bool ReportChange(const T& new_svr_resp);
  template <typename T>
  bool ServiceChange(const T& new_svr_resp);
  bool AddrChanged(const BussinessServiceConfig& new_config, AddrType type);
  template <typename T>
  void GetChangedRoute(const T& new_svr_resp, ServiceRouteInfo* service_route,
                       uint32_t* service_change_count,
                       ReportRouteInfo* report_route,
                       uint32_t* report_change_count);

 private:
  resp_body svr_resp_;
};

template <typename resp_body>
DirServiceApiImpBussinessMode<resp_body>::DirServiceApiImpBussinessMode()
    : DirServiceApiImpMode(IMP_MODE_BUSSINESS) {
  svr_resp_.construct();
}

template <typename resp_body>
DirServiceApiImpBussinessMode<resp_body>::~DirServiceApiImpBussinessMode() {
  // TODO Auto-generated destructor stub
}

template <typename resp_body>
void DirServiceApiImpBussinessMode<resp_body>::FillImpService() {
  this->imp_->service_count_ = svr_resp_.dwBussinessServiceConfigCount;
  for (unsigned int i = 0; i < this->imp_->service_count_; ++i) {
    this->imp_->service_[i] =
        svr_resp_.astBussinessServiceConfig[i].stBussinessService;
  }
}

template <typename resp_body>
void DirServiceApiImpBussinessMode<resp_body>::FillImpServiceInfo() {
  this->imp_->service_info_count_ = svr_resp_.stServiceInfoList.dwCount;
  for (unsigned int i = 0; i < this->imp_->service_info_count_; ++i) {
    ServiceInfoCopy(this->imp_->service_info_ + i,
                    svr_resp_.stServiceInfoList.astServiceInfo[i]);
  }
}

template <typename resp_body>
int DirServiceApiImpBussinessMode<resp_body>::Init(uint32_t timeout) {
  if (NULL == this->GetImp()) {
    return -1;
  }

  DIR_LOG_DEBUG(this->imp_->logger_, "dir is milti business mode[%d]",
                this->imp_->is_multi_business_);
  //从dirsvr获取所有路由信息
  static TDirMsg svr_resp_msg;

  int ret = 0;
  BussinessServiceConfig* config = NULL;
  int config_count = 0;
  PolicyInfo* policy = NULL;

  if (!this->imp_->is_multi_business_) {
    ret = this->imp_->svr_accessor_->InitAndGetAllRouteByBussiness(
        this->imp_->bussiness_id_, this->imp_->bussiness_key_, timeout,
        this->imp_->logger_, &svr_resp_msg);
  } else {
    ret = this->imp_->svr_accessor_->InitAndGetMultiBusinessInfo(
        this->imp_->business_info_, timeout, this->imp_->logger_,
        &svr_resp_msg);
  }

  if (ret) {
    DIR_LOG_ERROR(this->imp_->logger_,
                  "svr_accessor InitAndGetAllRouteByBussiness or "
                  "InitAndGetMultiBusinessInfo failed");
    return ret;
  }

  switch (svr_resp_msg.stHead.wCommand) {
    case TDIR_CMD_GET_CONFIG_BY_BUSSINESS_RESP: {
      if (this->imp_->is_multi_business_) {
        DIR_LOG_ERROR(this->imp_->logger_,
                      "dir start with multi business mode, recv unexpect cmd "
                      "TDIR_CMD_GET_CONFIG_BY_BUSSINESS_RESP");
        return -1;
      }

      ret = this->imp_->route_mgr_->Init(
          svr_resp_msg.stBody.stTDirGetConfigByBussinessResp,
          this->imp_->logger_);
      config = &(svr_resp_msg.stBody.stTDirGetConfigByBussinessResp
                     .astBussinessServiceConfig[0]);
      config_count = svr_resp_msg.stBody.stTDirGetConfigByBussinessResp
                         .dwBussinessServiceConfigCount;
      policy =
          &(svr_resp_msg.stBody.stTDirGetConfigByBussinessResp.stPolicyInfo);

      DirGetConfigByBussinessRespCopy(
          &(svr_resp_), svr_resp_msg.stBody.stTDirGetConfigByBussinessResp);
      break;
    }

    case TDIR_CMD_GET_MULTI_CONFIG_RESP: {
      if (!this->imp_->is_multi_business_) {
        DIR_LOG_ERROR(this->imp_->logger_,
                      "dir start with single business mode, recv unexpect cmd "
                      "TDIR_CMD_GET_MULTI_CONFIG_RESP");
        return -1;
      }

      ret = this->imp_->route_mgr_->Init(
          svr_resp_msg.stBody.stTDirGetMultiConfigResp, this->imp_->logger_);
      config = &(svr_resp_msg.stBody.stTDirGetMultiConfigResp
                     .astBussinessServiceConfig[0]);
      config_count = svr_resp_msg.stBody.stTDirGetMultiConfigResp
                         .dwBussinessServiceConfigCount;
      policy = &(svr_resp_msg.stBody.stTDirGetMultiConfigResp.stPolicyInfo);

      DirGetConfigByBussinessRespCopy(
          &(svr_resp_), svr_resp_msg.stBody.stTDirGetMultiConfigResp);
      break;
    }

    default: {
      if (TDIR_CMD_ERROR_RESP == svr_resp_msg.stHead.wCommand) {
        DIR_LOG_ERROR(this->imp_->logger_,
                      "svr response error, cmd=%u, errno=%d, errmsg=%s",
                      svr_resp_msg.stBody.stTDirErrorResp.wReqCmd,
                      svr_resp_msg.stBody.stTDirErrorResp.iErrorCode,
                      svr_resp_msg.stBody.stTDirErrorResp.szErrorMsg);
        return GetApiErrorFromSvrError(
            svr_resp_msg.stBody.stTDirErrorResp.iErrorCode);
      } else {
        DIR_LOG_ERROR(this->imp_->logger_,
                      "the svr response msg cmd is invalid, cmd=%u",
                      svr_resp_msg.stHead.wCommand);
        return APOLLO_DIR_ERROR_SYS;
      }

      break;  // lint !e527
    }
  }

  if (ret) {
    DIR_LOG_ERROR(this->imp_->logger_, "route_mgr_ Init failed");
    return ret;
  }

  this->imp_->FillRouteInfo(config, config_count, *policy);

  //需要填充imp的service数组
  // FillImpService();

  //填充imp的service_info数组
  FillImpServiceInfo();

  return 0;
}

template <typename resp_body>
void DirServiceApiImpBussinessMode<resp_body>::Fini() {
  svr_resp_.construct();
}

/// @brief 在收到新的server应答时，要做的事情
///        1. 看路由信息(包括report间隔)是否有变化， 是否需要触发事件通知
///        2.
///        把收到的策略信息设置给route_mgr_，如果服务地址列表有变化，需要对route_mgr_重新初始化
///        3. 如果bussiness_id对应的service有变化，那么应该触发事件通知
///        注意，这里与service
///        mode不同的地方在于，如果是服务/report地址列表有变动，那么就全量的通知给事件监听者，而不是精确的只告知具体的变动
template <typename resp_body>
int DirServiceApiImpBussinessMode<resp_body>::ProcessRouteAndPolicyInfo(
    const TDirMsg& resp, ServiceRouteInfo* service_change_route,
    uint32_t* service_change_count, ReportRouteInfo* report_change_route,
    uint32_t* report_change_count) {
  if (NULL == this->imp_) {
    return APOLLO_DIR_ERROR_SYS;
  }

  switch (resp.stHead.wCommand) {
    case TDIR_CMD_GET_CONFIG_BY_BUSSINESS_RESP: {
      if (this->imp_->is_multi_business_) {
        DIR_LOG_ERROR(this->imp_->logger_,
                      "dir start with multi business mode, recv unexpect cmd "
                      "TDIR_CMD_GET_CONFIG_BY_BUSSINESS_RESP");
        return -1;
      }

      GetChangedRoute(resp.stBody.stTDirGetConfigByBussinessResp,
                      service_change_route, service_change_count,
                      report_change_route, report_change_count);

      if (*service_change_count) {
        //服务地址有变化，路由要重新进行计算，重新初始化
        int ret = this->imp_->ResetRouteMgr(
            resp.stBody.stTDirGetConfigByBussinessResp, svr_resp_);
        if (ret) {
          //这里失败后，route_mgr应该还是在使用老的地址列表,
          //就认为新的应答是无效的，不进行接下来的通知操作
          DIR_LOG_ERROR(this->imp_->logger_, "ResetRouteMgr failed");
          return ret;
        }
      }

      IF_NOTNULL_CALL(
          this->imp_->route_mgr_,
          this->imp_->route_mgr_->SetPolicy(
              resp.stBody.stTDirGetConfigByBussinessResp.stPolicyInfo));

      DirGetConfigByBussinessRespCopy(
          &svr_resp_, resp.stBody.stTDirGetConfigByBussinessResp);

      this->imp_->FillRouteInfo(
          resp.stBody.stTDirGetConfigByBussinessResp.astBussinessServiceConfig,
          resp.stBody.stTDirGetConfigByBussinessResp
              .dwBussinessServiceConfigCount,
          resp.stBody.stTDirGetConfigByBussinessResp.stPolicyInfo);
      break;
    }

    case TDIR_CMD_GET_MULTI_CONFIG_RESP: {
      if (!this->imp_->is_multi_business_) {
        DIR_LOG_ERROR(this->imp_->logger_,
                      "dir start with single business mode, recv unexpect cmd "
                      "TDIR_CMD_GET_MULTI_CONFIG_RESP");
        return -1;
      }

      GetChangedRoute(resp.stBody.stTDirGetMultiConfigResp,
                      service_change_route, service_change_count,
                      report_change_route, report_change_count);

      if (*service_change_count) {
        //服务地址有变化，路由要重新进行计算，重新初始化
        int ret = this->imp_->ResetRouteMgr(
            resp.stBody.stTDirGetMultiConfigResp, svr_resp_);
        if (ret) {
          //这里失败后，route_mgr应该还是在使用老的地址列表,
          //就认为新的应答是无效的，不进行接下来的通知操作
          DIR_LOG_ERROR(this->imp_->logger_, "ResetRouteMgr failed");
          return ret;
        }
      }

      IF_NOTNULL_CALL(this->imp_->route_mgr_,
                      this->imp_->route_mgr_->SetPolicy(
                          resp.stBody.stTDirGetMultiConfigResp.stPolicyInfo));

      DirGetConfigByBussinessRespCopy(&svr_resp_,
                                      resp.stBody.stTDirGetMultiConfigResp);

      this->imp_->FillRouteInfo(
          resp.stBody.stTDirGetMultiConfigResp.astBussinessServiceConfig,
          resp.stBody.stTDirGetMultiConfigResp.dwBussinessServiceConfigCount,
          resp.stBody.stTDirGetMultiConfigResp.stPolicyInfo);
      break;
    }

    default: {
      DIR_LOG_ERROR(this->imp_->logger_, "resp is mismatch!");
      return APOLLO_DIR_ERROR_SYS;
    }
  }

  this->imp_->LogChangedRoute(TLOG_PRIORITY_DEBUG, service_change_route,
                              service_change_count, report_change_route,
                              report_change_count);

  FillImpServiceInfo();

  return 0;
}

template <typename resp_body>
void DirServiceApiImpBussinessMode<resp_body>::SetSvrResp(
    const resp_body& resp) {
  DirGetConfigByBussinessRespCopy(&svr_resp_, resp);
}

template <typename resp_body>
template <typename T>
bool DirServiceApiImpBussinessMode<resp_body>::ReportChange(
    const T& new_svr_resp) {
  //如果report_interval变化了，那么所有业务的report_list都要进行变化通知
  bool report_change = false;
  if (new_svr_resp.stPolicyInfo.dwReportInterval !=
      svr_resp_.stPolicyInfo.dwReportInterval) {
    report_change = true;
  }

  //数目不同，肯定就变了, 那么要进行全量的变化通知
  if (new_svr_resp.dwBussinessServiceConfigCount !=
      svr_resp_.dwBussinessServiceConfigCount) {
    DIR_LOG_DEBUG(this->imp_->logger_,
                  "new cfg count is not equal to old, new_count=%u, old=%u",
                  new_svr_resp.dwBussinessServiceConfigCount,
                  svr_resp_.dwBussinessServiceConfigCount);
    report_change = true;
  }

  //对比report地址有没有变化
  unsigned int i = 0;
  while (!report_change && i < new_svr_resp.dwBussinessServiceConfigCount) {
    const BussinessServiceConfig& new_config =
        new_svr_resp.astBussinessServiceConfig[i++];
    if (AddrChanged(new_config, ADDR_TYPE_REPORT)) {
      report_change = true;
      break;
    }
  }

  return report_change;
}

template <typename resp_body>
template <typename T>
bool DirServiceApiImpBussinessMode<resp_body>::ServiceChange(
    const T& new_svr_resp) {
  //数目不同，肯定就变了, 那么要进行全量的变化通知
  bool service_change = false;
  if (new_svr_resp.dwBussinessServiceConfigCount !=
      svr_resp_.dwBussinessServiceConfigCount) {
    DIR_LOG_DEBUG(this->imp_->logger_,
                  "new cfg count is not equal to old, new_count=%u, old=%u",
                  new_svr_resp.dwBussinessServiceConfigCount,
                  svr_resp_.dwBussinessServiceConfigCount);
    service_change = true;
  }

  //对比service地址有没有变化
  unsigned int i = 0;
  while (!service_change && i < new_svr_resp.dwBussinessServiceConfigCount) {
    const BussinessServiceConfig& new_config =
        new_svr_resp.astBussinessServiceConfig[i++];
    if (AddrChanged(new_config, ADDR_TYPE_SERVICE)) {
      service_change = true;
      break;
    }
  }

  return service_change;
}

template <typename resp_body>
template <typename T>
void DirServiceApiImpBussinessMode<resp_body>::GetChangedRoute(
    const T& new_svr_resp, ServiceRouteInfo* service_route,
    uint32_t* service_change_count, ReportRouteInfo* report_route,
    uint32_t* report_change_count) {
  if (service_route && service_change_count && report_route &&
      report_change_count) {
    (*service_change_count) = 0;
    (*report_change_count) = 0;

    bool report_change = ReportChange(new_svr_resp);
    bool service_change = ServiceChange(new_svr_resp);

    //只要有变化，就是全量通知

    if (report_change) {
      (*report_change_count) = new_svr_resp.dwBussinessServiceConfigCount;
      for (unsigned int i = 0; i < new_svr_resp.dwBussinessServiceConfigCount;
           ++i) {
        ReportRouteInfoCopy(&(report_route[i]),
                            new_svr_resp.astBussinessServiceConfig[i],
                            new_svr_resp.stPolicyInfo);
      }
    }

    if (service_change) {
      (*service_change_count) = new_svr_resp.dwBussinessServiceConfigCount;
      for (unsigned int i = 0; i < new_svr_resp.dwBussinessServiceConfigCount;
           ++i) {
        ServiceRouteInfoCopy(&(service_route[i]),
                             new_svr_resp.astBussinessServiceConfig[i]);
      }
    }
  }
}
// gdata stat: when stGdataReportAddrList changed
template <typename resp_body>
bool DirServiceApiImpBussinessMode<resp_body>::AddrChanged(
    const BussinessServiceConfig& new_config, AddrType type) {
  unsigned int i = 0;
  for (; i < svr_resp_.dwBussinessServiceConfigCount; ++i) {
    const BussinessServiceConfig& old_config =
        svr_resp_.astBussinessServiceConfig[i];
    if (new_config.stBussinessService.dwBussinessId ==
            old_config.stBussinessService.dwBussinessId &&
        new_config.stBussinessService.dwServiceId ==
            old_config.stBussinessService.dwServiceId) {
      int compare_ret =
          (type == ADDR_TYPE_SERVICE
               ? CompareAddrList(new_config.stServiceAddrList,
                                 old_config.stServiceAddrList)
               : (CompareAddrList(new_config.stReportAddrList,
                                  old_config.stReportAddrList) ||
                  CompareAddrList(new_config.stGdataReportAddrList,
                                  old_config.stGdataReportAddrList)));
      if (compare_ret) {
        return true;
      } else {
        break;
      }
    }
  }

  return i >= svr_resp_.dwBussinessServiceConfigCount;
}

}  // namespace apollo_dir

#endif /* DIR_SERVICE_API_IMP_BUSSINESS_MODE_H_ */
