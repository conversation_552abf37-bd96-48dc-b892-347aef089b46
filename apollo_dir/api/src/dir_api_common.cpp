/*
 * dir_api_common.cpp
 *
 *  Created on: 2014年3月22日
 *      Author: rogeryang
 */
#include "dir_api_common.h"

#include <sys/time.h>

#include "base_macro.h"

namespace apollo_dir {

void MultiBusinessInfoCopy(MultiBusinessInfo* dst,
                           const MultiBusinessInfo& src) {
  if (dst) {
    dst->business_count = src.business_count;
    for (unsigned int i = 0; i < src.business_count; ++i) {
      dst->business_list[i] = src.business_list[i];
    }
  }
}

uint32_t BKDRHash(const char* str) {
  uint32_t seed = 131;  // 31 131 1313 ************ etc..
  uint32_t hash = 0;
  while (*str) {
    hash = hash * seed + (*str++);
  }

  return (hash & 0x7FFFFFFF);
}

void BigAddrRouteInfoCopy(ServiceBigAddrRouteInfo* dest,
                          const ServiceBigAddrRouteInfo& src) {
  if (dest) {
    dest->bussiness_id = src.bussiness_id;
    dest->service_id = src.service_id;
    dest->region_id = src.region_id;

    dest->service_addr_count = src.service_addr_count;
    for (uint32_t i = 0; i < dest->service_addr_count; ++i) {
      STR_N_CPY(dest->service_addr_list[i].url, src.service_addr_list[i].url,
                sizeof(dest->service_addr_list[i].url));
    }
  }
}

void RouteInfoCopy(RouteInfo* dest, const RouteInfo& src) {
  if (dest) {
    dest->bussiness_id = src.bussiness_id;
    dest->service_id = src.service_id;
    dest->report_interval = src.report_interval;
    dest->service_addr_count = src.service_addr_count;
    dest->report_addr_count = src.report_addr_count;

    for (uint32_t i = 0; i < dest->service_addr_count; ++i) {
      STR_N_CPY(dest->service_addr_list[i].url, src.service_addr_list[i].url,
                sizeof(dest->service_addr_list[i].url));
    }

    for (uint32_t i = 0; i < dest->report_addr_count; ++i) {
      STR_N_CPY(dest->report_addr_list[i].url, src.report_addr_list[i].url,
                sizeof(dest->report_addr_list[i].url));
    }

    // add gdata stat report addr
    dest->gdata_report_addr_count = src.gdata_report_addr_count;
    for (uint32_t i = 0; i < dest->gdata_report_addr_count; ++i) {
      STR_N_CPY(dest->gdata_report_addr_list[i].url,
                src.gdata_report_addr_list[i].url,
                sizeof(dest->gdata_report_addr_list[i].url));
    }
  }
}

void ReportRouteInfoCopy(ReportRouteInfo* report_route,
                         const BussinessServiceConfig& config,
                         const PolicyInfo& policy) {
  if (report_route) {
    report_route->bussiness_id = config.stBussinessService.dwBussinessId;
    report_route->service_id = config.stBussinessService.dwServiceId;
    report_route->report_interval = policy.dwReportInterval;
    report_route->report_addr_count = config.stReportAddrList.dwAddrCount;
    for (uint32_t i = 0; i < report_route->report_addr_count; ++i) {
      STR_N_CPY(report_route->report_addr_list[i].url,
                config.stReportAddrList.astAddrList[i].szUrl,
                sizeof(report_route->report_addr_list[i].url));
    }

    // gdata stat: when gdata report addr changed, copy newest from config
    // no need to protect to avoid memory out of bounds, as other logic had
    // checked and make sure it will not happen
    report_route->gdata_report_addr_count =
        config.stGdataReportAddrList.dwAddrCount;
    for (uint32_t i = 0; i < report_route->gdata_report_addr_count; ++i) {
      STR_N_CPY(report_route->gdata_report_addr_list[i].url,
                config.stGdataReportAddrList.astAddrList[i].szUrl,
                sizeof(report_route->gdata_report_addr_list[i].url));
    }
  }
}

uint64_t RemainTimeMs(struct timeval* begin, uint32_t timeout_ms) {
  struct timeval now;
  uint64_t remain = 0;
  uint64_t begin_ms = 0;
  uint64_t now_ms = 0;

  gettimeofday(&now, NULL);

  begin_ms = begin->tv_sec * 1000 + begin->tv_usec / 1000;
  now_ms = now.tv_sec * 1000 + now.tv_usec / 1000;

  if (now_ms >= begin_ms) {
    uint32_t used_ms = now_ms - begin_ms;
    if (used_ms >= timeout_ms) {
      remain = 0;
    } else {
      remain = timeout_ms - used_ms;
    }
  }

  return remain;
}

void ServiceRouteInfoCopy(ServiceRouteInfo* service_route,
                          const BussinessServiceConfig& config) {
  if (service_route) {
    service_route->bussiness_id = config.stBussinessService.dwBussinessId;
    service_route->service_id = config.stBussinessService.dwServiceId;
    service_route->service_addr_count = config.stServiceAddrList.dwAddrCount;
    for (uint32_t i = 0; i < service_route->service_addr_count; ++i) {
      STR_N_CPY(service_route->service_addr_list[i].url,
                config.stServiceAddrList.astAddrList[i].szUrl,
                sizeof(service_route->service_addr_list[i].url));
    }
  }
}

void AddrListCopy(apollo_dir::Addr* dest, const dir_svr::AddrList& src) {
  for (uint32_t i = 0; i < src.dwAddrCount; ++i) {
    STR_N_CPY(dest[i].url, src.astAddrList[i].szUrl, sizeof(dest[i].url));
  }
}

int CompareAddrList(const dir_svr::AddrList& list1,
                    const dir_svr::AddrList& list2) {
  if (list1.dwAddrCount != list2.dwAddrCount) {
    return -1;
  }

  for (uint32_t i = 0; i < list2.dwAddrCount; ++i) {
    // const dir_svr::Addr* iter = find( list1.astAddrList,
    // list1.astAddrList+list1.dwAddrCount, list2.astAddrList[i] );

    const dir_svr::Addr* iter = list1.astAddrList;
    for (; iter != list1.astAddrList + list1.dwAddrCount; ++iter) {
      if (*iter == list2.astAddrList[i]) {
        break;
      }
    }

    if (iter == list1.astAddrList + list1.dwAddrCount) {
      return -1;
    }
  }

  return 0;
}

int CompareBigAddrList(const dir_svr::BigAddrList& list1,
                       const dir_svr::BigAddrList& list2) {
  if (list1.dwAddrCount != list2.dwAddrCount) {
    return -1;
  }

  for (uint32_t i = 0; i < list2.dwAddrCount; ++i) {
    // const dir_svr::Addr* iter = find( list1.astAddrList,
    // list1.astAddrList+list1.dwAddrCount, list2.astAddrList[i] );

    const dir_svr::Addr* iter = list1.astAddrList;
    for (; iter != list1.astAddrList + list1.dwAddrCount; ++iter) {
      if (*iter == list2.astAddrList[i]) {
        break;
      }
    }

    if (iter == list1.astAddrList + list1.dwAddrCount) {
      return -1;
    }
  }

  return 0;
}

void VisualServiceRouteInfo(char* dest_str, size_t dest_len,
                            uint32_t service_route_count,
                            const ServiceRouteInfo* service_route) {
  if (dest_str && dest_len && service_route) {
    for (uint32_t i = 0; i < service_route_count; ++i) {
      DECLARE_STR(tmp, 40960);
      snprintf(tmp, sizeof(tmp),
               "bussiness_id[%u] service_id[%u] service_addr_count[%u]\n",
               service_route[i].bussiness_id, service_route[i].service_id,
               service_route[i].service_addr_count);
      VisualAddrs(tmp, sizeof(tmp) - strlen(tmp),
                  service_route[i].service_addr_count,
                  service_route[i].service_addr_list);
      if (dest_len < strlen(tmp) + 1) {
        return;
      }
      strncat(dest_str, tmp, dest_len - 1);
      dest_len -= strlen(tmp);
    }
  }
}

void VisualReportRouteInfo(char* dest_str, size_t dest_len,
                           uint32_t report_route_count,
                           const ReportRouteInfo* report_route) {
  if (dest_str && dest_len && report_route) {
    for (uint32_t i = 0; i < report_route_count; ++i) {
      DECLARE_STR(tmp, 40960);
      snprintf(tmp, sizeof(tmp),
               "bussiness_id[%u] service_id[%u] report_interval[%u] "
               "report_addr_count[%u]\n",
               report_route[i].bussiness_id, report_route[i].service_id,
               report_route[i].report_interval,
               report_route[i].report_addr_count);
      VisualAddrs(tmp, sizeof(tmp) - strlen(tmp),
                  report_route[i].report_addr_count,
                  report_route[i].report_addr_list);
      if (dest_len < strlen(tmp) + 1) {
        return;
      }
      strncat(dest_str, tmp, dest_len - 1);
      dest_len -= strlen(tmp);
    }
  }
}

void VisualAddrs(char* dest_str, size_t dest_len, uint32_t addr_count,
                 const apollo_dir::Addr* addr_list) {
  if (dest_str && dest_len && addr_count && addr_list) {
    for (uint32_t i = 0; i < addr_count; ++i) {
      DECLARE_STR(tmp, 128);
      snprintf(tmp, sizeof(tmp), "%s\n", addr_list[i].url);
      if (dest_len < strlen(tmp) + 1) {
        return;
      }
      strncat(dest_str, tmp, dest_len - 1);
      dest_len -= strlen(tmp);
    }
  }
}

void ServiceInfoCopy(apollo_dir::ServiceInfo* dest,
                     const dir_svr::ServiceInfo& src) {
  dest->service_id = src.dwServiceId;
  dest->magic_value = src.stProtocolInfo.wMagicValue;
  STR_N_CPY(dest->service_name, src.szServiceName, sizeof(dest->service_name));
}

}  // namespace apollo_dir
