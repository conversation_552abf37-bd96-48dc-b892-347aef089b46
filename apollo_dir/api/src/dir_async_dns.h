#ifndef _DIR_ASYNC_DNS_H_
#define _DIR_ASYNC_DNS_H_
#include <netdb.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <sys/time.h>

#include <string>
#include <set>

#include "comm/comm.h"
#include "dir_log.h"

namespace apollo_dir {
uint64_t milli_seconds();
class IAsyncDnsNotify {
 public:
  IAsyncDnsNotify() {}
  virtual ~IAsyncDnsNotify() {}
  virtual void OnDomainResoluted(const std::set<std::string>& ip_addrs) = 0;
};

enum DnsParseStatus { kInited = 0, kParsing = 1 };

class AsyncDns {
 public:
  struct Options {
    std::string domain;
    uint32_t interval;
  };

  AsyncDns();
  virtual ~AsyncDns();
  void set_logger(TLOGCATEGORYINST* logger) { logger_ = logger; }
  void ResetInterval(uint32_t interval) { opts_.interval = interval; }
  int Subscribe(const AsyncDns::Options& opts, IAsyncDnsNotify* notifier);
  void Immediately();
  void Tick();
  void Close();

 private:
  void AsyncRequest();
  void CheckAndProcAyncResult();
  struct addrinfo* Hints();
  struct addrinfo* hints_;
  struct gaicb* req_ptrs_[1];
  bool subscribed_;
  DnsParseStatus status_;
  AsyncDns::Options opts_;
  IAsyncDnsNotify* notifier_;
  uint64_t last_req_ms_;
  uint64_t last_rsp_check_ms_;
  TLOGCATEGORYINST* logger_;
  bool immediate_resolove_;
};
}  // namespace apollo_dir
#endif
