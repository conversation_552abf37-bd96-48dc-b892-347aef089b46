/*
 * Copyright (c) 2013 Tencent all rights reserved
 *
 * time:   2014-03-20
 * file:   dir_service_api_imp.h
 * desc:   api实现类
 *
 */

#ifndef DIR_SERVICE_API_IMP_H_
#define DIR_SERVICE_API_IMP_H_

#include "dir_event_listener.h"
#include "dir_protocol.h"
#include "dir_route_manager.h"
#include "dir_service_api_imp_big_addr_mode.h"
#include "dir_service_api_imp_mode.h"
#include "dir_service_api_imp_service_mode.h"
#include "dir_service_error.h"
#include "dir_svr_accessor.h"
#include "dir_types.h"
#include "tlog/tlog.h"

using dir_svr::BussinessService;
using dir_svr::PolicyInfo;
using dir_svr::TDIR_MAX_ADDR_NUM;
using dir_svr::TDIR_MAX_KEY_LEN;
using dir_svr::TDIR_MAX_MULTI_ROUTE_NUM;
using dir_svr::TDIR_MAX_REQ_ROUTE_NUM;
using dir_svr::TDIR_MAX_SERVICE_COUNT;
using dir_svr::TDirGetConfigByBussinessResp;

namespace apollo_dir {
template <class T>
class DirServiceApiImpBussinessMode;

enum {
  DIR_SVR_READY = 0,
  DIR_SVR_INIT,
};

class DirServiceApiImpMode;

class DirServiceApiImp {
 public:
  DirServiceApiImp();
  virtual ~DirServiceApiImp();

  int AddDirUrl(const char* dir_url);
  void ClearDirUrl();
  bool UsingDomain();
  int RegistBussinessService(uint32_t bussiness_id, uint32_t service_id,
                             const char* bussiness_key);
  int SetBussiness(IN uint32_t business_id, const char* business_key);
  int SetMultiBusiness(IN const MultiBusinessInfo& business_info);
  int Init(uint32_t timeout, BaseDirEventListener* event_listener,
           TLOGCATEGORYINST* logger);

  int RegistBigAddrService(uint32_t bussiness_id, uint32_t region_id,
                           uint32_t service_id, const char* bussiness_key);
  void Fini();

  int GetAllBussinessServiceRoute(RouteInfo* list, uint32_t* size);

  int GetBigAddrServiceRoute(OUT ServiceBigAddrRouteInfo* route_info,
                             OUT ReportRouteInfo* report_route_info);

  int GetBussinessServiceRoute(uint32_t bussiness_id, uint32_t service_id,
                               Addr* addr);
  void ReportBussinessModRouteCallResult(uint32_t bussiness_id,
                                         uint32_t service_id, const Addr* addr,
                                         int result, uint32_t elapsed_ms);
  int GetAllServiceInfo(OUT ServiceInfo* service_info, INOUT uint32_t* size);
  int Update();

 private:
  void FillRouteInfo(const BussinessServiceConfig* config,
                     uint32_t config_count, const PolicyInfo& policy_info);
  void FillRoute4BigAddr(const TDirGetBigAddrResp* resp);
  void ProcessRouteAndPolicyInfo(const TDirMsg& svr_resp);
  void ProcessBigAddrRouteAndPolicyInfo(const TDirMsg& svr_resp);
  void LogCurrentRouteInfo(int priority);
  void LogChangedRoute(int pri, ServiceRouteInfo* service_route,
                       uint32_t* service_change_count,
                       ReportRouteInfo* report_route,
                       uint32_t* report_change_count);

  template <typename RESP, typename RESP1>
  int ResetRouteMgr(const RESP& new_svr_resp, const RESP1& old_svr_resp) {
    if (NULL == svr_accessor_ || NULL == route_mgr_) {
      return APOLLO_DIR_ERROR_ALLOC_FAIL;
    }

    route_mgr_->Fini();
    int ret = route_mgr_->Init(new_svr_resp, logger_);
    if (ret) {
      DIR_LOG_ERROR(logger_, "route_mgr_ Init failed, ret=%d", ret);
      route_mgr_->Fini();
      ret = route_mgr_->Init(old_svr_resp, logger_);
      if (ret) {
        DIR_LOG_ERROR(logger_, "route_mgr_ Init with old resp failed, ret=%d",
                      ret);
      } else {
        DIR_LOG_INFO(logger_, "route_mgr_ Init with old resp succ");
      }
      return APOLLO_DIR_ERROR_SYS;
    }

    return APOLLO_DIR_ERROR_NONE;
  }

  bool IsMultiBusiness() { return is_multi_business_; }

  /* 方便测试使用 */
  void SetSvrResp(const TDirGetConfigResp& resp);
  void SetSvrResp(const TDirGetConfigByBussinessResp& resp);
  void SetSvrResp(const TDirGetMultiConfigResp& resp);

  void SetSvrAccessor(DirSvrAccessor* accessor);
  DirServiceApiImpBussinessMode<TDirGetConfigByBussinessResp>*
  GetBussinessMode() {
    return bussiness_mode_;
  }
  DirServiceApiImpServiceMode* GetServiceMode() { return &service_mode_; }

  static const uint32_t DEFAULT_MIN_TIMEOUT = 1000;  // 单位毫秒
  bool inited_;
  TLOGCATEGORYINST* logger_;

  /* 拉取固定服务路由信息使用 */
  uint32_t service_count_;
  BussinessService service_[TDIR_MAX_REQ_ROUTE_NUM];

  BaseDirEventListener* event_listener_;

  DirRouteManager* route_mgr_;
  DirSvrAccessor* svr_accessor_;

  /* route的数量就是module_count_, 所以这里没有单独定义一个route_count_ */
  uint32_t route_count_;
  RouteInfo route_info_[TDIR_MAX_MULTI_ROUTE_NUM];

  ServiceBigAddrRouteInfo bigaddr_route_info_;
  ReportRouteInfo report_route_info_;

  DirServiceApiImpMode* mode_;
  DirServiceApiImpBussinessMode<TDirGetConfigByBussinessResp>* bussiness_mode_;
  DirServiceApiImpBussinessMode<TDirGetMultiConfigResp>* business_multi_mode_;
  DirServiceApiImpServiceMode service_mode_;
  DirServiceApiImpBigAddrMode big_addr_service_mode_;

  /* 拉取单业务全量服务路由信息使用 */
  uint32_t bussiness_id_;
  char bussiness_key_[TDIR_MAX_KEY_LEN];

  uint32_t service_info_count_;
  ServiceInfo service_info_[TDIR_MAX_SERVICE_COUNT];

  /* 拉取多业务全量服务路由信息使用 */
  MultiBusinessInfo business_info_;
  bool is_multi_business_;

  BigAddrServiceInfo big_addr_service_info_;
  TDirMsg svr_resp_;

  friend class DirServiceApiImpMode;
  friend class DirServiceApiImpBussinessMode<TDirGetConfigByBussinessResp>;
  friend class DirServiceApiImpBussinessMode<TDirGetMultiConfigResp>;
  friend class DirServiceApiImpServiceMode;
  friend class DirServiceApiImpBigAddrMode;

  ServiceBigAddrRouteInfo bigaddr_service_change_route_;
  ReportRouteInfo bigaddr_report_change_route_[MAX_MULTI_ROUTE_NUM];
  ServiceRouteInfo service_change_route_[TDIR_MAX_MULTI_ROUTE_NUM];
  ReportRouteInfo report_change_route_[MAX_MULTI_ROUTE_NUM];
};

}  // namespace apollo_dir

#endif  // DIR_SERVICE_API_IMP_H_
