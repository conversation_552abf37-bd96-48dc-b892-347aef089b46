/*
 * Copyright (c) 2013 Tencent all rights reserved
 *
 * time:   2014-03-20
 * file:   dir_service_api_imp.cpp
 * desc:   undo
 *
 */

#include "dir_service_api_imp.h"

#include <string.h>

#include <algorithm>

#include "base_macro.h"
#include "dir_api_common.h"
#include "dir_common_func.h"
#include "dir_log.h"
#include "dir_service_api_imp_bussiness_mode.h"
#include "dir_service_error.h"

using namespace std;

namespace apollo_dir {

DirServiceApiImp::DirServiceApiImp()
    : inited_(false),
      logger_(NULL),
      service_count_(0),
      event_listener_(NULL),
      route_mgr_(new(std::nothrow) DirRouteManager),
      svr_accessor_(new(std::nothrow) DirSvrAccessor),
      route_count_(0),
      bussiness_id_(0),
      service_info_count_(0),
      is_multi_business_(false) {
  memset(&service_, 0, sizeof(service_));
  bussiness_mode_ =
      new DirServiceApiImpBussinessMode<TDirGetConfigByBussinessResp>();
  bussiness_mode_->SetImp(this);
  business_multi_mode_ =
      new DirServiceApiImpBussinessMode<TDirGetMultiConfigResp>();
  business_multi_mode_->SetImp(this);
  service_mode_.SetImp(this);
  big_addr_service_mode_.SetImp(this);
  mode_ = NULL;  // 默认是service模式
  memset(&big_addr_service_info_, 0, sizeof(big_addr_service_info_));
  bigaddr_route_info_.service_addr_count = 0;
  report_route_info_.report_addr_count = 0;
  memset(bussiness_key_, 0, sizeof(bussiness_key_));
}  // lint !e1401

DirServiceApiImp::~DirServiceApiImp() {
  logger_ = NULL;
  event_listener_ = NULL;
  SAFE_DELETE(route_mgr_);
  SAFE_DELETE(svr_accessor_);
  SAFE_DELETE(bussiness_mode_);
  SAFE_DELETE(business_multi_mode_);
}

int DirServiceApiImp::AddDirUrl(const char* dir_url) {
  if (NULL == svr_accessor_) {
    DIR_LOG_ERROR(logger_, "svr_accessor_ is null");
    return APOLLO_DIR_ERROR_ALLOC_FAIL;
  }
  return svr_accessor_->AddDirUrl(dir_url);
}

void DirServiceApiImp::ClearDirUrl() {
  if (NULL == svr_accessor_) {
    DIR_LOG_ERROR(logger_, "svr_accessor_ is null");
  } else {
    svr_accessor_->ClearDirUrl();
  }
}

bool DirServiceApiImp::UsingDomain() {
  if (NULL == svr_accessor_) {
    DIR_LOG_ERROR(logger_, "svr_accessor_ is null");
    return false;
  }
  return svr_accessor_->UsingDomain();
}

int DirServiceApiImp::RegistBigAddrService(uint32_t bussiness_id,
                                           uint32_t region_id,
                                           uint32_t service_id,
                                           const char* bussiness_key) {
  // 在Init之后不允许再次调用本接口
  if (inited_) {
    return APOLLO_DIR_ERROR_INITED;
  }

  // 之前已经调用了其他方法
  if (mode_ && (DirServiceApiImpMode::IMP_MODE_BIG_ADDR != mode_->GetMode())) {
    return APOLLO_DIR_ERROR_MODE_CONFLICT;
  }

  bussiness_id_ = bussiness_id;
  if (NULL == bussiness_key) {
    bussiness_key_[0] = 0;
  } else {
    STR_N_CPY(bussiness_key_, bussiness_key, sizeof(bussiness_key_));
  }

  big_addr_service_info_.dwRegionId = region_id;
  big_addr_service_info_.dwServiceId = service_id;

  mode_ = &big_addr_service_mode_;
  return 0;
}

int DirServiceApiImp::RegistBussinessService(uint32_t bussiness_id,
                                             uint32_t service_id,
                                             const char* bussiness_key) {
  // 在Init之后不允许再次调用本接口
  if (inited_) {
    return APOLLO_DIR_ERROR_INITED;
  }

  // 之前已经调用了其他方法
  if (mode_ && (DirServiceApiImpMode::IMP_MODE_SERVICE != mode_->GetMode())) {
    return APOLLO_DIR_ERROR_MODE_CONFLICT;
  }

  if (service_count_ >= (uint32_t)MAX_REQ_SERVICE_COUNT) {
    return APOLLO_DIR_ERROR_BUFFER_IS_SHORT;
  }

  // 不允许重复注册
  for (uint32_t i = 0; i < service_count_; i++) {
    if (bussiness_id == service_[i].dwBussinessId &&
        service_id == service_[i].dwServiceId) {
      return APOLLO_DIR_ERROR_EXISTING_MODULE;
    }
  }

  service_[service_count_].dwBussinessId = bussiness_id;
  service_[service_count_].dwServiceId = service_id;
  if (NULL == bussiness_key) {
    service_[service_count_].szBussinessKey[0] = 0;
  } else {
    STR_N_CPY(service_[service_count_].szBussinessKey, bussiness_key,
              sizeof(service_[service_count_].szBussinessKey));
  }
  service_count_++;

  mode_ = &service_mode_;

  return APOLLO_DIR_ERROR_NONE;
}

void DirServiceApiImp::FillRoute4BigAddr(const TDirGetBigAddrResp* resp) {
  if (!resp) {
    return;
  }

  // 上报的路由信息
  report_route_info_.bussiness_id = resp->dwBussinessId;
  report_route_info_.service_id = resp->dwServiceId;
  report_route_info_.report_interval = resp->stPolicyInfo.dwReportInterval;
  report_route_info_.report_addr_count = resp->stReportAddrList.dwAddrCount;
  AddrListCopy(report_route_info_.report_addr_list, resp->stReportAddrList);

  bigaddr_route_info_.bussiness_id = resp->dwBussinessId;
  bigaddr_route_info_.service_id = resp->dwServiceId;
  bigaddr_route_info_.region_id = resp->dwRegionId;
  bigaddr_route_info_.service_addr_count = resp->stBigAddrList.dwAddrCount;

  for (uint32_t i = 0; i < bigaddr_route_info_.service_addr_count; ++i) {
    STR_N_CPY(bigaddr_route_info_.service_addr_list[i].url,
              resp->stBigAddrList.astAddrList[i].szUrl, MAX_HOST_LEN);
  }
}

void DirServiceApiImp::FillRouteInfo(const BussinessServiceConfig* config,
                                     uint32_t config_count,
                                     const PolicyInfo& policy_info) {
  route_count_ = config_count;
  for (uint32_t i = 0; i < route_count_; ++i) {
    // copy service信息
    const BussinessServiceConfig& tmp_config = config[i];

    // copy 路由信息
    route_info_[i].bussiness_id = tmp_config.stBussinessService.dwBussinessId;
    route_info_[i].service_id = tmp_config.stBussinessService.dwServiceId;
    route_info_[i].report_interval = policy_info.dwReportInterval;

    if (tmp_config.stReportAddrList.dwAddrCount >
            ARRAY_ITEM_COUNT(route_info_[i].report_addr_list) ||
        tmp_config.stServiceAddrList.dwAddrCount >
            ARRAY_ITEM_COUNT(route_info_[i].service_addr_list)) {
      // 返回的地址数目大于最大值，不应该出现
      DIR_LOG_ERROR(logger_,
                    "suprise!!!! svr resp addr count is too large, "
                    "report_addr_count=%u, service_addr_count=%u",
                    tmp_config.stReportAddrList.dwAddrCount,
                    tmp_config.stServiceAddrList.dwAddrCount);
      return;
    }

    route_info_[i].service_addr_count =
        tmp_config.stServiceAddrList.dwAddrCount;
    route_info_[i].report_addr_count = tmp_config.stReportAddrList.dwAddrCount;
    AddrListCopy(route_info_[i].service_addr_list,
                 tmp_config.stServiceAddrList);
    AddrListCopy(route_info_[i].report_addr_list, tmp_config.stReportAddrList);

    // add gdata stat report addr, dir service api copy from config xml file to
    // memory
    route_info_[i].gdata_report_addr_count =
        tmp_config.stGdataReportAddrList.dwAddrCount;
    AddrListCopy(route_info_[i].gdata_report_addr_list,
                 tmp_config.stGdataReportAddrList);
  }
}

void DirServiceApiImp::LogCurrentRouteInfo(int priority) {
  if (CanLog(logger_, priority)) {
    DECLARE_STR(visual, 65535);

    for (uint32_t i = 0; i < route_count_; ++i) {
      DECLARE_STR(service_visual, 10240);
      snprintf(service_visual, sizeof(service_visual),
               "bussiness_id[%u]\t service_id[%u]\n"
               "\t\treport_interval[%u]\n"
               "\t\tservice_addr_count[%u]\n",
               route_info_[i].bussiness_id, route_info_[i].service_id,
               route_info_[i].report_interval,
               route_info_[i].service_addr_count);
      VisualAddrs(
          service_visual, sizeof(service_visual) - strlen(service_visual),
          route_info_[i].service_addr_count, route_info_[i].service_addr_list);

      DECLARE_STR(tmp, 128);
      snprintf(tmp, sizeof(tmp), "report_addr_count[%u]\n",
               route_info_[i].report_addr_count);
      strncat(service_visual, tmp,
              sizeof(service_visual) - strlen(service_visual) - 1);
      VisualAddrs(
          service_visual, sizeof(service_visual) - strlen(service_visual),
          route_info_[i].report_addr_count, route_info_[i].report_addr_list);

      if (sizeof(visual) > strlen(visual) &&
          sizeof(visual) - strlen(visual) >
              strlen(service_visual) + 1)  // lint !e574
      {
        strncat(visual, service_visual, sizeof(visual) - strlen(visual) - 1);
      } else {
        break;
      }
    }

    DIR_LOG(logger_, priority, "current route info:\n%s\n", visual);
  }
}

int DirServiceApiImp::Init(uint32_t timeout,
                           BaseDirEventListener* event_listener,
                           TLOGCATEGORYINST* logger) {
  if (timeout < DEFAULT_MIN_TIMEOUT) {
    return APOLLO_DIR_ERROR_INVALID_ARG;
  }

  if (inited_) {
    return APOLLO_DIR_ERROR_INITED;
  }

  if (NULL == mode_) {
    // 没有调用过RegistBussinessService或SetBussiness
    return APOLLO_DIR_ERROR_NO_MODULE;
  }

  if (NULL == svr_accessor_ || NULL == route_mgr_) {
    DIR_LOG_ERROR(logger_, "svr_accessor_ or route_mgr_ is null");
    return APOLLO_DIR_ERROR_ALLOC_FAIL;
  }

  logger_ = logger;
  event_listener_ = event_listener;

  // 从svr拉取信息，并初始化路由数据
  int ret = mode_->Init(timeout);
  if (ret != 0) {
    DIR_LOG_ERROR(logger_, "mode Init failed, ret=%d", ret);
    return ret;
  }

  LogCurrentRouteInfo(TLOG_PRIORITY_DEBUG);

  inited_ = true;

  DIR_LOG_DEBUG(logger_, "api init succ");

  return APOLLO_DIR_ERROR_NONE;
}

void DirServiceApiImp::Fini() {
  service_count_ = 0;
  event_listener_ = NULL;
  route_count_ = 0;

  IF_NOTNULL_CALL(route_mgr_, route_mgr_->Fini());
  IF_NOTNULL_CALL(svr_accessor_, svr_accessor_->Fini());

  bussiness_id_ = 0;
  bussiness_key_[0] = 0;

  bussiness_mode_->Fini();
  business_multi_mode_->Fini();
  service_mode_.Fini();
  big_addr_service_mode_.Fini();

  mode_ = NULL;

  service_info_count_ = 0;

  inited_ = false;

  DIR_LOG_DEBUG(logger_, "api fini succ");
}

int DirServiceApiImp::GetBigAddrServiceRoute(
    OUT ServiceBigAddrRouteInfo* route_info,
    OUT ReportRouteInfo* report_route_info) {
  if (!inited_) {
    return APOLLO_DIR_ERROR_NOT_INITED;
  }

  if ((NULL == route_info) || (NULL == report_route_info)) {
    return APOLLO_DIR_ERROR_INVALID_ARG;
  }

  BigAddrRouteInfoCopy(route_info, bigaddr_route_info_);
  memcpy(report_route_info, &report_route_info_, sizeof(ReportRouteInfo));
  // TODO: 看看要不要优化
  // ReportRouteInfoCopy(report_route_info, report_route_info_);
  return 0;
}

int DirServiceApiImp::GetAllBussinessServiceRoute(RouteInfo* list,
                                                  uint32_t* size) {
  if (!inited_) {
    return APOLLO_DIR_ERROR_NOT_INITED;
  }

  if (NULL == list || NULL == size) {
    return APOLLO_DIR_ERROR_INVALID_ARG;
  }

  if ((*size) < route_count_) {
    return APOLLO_DIR_ERROR_BUFFER_IS_SHORT;
  }

  for (uint32_t i = 0; i < route_count_; ++i) {
    RouteInfoCopy(list + i, route_info_[i]);
  }

  (*size) = route_count_;

  return APOLLO_DIR_ERROR_NONE;
}

int DirServiceApiImp::GetBussinessServiceRoute(uint32_t bussiness_id,
                                               uint32_t service_id,
                                               Addr* addr) {
  if (!inited_) {
    return APOLLO_DIR_ERROR_NOT_INITED;
  }

  if (NULL == route_mgr_) {
    DIR_LOG_ERROR(logger_, "route_mgr_ is null");
    return APOLLO_DIR_ERROR_ALLOC_FAIL;
  }

  int ret = 0;

  ret = route_mgr_->GetRoute(bussiness_id, service_id, addr);
  if (ret) {
    DIR_LOG_ERROR(
        logger_,
        "route_mgr_ GetRoute failed, ret=%d, bussiness_id=%u, service_id=%u",
        ret, bussiness_id, service_id);
  }

  return ret;
}

void DirServiceApiImp::ReportBussinessModRouteCallResult(uint32_t bussiness_id,
                                                         uint32_t service_id,
                                                         const Addr* addr,
                                                         int result,
                                                         uint32_t elapsed_ms) {
  if (inited_ && route_mgr_) {
    route_mgr_->ReportRouteCallResult(bussiness_id, service_id, addr, result,
                                      elapsed_ms);
  }
}

int DirServiceApiImp::Update() {
  if (!inited_) {
    return APOLLO_DIR_ERROR_NOT_INITED;
  }

  if (NULL == svr_accessor_ || NULL == route_mgr_) {
    return APOLLO_DIR_ERROR_ALLOC_FAIL;
  }

  int ret = 0;

  ret = svr_accessor_->Update(&svr_resp_);
  if (DirSvrAccessor::ACCESSOR_BUSY_GOT_RESULT == ret) {
    DIR_LOG_DEBUG(logger_, "got resp from svr");
    // DIR_VISUALIZE( logger_, svr_resp, TLOG_PRIORITY_DEBUG );
    switch (svr_resp_.stHead.wCommand) {
      case TDIR_CMD_GET_CONFIG_RESP:
      case TDIR_CMD_GET_CONFIG_BY_BUSSINESS_RESP:
      case TDIR_CMD_GET_MULTI_CONFIG_RESP:
        ProcessRouteAndPolicyInfo(svr_resp_);
        break;
      case TDIR_CMD_GET_BIG_ADDR_RESP:
        ProcessBigAddrRouteAndPolicyInfo(svr_resp_);
        break;
      default:
        DIR_LOG_ERROR(logger_, "unknow svr rsp, cmd=%u",
                      svr_resp_.stHead.wCommand);
        break;
    }
  }

  route_mgr_->Update();

  return ret;
}

void DirServiceApiImp::ProcessBigAddrRouteAndPolicyInfo(
    const TDirMsg& svr_resp) {
  //    static ServiceBigAddrRouteInfo bigaddr_service_change_route;
  //    static ReportRouteInfo report_change_route[MAX_MULTI_ROUTE_NUM];
  char service_big_addr_change_flag = 0;
  uint32_t report_change_count = 0;

  int ret = mode_->ProcessBigAddrAndPolicyInfo(
      svr_resp, &bigaddr_service_change_route_, service_big_addr_change_flag,
      bigaddr_report_change_route_, &report_change_count);
  if (ret) {
    DIR_LOG_ERROR(logger_, "mode ProcessRouteAndPolicyInfo failed, ret=%d",
                  ret);
    return;
  }

  // 在内部都调整完毕以后再触发事件通知
  if (service_big_addr_change_flag && event_listener_) {
    DIR_LOG_DEBUG(logger_, "fire on service change event");
    event_listener_->OnServiceBigAddrChange(&bigaddr_service_change_route_);
  }

  if (report_change_count && event_listener_) {
    DIR_LOG_DEBUG(logger_, "fire on report change event");
    event_listener_->OnReportChange(bigaddr_report_change_route_,
                                    report_change_count);
  }
}

void DirServiceApiImp::ProcessRouteAndPolicyInfo(const TDirMsg& svr_resp) {
  //    static ServiceRouteInfo service_change_route[TDIR_MAX_MULTI_ROUTE_NUM];
  //    static ReportRouteInfo report_change_route[MAX_MULTI_ROUTE_NUM];
  uint32_t service_change_count = 0;
  uint32_t report_change_count = 0;

  int ret = mode_->ProcessRouteAndPolicyInfo(
      svr_resp, service_change_route_, &service_change_count,
      report_change_route_, &report_change_count);
  if (ret) {
    DIR_LOG_ERROR(logger_, "mode ProcessRouteAndPolicyInfo failed, ret=%d",
                  ret);
    return;
  }

  // 在内部都调整完毕以后再触发事件通知
  if (service_change_count && event_listener_) {
    DIR_LOG_DEBUG(logger_, "fire on service change event");

    event_listener_->OnServiceChange(service_change_route_,
                                     service_change_count);
  }

  if (report_change_count && event_listener_) {
    DIR_LOG_DEBUG(logger_, "fire on report change event");
    event_listener_->OnReportChange(report_change_route_, report_change_count);
  }
}

void DirServiceApiImp::LogChangedRoute(int pri, ServiceRouteInfo* service_route,
                                       uint32_t* service_change_count,
                                       ReportRouteInfo* report_route,
                                       uint32_t* report_change_count) {
  if (CanLog(logger_, pri)) {
    DECLARE_STR(visual, 65535);
    snprintf(visual, sizeof(visual),
             "service_change_count[%u] report_change_count[%u]\n",
             *service_change_count, *report_change_count);
    VisualServiceRouteInfo(visual, sizeof(visual) - strlen(visual),
                           *service_change_count, service_route);
    VisualReportRouteInfo(visual, sizeof(visual) - strlen(visual),
                          *report_change_count, report_route);
    DIR_LOG(logger_, pri, visual);
  }
}

void DirServiceApiImp::SetSvrResp(const TDirGetConfigResp& resp) {
  service_mode_.SetSvrResp(resp);
}

void DirServiceApiImp::SetSvrResp(const TDirGetConfigByBussinessResp& resp) {
  bussiness_mode_->SetSvrResp(resp);
}

void DirServiceApiImp::SetSvrResp(const TDirGetMultiConfigResp& resp) {
  business_multi_mode_->SetSvrResp(resp);
}

void DirServiceApiImp::SetSvrAccessor(DirSvrAccessor* accessor) {
  if (accessor && accessor != svr_accessor_) {
    SAFE_DELETE(svr_accessor_);
    svr_accessor_ = accessor;
  }
}

int DirServiceApiImp::SetBussiness(IN uint32_t bussiness_id,
                                   const char* bussiness_key) {
  if (inited_) {
    return APOLLO_DIR_ERROR_INITED;
  }

  // 之前已经调用了其他方法
  if (mode_ && (DirServiceApiImpMode::IMP_MODE_BUSSINESS != mode_->GetMode())) {
    return APOLLO_DIR_ERROR_MODE_CONFLICT;
  }

  bussiness_id_ = bussiness_id;
  if (NULL == bussiness_key) {
    bussiness_key_[0] = 0;
  } else {
    STR_N_CPY(bussiness_key_, bussiness_key, sizeof(bussiness_key_));
  }

  mode_ = bussiness_mode_;
  is_multi_business_ = false;

  return 0;
}

int DirServiceApiImp::SetMultiBusiness(
    IN const MultiBusinessInfo& business_info) {
  // 之前已经调用了其他方法
  if (mode_ && (DirServiceApiImpMode::IMP_MODE_BUSSINESS != mode_->GetMode())) {
    return APOLLO_DIR_ERROR_MODE_CONFLICT;
  }

  if (0 == business_info.business_count) {
    return APOLLO_DIR_ERROR_INVALID_ARG;
  }

  if (NULL == svr_accessor_) {
    DIR_LOG_ERROR(logger_, "svr_accessor_ is null");
    return APOLLO_DIR_ERROR_ALLOC_FAIL;
  }

  business_info_ = business_info;
  mode_ = business_multi_mode_;
  is_multi_business_ = true;

  svr_accessor_->trans_mgr_.MultiBusiness(business_info);

  return 0;
}

int DirServiceApiImp::GetAllServiceInfo(OUT ServiceInfo* service_info,
                                        INOUT uint32_t* size) {
  if (!inited_) {
    return APOLLO_DIR_ERROR_NOT_INITED;
  }

  if (mode_->GetMode() != DirServiceApiImpMode::IMP_MODE_BUSSINESS) {
    return APOLLO_DIR_ERROR_WRONG_MODE;
  }

  if (NULL == service_info || NULL == size) {
    return APOLLO_DIR_ERROR_INVALID_ARG;
  }

  if (*size < service_info_count_) {
    return APOLLO_DIR_ERROR_BUFFER_IS_SHORT;
  }

  ::copy(service_info_, service_info_ + service_info_count_, service_info);
  *size = service_info_count_;

  return 0;
}

}  // namespace apollo_dir
