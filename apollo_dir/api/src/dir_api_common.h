/*
 * dir_api_common.h
 *
 *  Created on: 2014年3月22日
 *      Author: rogeryang
 */

#ifndef DIR_API_COMMON_H_
#define DIR_API_COMMON_H_

#include <sys/time.h>

#include "dir_protocol.h"
#include "dir_types.h"

using dir_svr::BussinessServiceConfig;
using dir_svr::PolicyInfo;

namespace apollo_dir {
uint32_t BKDRHash(const char* str);
void RouteInfoCopy(RouteInfo* dest, const RouteInfo& src);
uint64_t RemainTimeMs(struct timeval* begin, uint32_t timeout_ms);
void ReportRouteInfoCopy(ReportRouteInfo* report_route,
                         const BussinessServiceConfig& config,
                         const PolicyInfo& policy);
void BigAddrRouteInfoCopy(ServiceBigAddrRouteInfo* dest,
                          const ServiceBigAddrRouteInfo& src);
void ServiceRouteInfoCopy(ServiceRouteInfo* service_route,
                          const BussinessServiceConfig& config);
void AddrListCopy(apollo_dir::Addr* dest, const dir_svr::AddrList& src);
int CompareAddrList(const dir_svr::AddrList& list1,
                    const dir_svr::AddrList& list2);
int CompareBigAddrList(const dir_svr::BigAddrList& list1,
                       const dir_svr::BigAddrList& list2);
void VisualServiceRouteInfo(char* dest_str, size_t dest_len,
                            uint32_t service_route_count,
                            const ServiceRouteInfo* service_route);
void VisualReportRouteInfo(char* dest_str, size_t dest_len,
                           uint32_t report_route_count,
                           const ReportRouteInfo* report_route);
void VisualAddrs(char* dest_str, size_t dest_len, uint32_t addr_count,
                 const apollo_dir::Addr* addr_list);
void ServiceInfoCopy(apollo_dir::ServiceInfo* dest,
                     const dir_svr::ServiceInfo& src);

inline bool operator==(const dir_svr::Addr& dest, const dir_svr::Addr& src) {
  return strcmp(dest.szUrl, src.szUrl) == 0;
}

inline bool operator!=(const dir_svr::Addr& dest, const dir_svr::Addr& src) {
  return !(dest == src);
}

void MultiBusinessInfoCopy(MultiBusinessInfo* dst,
                           const MultiBusinessInfo& src);

}  // namespace apollo_dir

#endif /* DIR_API_COMMON_H_ */
