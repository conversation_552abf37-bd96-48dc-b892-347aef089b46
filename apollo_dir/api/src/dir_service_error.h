/*
 * Copyright (c) 2013 Tencent all rights reserved
 *
 * time:   2014-03-20
 * file:   dir_service_error.h
 * desc:   undo
 *
 */

#ifndef DIR_SERVICE_ERROR_H_
#define DIR_SERVICE_ERROR_H_

namespace apollo_dir {

enum {
  APOLLO_DIR_ERROR_NONE = 0,
  APOLLO_DIR_ERROR_ALLOC_FAIL = -1,
  APOLLO_DIR_ERROR_INVALID_ARG = -2,
  APOLLO_DIR_ERROR_BUFFER_IS_SHORT = -3,
  APOLLO_DIR_ERROR_EXISTING_DIR_URL = -4,
  APOLLO_DIR_ERROR_EXISTING_MODULE = -5,
  APOLLO_DIR_ERROR_NO_DIR_URL = -6,
  APOLLO_DIR_ERROR_NO_MODULE = -7,
  APOLLO_DIR_ERROR_FD_FAIL = -8,
  APOLLO_DIR_ERROR_INVALID_URL = -9,
  APOLLO_DIR_ERROR_CONNECT_FAIL = -10,
  APOLLO_DIR_ERROR_CONNECT_TIMEOUT = -11,
  APOLLO_DIR_ERROR_PACK_FAIL = -12,
  APOLLO_DIR_ERROR_SEND_FAIL = -13,
  APOLLO_DIR_ERROR_SEND_UNCOMPLETED = -14,
  APOLLO_DIR_ERROR_GET_ROUTE_INFO_TIMEOUT = -15,
  APOLLO_DIR_ERROR_NOAVAILABLE_ADDR = -16,
  APOLLO_DIR_ERROR_ROUTE_NOT_FOUND = -17,
  APOLLO_DIR_ERROR_NO_ADDR = -18,
  APOLLO_DIR_ERROR_INITED = -19,
  APOLLO_DIR_ERROR_NOT_INITED = -20,
  APOLLO_DIR_ERROR_WOULD_BLOCK = -21,
  APOLLO_DIR_ERROR_SVR_CLOSE_CONN = -22,
  APOLLO_DIR_ERROR_UNPACK_FAIL = -23,
  APOLLO_DIR_ERROR_RECV_FAIL = -24,
  APOLLO_DIR_ERROR_RECV_UNCOMPLETED = -25,
  APOLLO_DIR_ERROR_SOCKET_IN_PROCESS = -26,
  APOLLO_DIR_ERROR_SYS = -27,
  APOLLO_DIR_ERROR_IN_TRANS = -28,
  APOLLO_DIR_ERROR_ERROR_CMD = -29,
  APOLLO_DIR_ERROR_TRANS_TIMEOUT = -30,
  APOLLO_DIR_ERROR_MODE_CONFLICT = -31,
  APOLLO_DIR_ERROR_WRONG_MODE = -32,
  APOLLO_DIR_ERROR_KEY_INVALID = -33
};

}
#endif  // DIR_SERVICE_ERROR_H_
