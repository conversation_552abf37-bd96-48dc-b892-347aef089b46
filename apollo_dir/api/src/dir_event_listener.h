/*
 * Copyright (c) 2013 Tencent all rights reserved
 *
 * time:   2014-03-20
 * file:   dir_event_listener.h
 * desc:   undo
 *
 */

#ifndef DIR_EVENT_LISTENER_H_
#define DIR_EVENT_LISTENER_H_

#include <stdint.h>

#ifndef IN
#define IN
#endif
#ifndef OUT
#define OUT
#endif
#ifndef INOUT
#define INOUT
#endif

namespace apollo_dir {

///向前引用声明
struct ServiceRouteInfo;
struct ReportRouteInfo;
struct ServiceBigAddrRouteInfo;

/// @brief
class BaseDirEventListener {
 public:
  BaseDirEventListener(){};
  virtual ~BaseDirEventListener(){};

 public:
  /// @brief 业务服务地址列表变化事件响应方法
  /// 当注册的业务模块中，有地址列表发生变化时，会回调该方法
  ///
  /// @param[in] list    发生变化的业务服务地址列表
  /// @param[in] size    发生变化的业务服务数量
  ///
  /// @return void
  virtual void OnServiceChange(IN const ServiceRouteInfo* list,
                               IN uint32_t size) {}

  /// @brief 业务上报路由信息(地址，上报间隔)变化事件响应方法
  /// 当注册的业务中，有上报路由信息发生变化时，会回调该方法
  ///
  /// @param[in] list    发生变化的业务服务路由信息列表
  /// @param[in] size    发生变化的业务服务数量
  ///
  /// @return void
  virtual void OnReportChange(IN const ReportRouteInfo* list,
                              IN uint32_t size) {}

  /// @brief 业务(大地址)服务地址列表变化事件响应方法
  /// 当注册的业务模块中，有地址列表发生变化时，会回调该方法
  ///
  /// @param[in] route_info    发生变化的路由信息
  ///
  /// @return void
  virtual void OnServiceBigAddrChange(
      IN const ServiceBigAddrRouteInfo* route_info) {}
};

}  // namespace apollo_dir

#endif /* DIR_EVENT_LISTENER_H_ */
