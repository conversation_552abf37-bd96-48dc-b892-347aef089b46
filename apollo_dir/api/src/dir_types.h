/*
 * Copyright (c) 2013 Tencent all rights reserved
 *
 * time:   2014-03-20
 * file:   dir_types.h
 * desc:   undo
 *
 */

#ifndef DIR_TYPES_H_
#define DIR_TYPES_H_

#include <stdint.h>

namespace apollo_dir {

const int MAX_HOST_LEN = 128;
const int MAX_ADDR_COUNT = 32;
const int MAX_BIG_ADDR_COUNT = 2000;
const int MAX_REQ_SERVICE_COUNT = 128;
const int MAX_REQ_BUSINESS_COUNT = 128;
const int MAX_SERVICE_NAME_LEN = 32;
const int MAX_KEY_LEN = 64;
const int MAX_MULTI_ROUTE_NUM = 1024;

enum APOLLO_SERVICE_ID {
  APOLLO_SERVICE_APS = 1,
  APOLLO_SERVICE_ID = 2,
  APOLLO_SERVICE_SNS = 3,
  APOLLO_SERVICE_TPAY = 4
};

struct Addr {
  char url[MAX_HOST_LEN];
};

struct RouteInfo {
  uint32_t bussiness_id;
  uint32_t service_id;
  uint32_t report_interval;
  uint32_t service_addr_count;
  uint32_t report_addr_count;
  Addr service_addr_list[MAX_ADDR_COUNT];
  Addr report_addr_list[MAX_ADDR_COUNT];

  // add gdata stat report addr
  uint32_t gdata_report_addr_count;
  Addr gdata_report_addr_list[MAX_ADDR_COUNT];
};

struct ServiceRouteInfo {
  uint32_t bussiness_id;
  uint32_t service_id;
  uint32_t service_addr_count;
  Addr service_addr_list[MAX_ADDR_COUNT];
};

struct ReportRouteInfo {
  uint32_t bussiness_id;
  uint32_t service_id;
  uint32_t report_interval;
  uint32_t report_addr_count;
  Addr report_addr_list[MAX_ADDR_COUNT];

  // add gdata stat report addr, when gdata report addr changed
  uint32_t gdata_report_addr_count;
  Addr gdata_report_addr_list[MAX_ADDR_COUNT];
};

struct ServiceBigAddrRouteInfo {
  uint32_t bussiness_id;
  uint32_t service_id;
  uint32_t region_id;
  uint32_t service_addr_count;
  Addr service_addr_list[MAX_BIG_ADDR_COUNT];
};

struct ServiceInfo {
  uint32_t service_id;
  char service_name[MAX_SERVICE_NAME_LEN];
  uint16_t magic_value;
};

struct BusinessInfo {
  uint32_t business_id;
  char business_key[MAX_KEY_LEN];
};

struct MultiBusinessInfo {
  uint32_t business_count;
  BusinessInfo business_list[MAX_REQ_BUSINESS_COUNT];
};

struct BigAddrServiceInfo {
  uint32_t dwServiceId;
  uint32_t dwRegionId;
};

}  // namespace apollo_dir

#endif /* DIR_TYPES_H_ */
