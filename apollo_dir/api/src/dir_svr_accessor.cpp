/*
 * dir_svr_accessor.cpp
 *
 *  Created on: 2014年3月21日
 *      Author: rogeryang
 */

#include "dir_svr_accessor.h"

#include <sys/time.h>

#include <set>

#include "base_macro.h"
#include "dir_api_common.h"
#include "dir_common_func.h"
#include "dir_log.h"
#include "dir_service_error.h"

using dir_svr::TDIR_CMD_GET_CONFIG_REQ;

namespace apollo_dir {
void DirDnsNotify::OnDomainResoluted(const std::set<std::string>& ip_addrs) {
  if (cached_ip_addrs_ == ip_addrs) {
    return;
  }
  svr_acc_->ResetDirIPAddress(ip_addrs);
  cached_ip_addrs_ = ip_addrs;
}

DirSvrAccessor::DirSvrAccessor()
    : logger_(NULL),
      dir_idx_(0),
      dir_url_count_(0),
      last_get_route_time_(0),
      get_cfg_interval_(0),
      retry_num_(0),
      use_bussiness_mode_(false),
      use_multi_business_(false),
      use_big_addr_mode_(false),
      using_domain_(false),
      domain_port_(0) {}  // lint !e1401

DirSvrAccessor::~DirSvrAccessor() { logger_ = NULL; }

void DirSvrAccessor::ResetDirIPAddress(const std::set<std::string>& ip_addrs) {
  dir_url_count_ = 0;
  DIR_LOG_INFO(logger_, "dns resolve ip changed,need reset dir_url");
  char strport[16];
  snprintf(strport, sizeof(strport), ":%d", domain_port_);
  std::set<std::string>::const_iterator itor = ip_addrs.begin();
  for (; itor != ip_addrs.end(); ++itor) {
    std::string ip_url = "tcp://" + *itor + strport;
    snprintf(dir_url_[dir_url_count_].url, sizeof(dir_url_[dir_url_count_].url),
             "%s", ip_url.c_str());
    DIR_LOG_INFO(logger_, "add ip=%s", dir_url_[dir_url_count_].url);
    dir_url_count_++;
  }
  dir_idx_ = 0;
}

int DirSvrAccessor::AddDirUrl(const char* dir_url) {
  if (NULL == dir_url) {
    return APOLLO_DIR_ERROR_INVALID_ARG;
  }

  if (dir_url_count_ >= static_cast<uint32_t>(MAX_ADDR_COUNT)) {
    return APOLLO_DIR_ERROR_BUFFER_IS_SHORT;
  }

  int len = static_cast<int>(strlen(dir_url));

  if (len >= MAX_HOST_LEN) {
    return APOLLO_DIR_ERROR_BUFFER_IS_SHORT;
  }

  int ret = 0;
  Addr trim_url;
  const char* after = NULL;

  after = TrimCString(dir_url);
  STR_N_CPY(trim_url.url, after, sizeof(trim_url.url));

  ret = CheckValidUrl(trim_url.url);
  if (0 != ret) {
    return APOLLO_DIR_ERROR_INVALID_URL;
  }

  if (using_domain_) {
    std::string ip_soluted;
    ret = InitDnsResolution(trim_url.url, &ip_soluted);
    if (ret != 0) {
      return APOLLO_DIR_ERROR_SYS;
    }
    snprintf(dir_url_[dir_url_count_].url, sizeof(dir_url_[dir_url_count_].url),
             "tcp://%s:%d", ip_soluted.c_str(), domain_port_);
  } else {
    STR_N_CPY(dir_url_[dir_url_count_].url, trim_url.url,
              sizeof(dir_url_[dir_url_count_].url));
  }

  dir_url_count_++;

  return APOLLO_DIR_ERROR_NONE;
}

int DirSvrAccessor::InitDnsResolution(const std::string& domain,
                                      std::string* ip_sync) {
  // 启动时只获取一个ip
  struct sockaddr_in addr;
  URLInfo url_info;
  int ret = net_api_.GetURLInfo(domain.c_str(), &url_info);
  if (ret != 0) {
    fprintf(stderr, "get urlinfo failed by domain=%s\n", domain.c_str());
    return APOLLO_DIR_ERROR_INVALID_URL;
  }
  ret = net_api_.GetAddrByDomainSync(url_info.host, &addr);
  if (ret != 0) {
    fprintf(stderr, "GetAddrByDomainSync failed\n");
    return APOLLO_DIR_ERROR_INVALID_URL;
  }

  char ip_soluted[MAX_HOST_LEN];
  inet_ntop(AF_INET, &addr.sin_addr, ip_soluted, sizeof(ip_soluted));
  *ip_sync = ip_soluted;

  AsyncDns::Options dns_opts;
  dns_opts.domain = url_info.host;
  domain_ = url_info.host;
  domain_port_ = url_info.port;
  // 初始77s解析一次dns
  dns_opts.interval = 77;
  dns_notifier_.set_avr_acc(this);
  ret = async_dns_.Subscribe(dns_opts, &dns_notifier_);
  if (ret != 0) {
    fprintf(stderr, "subscribe dns parse failed\n");
    return APOLLO_DIR_ERROR_SYS;
  }
  async_dns_.Immediately();
  return 0;
}

void DirSvrAccessor::ClearDirUrl() {
  if (using_domain_) {
    return;
  }
  DIR_LOG_DEBUG(logger_, "clear dir url");
  dir_url_count_ = 0;
}

bool DirSvrAccessor::UsingDomain() { return using_domain_; }

int DirSvrAccessor::InitAndGetAllRouteByBussiness(uint32_t bussiness_id,
                                                  const char* bussiness_key,
                                                  uint32_t timeout,
                                                  TLOGCATEGORYINST* logger,
                                                  TDirMsg* dir_response) {
  if (0 == dir_url_count_) {
    return APOLLO_DIR_ERROR_NO_DIR_URL;
  }

  logger_ = logger;
  async_dns_.set_logger(logger);

  trans_mgr_.Logcat(logger);
  trans_mgr_.Bussiness(bussiness_id, bussiness_key);

  use_bussiness_mode_ = true;
  use_multi_business_ = false;

  return GetAllRoute(timeout, dir_response);
}

int DirSvrAccessor::InitAndGetMultiBusinessInfo(
    const MultiBusinessInfo& business_info, uint32_t timeout,
    TLOGCATEGORYINST* logger, TDirMsg* dir_response) {
  if (0 == dir_url_count_) {
    return APOLLO_DIR_ERROR_NO_DIR_URL;
  }

  logger_ = logger;
  async_dns_.set_logger(logger);

  trans_mgr_.Logcat(logger);
  trans_mgr_.MultiBusiness(business_info);

  use_bussiness_mode_ = true;
  use_multi_business_ = true;

  return GetAllRoute(timeout, dir_response);
}

int DirSvrAccessor::GetAllRoute(uint32_t timeout, TDirMsg* dir_response) {
  DIR_LOG_INFO(logger_, "set timeout[%u]", timeout);

  int ret = 0;
  struct timeval start;
  uint32_t remain = 0;
  uint32_t every_timeout = 0;

  gettimeofday(&start, NULL);

  remain = RemainTimeMs(&start, timeout);

  //如果第一个dir svr连接失败了，最多重试的次数
  retry_num_ =
      (dir_url_count_ > MAX_RETRY_NUM) ? MAX_RETRY_NUM : dir_url_count_;

  if (0 == retry_num_) {
    DIR_LOG_ERROR(logger_, "suprise! retry_num_ is zero!");
    return APOLLO_DIR_ERROR_SYS;
  }

  //每一次操作的时间片
  every_timeout = timeout / retry_num_;
  if (every_timeout < DEFAULT_MIN_TIMEOUT) {
    every_timeout = DEFAULT_MIN_TIMEOUT;
  }

  trans_mgr_.Timeout(every_timeout);

  ret = ACCESSOR_IDLE;
  while (remain > 0 && retry_num_ > 0) {
    ret = Update(dir_response);
    if (ACCESSOR_BUSY_GOT_RESULT == ret) {
      break;
    }

    remain = RemainTimeMs(&start, timeout);
    usleep(1000);
  }

  DIR_LOG_DEBUG(logger_, "remain time[%u], retry num[%u]", remain, retry_num_);

  if (ACCESSOR_BUSY_GOT_RESULT == ret) {
    //重新设定每个事物的timeout
    trans_mgr_.Timeout(timeout);

    return APOLLO_DIR_ERROR_NONE;
  }

  return APOLLO_DIR_ERROR_CONNECT_TIMEOUT;
}

int DirSvrAccessor::InitAndGetAllBigAddrRoute(
    uint32_t bussiness_id, const char* bussiness_key,
    const BigAddrServiceInfo& bigaddr_service_info, uint32_t timeout,
    TLOGCATEGORYINST* logger, TDirMsg* dir_response) {
  if (NULL == dir_response) {
    return APOLLO_DIR_ERROR_INVALID_ARG;
  }

  if (0 == dir_url_count_) {
    return APOLLO_DIR_ERROR_NO_DIR_URL;
  }

  logger_ = logger;
  async_dns_.set_logger(logger);

  for (uint32_t i = 0; i < dir_url_count_; i++) {
    DIR_LOG_INFO(logger_, "regist url: %s", dir_url_[i].url);
  }

  DIR_LOG_INFO(logger_, "set timeout[%u]", timeout);

  trans_mgr_.Logcat(logger);
  trans_mgr_.Bussiness(bussiness_id, bussiness_key);
  trans_mgr_.SetBigAddrServiceInfo(bigaddr_service_info);

  use_big_addr_mode_ = true;
  ;

  return GetAllRoute(timeout, dir_response);
}

void DirSvrAccessor::RefreshBigAddrMd5(const char* big_addr_md5) {
  trans_mgr_.RefreshBigAddrMd5(big_addr_md5);
}

int DirSvrAccessor::InitAndGetAllRoute(const BussinessService* service_list,
                                       uint32_t service_count, uint32_t timeout,
                                       TLOGCATEGORYINST* logger,
                                       TDirMsg* dir_response)

{
  if (NULL == service_list || NULL == dir_response) {
    return APOLLO_DIR_ERROR_INVALID_ARG;
  }

  if (0 == service_count) {
    return APOLLO_DIR_ERROR_NO_MODULE;
  }

  if (0 == dir_url_count_) {
    return APOLLO_DIR_ERROR_NO_DIR_URL;
  }

  logger_ = logger;
  async_dns_.set_logger(logger);

  for (uint32_t i = 0; i < dir_url_count_; i++) {
    DIR_LOG_INFO(logger_, "regist url: %s", dir_url_[i].url);
  }

  for (uint32_t i = 0; i < service_count; i++) {
    DIR_LOG_INFO(logger_, "regist service, BussinessID[%u], ServiceID[%u]",
                 service_list[i].dwBussinessId, service_list[i].dwServiceId);
  }

  DIR_LOG_INFO(logger_, "set timeout[%u]", timeout);

  trans_mgr_.Logcat(logger);
  trans_mgr_.ServiceCount(service_count);
  trans_mgr_.ServiceList(service_list);

  return GetAllRoute(timeout, dir_response);

  /*
      int ret = 0;
      struct timeval start;
      uint32_t remain = 0;
      uint32_t every_timeout = 0;

      gettimeofday(&start, NULL);

      remain = RemainTimeMs(&start , timeout);

      //如果第一个dir svr连接失败了，最多重试的次数
      retry_num_ = (dir_url_count_ > MAX_RETRY_NUM) ? MAX_RETRY_NUM :
     dir_url_count_;

      //每一次操作的时间片
      every_timeout = timeout / retry_num_;
      if (every_timeout < DEFAULT_MIN_TIMEOUT)
      {
          every_timeout = DEFAULT_MIN_TIMEOUT;
      }

      trans_mgr_.Timeout(every_timeout);
      trans_mgr_.Logcat(logger);

      ret = ACCESSOR_IDLE;
      while (remain > 0 && retry_num_ > 0)
      {
          ret = Update(dir_response);
          if (ACCESSOR_BUSY_GOT_RESULT == ret)
          {
              break;
          }

          remain = RemainTimeMs(&start , timeout);
          usleep(1000);
      }

      DIR_LOG_DEBUG(logger_, "remain time[%u], retry num[%u]", remain,
     retry_num_);

      if (ACCESSOR_BUSY_GOT_RESULT == ret)
      {
          //重新设定每个事物的timeout
          trans_mgr_.Timeout(timeout);

          return APOLLO_DIR_ERROR_NONE;
      }

      return APOLLO_DIR_ERROR_CONNECT_TIMEOUT;
      */
}

int DirSvrAccessor::Update(TDirMsg* dir_response) {
  int ret = 0;
  int result = ACCESSOR_IDLE;

  async_dns_.Tick();

  //检查是否需要刷新路由信息
  RefreshRouteInfo();

  ret = trans_mgr_.Update();
  if (APOLLO_DIR_ERROR_NONE != ret) {
    if (retry_num_ > 0) {
      DIR_LOG_TRACE(logger_, "trans update ret[%d]", ret);
      retry_num_--;
    }

    return result;
  }

  switch (trans_mgr_.GetState()) {
    case TRANS_STATE_IDLE: {
      break;
    }

    case TRANS_STATE_END: {
      ret = trans_mgr_.GetMsg(dir_response);
      if (APOLLO_DIR_ERROR_NONE == ret) {
        if (TDIR_CMD_GET_CONFIG_RESP == dir_response->stHead.wCommand ||
            TDIR_CMD_GET_CONFIG_BY_BUSSINESS_RESP ==
                dir_response->stHead.wCommand ||
            TDIR_CMD_GET_BIG_ADDR_RESP == dir_response->stHead.wCommand ||
            TDIR_CMD_GET_MULTI_CONFIG_RESP == dir_response->stHead.wCommand) {
          last_get_route_time_ = time(NULL);

          if (TDIR_CMD_GET_CONFIG_RESP == dir_response->stHead.wCommand) {
            get_cfg_interval_ = dir_response->stBody.stTDirGetConfigResp
                                    .stPolicyInfo.dwGetConfigInterval;
          } else if (TDIR_CMD_GET_CONFIG_BY_BUSSINESS_RESP ==
                     dir_response->stHead.wCommand) {
            get_cfg_interval_ =
                dir_response->stBody.stTDirGetConfigByBussinessResp.stPolicyInfo
                    .dwGetConfigInterval;
          } else if (TDIR_CMD_GET_MULTI_CONFIG_RESP ==
                     dir_response->stHead.wCommand) {
            get_cfg_interval_ = dir_response->stBody.stTDirGetMultiConfigResp
                                    .stPolicyInfo.dwGetConfigInterval;
          } else if (TDIR_CMD_GET_BIG_ADDR_RESP ==
                     dir_response->stHead.wCommand) {
            get_cfg_interval_ = dir_response->stBody.stTDirGetBigAddrResp
                                    .stPolicyInfo.dwGetConfigInterval;
          }

          //这里做一个保护，防止下发间隔值太小
          if (0 == get_cfg_interval_) {
            DIR_LOG_DEBUG(logger_,
                          "get_cfg_interval_ is zero, adjust to default value");
            get_cfg_interval_ = DEFAULT_GET_CFG_INTERVAL;
          }

          DIR_LOG_DEBUG(logger_, "set get cfg interval[%u]", get_cfg_interval_);
        }

        result = ACCESSOR_BUSY_GOT_RESULT;
      }

      break;
    }

    default: {
      result = ACCESSOR_BUSY_NO_RESULT;
      break;
    }
  }
  // 5个周期刷新下dns
  async_dns_.ResetInterval(get_cfg_interval_ * 5);
  return result;
}

void DirSvrAccessor::Fini() {
  logger_ = NULL;

  dir_idx_ = 0;
  dir_url_count_ = 0;

  use_bussiness_mode_ = false;
  use_big_addr_mode_ = false;

  trans_mgr_.End();
  async_dns_.Close();
}

void DirSvrAccessor::RefreshRouteInfo() {
  int ret = 0;
  time_t now = time(NULL);
  uint32_t interval = 0;

  interval = static_cast<uint32_t>(now - last_get_route_time_);

  if (interval >= get_cfg_interval_) {
    if (TRANS_STATE_IDLE != trans_mgr_.GetState()) {
      return;
    }

    DIR_LOG_DEBUG(logger_,
                  "time to refresh route info, interval[%u], cfg interval[%u]",
                  (uint32_t)interval, get_cfg_interval_);

    const char* url = NULL;

    url = RandomUrl();

    TDIR_CMD cmd;
    if (use_big_addr_mode_) {
      cmd = TDIR_CMD_GET_BIG_ADDR_REQ;
    } else if (use_bussiness_mode_) {
      if (use_multi_business_) {
        cmd = TDIR_CMD_GET_MULTI_CONFIG_REQ;
      } else {
        cmd = TDIR_CMD_GET_CONFIG_BY_BUSSINESS_REQ;
      }
    } else {
      cmd = TDIR_CMD_GET_CONFIG_REQ;
    }
    ret = trans_mgr_.Start(cmd, url);
    if (APOLLO_DIR_ERROR_IN_TRANS != ret && APOLLO_DIR_ERROR_NONE != ret) {
      DIR_LOG_ERROR(logger_, "start transaction fail, ret[%d]", ret);
      if (retry_num_ > 0) {
        retry_num_--;
      }
    }

    last_get_route_time_ = now;
  }
}

const char* DirSvrAccessor::RandomUrl() {
  if (0 == dir_url_count_) {
    DIR_LOG_ERROR(logger_, "dir_url_count_ must bigger than 0");
    return NULL;
  }

  uint32_t dir_idx = (dir_idx_) % dir_url_count_;

  DIR_LOG_DEBUG(logger_, "try to connect url[%s]", dir_url_[dir_idx].url);

  ++dir_idx_;
  return dir_url_[dir_idx].url;
}

int DirSvrAccessor::CheckValidUrl(const char* url) {
  // 只允许使用一个域名
  if (using_domain_) {
    DIR_LOG_ERROR(logger_, "can not using multiple url when has domain");
    return APOLLO_DIR_ERROR_INVALID_URL;
  }

  int ret = 0;
  if (!net_api_.IsTCP(url)) {
    DIR_LOG_ERROR(logger_, "invalid url[%s],only support tcp", url);
    return APOLLO_DIR_ERROR_INVALID_URL;
  }

  URLInfo url_info;
  ret = net_api_.GetURLInfo(url, &url_info);
  if (ret != 0) {
    DIR_LOG_ERROR(logger_, "GetURLInfo failed.invalid url[%s]", url);
    return APOLLO_DIR_ERROR_INVALID_URL;
  }
  if (net_api_.IsIP(url_info.host.c_str())) {
    struct sockaddr_in addr;
    ret = inet_aton(url_info.host.c_str(), &addr.sin_addr);
    if (0 == ret) {
      DIR_LOG_ERROR(logger_, "inet_aton fail[%d], invalid url", ret);
      return APOLLO_DIR_ERROR_INVALID_URL;
    }

    // check repeat
    for (uint32_t i = 0; i < dir_url_count_; i++) {
      if (0 == strncmp(dir_url_[i].url, url, sizeof(dir_url_[i].url))) {
        DIR_LOG_ERROR(logger_, "url=%s repeated\n", url);
        return APOLLO_DIR_ERROR_EXISTING_DIR_URL;
      }
    }
  } else {
    // check domain
    // 已经配置了ip，不能在配置域名
    if (dir_url_count_ != 0) {
      // DIR_LOG_ERROR(logger_, "can not use domain and ip by the same time");
      // fprintf(stderr, "can not use domain and ip by the same time\n");
      DIR_LOG_ERROR(logger_, "can not use domain and ip by the same time");
      return APOLLO_DIR_ERROR_INVALID_URL;
    }
    using_domain_ = true;
  }
  return 0;
}

} /* namespace apollo_dir */
