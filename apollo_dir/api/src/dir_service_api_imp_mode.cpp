/*
 * Copyright (c) 2013 Tencent all rights reserved
 *
 * date:   2014年6月12日
 * file:   dir_service_api_imp_mode.cpp
 * desc:
 *
 */

#include "dir_service_api_imp_mode.h"

namespace apollo_dir {

DirServiceApiImpMode::DirServiceApiImpMode(ImpMode mode)
    : imp_(NULL), mode_(mode) {}

DirServiceApiImpMode::~DirServiceApiImpMode() { imp_ = NULL; }

int DirServiceApiImpMode::ProcessBigAddrAndPolicyInfo(
    const TDirMsg& resp, ServiceBigAddrRouteInfo* service_big_addr_change_route,
    char& service_big_addr_change_flag, ReportRouteInfo* report_change_route,
    uint32_t* report_change_count) {
  return 0;
}

}  // namespace apollo_dir
