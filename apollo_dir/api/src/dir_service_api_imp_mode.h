/*
 * Copyright (c) 2013 Tencent all rights reserved
 *
 * date:   2014年6月12日
 * file:   dir_service_api_imp_mode.h
 * desc:
 *
 */

#ifndef DIR_SERVICE_API_IMP_MODE_H_
#define DIR_SERVICE_API_IMP_MODE_H_

#include "dir_protocol.h"
#include "dir_types.h"

using dir_svr::TDirMsg;

namespace apollo_dir {

class DirServiceApiImp;

class DirServiceApiImpMode {
 public:
  enum ImpMode { IMP_MODE_SERVICE = 0, IMP_MODE_BUSSINESS, IMP_MODE_BIG_ADDR };
  DirServiceApiImpMode(ImpMode mode);
  virtual ~DirServiceApiImpMode();
  void SetImp(DirServiceApiImp* imp) { imp_ = imp; }
  ImpMode GetMode() { return mode_; }
  DirServiceApiImp* GetImp() { return imp_; }
  virtual int Init(uint32_t timeout) = 0;
  virtual int ProcessRouteAndPolicyInfo(const TDirMsg& resp,
                                        ServiceRouteInfo* service_change_route,
                                        uint32_t* service_change_count,
                                        ReportRouteInfo* report_change_route,
                                        uint32_t* report_change_count) = 0;

  virtual int ProcessBigAddrAndPolicyInfo(
      const TDirMsg& resp,
      ServiceBigAddrRouteInfo* service_big_addr_change_route,
      char& service_big_addr_change_flag, ReportRouteInfo* report_change_route,
      uint32_t* report_change_count);

 protected:
  DirServiceApiImp* imp_;
  const ImpMode mode_;
};

}  // namespace apollo_dir

#endif /* DIR_SERVICE_API_IMP_MODE_H_ */
