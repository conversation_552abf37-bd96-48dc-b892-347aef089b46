/*
 * dir_svr_accessor.h
 *
 *  Created on: 2014年3月21日
 *      Author: rogery<PERSON>
 */

#ifndef DIR_SVR_ACCESSOR_H_
#define DIR_SVR_ACCESSOR_H_
#include <stddef.h>

#include <set>

#include "dir_async_dns.h"
#include "dir_protocol.h"
#include "dir_service_transaction.h"
#include "dir_types.h"
#include "tlog/tlog_category.h"

using dir_svr::TDIR_CMD_GET_CONFIG_REQ;
using dir_svr::TDIR_CMD_GET_CONFIG_RESP;
using dir_svr::TDIR_MAX_ADDR_NUM;
using dir_svr::TDIR_MAX_REQ_ROUTE_NUM;

using dir_svr::BussinessService;
using dir_svr::PolicyInfo;
using dir_svr::TDirGetConfigResp;
using dir_svr::TDirMsg;

namespace apollo_dir {

class DirSvrAccessor;

class DirDnsNotify : public IAsyncDnsNotify {
 public:
  DirDnsNotify() : svr_acc_(NULL) {}
  virtual ~DirDnsNotify() {}
  void set_avr_acc(DirSvrAccessor* svr_acc) { svr_acc_ = svr_acc; }
  virtual void OnDomainResoluted(const std::set<std::string>& ip_addrs);

 private:
  DirSvrAccessor* svr_acc_;
  std::set<std::string> cached_ip_addrs_;
};

class DirSvrAccessor {
 public:
  DirSvrAccessor();
  virtual ~DirSvrAccessor();

 public:
  enum DirSvrAccessorRet {
    ACCESSOR_IDLE = -1,
    ACCESSOR_BUSY_NO_RESULT = 0,
    ACCESSOR_BUSY_GOT_RESULT = 1,
    ACCESSOR_INNER_ERROR = 2,
  };

 public:
  void ResetDirIPAddress(const std::set<std::string>& ip_addrs);
  virtual int AddDirUrl(const char* dir_url);
  virtual void ClearDirUrl();
  virtual bool UsingDomain();

  virtual int InitAndGetAllRoute(const BussinessService* service_list,
                                 uint32_t service_count, uint32_t timeout,
                                 TLOGCATEGORYINST* logger,
                                 TDirMsg* dir_response);
  virtual int InitAndGetAllRouteByBussiness(uint32_t bussiness_id,
                                            const char* bussiness_key,
                                            uint32_t timeout,
                                            TLOGCATEGORYINST* logger,
                                            TDirMsg* dir_response);
  int InitAndGetMultiBusinessInfo(const MultiBusinessInfo& business_info,
                                  uint32_t timeout, TLOGCATEGORYINST* logger,
                                  TDirMsg* dir_response);

  int InitAndGetAllBigAddrRoute(uint32_t bussiness_id,
                                const char* bussiness_key,
                                const BigAddrServiceInfo& bigaddr_service_info,
                                uint32_t timeout, TLOGCATEGORYINST* logger,
                                TDirMsg* dir_response);

  void RefreshBigAddrMd5(const char* big_addr_md5);

  virtual void Fini();

  /// @brief 根据从DirSvrl拉取到的信息定期去DirSvr拉路由
  ///
  /// @param[out] dir_reponse 返回从dirsvr拉到的信息
  /// @return ACCESSOR_IDLE 闲
  ///         ACCESSOR_BUSY_NO_RESULT 忙，但没有收到完整的路由信息
  ///         ACCESSOR_BUSY_GOT_RESULT
  ///         得到了路由信息，此时list和size返回拉到的信息
  virtual int Update(TDirMsg* dir_response);

 private:
  int GetAllRoute(uint32_t timeout, TDirMsg* dir_response);
  void RefreshRouteInfo();
  const char* RandomUrl();
  int CheckValidUrl(const char* url);
  int InitDnsResolution(const std::string& domain, std::string* ip_sync);

 private:
 private:
  static const uint32_t MAX_RETRY_NUM = 3;
  static const uint32_t DEFAULT_GET_CFG_INTERVAL = 30;
  static const uint32_t DEFAULT_MIN_TIMEOUT = 1000;  //单位毫秒

 private:
  TLOGCATEGORYINST* logger_;

  uint32_t dir_idx_;
  uint32_t dir_url_count_;
  Addr dir_url_[TDIR_MAX_ADDR_NUM];

  time_t last_get_route_time_;
  uint32_t get_cfg_interval_;

  DirServiceTransaction trans_mgr_;

  uint32_t retry_num_;

  DirServiceNet net_api_;

  bool use_bussiness_mode_;
  bool use_multi_business_;
  bool use_big_addr_mode_;

  friend class DirServiceApiImp;

  bool using_domain_;
  AsyncDns async_dns_;
  std::string domain_;
  uint16_t domain_port_;
  DirDnsNotify dns_notifier_;
};

} /* namespace apollo_dir */

#endif /* DIR_SVR_ACCESSOR_H_ */
