/*
 * Copyright (c) 2013 Tencent all rights reserved
 *
 * date:   2014年6月12日
 * file:   dir_service_api_imp_service_mode.h
 * desc:
 *
 */

#ifndef DIR_SERVICE_API_IMP_SERVICE_MODE_H_
#define DIR_SERVICE_API_IMP_SERVICE_MODE_H_

#include "dir_protocol.h"
#include "dir_service_api_imp_mode.h"

using dir_svr::BussinessServiceConfig;
using dir_svr::TDirGetConfigResp;

namespace apollo_dir {

class DirServiceApiImpServiceMode : public DirServiceApiImpMode {
 public:
  DirServiceApiImpServiceMode();
  virtual ~DirServiceApiImpServiceMode();

 public:
  void SetSvrResp(const TDirGetConfigResp& resp);
  virtual int Init(uint32_t timeout);
  void Fini();
  virtual int ProcessRouteAndPolicyInfo(const TDirMsg& resp,
                                        ServiceRouteInfo* service_change_route,
                                        uint32_t* service_change_count,
                                        ReportRouteInfo* report_change_route,
                                        uint32_t* report_change_count);

 private:
  void CompareRoute(const BussinessServiceConfig& old_route,
                    const TDirGetConfigResp& new_svr_resp,
                    bool report_policy_change, ServiceRouteInfo* service_route,
                    uint32_t* service_change_count,
                    ReportRouteInfo* report_route,
                    uint32_t* report_change_count);
  void GetChangedRoute(const TDirGetConfigResp& new_svr_resp,
                       ServiceRouteInfo* service_route,
                       uint32_t* service_change_count,
                       ReportRouteInfo* report_route,
                       uint32_t* report_change_count);

 private:
  TDirGetConfigResp svr_resp_;  //保存当前生效的svr应答
};

}  // namespace apollo_dir

#endif /* DIR_SERVICE_API_IMP_SERVICE_MODE_H_ */
