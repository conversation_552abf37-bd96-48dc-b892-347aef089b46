/*
 * dir_service_api.cpp
 *
 *  Created on: 2014年3月11日
 *      Author: rogeryang
 */

#include "dir_service_api.h"

#include <new>

#include "dir_service_api_imp.h"
#include "dir_service_error.h"

namespace apollo_dir {

DirServiceApi::DirServiceApi() : impl_(new (std::nothrow) DirServiceApiImp) {}

DirServiceApi::~DirServiceApi() {
  if (NULL != impl_) {
    delete impl_;
    impl_ = NULL;
  }
}

int DirServiceApi::AddDirUrl(const char* url) {
  if (NULL == impl_) {
    return APOLLO_DIR_ERROR_ALLOC_FAIL;
  }

  return impl_->AddDirUrl(url);
}

void DirServiceApi::ClearDirUrl() { impl_->ClearDirUrl(); }

bool DirServiceApi::UsingDomain() { return impl_->UsingDomain(); }

int DirServiceApi::RegistBigAddrService(IN uint32_t bussiness_id,
                                        IN uint32_t region_id,
                                        IN uint32_t service_id,
                                        const char* business_key) {
  if (NULL == impl_) {
    return APOLLO_DIR_ERROR_ALLOC_FAIL;
  }

  return impl_->RegistBigAddrService(bussiness_id, region_id, service_id,
                                     business_key);
}

int DirServiceApi::RegistBussinessService(uint32_t bussiness_id,
                                          uint32_t service_id,
                                          const char* bussiness_key) {
  if (NULL == impl_) {
    return APOLLO_DIR_ERROR_ALLOC_FAIL;
  }

  return impl_->RegistBussinessService(bussiness_id, service_id, bussiness_key);
}

int DirServiceApi::SetBussiness(IN uint32_t bussiness_id,
                                const char* bussiness_key) {
  if (NULL == impl_) {
    return APOLLO_DIR_ERROR_ALLOC_FAIL;
  }

  return impl_->SetBussiness(bussiness_id, bussiness_key);
}

int DirServiceApi::SetMultiBusiness(IN const MultiBusinessInfo& business_info) {
  if (NULL == impl_) {
    return APOLLO_DIR_ERROR_ALLOC_FAIL;
  }

  return impl_->SetMultiBusiness(business_info);
}

int DirServiceApi::Init(uint32_t timeout, BaseDirEventListener* event_listener,
                        TLOGCATEGORYINST* logger) {
  if (NULL == impl_) {
    return APOLLO_DIR_ERROR_ALLOC_FAIL;
  }

  return impl_->Init(timeout, event_listener, logger);
}

void DirServiceApi::Fini() {
  if (NULL == impl_) {
    return;
  }

  impl_->Fini();
}

int DirServiceApi::GetAllBussinessServiceRoute(RouteInfo* list,
                                               uint32_t* size) {
  if (NULL == impl_) {
    return APOLLO_DIR_ERROR_ALLOC_FAIL;
  }

  return impl_->GetAllBussinessServiceRoute(list, size);
}

int DirServiceApi::GetBussinessServiceRoute(uint32_t bussiness_id,
                                            uint32_t service_id, Addr* addr) {
  if (NULL == impl_) {
    return APOLLO_DIR_ERROR_ALLOC_FAIL;
  }

  return impl_->GetBussinessServiceRoute(bussiness_id, service_id, addr);
}

void DirServiceApi::ReportBussinessModRouteCallResult(uint32_t bussiness_id,
                                                      uint32_t service_id,
                                                      const Addr* addr,
                                                      int result,
                                                      uint32_t elapsed_ms) {
  if (NULL == impl_) {
    return;
  }

  impl_->ReportBussinessModRouteCallResult(bussiness_id, service_id, addr,
                                           result, elapsed_ms);
}

int DirServiceApi::GetAllServiceInfo(OUT ServiceInfo* service_info,
                                     INOUT uint32_t* size) {
  if (NULL == impl_) {
    return APOLLO_DIR_ERROR_ALLOC_FAIL;
  }

  return impl_->GetAllServiceInfo(service_info, size);
}

int DirServiceApi::Update() {
  if (NULL == impl_) {
    return APOLLO_DIR_ERROR_ALLOC_FAIL;
  }

  return impl_->Update();
}

int DirServiceApi::GetBigAddrServiceRoute(
    OUT ServiceBigAddrRouteInfo* route_info,
    OUT ReportRouteInfo* report_route_info) {
  if (NULL == impl_) {
    return APOLLO_DIR_ERROR_ALLOC_FAIL;
  }

  return impl_->GetBigAddrServiceRoute(route_info, report_route_info);
}

} /* namespace apollo_dir */
