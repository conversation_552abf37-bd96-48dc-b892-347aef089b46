#include "dir_service_api_imp_big_addr_mode.h"

#include "dir_api_common.h"
#include "dir_common_func.h"
#include "dir_service_api_imp.h"

namespace apollo_dir {

DirServiceApiImpBigAddrMode::DirServiceApiImpBigAddrMode()
    : DirServiceApiImpMode(IMP_MODE_BIG_ADDR) {
  svr_resp_.construct();
  last_list_md5_[0] = 0;
}

DirServiceApiImpBigAddrMode::~DirServiceApiImpBigAddrMode() {
  // TODO Auto-generated destructor stub
}

int DirServiceApiImpBigAddrMode::Init(uint32_t timeout) {
  if (NULL == this->GetImp()) {
    return -1;
  }

  DirServiceApiImp* imp = this->GetImp();

  //从dirsvr获取所有路由信息
  static TDirMsg svr_resp_msg;
  int ret = imp->svr_accessor_->InitAndGetAllBigAddrRoute(
      this->imp_->bussiness_id_, this->imp_->bussiness_key_,
      imp->big_addr_service_info_, timeout, imp->logger_, &svr_resp_msg);
  if (ret) {
    DIR_LOG_ERROR(imp->logger_, "svr_accessor InitAndGetAllRoute failed");
    return ret;
  }

  if (svr_resp_msg.stHead.wCommand != dir_svr::TDIR_CMD_GET_BIG_ADDR_RESP) {
    if (TDIR_CMD_ERROR_RESP == svr_resp_msg.stHead.wCommand) {
      DIR_LOG_ERROR(imp->logger_,
                    "svr response error, cmd=%u, errno=%d, errmsg=%s",
                    svr_resp_msg.stBody.stTDirErrorResp.wReqCmd,
                    svr_resp_msg.stBody.stTDirErrorResp.iErrorCode,
                    svr_resp_msg.stBody.stTDirErrorResp.szErrorMsg);
      return GetApiErrorFromSvrError(
          svr_resp_msg.stBody.stTDirErrorResp.iErrorCode);
    } else {
      DIR_LOG_ERROR(imp->logger_, "the svr response msg cmd is invalid, cmd=%u",
                    svr_resp_msg.stHead.wCommand);
      return APOLLO_DIR_ERROR_SYS;
    }
  }

  ret = imp->route_mgr_->Init(svr_resp_msg.stBody.stTDirGetBigAddrResp,
                              imp->logger_);
  if (ret) {
    DIR_LOG_ERROR(imp->logger_, "route_mgr_ Init failed");
    return ret;
  }

  // TODO: XXXX
  imp_->FillRoute4BigAddr(&(svr_resp_msg.stBody.stTDirGetBigAddrResp));
  /*
  imp_->FillRouteInfo(
  svr_resp_msg.stBody.stTDirGetBigAddrResp.astBussinessServiceConfig,
                      svr_resp_msg.stBody.stTDirGetBigAddrResp.dwBussinessServiceConfigCount,
                      svr_resp_msg.stBody.stTDirGetBigAddrResp.stPolicyInfo );
  */
  DirGetBigAddrRespCopy(&(svr_resp_), svr_resp_msg.stBody.stTDirGetBigAddrResp);

  STR_N_CPY(last_list_md5_,
            svr_resp_msg.stBody.stTDirGetBigAddrResp.szBigAddrListMd5,
            TDIR_MD5_VALUE_LEN);
  imp->svr_accessor_->RefreshBigAddrMd5(
      svr_resp_msg.stBody.stTDirGetBigAddrResp.szBigAddrListMd5);

  return 0;
}

void DirServiceApiImpBigAddrMode::Fini() { svr_resp_.construct(); }

int DirServiceApiImpBigAddrMode::ProcessBigAddrAndPolicyInfo(
    const TDirMsg& resp, ServiceBigAddrRouteInfo* service_big_addr_change_route,
    char& service_big_addr_change_flag, ReportRouteInfo* report_change_route,
    uint32_t* report_change_count) {
  if (NULL == imp_) {
    return APOLLO_DIR_ERROR_SYS;
  }

  if (resp.stHead.wCommand != TDIR_CMD_GET_BIG_ADDR_RESP) {
    //不应该出现
    DIR_LOG_ERROR(imp_->logger_, "resp is mismatch!");
    return APOLLO_DIR_ERROR_SYS;
  }

  (*report_change_count) = 0;

  GetChangedRoute(resp.stBody.stTDirGetBigAddrResp,
                  service_big_addr_change_route, service_big_addr_change_flag,
                  report_change_route, report_change_count);

  //    imp_->LogChangedRoute( TLOG_PRIORITY_DEBUG,
  //    service_big_addr_change_route, service_change_count,
  //                           report_change_route, report_change_count );

// TODO: XXXX
#if 0
    if( *service_change_count )
    {
        //服务地址有变化，路由要重新进行计算，重新初始化
        int ret = imp_->ResetRouteMgr( resp.stBody.stTDirGetBigAddrResp, svr_resp_ );
        if( ret )
        {
            //这里失败后，route_mgr应该还是在使用老的地址列表, 就认为新的应答是无效的，不进行接下来的通知操作
            DIR_LOG_ERROR( imp_->logger_, "ResetRouteMgr failed" );
            return ret;
        }

    }
#endif

  IF_NOTNULL_CALL(imp_->route_mgr_,
                  imp_->route_mgr_->SetPolicy(
                      resp.stBody.stTDirGetBigAddrResp.stPolicyInfo));

  DirGetBigAddrRespCopy(&svr_resp_, resp.stBody.stTDirGetBigAddrResp);

  imp_->FillRoute4BigAddr(&(resp.stBody.stTDirGetBigAddrResp));

  STR_N_CPY(last_list_md5_, svr_resp_.szBigAddrListMd5, TDIR_MD5_VALUE_LEN);
  imp_->svr_accessor_->RefreshBigAddrMd5(svr_resp_.szBigAddrListMd5);
  return 0;
}

int DirServiceApiImpBigAddrMode::ProcessRouteAndPolicyInfo(
    const TDirMsg& resp, ServiceRouteInfo* service_change_route,
    uint32_t* service_change_count, ReportRouteInfo* report_change_route,
    uint32_t* report_change_count) {
  DIR_LOG_ERROR(
      imp_->logger_,
      "should not call DirServiceApiImpBigAddrMode::ProcessRouteAndPolicyInfo");

  return 0;
}

void DirServiceApiImpBigAddrMode::CompareRoute(
    const TDirGetBigAddrResp& new_svr_resp, bool report_policy_change,
    ServiceBigAddrRouteInfo* service_route, char& service_big_addr_change_flag,
    ReportRouteInfo* report_route, uint32_t* report_change_count) {
  if (report_policy_change || CompareAddrList(svr_resp_.stReportAddrList,
                                              new_svr_resp.stReportAddrList)) {
    report_route->bussiness_id = new_svr_resp.dwBussinessId;
    report_route->service_id = new_svr_resp.dwServiceId;
    report_route->report_interval = new_svr_resp.stPolicyInfo.dwReportInterval;
    report_route->report_addr_count = new_svr_resp.stReportAddrList.dwAddrCount;
    for (uint32_t i = 0; i < report_route->report_addr_count; ++i) {
      STR_N_CPY(report_route->report_addr_list[i].url,
                new_svr_resp.stReportAddrList.astAddrList[i].szUrl,
                sizeof(report_route->report_addr_list[i].url));
    }

    ++(*report_change_count);
  }

  if (new_svr_resp.chBigAddrSameFlag) {
    service_big_addr_change_flag = 0;
  } else if (CompareBigAddrList(svr_resp_.stBigAddrList,
                                new_svr_resp.stBigAddrList)) {
    service_route->bussiness_id = new_svr_resp.dwBussinessId;
    service_route->service_id = new_svr_resp.dwServiceId;
    service_route->region_id = new_svr_resp.dwRegionId;

    service_route->service_addr_count = new_svr_resp.stBigAddrList.dwAddrCount;
    for (uint32_t i = 0; i < service_route->service_addr_count; ++i) {
      STR_N_CPY(service_route->service_addr_list[i].url,
                new_svr_resp.stBigAddrList.astAddrList[i].szUrl,
                sizeof(service_route->service_addr_list[i].url));
    }

    service_big_addr_change_flag = 1;
  }
}

void DirServiceApiImpBigAddrMode::GetChangedRoute(
    const TDirGetBigAddrResp& new_svr_resp,
    ServiceBigAddrRouteInfo* service_route, char& service_big_addr_change_flag,
    ReportRouteInfo* report_route, uint32_t* report_change_count) {
  if (NULL == imp_) {
    return;
  }

  if (service_route && report_route && report_change_count) {
    (*report_change_count) = 0;
    service_big_addr_change_flag = 0;

    //如果report_interval变化了，那么所有业务的report_list都要进行变化通知
    bool report_policy_change = false;
    if (new_svr_resp.stPolicyInfo.dwReportInterval !=
        svr_resp_.stPolicyInfo.dwReportInterval) {
      report_policy_change = true;
    }

    CompareRoute(new_svr_resp, report_policy_change, service_route,
                 service_big_addr_change_flag, report_route,
                 report_change_count);
  }
}

}  // namespace apollo_dir
