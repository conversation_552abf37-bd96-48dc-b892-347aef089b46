/*
 * dir_route_manager.cpp
 *
 *  Created on: 2014年3月22日
 */

#include "dir_route_manager.h"

#include <fcntl.h>

#include "base_macro.h"
#include "dir_api_common.h"
#include "dir_log.h"
#include "dir_service_error.h"

namespace apollo_dir {

/// @brief 用于记录单个地址单个周期内的调用情况
struct AddrCallInfo {
  uint32_t bussiness_id;
  uint32_t service_id;
  Addr addr;
  uint32_t call_count;        // 本周期内的总调用次数(通过Report接口反馈的次数，不是指用GetRoute对外返回的次数)
  uint32_t call_fail_count;   // 本周期内失败次数
  uint32_t call_succ_count;   // 本周期内成功次数
  uint64_t call_total_time_;  // 本周期内的调用总共耗时，单位毫秒

  AddrCallInfo()
      : bussiness_id(0), service_id(0), call_count(0), call_fail_count(0), call_succ_count(0), call_total_time_(0) {
    addr.url[0] = 0;
  }

  void ResetCallData() {
    call_count = 0;
    call_fail_count = 0;
    call_succ_count = 0;
    call_total_time_ = 0;
  }

  static int Compare(const void* data1, const void* data2) {
    AddrCallInfo* item1 = (AddrCallInfo*)data1;
    AddrCallInfo* item2 = (AddrCallInfo*)data2;
    return (item1->bussiness_id == item2->bussiness_id) && (item1->service_id == item2->service_id) &&
                   (strcmp(item1->addr.url, item2->addr.url) == 0)
               ? 0
               : 1;
  }

  static unsigned int Hash(const void* data) {
    AddrCallInfo* item = (AddrCallInfo*)data;
    return BKDRHash(item->addr.url) + item->bussiness_id * item->service_id;
  }
};

/// @brief 单个地址的状态定义
enum AddrStatus {
  ADDR_NORMAL = 0,  // 正常， 会对外部返回
  ADDR_INVALID = 1  // 异常， 除了用于探测的情况， 不会对外返回
};

/// @brief 单个地址的当前路由状态数据
struct AddrStatusInfo {
  Addr addr;
  AddrStatus status;
  uint32_t invalid_call_succ_round_count;  // 在状态为invalid后，连续调用成功的周期数
  uint32_t invalid_return_count;           // 在invalid后，本周期内已经对外返回过的次数

  AddrStatusInfo() : status(ADDR_NORMAL), invalid_call_succ_round_count(0), invalid_return_count(0) { addr.url[0] = 0; }
};

/// @brief 单个业务服务的路由状态信息
struct RouteStatusInfo {
  uint32_t bussiness_id;
  uint32_t service_id;
  uint32_t addr_count;      // 地址数量
  uint32_t cur_addr_index;  // 当前使用的地址索引(是一直自增的，要mod一下addr_count才是addr_status_list的下标
  AddrStatusInfo addr_status_list[MAX_ADDR_COUNT];  // 地址状态列表

  RouteStatusInfo() : bussiness_id(0), service_id(0), addr_count(0), cur_addr_index(0) {}

  static int Compare(const void* data1, const void* data2) {
    RouteStatusInfo* item1 = (RouteStatusInfo*)data1;
    RouteStatusInfo* item2 = (RouteStatusInfo*)data2;
    return item1->bussiness_id == item2->bussiness_id && item1->service_id == item2->service_id ? 0 : 1;
  }

  static unsigned int HashCode(const void* data) {
    RouteStatusInfo* item = (RouteStatusInfo*)data;
    return item->bussiness_id * item->service_id;
  }
};

DirRouteManager::DirRouteManager()
    : logger_(NULL),
      last_statistic_time_(0),
      call_statistic_interval_(0),
      req_count_threshold_(0),
      fail_rate_threshold_(0),
      call_info_table_(NULL),
      route_info_table_(NULL),
      service_count_(0) {
  // TODO Auto-generated constructor stub
}  // lint !e1401

DirRouteManager::~DirRouteManager() { Fini(); }

int DirRouteManager::CreateRouteInfoTable(uint32_t service_count) {
  int route_info_table_item_count = service_count;

  /*这里加一个保护，如果桶的数量为0，则强制设置为1，防止sht_find core掉*/
  if (0 == route_info_table_item_count) {
    route_info_table_item_count = 1;
  }

  int route_info_table_bucket_count = 3 * route_info_table_item_count / 2;
  size_t route_info_table_size = 0;
  route_info_table_ = sht_create(route_info_table_bucket_count, route_info_table_item_count, sizeof(RouteStatusInfo),
                                 &route_info_table_size);
  if (NULL == route_info_table_) {
    DIR_LOG_ERROR(logger_, "route_info_table_ sht_create failed, item_count=%d, bucket_count=%d",
                  route_info_table_item_count, route_info_table_bucket_count);
    return APOLLO_DIR_ERROR_ALLOC_FAIL;
  }

  DIR_LOG_DEBUG(logger_,
                "route_info_table_ init succ, item_count=%d, bucket_count=%d, "
                "size=" FORMAT_SIZE_T,
                route_info_table_item_count, route_info_table_bucket_count, route_info_table_size);

  return 0;
}

int DirRouteManager::CreateCallInfoTable(uint32_t service_count) {
  int call_info_table_item_count = service_count * MAX_ADDR_COUNT;

  /*这里加一个保护，如果桶的数量为0，则强制设置为MAX_ADDR_COUNT，防止sht_find
   * core掉*/
  if (0 == call_info_table_item_count) {
    call_info_table_item_count = MAX_ADDR_COUNT;
  }

  int call_info_table_bucket_count = 3 * call_info_table_item_count / 2;
  size_t call_info_table_size = 0;
  call_info_table_ =
      sht_create(call_info_table_bucket_count, call_info_table_item_count, sizeof(AddrCallInfo), &call_info_table_size);
  if (NULL == call_info_table_) {
    DIR_LOG_ERROR(logger_, "call_info_table_ sht_create failed, item_count=%d, bucket_count=%d",
                  call_info_table_item_count, call_info_table_bucket_count);
    return APOLLO_DIR_ERROR_ALLOC_FAIL;
  }

  DIR_LOG_DEBUG(logger_,
                "call_info_table_ init succ, item_count=%d, bucket_count=%d, "
                "size=" FORMAT_SIZE_T,
                call_info_table_item_count, call_info_table_bucket_count, call_info_table_size);

  return 0;
}

int DirRouteManager::InsertDataIntoCallInfoTable(const BussinessServiceConfig& config) {
  for (uint32_t i = 0; i < config.stServiceAddrList.dwAddrCount; ++i) {
    AddrCallInfo key;
    key.bussiness_id = config.stBussinessService.dwBussinessId;
    key.service_id = config.stBussinessService.dwServiceId;
    STR_N_CPY(key.addr.url, config.stServiceAddrList.astAddrList[i].szUrl, sizeof(key.addr.url));

    AddrCallInfo* insert =
        (AddrCallInfo*)sht_insert_unique(call_info_table_, &key, AddrCallInfo::Compare, AddrCallInfo::Hash);
    if (NULL == insert) {
      DIR_LOG_ERROR(logger_, "sht_insert_unique failed, bussiness_id=%u, service_id=%u, addr=%s",
                    config.stBussinessService.dwBussinessId, config.stBussinessService.dwServiceId,
                    config.stServiceAddrList.astAddrList[i].szUrl);
      return APOLLO_DIR_ERROR_SYS;
    }

    insert->bussiness_id = config.stBussinessService.dwBussinessId;
    insert->service_id = key.service_id = config.stBussinessService.dwServiceId;
    STR_N_CPY(insert->addr.url, config.stServiceAddrList.astAddrList[i].szUrl, sizeof(insert->addr.url));
    insert->ResetCallData();
  }

  return 0;
}

int DirRouteManager::InitCallInfoTableData(const BussinessServiceConfig* route_config, uint32_t route_count) {
  for (uint32_t i = 0; i < route_count; ++i) {
    int ret = InsertDataIntoCallInfoTable(route_config[i]);
    if (ret) {
      DIR_LOG_ERROR(logger_, "InsertDataIntoCallInfoTable failed");
      return ret;
    }
  }

  return 0;
}

int DirRouteManager::InitCallInfoTable(const BussinessServiceConfig* route_config, uint32_t route_count) {
  int ret = CreateCallInfoTable(route_count);
  if (ret) {
    DIR_LOG_ERROR(logger_, "CreateCallInfoTable failed");
    return ret;
  }

  ret = InitCallInfoTableData(route_config, route_count);
  if (ret) {
    DIR_LOG_ERROR(logger_, "InitCallInfoTableData failed");
    return ret;
  }

  return 0;
}

uint32_t DirRouteManager::RandomAddrIndex(uint32_t val) {
  if (val == 0) {
    return 0;
  }
  int fd = -1;
  fd = open("/dev/urandom", O_RDONLY);
  if (fd < 0) {
    return 0;
  }
  uint64_t bigint = 0;
  if (read(fd, &bigint, sizeof(bigint)) != sizeof(bigint)) {
    close(fd);
    return 0;
  }
  close(fd);
  return (bigint % val);
}

int DirRouteManager::InitRouteInfoTableData(const BussinessServiceConfig* route_config, uint32_t route_count) {
  for (uint32_t i = 0; i < route_count; ++i) {
    const BussinessServiceConfig& config = route_config[i];
    RouteStatusInfo key;
    key.bussiness_id = config.stBussinessService.dwBussinessId;
    key.service_id = config.stBussinessService.dwServiceId;

    RouteStatusInfo* insert = (RouteStatusInfo*)sht_insert_unique(route_info_table_, &key, RouteStatusInfo::Compare,
                                                                  RouteStatusInfo::HashCode);
    if (NULL == insert) {
      DIR_LOG_ERROR(logger_, "sht_insert_unique failed, bussiness_id=%u, service_id=%u",
                    config.stBussinessService.dwBussinessId, config.stBussinessService.dwServiceId);
      return APOLLO_DIR_ERROR_SYS;
    }

    insert->bussiness_id = config.stBussinessService.dwBussinessId;
    insert->service_id = config.stBussinessService.dwServiceId;
    insert->cur_addr_index = RandomAddrIndex(dir_svr::TDIR_MAX_ADDR_NUM);

    DIR_LOG_INFO(logger_, "biz=%u,service_id=%u,start_addr_index=%u", insert->bussiness_id, insert->service_id,
                 insert->cur_addr_index);
    insert->addr_count = config.stServiceAddrList.dwAddrCount;
    for (uint32_t j = 0; j < insert->addr_count; ++j) {
      AddrStatusInfo& addr_status = insert->addr_status_list[j];
      STR_N_CPY(addr_status.addr.url, config.stServiceAddrList.astAddrList[j].szUrl, sizeof(addr_status.addr.url));
      addr_status.invalid_call_succ_round_count = 0;
      addr_status.invalid_return_count = 0;
      addr_status.status = ADDR_NORMAL;
    }
  }

  return 0;
}

int DirRouteManager::InitRouteInfoTable(const BussinessServiceConfig* route_config, uint32_t route_count) {
  int ret = CreateRouteInfoTable(route_count);
  if (ret) {
    DIR_LOG_ERROR(logger_, "CreateRouteInfoTable failed");
    return ret;
  }

  ret = InitRouteInfoTableData(route_config, route_count);
  if (ret) {
    DIR_LOG_ERROR(logger_, "InitRouteInfoTableData failed");
    return ret;
  }

  return 0;
}

int DirRouteManager::Init(const BussinessServiceConfig* route_config, uint32_t route_count,
                          const PolicyInfo& policy_info, TLOGCATEGORYINST* logger) {
  logger_ = logger;

  if (NULL == route_config) {
    return APOLLO_DIR_ERROR_INVALID_ARG;
  }

  if (route_count > (uint32_t)MAX_MULTI_ROUTE_NUM) {
    DIR_LOG_ERROR(logger_, "svr_resp config count is invalid, count=%u", route_count);
    return APOLLO_DIR_ERROR_BUFFER_IS_SHORT;
  }

  // 初始化services_
  service_count_ = route_count;
  for (uint32_t i = 0; i < service_count_; ++i) {
    services_[i].dwBussinessId = route_config[i].stBussinessService.dwBussinessId;
    services_[i].dwServiceId = route_config[i].stBussinessService.dwServiceId;
  }

  // 设置policy
  SetPolicy(policy_info);

  // 初始化route_info_table_
  int ret = InitRouteInfoTable(route_config, route_count);
  if (ret) {
    Fini();
    return ret;
  }

  // 初始化call_info_table_
  ret = InitCallInfoTable(route_config, route_count);
  if (ret) {
    Fini();
    return ret;
  }

  DIR_LOG_DEBUG(logger_, "route mgr init succ");

  return APOLLO_DIR_ERROR_NONE;
}

int DirRouteManager::Init(const TDirGetConfigByBussinessResp& svr_resp, TLOGCATEGORYINST* logger) {
  return Init(svr_resp.astBussinessServiceConfig, svr_resp.dwBussinessServiceConfigCount, svr_resp.stPolicyInfo,
              logger);
}

int DirRouteManager::Init(const TDirGetConfigResp& svr_resp, TLOGCATEGORYINST* logger) {
  return Init(svr_resp.astBussinessServiceConfig, svr_resp.dwBussinessServiceConfigCount, svr_resp.stPolicyInfo,
              logger);
}

int DirRouteManager::Init(const TDirGetMultiConfigResp& svr_resp, TLOGCATEGORYINST* logger) {
  return Init(svr_resp.astBussinessServiceConfig, svr_resp.dwBussinessServiceConfigCount, svr_resp.stPolicyInfo,
              logger);
}

int DirRouteManager::Init(const TDirGetBigAddrResp& svr_resp, TLOGCATEGORYINST* logger) {
  // TODO: XXX 确认是不是可以不用开发
  return 0;
}

void DirRouteManager::Fini() {
  logger_ = NULL;
  last_statistic_time_ = 0;
  call_statistic_interval_ = 0;
  req_count_threshold_ = 0;
  fail_rate_threshold_ = 0;

  if (call_info_table_) {
    sht_destroy(&call_info_table_);
    call_info_table_ = NULL;
  }

  if (route_info_table_) {
    sht_destroy(&route_info_table_);
    route_info_table_ = NULL;
  }

  DIR_LOG_DEBUG(logger_, "route mgr fini succ");

  service_count_ = 0;
}

void DirRouteManager::SetPolicy(const PolicyInfo& policy) {
  call_statistic_interval_ = policy.dwCallStatisticInterval;
  req_count_threshold_ = policy.dwAddrFailReqCountThreshold;
  fail_rate_threshold_ = policy.dwAddrFailRateThreshold;
}

inline uint32_t GetCurrentIndex(const RouteStatusInfo* route_info) {
  return route_info->cur_addr_index % route_info->addr_count;
}

inline AddrStatusInfo& GetCurrentAddr(RouteStatusInfo* route_info) {
  return route_info->addr_status_list[GetCurrentIndex(route_info)];
}

/// @brief 找到下一个应该返回给调用者的地址，设置好current
int DirRouteManager::GetNextAddr(RouteStatusInfo* route_info) {
  uint32_t try_count = 0;
  for (; try_count < route_info->addr_count; ++try_count, ++(route_info->cur_addr_index)) {
    AddrStatusInfo& addr_info = GetCurrentAddr(route_info);
    if (ADDR_INVALID == addr_info.status) {
      // 地址为invalid状态，如果本周期没有返回过，可以对外返回
      if (addr_info.invalid_return_count < static_cast<uint32_t>(INVALID_ADDR_ROUND_RETURN_COUNT)) {
        ++(addr_info.invalid_return_count);
        break;
      }
    } else {
      break;
    }
  }

  // 没有可用的地址,全部是invalid状态的
  if (try_count >= route_info->addr_count) {
    return APOLLO_DIR_ERROR_NOAVAILABLE_ADDR;
  } else {
    return APOLLO_DIR_ERROR_NONE;
  }
}

int DirRouteManager::GetRoute(uint32_t bussiness_id, uint32_t service_id, Addr* addr) {
  if (NULL == addr) {
    DIR_LOG_ERROR(logger_, "addr is null");
    return APOLLO_DIR_ERROR_INVALID_ARG;
  }

  addr->url[0] = 0;

  // 从route_info查找
  RouteStatusInfo key_item;
  key_item.bussiness_id = bussiness_id;
  key_item.service_id = service_id;
  RouteStatusInfo* route_info =
      (RouteStatusInfo*)sht_find(route_info_table_, &key_item, RouteStatusInfo::Compare, RouteStatusInfo::HashCode);
  if (NULL == route_info) {
    // 业务id没找到
    return APOLLO_DIR_ERROR_ROUTE_NOT_FOUND;
  }
  if (0 == route_info->addr_count) {
    // DIR_LOG_ERROR( logger_, "service has no addr, bussiness_id=%u,
    // service_id=%u", bussiness_id, service_id );
    return APOLLO_DIR_ERROR_NO_ADDR;
  }

  int ret = GetNextAddr(route_info);
  if (ret) {
    return ret;
  }

  STR_N_CPY(addr->url, GetCurrentAddr(route_info).addr.url, sizeof(addr->url));

  ++(route_info->cur_addr_index);

  return APOLLO_DIR_ERROR_NONE;
}

void DirRouteManager::ReportRouteCallResult(uint32_t bussiness_id, uint32_t service_id, const Addr* addr, int result,
                                            uint32_t elapsed_ms) {
  if (NULL == addr) {
    return;
  }

  AddrCallInfo key_item;
  key_item.bussiness_id = bussiness_id;
  key_item.service_id = service_id;
  STR_N_CPY(key_item.addr.url, addr->url, sizeof(key_item.addr.url));
  AddrCallInfo* call_info =
      (AddrCallInfo*)sht_find(call_info_table_, &key_item, AddrCallInfo::Compare, AddrCallInfo::Hash);
  if (NULL == call_info) {
    return;
  }

  ++(call_info->call_count);
  if (result < 0) {
    ++(call_info->call_fail_count);
  } else {
    ++(call_info->call_succ_count);
  }
  call_info->call_total_time_ += elapsed_ms;
}

void DirRouteManager::Update() {
  time_t now = time(NULL);
  if (now - last_statistic_time_ > (int)call_statistic_interval_) {
    for (uint32_t i = 0; i < service_count_; ++i) {
      RouteProcess(services_[i]);
    }

    last_statistic_time_ = now;
  }
}

inline bool DirRouteManager::IsAddrInvalid(const AddrCallInfo& addr_call_info) {
  return addr_call_info.call_count >= req_count_threshold_ && addr_call_info.call_count > 0 &&
         (addr_call_info.call_fail_count * 1000) / addr_call_info.call_count >= fail_rate_threshold_;
}

bool DirRouteManager::IsInvalidAddrVerifySucc(const AddrCallInfo& addr_call_info) {
  return addr_call_info.call_count > 0 && addr_call_info.call_count == addr_call_info.call_succ_count;
}

void DirRouteManager::LogAddrInfo(int pri, const AddrCallInfo& addr_info) {
  DECLARE_STR(visual, 1024);
  snprintf(visual, sizeof(visual),
           "addr info: bussiness_id=%u, service_id=%u, addr=%s, call_count=%u, "
           "fail_count=%u, "
           "succ_count=%u, avg_use_time=" FORMAT_64UINT "(ms)",
           addr_info.bussiness_id, addr_info.service_id, addr_info.addr.url, addr_info.call_count,
           addr_info.call_fail_count, addr_info.call_succ_count,
           0 == addr_info.call_count ? 0 : addr_info.call_total_time_ / addr_info.call_count);
  DIR_LOG(logger_, pri, visual);
}

void DirRouteManager::UpdateAddrRouteStatus(uint32_t bussiness_id, uint32_t service_id, AddrStatusInfo* addr_info) {
  // 从call_table中找到addr的调用情况item
  AddrCallInfo call_key_item;
  call_key_item.bussiness_id = bussiness_id;
  call_key_item.service_id = service_id;
  STR_N_CPY(call_key_item.addr.url, addr_info->addr.url, sizeof(call_key_item.addr.url));
  AddrCallInfo* call_item =
      (AddrCallInfo*)sht_find(call_info_table_, &call_key_item, AddrCallInfo::Compare, AddrCallInfo::Hash);
  if (NULL == call_item) {
    DIR_LOG_ERROR(logger_, "FATAL,addr not found,bussiness_id=%u,service_id=%u,addr=%s", bussiness_id, service_id,
                  call_key_item.addr.url);
    return;
  }

  LogAddrInfo(TLOG_PRIORITY_INFO, *call_item);

  if (ADDR_INVALID == addr_info->status) {
    if (IsInvalidAddrVerifySucc(*call_item)) {
      DIR_LOG_DEBUG(logger_, "invalid addr verify succ this round, addr=%s", call_item->addr.url);
      ++(addr_info->invalid_call_succ_round_count);
    } else {
      // 要连续INVALID_ADDR_RECOVERY_COUNT轮周期都成功，才恢复，
      // 所以如果一轮周期没有成功，就要重置为0
      addr_info->invalid_call_succ_round_count = 0;
    }

    if (addr_info->invalid_call_succ_round_count >= static_cast<uint32_t>(INVALID_ADDR_RECOVERY_COUNT)) {
      DIR_LOG_ERROR(logger_, "addr=%s is ok now,restore it", call_item->addr.url);
      addr_info->status = ADDR_NORMAL;
      addr_info->invalid_call_succ_round_count = 0;
    }

    addr_info->invalid_return_count = 0;
  } else {
    // 调用次数和失败率都大于阈值，就设置为不可用
    if (IsAddrInvalid(*call_item)) {
      DIR_LOG_ERROR(logger_,
                    "addr is invalid! bussiness_id=%u, service_id=%u, addr=%s, "
                    "call_count=%u, fail_count=%u",
                    call_item->bussiness_id, call_item->service_id, call_item->addr.url, call_item->call_count,
                    call_item->call_fail_count);
      addr_info->status = ADDR_INVALID;
    }
  }

  call_item->ResetCallData();
}

void DirRouteManager::LogRouteInfo(int pri, const RouteStatusInfo& route_info) {
  if (CanLog(logger_, pri)) {
    DECLARE_STR(visual, 40960);
    snprintf(visual, sizeof(visual),
             "service route info:\nbussiness_id=%u, service_id=%u, "
             "addr_count=%u, cur_addr_idx=%u\n",
             route_info.bussiness_id, route_info.service_id, route_info.addr_count,
             0 == route_info.addr_count ? 0 : route_info.cur_addr_index % route_info.addr_count);
    for (uint32_t i = 0; i < route_info.addr_count; ++i) {
      DECLARE_STR(tmp, 1024);
      const AddrStatusInfo& addr_info = route_info.addr_status_list[i];
      snprintf(tmp, sizeof(tmp),
               "addr=%s, status=%d, invalid_call_succ_round_count=%u, "
               "invalid_return_count=%u\n",
               addr_info.addr.url, addr_info.status, addr_info.invalid_call_succ_round_count,
               addr_info.invalid_return_count);
      if (sizeof(visual) > strlen(visual) && sizeof(visual) - strlen(visual) < strlen(tmp) + 1)  // lint !e574
      {
        break;
      }
      strncat(visual, tmp, sizeof(visual) - strlen(visual) - 1);
    }
    DIR_LOG(logger_, pri, visual);
  }
}

void DirRouteManager::RouteProcess(const BussinessService& service) {
  // 先在status表中找出当前的路由情况
  RouteStatusInfo status_key_item;
  status_key_item.bussiness_id = service.dwBussinessId;
  status_key_item.service_id = service.dwServiceId;
  RouteStatusInfo* route_info = (RouteStatusInfo*)sht_find(route_info_table_, &status_key_item,
                                                           RouteStatusInfo::Compare, RouteStatusInfo::HashCode);
  if (NULL == route_info) {
    // 业务id没找到
    return;
  }

  LogRouteInfo(TLOG_PRIORITY_INFO, *route_info);

  // 遍历每个addr的调用情况，决定是否设置为invalid, 或者从invalid恢复
  for (uint32_t i = 0; i < route_info->addr_count; ++i) {
    UpdateAddrRouteStatus(service.dwBussinessId, service.dwServiceId, &(route_info->addr_status_list[i]));
  }
}

} /* namespace apollo_dir */
