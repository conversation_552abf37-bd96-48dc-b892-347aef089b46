/*
 * dir_route_manager.h
 *
 *  Created on: 2014年3月22日
 *      Author: rog<PERSON><PERSON>
 */

#ifndef DIR_ROUTE_MANAGER_H_
#define DIR_ROUTE_MANAGER_H_

#include "comm/comm.h"
#include "dir_log.h"
#include "dir_protocol.h"
#include "dir_types.h"
#include "tlog/tlog.h"

using dir_svr::BussinessService;
using dir_svr::BussinessServiceConfig;
using dir_svr::PolicyInfo;
using dir_svr::TDirGetBigAddrResp;
using dir_svr::TDirGetConfigByBussinessResp;
using dir_svr::TDirGetConfigResp;
using dir_svr::TDirGetMultiConfigResp;

namespace apollo_dir {

struct RouteStatusInfo;
struct AddrStatusInfo;
struct AddrCallInfo;

const int INVALID_ADDR_ROUND_RETURN_COUNT =
    1;  // 对于不可用的地址， 每周期返回的次数
const int INVALID_ADDR_RECOVERY_COUNT =
    2;  // 对于不可用的地址， 连续几个周期成功后，就标识为已恢复

class DirRouteManager {
 public:
  DirRouteManager();
  virtual ~DirRouteManager();

 public:
  int Init(const TDirGetConfigResp& svr_resp, TLOGCATEGORYINST* logger);
  int Init(const TDirGetConfigByBussinessResp& svr_resp,
           TLOGCATEGORYINST* logger);
  int Init(const TDirGetMultiConfigResp& svr_resp, TLOGCATEGORYINST* logger);
  int Init(const TDirGetBigAddrResp& svr_resp, TLOGCATEGORYINST* logger);
  void Fini();
  void SetPolicy(const PolicyInfo& policy);
  int GetRoute(uint32_t bussiness_id, uint32_t service_id, Addr* addr);
  void ReportRouteCallResult(uint32_t bussiness_id, uint32_t service_id,
                             const Addr* addr, int result, uint32_t elapsed_ms);
  void Update();

 private:
  uint32_t RandomAddrIndex(uint32_t val);
  int Init(const BussinessServiceConfig* route_config, uint32_t route_count,
           const PolicyInfo& policy_info, TLOGCATEGORYINST* logger);
  int GetNextAddr(RouteStatusInfo* route_info);
  void RouteProcess(const BussinessService& service);
  int CreateCallInfoTable(uint32_t service_count);
  int CreateRouteInfoTable(uint32_t service_count);
  void UpdateAddrRouteStatus(uint32_t bussiness_id, uint32_t service_id,
                             AddrStatusInfo* addr_info);
  bool IsAddrInvalid(const AddrCallInfo& addr_call_info);
  bool IsInvalidAddrVerifySucc(const AddrCallInfo& addr_call_info);
  void LogAddrInfo(int pri, const AddrCallInfo& addr_info);
  void LogRouteInfo(int pri, const RouteStatusInfo& route_info);
  int InitCallInfoTableData(const BussinessServiceConfig* route_config,
                            uint32_t route_count);
  int InsertDataIntoCallInfoTable(const BussinessServiceConfig& config);
  int InitCallInfoTable(const BussinessServiceConfig* route_config,
                        uint32_t route_count);
  int InitRouteInfoTableData(const BussinessServiceConfig* route_config,
                             uint32_t route_count);
  int InitRouteInfoTable(const BussinessServiceConfig* route_config,
                         uint32_t route_count);

 private:
  TLOGCATEGORYINST* logger_;
  time_t last_statistic_time_;
  uint32_t call_statistic_interval_;  // 调用情况统计周期
  uint32_t req_count_threshold_;      // 地址失效的调用次数阈值
  uint32_t fail_rate_threshold_;      // 地址失效的失败率阈值
  LPSHTABLE call_info_table_;         // 记录每个地址的调用统计数据
  LPSHTABLE route_info_table_;  // 记录每个业务服务的当前路由信息

  BussinessService services_[MAX_MULTI_ROUTE_NUM];
  uint32_t service_count_;
};

} /* namespace apollo_dir */

#endif /* DIR_ROUTE_MANAGER_H_ */
