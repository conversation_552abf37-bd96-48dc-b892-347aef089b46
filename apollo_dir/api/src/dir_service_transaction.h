/*
 * Copyright (c) 2013 Tencent all rights reserved
 *
 * time:   2014-04-01
 * file:   dir_service_transaction.h
 * desc:   事物管理
 *
 */

#ifndef DIR_SERVICE_TRANSACTION_H_
#define DIR_SERVICE_TRANSACTION_H_

#include "base_macro.h"
#include "dir_api_common.h"
#include "dir_log.h"
#include "dir_protocol.h"
#include "dir_service_net.h"
#include "dir_types.h"

using dir_svr::TDIR_CMD;
using dir_svr::TDIR_CMD_GET_BIG_ADDR_REQ;
using dir_svr::TDIR_CMD_GET_CONFIG_BY_BUSSINESS_REQ;
using dir_svr::TDIR_CMD_GET_CONFIG_REQ;
using dir_svr::TDIR_CMD_GET_MULTI_CONFIG_REQ;
using dir_svr::TDIR_MAGIC_CURRENT;
using dir_svr::TDIR_MAX_KEY_LEN;

using dir_svr::BussinessService;
using dir_svr::TDirMsg;

namespace apollo_dir {

enum TransState {
  TRANS_STATE_IDLE = 0,
  TRANS_STATE_START,
  TRANS_STATE_READY,
  TRANS_STATE_SEND,
  TRANS_STATE_RECV,
  TRANS_STATE_END,
};

class DirServiceTransaction {
 public:
  DirServiceTransaction();
  virtual ~DirServiceTransaction();

 public:
  int Start(TDIR_CMD cmd, const char* url);
  int Update();
  void End();

 public:
  inline void ServiceCount(uint32_t count) { service_count_ = count; }

  inline void ServiceList(const BussinessService* service_list) {
    service_list_ = service_list;
  }

  inline void SetBigAddrServiceInfo(
      const BigAddrServiceInfo& bigaddr_service_info) {
    big_addr_service_info_.dwRegionId = bigaddr_service_info.dwRegionId;
    big_addr_service_info_.dwServiceId = bigaddr_service_info.dwServiceId;
  }

  inline void Bussiness(uint32_t bussiness_id, const char* bussiness_key) {
    bussiness_id_ = bussiness_id;
    if (NULL == bussiness_key) {
      bussiness_key_[0] = 0;
    } else {
      STR_N_CPY(bussiness_key_, bussiness_key, sizeof(bussiness_key_));
    }
  }

  inline void MultiBusiness(const MultiBusinessInfo& business_info) {
    MultiBusinessInfoCopy(&business_info_, business_info);
  }

  inline void RefreshBigAddrMd5(const char* big_addr_md5) {
    if (big_addr_md5) {
      STR_N_CPY(last_list_md5_, big_addr_md5, dir_svr::TDIR_MD5_VALUE_LEN);
    }
  }

 private:
  int DispatchRequstCmd(TDIR_CMD cmd);
  bool IsTimeout();
  int Pack();
  int Send();
  int Recv();
  int Unpack();
  bool IsValidRespPkg();
  int RecvAndTryUnpack();

 private:
  inline void BuildHead(uint16_t cmd) {
    send_msg_.stHead.wMagic = TDIR_MAGIC_CURRENT;
    send_msg_.stHead.wVersion = 0;
    send_msg_.stHead.ullAsyncId = async_++;
    send_msg_.stHead.wAsyncLen = 0;
    send_msg_.stHead.wCommand = cmd;
  }

  void BuildGetConfigReq();
  void BuildGetConfigByBussinessReq();
  void BuildGetMultiConfigReq();
  void BuildGetBigAddrReq();

 public:
  inline void Timeout(uint32_t timeout) {
    timeout_ms_ = timeout;
    net_api_.Timeout(timeout);
  }

  inline void Logcat(TLOGCATEGORYINST* logger) {
    logger_ = logger;
    net_api_.Logcat(logger);
  }

  inline TransState GetState() const { return state_; }

  inline int GetMsg(TDirMsg* msg) {
    if (TRANS_STATE_END != state_) {
      return -1;
    }

    memcpy(msg, &recv_msg_, sizeof(TDirMsg));
    state_ = TRANS_STATE_IDLE;

    return 0;
  }

 private:
  inline void ResetNode() {
    send_buff_.data_len = 0;
    send_buff_.offset = 0;

    recv_buff_.data_len = 0;
    recv_buff_.offset = 0;
  }

 private:
  static const size_t PACKAGE_LEN = sizeof(TDirMsg);

  struct BuffNode {
    uint32_t offset;
    size_t data_len;
    size_t buff_len;
    char buff[3 * PACKAGE_LEN];

    BuffNode() {
      offset = 0;
      data_len = 0;
      buff_len = sizeof(buff);
      memset(buff, 0, sizeof(buff));
    }  // lint !e1401
  };

 private:
  TLOGCATEGORYINST* logger_;

  struct timeval start_;
  uint32_t timeout_ms_;
  const char* curr_url_;
  TransState state_;

  /*
   * 收发包相关
   */
  DirServiceNet net_api_;

  BuffNode send_buff_;
  BuffNode recv_buff_;

  TDirMsg send_msg_;
  TDirMsg recv_msg_;

  uint64_t async_;
  uint32_t service_count_;
  const BussinessService* service_list_;

  uint32_t bussiness_id_;
  char bussiness_key_[TDIR_MAX_KEY_LEN];

  MultiBusinessInfo business_info_;
  BigAddrServiceInfo big_addr_service_info_;

  char last_list_md5_[dir_svr::TDIR_MD5_VALUE_LEN];  // 地址列表MD5值
};

}  // namespace apollo_dir
#endif
