/*
 * Copyright (c) 2013 Tencent all rights reserved
 *
 * time:   2014-03-21
 * file:   dir_service_net.cpp
 * desc:   undo
 *
 */

#include "dir_service_net.h"

#include <errno.h>
#include <fcntl.h>
#include <netdb.h>
#include <stdlib.h>
#include <string.h>
#include <sys/epoll.h>
#include <sys/socket.h>
#include <sys/time.h>
#include <unistd.h>

#include "dir_api_common.h"
#include "dir_protocol.h"
#include "dir_service_error.h"

using dir_svr::TDIR_URL_LEN;

namespace apollo_dir {

const char* DirServiceNet::TCP = "tcp";
const char* DirServiceNet::UDP = "udp";
const char* DirServiceNet::PROTO_SEP = "://";
const char DirServiceNet::PORT_SEP = ':';
const char* DirServiceNet::NUMBER = "1234567890.:";

DirServiceNet::DirServiceNet()
    : fd_(-1),
      timeout_(0),
      bufflen_(0),
      net_state_(NET_STATE_CLOSED),
      logger_(NULL) {
  start_.tv_sec = 0;
  start_.tv_usec = 0;
}

DirServiceNet::~DirServiceNet() { logger_ = NULL; }

int DirServiceNet::Connect(const char* url) {
  if (NULL == url) {
    return APOLLO_DIR_ERROR_INVALID_ARG;
  }

  // 同一时间只能存在一个连接
  if (NET_STATE_CLOSED != net_state_) {
    DIR_LOG_ERROR(logger_, "last time request keep alive");
    return APOLLO_DIR_ERROR_SOCKET_IN_PROCESS;
  }

  int ret = 0;
  struct sockaddr_in addr;

  gettimeofday(&start_, NULL);

  ret = OpenSocket(url);
  if (ret < 0) {
    return APOLLO_DIR_ERROR_FD_FAIL;
  }

  ret = Str2Net(url, &addr);
  if (0 != ret) {
    DIR_LOG_ERROR(logger_, "open socket fail");
    Close();
    return ret;
  }

  SetNonBlock();
  DIR_LOG_DEBUG(logger_, "set socket fd nonblock");

  if (bufflen_ > 0) {
    AdjustSendAndRecvBuffLen();
  }

  ret = Connect(&addr);
  if (0 == ret) {
    // 异步connect如果返回0，说明连接已经建立，这种情况发生在同一主机上
    net_state_ = NET_STATE_CONNECTED;

    DIR_LOG_DEBUG(logger_, "connect succ, change state[NET_STATE_CONNECTED]");
    return APOLLO_DIR_ERROR_NONE;
  }

  if (ret > 0) {
    net_state_ = NET_STATE_CONNECTING;

    DIR_LOG_DEBUG(logger_, "connecting, change state[NET_STATE_CONNECTING]");
    return APOLLO_DIR_ERROR_NONE;
  }

  // 小于0出错
  return ret;
}

int DirServiceNet::OpenSocket(const char* url) {
  if (NULL == url) {
    return APOLLO_DIR_ERROR_INVALID_ARG;
  }

  int type = 0;

  DIR_LOG_DEBUG(logger_, "open socket[%s]", url);

  if (IsTCP(url)) {
    type = SOCK_STREAM;
  } else {
    return APOLLO_DIR_ERROR_INVALID_URL;
  }

  fd_ = socket(PF_INET, type, 0);

  return fd_;
}

bool DirServiceNet::IsTCP(const char* url) {
  if (NULL == url) {
    return false;
  }

  const char* flag = NULL;
  int proto_len = 0;

  proto_len = strlen(TCP);

  flag = strstr(url, PROTO_SEP);
  if (NULL == flag) {
    DIR_LOG_ERROR(logger_, "url format error, it must like tcp://127.0.0.1:80");
    return false;
  }

  // 目前仅支持TCP链接
  if (0 == (strncasecmp(url, TCP, proto_len))) {
    DIR_LOG_DEBUG(logger_, "proto[%s]", TCP);
    return true;
  }
  DIR_LOG_ERROR(logger_, "error proto, just support TCP connection");
  return false;
}

int DirServiceNet::SetNonBlock() {
  int flag = fcntl(fd_, F_GETFL, 0);
  flag |= O_NONBLOCK | O_ASYNC;

  return fcntl(fd_, F_SETFL, flag);
}

int DirServiceNet::GetAddrByDomainSync(const std::string& domain,
                                       struct sockaddr_in* addr) {
  struct addrinfo* result = NULL;
  struct addrinfo hints = {0};
  hints.ai_family = AF_INET;  // ipv4
  hints.ai_socktype = SOCK_STREAM;
  hints.ai_protocol = IPPROTO_IP;

  int ret = getaddrinfo(domain.c_str(), NULL, &hints, &result);
  if (ret != 0 || result == NULL) {
    DIR_LOG_ERROR(logger_, "getaddrinfo fail, invalid url, ret[%d]", ret);
    return ret;
  }
  struct sockaddr_in* addr_in = (struct sockaddr_in*)result->ai_addr;
  // 只取第一个地址
  addr->sin_addr = addr_in->sin_addr;
  freeaddrinfo(result);
  return 0;
}

int DirServiceNet::GetURLInfo(const char* url, URLInfo* url_info) {
  const char* ip = NULL;
  const char* port = NULL;
  char host[TDIR_URL_LEN];
  ip = strstr(url, PROTO_SEP);
  if (NULL == ip) {
    ip = url;
  } else {
    ip += strlen(PROTO_SEP);
  }

  DIR_LOG_DEBUG(logger_, "ip[%s]", ip);

  port = strchr(ip, PORT_SEP);
  if (NULL == port) {
    int len = strlen(ip);
    if (len >= TDIR_URL_LEN) {
      return APOLLO_DIR_ERROR_INVALID_URL;
    }
    strncpy(host, ip, len);
    url_info->port = 0;
  } else {
    int len = port - ip;
    if (len >= TDIR_URL_LEN) {
      return APOLLO_DIR_ERROR_INVALID_URL;
    }

    memcpy(host, ip, len);
    host[len] = '\0';
    port++;
    url_info->port = (uint16_t)atoi(port);
  }
  url_info->host = host;
  return 0;
}

int DirServiceNet::Str2Net(const char* url, struct sockaddr_in* addr) {
  if (NULL == url || NULL == addr) {
    return APOLLO_DIR_ERROR_INVALID_ARG;
  }

  const char* ip = NULL;
  const char* port = NULL;
  char host[TDIR_URL_LEN];
  int ret = 0;

  memset(addr, 0, sizeof(*addr));
  addr->sin_family = AF_INET;

  ip = strstr(url, PROTO_SEP);
  if (NULL == ip) {
    ip = url;
  } else {
    ip += strlen(PROTO_SEP);
  }

  DIR_LOG_DEBUG(logger_, "ip[%s]", ip);

  port = strchr(ip, PORT_SEP);
  if (NULL == port) {
    int len = strlen(ip);
    if (len >= TDIR_URL_LEN) {
      return APOLLO_DIR_ERROR_INVALID_URL;
    }
    strncpy(host, ip, len);
    addr->sin_port = 0;
  } else {
    int len = port - ip;
    if (len >= TDIR_URL_LEN) {
      return APOLLO_DIR_ERROR_INVALID_URL;
    }

    memcpy(host, ip, len);
    host[len] = '\0';
    port++;

    uint16_t p = (uint16_t)atoi(port);
    addr->sin_port = htons(p);
  }
  DIR_LOG_DEBUG(logger_, "ip[%s], host[%s], prot[%s]", ip, host, port);

  if (IsIP(host)) {
    ret = inet_aton(host, &addr->sin_addr);
    if (0 == ret) {
      DIR_LOG_ERROR(logger_, "inet_aton fail[%d], invalid url", ret);
      return APOLLO_DIR_ERROR_INVALID_URL;
    }
  } else {
    // 采用异步dns解析
    // 如果直接拿域名来请求，返回失败
    DIR_LOG_ERROR(logger_, "invalid url=%s is not ip address", host);
    return APOLLO_DIR_ERROR_INVALID_URL;
    // DIR_LOG_DEBUG(logger_, "try to GetAddrByDomain");

    // //跟之前语义实现保持一致，暂时不采用异步的方式获取ip
    // int ret = GetAddrByDomain(host, addr);
    // if (ret != 0) {
    //   DIR_LOG_ERROR(logger_, "GetAddrByDomain fail, invalid url, ret[%d]",
    //   ret); return APOLLO_DIR_ERROR_INVALID_URL;
    // }
  }

  return APOLLO_DIR_ERROR_NONE;
}

int DirServiceNet::IsIP(const char* host) {
  if (NULL == host) {
    return APOLLO_DIR_ERROR_INVALID_ARG;
  }

  size_t num = strspn(host, NUMBER);
  if (num == strlen(host)) {
    return 1;
  } else {
    return 0;
  }
}

int DirServiceNet::Connect(const struct sockaddr_in* addr) {
  if (NULL == addr) {
    return APOLLO_DIR_ERROR_INVALID_ARG;
  }

  int ret = 0;
  int error_code = 0;

  ret = connect(fd_, (struct sockaddr*)addr, sizeof(*addr));
  if (0 == ret) {
    DIR_LOG_DEBUG(logger_, "connect ret[%d]", ret);
    return APOLLO_DIR_ERROR_NONE;
  }

  error_code = errno;
  if (EISCONN == error_code) {
    DIR_LOG_DEBUG(logger_,
                  "connect state[%d], EISCONN[%d], already connect succ",
                  error_code, EISCONN);
    return APOLLO_DIR_ERROR_NONE;
  }

  if (EINPROGRESS == error_code || EALREADY == error_code) {
    DIR_LOG_DEBUG(logger_, "connect state[%d], EINPROGRESS[%d], EALREADY[%d]",
                  error_code, EINPROGRESS, EALREADY);
    return 1;
  } else {
    DIR_LOG_ERROR(logger_, "connect fail, errno[%d]", error_code);
    return APOLLO_DIR_ERROR_CONNECT_FAIL;
  }
}

int DirServiceNet::CheckConnect() {
  int ret = 0;

  int socket_error = 0;
  socklen_t len = sizeof(socket_error);

  ret = EpollCheck(EPOLLOUT);
  if (0 == ret) {
    return APOLLO_DIR_ERROR_CONNECT_TIMEOUT;
  }

  if (ret < 0) {
    DIR_LOG_ERROR(logger_, "fd[%d] epoll check fail, errno[%d]", fd_, errno);
    return APOLLO_DIR_ERROR_CONNECT_FAIL;
  }

  ret = getsockopt(fd_, SOL_SOCKET, SO_ERROR, &socket_error, &len);
  if ((0 == ret) && (0 == socket_error)) {
    return APOLLO_DIR_ERROR_NONE;
  } else {
    DIR_LOG_ERROR(logger_,
                  "fd[%d] getsockopt has error, errno[%d], ECONNREFUSED[111]",
                  fd_, socket_error);
    return APOLLO_DIR_ERROR_CONNECT_FAIL;
  }
}

void DirServiceNet::AdjustSendAndRecvBuffLen() {
  uint32_t len = 0;
  socklen_t sock_len = sizeof(len);
  int ret = 0;

  // adjust send buff
  if (0 == getsockopt(fd_, SOL_SOCKET, SO_SNDBUF, &len, &sock_len)) {
    DIR_LOG_DEBUG(logger_, "socket send buffer[%u], bussiness buffer[%u]", len,
                  bufflen_);
    if (len < bufflen_) {
      ret = setsockopt(fd_, SOL_SOCKET, SO_SNDBUF, &bufflen_, sizeof(bufflen_));
      DIR_LOG_DEBUG(logger_, "set socket send buffer, ret[%d]", ret);
    }
  }

  if (0 == getsockopt(fd_, SOL_SOCKET, SO_RCVBUF, &len, &sock_len)) {
    DIR_LOG_DEBUG(logger_, "socket recv buffer[%u], bussiness buffer[%u]", len,
                  bufflen_);
    if (len < bufflen_) {
      ret = setsockopt(fd_, SOL_SOCKET, SO_RCVBUF, &bufflen_, sizeof(bufflen_));
      DIR_LOG_DEBUG(logger_, "set socket recv buffer, ret[%d]", ret);
    }
  }
}

int DirServiceNet::Send(const char* buff, size_t len) {
  if (NULL == buff || 0 == len) {
    return APOLLO_DIR_ERROR_INVALID_ARG;
  }

  int ret = 0;

  ret = send(fd_, buff, len, 0);
  if (ret >= 0) {
    return ret;
  }

  if (EAGAIN == errno || EWOULDBLOCK == errno) {
    return APOLLO_DIR_ERROR_WOULD_BLOCK;
  } else {
    DIR_LOG_ERROR(logger_, "send fail, system errno[%d]", errno);
    return APOLLO_DIR_ERROR_SYS;
  }
}

int DirServiceNet::Recv(char* buff, size_t len) {
  int ret = 0;

  ret = recv(fd_, buff, len, 0);
  if (ret > 0) {
    return ret;
  }

  // 等于0，对端关闭
  if (0 == ret) {
    return APOLLO_DIR_ERROR_SVR_CLOSE_CONN;
  }

  // 小于0，出错
  if (EAGAIN == errno || EWOULDBLOCK == errno) {
    DIR_LOG_TRACE(logger_, "recv fail, socket would be block");
    return APOLLO_DIR_ERROR_WOULD_BLOCK;
  } else {
    DIR_LOG_ERROR(logger_, "recv fail, system errno[%d]", errno);
    return APOLLO_DIR_ERROR_SYS;
  }
}

int DirServiceNet::Update() {
  int ret = 0;

  switch (net_state_) {
    case NET_STATE_CONNECTING: {
      DIR_LOG_TRACE(logger_, "check connect");

      ret = CheckConnect();
      if (APOLLO_DIR_ERROR_NONE != ret) {
        if (APOLLO_DIR_ERROR_CONNECT_TIMEOUT == ret) {
          uint32_t remain = 0;

          remain = RemainTimeMs(&start_, timeout_);
          if (remain > 0) {
            ret = APOLLO_DIR_ERROR_NONE;
          } else {
            DIR_LOG_DEBUG(logger_,
                          "connect timeout, change state[NET_STATE_CLOSED]");
            Close();
            ret = APOLLO_DIR_ERROR_CONNECT_TIMEOUT;
          }
        } else {
          DIR_LOG_DEBUG(logger_,
                        "connect fail, change state[NET_STATE_CLOSED]");
          Close();
          ret = APOLLO_DIR_ERROR_CONNECT_FAIL;
        }
      } else {
        DIR_LOG_DEBUG(logger_,
                      "connect succ, change state[NET_STATE_CONNECTED]");
        net_state_ = NET_STATE_CONNECTED;
      }

      break;
    }

    default:
      break;
  }

  return ret;
}

int DirServiceNet::EpollCheck(int events) {
  int ret = 0;
  int epoll_fd = -1;
  struct epoll_event e;
  struct epoll_event epoll_events;

  epoll_fd = epoll_create(1);
  if (epoll_fd < 0) {
    DIR_LOG_ERROR(logger_, "epoll fd create fail");
    return -1;
  }

  memset(&e, 0, sizeof(e));
  e.events = events;
  e.data.fd = fd_;

  ret = epoll_ctl(epoll_fd, EPOLL_CTL_ADD, fd_, &e);
  if (0 == ret) {
    ret = epoll_wait(epoll_fd, &epoll_events, 1, 0);
  }

  close(epoll_fd);

  return ret;
}

}  // namespace apollo_dir
