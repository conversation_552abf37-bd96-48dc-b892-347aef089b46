#include "dir_async_dns.h"

#include <set>
#include <vector>

#include "base_macro.h"
#include "dir_log.h"

namespace apollo_dir {

uint64_t milli_seconds() {
  timeval tnow;
  gettimeofday(&tnow, NULL);
  return (static_cast<uint64_t>(tnow.tv_sec) * 1000 + tnow.tv_usec / 1000);
}

AsyncDns::AsyncDns()
    : hints_(NULL),
      subscribed_(false),
      status_(kInited),
      notifier_(NULL),
      logger_(NULL),
      immediate_resolove_(false) {
  req_ptrs_[0] = NULL;
  last_req_ms_ = milli_seconds();
  last_rsp_check_ms_ = milli_seconds();
}

AsyncDns::~AsyncDns() {}

int AsyncDns::Subscribe(const AsyncDns::Options& opts,
                        IAsyncDnsNotify* notifier) {
  if (subscribed_) {
    return -1;
  }
  if (opts.domain.size() == 0) {
    return -1;
  }
  if (opts.interval == 0) {
    return -1;
  }
  if (notifier == NULL) {
    return -1;
  }
  opts_ = opts;
  notifier_ = notifier;
  struct gaicb* dns_req = (struct gaicb*)calloc(1, sizeof(struct gaicb));
  if (dns_req == NULL) {
    return -1;
  }
  hints_ = Hints();
  if (hints_ == NULL) {
    free(dns_req);
    return -1;
  }

  dns_req->ar_name = opts_.domain.c_str();
  dns_req->ar_request = hints_;
  req_ptrs_[0] = dns_req;
  subscribed_ = true;
  return 0;
}

struct addrinfo* AsyncDns::Hints() {
  struct addrinfo* hint = (addrinfo*)calloc(1, sizeof(struct addrinfo));
  if (hint == NULL) {
    return hint;
  }
  hint->ai_socktype = SOCK_STREAM;
  hint->ai_protocol = IPPROTO_TCP;
  hint->ai_family = AF_INET;
  return hint;
}

void AsyncDns::Tick() {
  if (!subscribed_) return;
  uint64_t now = milli_seconds();
  if ((abs(static_cast<int>(now - last_req_ms_)) >=
       (int)(opts_.interval * 1000)) ||
      immediate_resolove_) {
    AsyncRequest();
    last_req_ms_ = now;
  }
  // 100ms查一次
  if (abs(static_cast<int>(now - last_rsp_check_ms_)) >= 100) {
    CheckAndProcAyncResult();
    last_rsp_check_ms_ = now;
  }
}

void AsyncDns::CheckAndProcAyncResult() {
  if (status_ != kParsing) {
    return;
  }
  int ret = gai_error(req_ptrs_[0]);
  if (ret) {
    if (ret == EAI_INPROGRESS) {
      return;
    };
    status_ = kInited;
    DIR_LOG_ERROR(logger_, "getaddrinfo_a result failed: %s",
                  gai_strerror(ret));
    return;
  }
  DIR_LOG_DEBUG(logger_, "asyn dns response ready");
  status_ = kInited;
  struct addrinfo* res = req_ptrs_[0]->ar_result;
  if (res == NULL) {
    DIR_LOG_ERROR(logger_, "assert failed!!res == NULL");
    return;
  }
  std::set<std::string> ips;
  ret = 0;
  struct addrinfo* res_head = res;
  char host[NI_MAXHOST];
  while (res != NULL) {
    ret = getnameinfo(res->ai_addr, res->ai_addrlen, host, sizeof(host), NULL,
                      0, NI_NUMERICHOST);
    if (ret != 0) {
      DIR_LOG_ERROR(logger_, "getnameinfo() failed: %s", gai_strerror(ret));
      break;
    }
    DIR_LOG_DEBUG(logger_, "resolved ip:%s", host);
    ips.insert(std::string(host));
    res = res->ai_next;
  }
  freeaddrinfo(res_head);
  req_ptrs_[0]->ar_result = NULL;
  if (ips.size() > 0) {
    notifier_->OnDomainResoluted(ips);
  }
}

void AsyncDns::Immediately() { immediate_resolove_ = true; }

void AsyncDns::Close() {
  if (req_ptrs_[0]) {
    free(req_ptrs_[0]);
    req_ptrs_[0] = NULL;
  }
  if (hints_) {
    free(hints_);
    hints_ = NULL;
  }
  subscribed_ = false;
  status_ = kInited;
}

void AsyncDns::AsyncRequest() {
  if (status_ != kInited) {
    return;
  }
  immediate_resolove_ = false;
  int ret = getaddrinfo_a(GAI_NOWAIT, req_ptrs_, 1, NULL);
  if (ret != 0) {
    DIR_LOG_ERROR(logger_, "asyn request dns failed,ret=%d", ret);
    status_ = kInited;
    return;
  }
  DIR_LOG_DEBUG(logger_, "asyn request dns");
  status_ = kParsing;
}

}  // namespace apollo_dir