#ifndef DIR_SERVICE_API_IMP_BIG_ADDR_MODE_H_
#define DIR_SERVICE_API_IMP_BIG_ADDR_MODE_H_

#include "dir_protocol.h"
#include "dir_service_api_imp_mode.h"

using dir_svr::BussinessServiceConfig;
using dir_svr::TDirGetBigAddrResp;

namespace apollo_dir {

class DirServiceApiImpBigAddrMode : public DirServiceApiImpMode {
 public:
  DirServiceApiImpBigAddrMode();
  virtual ~DirServiceApiImpBigAddrMode();

 public:
  virtual int Init(uint32_t timeout);
  void Fini();
  virtual int ProcessRouteAndPolicyInfo(const TDirMsg& resp,
                                        ServiceRouteInfo* service_change_route,
                                        uint32_t* service_change_count,
                                        ReportRouteInfo* report_change_route,
                                        uint32_t* report_change_count);

  virtual int ProcessBigAddrAndPolicyInfo(
      const TDirMsg& resp,
      ServiceBigAddrRouteInfo* service_big_addr_change_route,
      char& service_big_addr_change_flag, ReportRouteInfo* report_change_route,
      uint32_t* report_change_count);

 private:
  void GetChangedRoute(const TDirGetBigAddrResp& new_svr_resp,
                       ServiceBigAddrRouteInfo* service_route,
                       char& service_big_addr_change_flag,
                       ReportRouteInfo* report_route,
                       uint32_t* report_change_count);

  void CompareRoute(const TDirGetBigAddrResp& new_svr_resp,
                    bool report_policy_change,
                    ServiceBigAddrRouteInfo* service_route,
                    char& service_big_addr_change_flag,
                    ReportRouteInfo* report_route,
                    uint32_t* report_change_count);

 private:
  TDirGetBigAddrResp svr_resp_;                      //保存当前生效的svr应答
  char last_list_md5_[dir_svr::TDIR_MD5_VALUE_LEN];  //地址列表MD5值
};

}  // namespace apollo_dir

#endif /* DIR_SERVICE_API_IMP_SERVICE_MODE_H_ */
