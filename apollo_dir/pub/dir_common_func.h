/*
 * dir_common_func.h
 *
 *  Created on: 2014
 *      Author: rog<PERSON><PERSON>
 */

#ifndef DIR_COMMON_FUNC_H_
#define DIR_COMMON_FUNC_H_

#include "base_macro.h"
#include "dir_app_def.h"
#include "dir_log.h"
#include "dir_protocol.h"

using namespace dir_svr;

namespace dir_report_protocol {
class TDirReportMsg;
}

namespace apollo_dir {
void CfgAddrListCopy(CFGADDRLIST* dest_list, const CFGADDRLIST& src_list);
void RouteListCopy(ROUTELIST* dest_list, const ROUTELIST& src_list);
void RouteCopy(ROUTE* dest, const ROUTE& src);
void AddrListCopy(ADDRLIST* dest_list, const ADDRLIST& src_list);
void BigAddrListCopy(BIGADDRLIST* dest_list, const BIGADDRLIST& src_list);
void DirConfigCopy(DIR_APP_CFG* dest_cfg, const DIR_APP_CFG& src_cfg);
void DirGetConfigRespCopy(TDirGetConfigResp* dest,
                          const TDirGetConfigResp& src);
void DirConfigConstruct(DIR_APP_CFG* cfg);
void TDirMsgConstruct(TDirMsg* msg);
void TDirGetConfigRespConstruct(TDirGetConfigResp* resp);
bool IsValidString(const char* str, size_t max_len);
bool IsValidCfgAddrList_NotEmpty(const CFGADDRLIST& addr_list,
                                 bool allow_repeat);
bool IsValidCfgAddrList_AllowEmpty(const CFGADDRLIST& addr_list,
                                   bool allow_repeat);
const char* TrimCString(const char* str);
uint64_t RemainTimeMs(struct timeval* begin, uint32_t timeout_ms);
const char* GetErrorMsg(int retcode);
void BussinessServiceConfigCopy(BussinessServiceConfig* dest,
                                const BussinessServiceConfig* src,
                                uint32_t count);
void ServiceInfoListCopy(dir_svr::ServiceInfo* dest,
                         const dir_svr::ServiceInfo* src, uint32_t count);
void ServiceInfoListCopy(dir_svr::ServiceInfoList* dest,
                         const dir_svr::ServiceInfoList& src);
template <typename A, typename B>
void DirGetConfigByBussinessRespCopy(A* dest, const B& src) {
  dest->stPolicyInfo = src.stPolicyInfo;

  ServiceInfoListCopy(&(dest->stServiceInfoList), src.stServiceInfoList);

  dest->dwBussinessServiceConfigCount = src.dwBussinessServiceConfigCount;
  BussinessServiceConfigCopy(dest->astBussinessServiceConfig,
                             src.astBussinessServiceConfig,
                             src.dwBussinessServiceConfigCount);
}

void ServiceInfoCopy(dir_svr::ServiceInfo* dest,
                     const dir_svr::ServiceInfo& src);
void BussinessServiceInfoCopy(BussinessServiceInfo* dest,
                              const BussinessServiceInfo& src);
int GetApiErrorFromSvrError(int svr_error_code);
void TDirReportMsgConstruct(dir_report_protocol::TDirReportMsg* msg);

void AddrTplListCopy(dir_svr::AddrTplList* dest,
                     const dir_svr::AddrTplList& src);
void AddrTplCopy(dir_svr::AddrTpl* dest, const dir_svr::AddrTpl& src);
void BigServiceRouteCopy(dir_svr::BigServiceRoute* dest,
                         const dir_svr::BigServiceRoute& src);

void DirGetBigAddrRespCopy(TDirGetBigAddrResp* dest,
                           const TDirGetBigAddrResp& src);
}  // namespace apollo_dir

#endif /* DIR_COMMON_FUNC_H_ */
