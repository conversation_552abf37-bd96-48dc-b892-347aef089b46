<?xml version="1.0" encoding="GBK" standalone="yes" ?>

<metalib tagsetversion="1" name="dir_svr" version="4">

  <include file="dir_common.xml" name="引用公共文件" />

  <macro name="TDIR_URL_LEN" value="64" desc="地址url最大长度" />
  <macro name="TDIR_MAX_REQ_ROUTE_NUM" value="128" desc="最大请求业务模块数" />
  <macro name="TDIR_MAX_ERR_LEN" value="1024" desc="出错信息最大长度" />
  <macro name="TDIR_MAX_KEY_LEN" value="64" desc="business_key的最大长度" />
  <macro name="TDIR_MAX_REQ_BUSINESS_NUM" value="128" desc="请求最大业务数量" />
  <macro name="TDIR_MAX_MULTI_ROUTE_NUM" value="1024" desc="获取多业务信息时的最大路由数" />

  <!-- 在协议无法兼容时，可以通过升级 magic 值，定义新协议，同时支持两套协议实现兼容 -->
  <macrosgroup name="TDIR_MAGIC">
    <macro name="TDIR_MAGIC_CURRENT" value="0x139c" desc="初始版本MAGIC" />
  </macrosgroup>

  <macrosgroup name="TDIR_CMD">
    <macro name="TDIR_CMD_INVALID" value="0x0000" desc="" />
    <macro name="TDIR_CMD_GET_CONFIG_REQ" value="0x0001" desc="拉取业务配置的请求" />
    <macro name="TDIR_CMD_GET_CONFIG_RESP" value="0x0002" desc="拉取业务配置的应答" />
    <macro name="TDIR_CMD_GET_CONFIG_BY_BUSSINESS_REQ" value="0x0003" desc="按业务id拉取配置的请求" />
    <macro name="TDIR_CMD_GET_CONFIG_BY_BUSSINESS_RESP" value="0x0004" desc="按业务id拉取配置的应答" />
    <macro name="TDIR_CMD_GET_MULTI_CONFIG_REQ" value="0x0005" desc="拉取多业务配置的请求" />
    <macro name="TDIR_CMD_GET_MULTI_CONFIG_RESP" value="0x0006" desc="拉取多业务配置的应答" />

    <macro name="TDIR_CMD_GET_BIG_ADDR_REQ" value="0x0007" desc="拉取业务配置的请求" />
    <macro name="TDIR_CMD_GET_BIG_ADDR_RESP" value="0x0008" desc="拉取业务配置的应答" />

    <macro name="TDIR_CMD_ERROR_RESP" value="0x00ff" desc="出错应答" />
  </macrosgroup>

  <macrosgroup name="TDIR_ERROR_CODE">
    <macro name="TDIR_ERROR_SYS" value="-100" desc="系统错误" />
    <macro name="TDIR_ERROR_WRONGKEY" value="-110" desc="业务key不正确" />
    <macro name="TDIR_ERROR_INVALIDPARAM" value="-120" desc="参数不正确" />
  </macrosgroup>

  <macro name="TDIR_MAX_ASYNCDATA_SIZE" value="1024" desc="回调数据大小" />

  <struct name="BussinessService" version="1">
    <entry name="BussinessId" type="uint32" desc="业务id" />
    <entry name="ServiceId" type="uint32" desc="服务id" />
    <entry name="BussinessKey" type="string" size="TDIR_MAX_KEY_LEN" desc="业务key" version="2" />
  </struct>

  <struct name="Addr" version="1">
    <entry name="Url" type="string" size="TDIR_URL_LEN" desc="地址的url" />
  </struct>

  <struct name="AddrList" version="1">
    <entry name="AddrCount" type="uint32" desc="地址数目" />
    <entry name="AddrList" type="Addr" count="TDIR_MAX_ADDR_NUM" refer="AddrCount" desc="地址列表" />
  </struct>

  <struct name="BussinessServiceConfig" version="1">
    <entry name="BussinessService" type="BussinessService" desc="业务id和服务id" />
    <entry name="ServiceAddrList" type="AddrList" desc="服务地址列表" />
    <entry name="ReportAddrList" type="AddrList" desc="上报地址列表" />
    <!--add by chadli report gdata -->
    <entry name="GdataReportAddrList" type="AddrList" desc="gdata上报路由列表" version="4" />
  </struct>

  <struct name="PolicyInfo" version="1">
    <entry name="GetConfigInterval" type="uint32" desc="api从Dir拉取配置的时间间隔,单位秒" />
    <entry name="CallStatisticInterval" type="uint32" desc="调用情况统计周期" />
    <entry name="AddrFailReqCountThreshold" type="uint32" desc="地址失效的调用次数阈值" />
    <entry name="AddrFailRateThreshold" type="uint32" desc="地址失效的失败率阈值" />
    <entry name="ReportInterval" type="uint32" desc="当服务需要数据上报时，上报的时间间隔，单位秒" />
  </struct>

  <struct name="TDirGetConfigReq" version="1">
    <entry name="Count" type="uint32" desc="" />
    <entry name="BussinessService" type="BussinessService" count="TDIR_MAX_REQ_ROUTE_NUM" refer="Count" desc="" />
  </struct>

  <struct name="TDirGetConfigByBussinessReq" version="2">
    <entry name="BussinessId" type="uint32" desc="业务id" />
    <entry name="BussinessKey" type="string" size="TDIR_MAX_KEY_LEN" desc="业务key" />
  </struct>

  <struct name="TDirGetConfigResp" version="1">
    <entry name="PolicyInfo" type="PolicyInfo" desc="策略相关数据" />
    <entry name="BussinessServiceConfigCount" type="uint32" desc="返回配置的数量" />
    <entry name="BussinessServiceConfig" type="BussinessServiceConfig" count="TDIR_MAX_REQ_ROUTE_NUM" refer="BussinessServiceConfigCount" desc="返回的业务服务配置列表" />
  </struct>

  <struct name="TDirGetConfigByBussinessResp" version="2">
    <entry name="PolicyInfo" type="PolicyInfo" desc="策略相关数据" />
    <entry name="ServiceInfoList" type="ServiceInfoList" desc="服务相关信息" />
    <entry name="BussinessServiceConfigCount" type="uint32" desc="返回配置的数量" />
    <entry name="BussinessServiceConfig" type="BussinessServiceConfig" count="TDIR_MAX_REQ_ROUTE_NUM" refer="BussinessServiceConfigCount" desc="返回的业务服务配置列表" />
  </struct>

  <struct name="TDirGetMultiConfigReq" version="3">
    <entry name="BusinessReqCount" type="uint32" />
    <entry name="BusinessReq" type="TDirGetConfigByBussinessReq" count="TDIR_MAX_REQ_BUSINESS_NUM" refer="BusinessReqCount" />
  </struct>

  <struct name="TDirGetMultiConfigResp" version="3">
    <entry name="PolicyInfo" type="PolicyInfo" desc="策略相关数据" />
    <entry name="ServiceInfoList" type="ServiceInfoList" desc="服务相关信息" />
    <entry name="BussinessServiceConfigCount" type="uint32" desc="返回配置的数量" />
    <entry name="BussinessServiceConfig" type="BussinessServiceConfig" count="TDIR_MAX_MULTI_ROUTE_NUM" refer="BussinessServiceConfigCount" desc="返回的业务服务配置列表" />
  </struct>

  <struct name="TDirErrorResp" version="1">
    <entry name="ReqCmd" type="uint16" bindmacrosgroup="TDIR_CMD" desc="请求的命令字" />
    <entry name="ErrorCode" type="int" bindmacrosgroup="TDIR_ERROR_CODE" desc="错误码" />
    <entry name="ErrorMsg" type="string" size="TDIR_MAX_ERR_LEN" desc="出错描述" />
  </struct>

  <struct name="BigAddrList" version="1">
    <entry name="AddrCount" type="uint32" desc="地址数目" />
    <entry name="AddrList" type="Addr" count="TDIR_MAX_BIG_ADDR_NUM" refer="AddrCount" desc="地址列表" />
  </struct>

  <struct name="TDirGetBigAddrReq" version="1">
    <entry name="BussinessId" type="uint32" desc="业务id" />
    <entry name="ServiceId" type="uint32" desc="服务id" />
    <entry name="RegionId" type="uint32" desc="地区id" />
    <entry name="BussinessKey" type="string" size="TDIR_MAX_KEY_LEN" desc="业务key" version="2" />
    <entry name="BigAddrListMd5" type="string" size="TDIR_MD5_VALUE_LEN" desc="地址列表MD5值，如果DirSvr发现此值与服务器一致，则不予下发" />
  </struct>

  <struct name="TDirGetBigAddrResp" version="1">
    <entry name="BussinessId" type="uint32" desc="业务id" />
    <entry name="ServiceId" type="uint32" desc="服务id" />
    <entry name="RegionId" type="uint32" desc="地区id" />
    <entry name="BigAddrListMd5" type="string" size="TDIR_MD5_VALUE_LEN" desc="地址列表MD5值" />
    <entry name="BigAddrSameFlag" type="char" desc="地址列表无变化标志" />
    <entry name="BigAddrList" type="BigAddrList" desc="地址列表（大）" />
    <entry name="ReportAddrList" type="AddrList" desc="上报地址列表" />
    <entry name="PolicyInfo" type="PolicyInfo" desc="策略相关数据" />
  </struct>

  <struct name="TDirHead" version="1">
    <entry name="Magic" type="uint16" bindmacrosgroup="TDIR_MAGIC" desc="魔数" />
    <entry name="HeadLen" type="uint32" desc="包头长度" />
    <entry name="BodyLen" type="uint32" desc="数据长度" />
    <entry name="Version" type="uint16" desc="协议版本" />
    <entry name="Command" type="uint16" bindmacrosgroup="TDIR_CMD" desc="包类型" />
    <entry name="AsyncId" type="uint64" desc="异步请求事务ID，返回请求携带的数据" />
    <entry name="AsyncLen" type="uint16" desc="异步回调数据大小" />
    <entry name="AsyncData" type="char" count="TDIR_MAX_ASYNCDATA_SIZE" refer="AsyncLen" desc="异步回调数据" />
  </struct>

  <union name="TDirBody" version="1">
    <entry name="TDirGetConfigReq" type="TDirGetConfigReq" id="TDIR_CMD_GET_CONFIG_REQ" desc="" />
    <entry name="TDirGetConfigResp" type="TDirGetConfigResp" id="TDIR_CMD_GET_CONFIG_RESP" desc="" />
    <entry name="TDirGetConfigByBussinessReq" type="TDirGetConfigByBussinessReq" id="TDIR_CMD_GET_CONFIG_BY_BUSSINESS_REQ" desc="" version="2" />
    <entry name="TDirGetConfigByBussinessResp" type="TDirGetConfigByBussinessResp" id="TDIR_CMD_GET_CONFIG_BY_BUSSINESS_RESP" desc="" version="2" />
    <entry name="TDirGetMultiConfigReq" type="TDirGetMultiConfigReq" id="TDIR_CMD_GET_MULTI_CONFIG_REQ" version="3" />
    <entry name="TDirGetMultiConfigResp" type="TDirGetMultiConfigResp" id="TDIR_CMD_GET_MULTI_CONFIG_RESP" version="3" />

    <entry name="TDirGetBigAddrReq" type="TDirGetBigAddrReq" id="TDIR_CMD_GET_BIG_ADDR_REQ" version="3" />
    <entry name="TDirGetBigAddrResp" type="TDirGetBigAddrResp" id="TDIR_CMD_GET_BIG_ADDR_RESP" version="3" />

    <entry name="TDirErrorResp" type="TDirErrorResp" id="TDIR_CMD_ERROR_RESP" desc="" />
  </union>

  <struct name="TDirMsg" version="1" versionindicator="Head.Version" desc="Dir同访问端的协议">
    <entry name="Head" type="TDirHead" sizeinfo="Head.HeadLen"/>
    <entry name="Body" type="TDirBody" select="Head.Command" sizeinfo="Head.BodyLen"/>
  </struct>

</metalib>
