<?xml version="1.0" encoding="GBK" standalone="yes" ?>

<metalib tagsetversion="1" name="dir_svr" version="1">

  <macro name="TDIR_MAX_ADDR_NUM" value="32" desc="单个模块最大地址数" />
  <macro name="TDIR_MAX_SERVICE_NAME_LEN" value="64" desc="服务名最大的长度" />
  <macro name="TDIR_MAX_SERVICE_COUNT" value="64" desc="单个业务最大service的数目" />
  <macro name="TDIR_MAX_BIG_ADDR_NUM" value="2000" desc="单个模块最大地址数(大)" />
  <macro name="TDIR_MD5_VALUE_LEN" value="33" desc="MD5值的长度" />

  <struct name="ServiceProtocolInfo" version="1">
    <entry name="MagicValue" type="uint16" desc="" />
  </struct>

  <struct name="ServiceInfo" version="1">
    <entry name="ServiceId" type="uint32" desc="" />
    <entry name="ServiceName" type="string" size="TDIR_MAX_SERVICE_NAME_LEN" desc="" />
    <entry name="ProtocolInfo" type="ServiceProtocolInfo" desc="" />
  </struct>

  <struct name="ServiceInfoList" version="1">
    <entry name="Count" type="uint32" defaultvalue="0" io="nooutput" desc="" />
    <entry name="ServiceInfo" type="ServiceInfo" count="TDIR_MAX_SERVICE_COUNT" refer="Count" />
  </struct>

</metalib>