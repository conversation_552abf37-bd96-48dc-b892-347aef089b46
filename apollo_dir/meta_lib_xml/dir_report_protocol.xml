<?xml version="1.0" encoding="GBK" standalone="yes" ?>

<metalib tagsetversion="1" name="dir_report_protocol" version="2">

  <!-- 在协议无法兼容时，可以通过升级 magic 值，定义新协议，同时支持两套协议实现兼容 -->
  <macrosgroup name="TDIR_REPORT_MAGIC">
    <macro name="TDIR_REPORT_MAGIC_CURRENT" value="0x239c" desc="初始版本MAGIC" />
  </macrosgroup>

  <macrosgroup name="TDIR_REPORT_CMD">
    <macro name="TDIR_REPORT_CMD_INVALID" value="0x0000" desc="" />
    <macro name="TDIR_REPORT_CMD_GET_REPORT_ADDR_REQ" value="0x0007" desc="拉取数据上报地址的请求" />
    <macro name="TDIR_REPORT_CMD_GET_REPORT_ADDR_RESP" value="0x0008" desc="拉取数据上报地址的应答" />
    <macro name="TDIR_REPORT_CMD_ERROR_RESP" value="0x00ff" desc="出错应答" />
  </macrosgroup>

  <macro name="TDIR_REPORT_MAX_ADDR_NUM" value="32" desc="最大上报地址数" />
  <macro name="TDIR_REPORT_MAX_ASYNCDATA_SIZE" value="1024" desc="回调数据大小" />
  <macro name="TDIR_REPORT_URL_LEN" value="64" desc="" />
  <macro name="TDIR_REPORT_MAX_ERR_LEN" value="128" desc="" />

  <struct name="Addr" version="1">
    <entry name="Url" type="string" size="TDIR_REPORT_URL_LEN" desc="地址的url" />
  </struct>

  <struct name="AddrList" version="1">
    <entry name="AddrCount" type="uint32" desc="地址数目" />
    <entry name="AddrList" type="Addr" count="TDIR_REPORT_MAX_ADDR_NUM" refer="AddrCount" desc="地址列表" />
  </struct>

  <struct name="TDirGetReportAddrReq" version="1">
    <entry name="Reserve" type="int" />
  </struct>

  <struct name="TDirGetReportAddrResp" version="1">
    <entry name="ReportAddrList" type="AddrList" desc="上报地址列表" />
    <entry name="ReportTcpAddrList" type="AddrList" desc="tcp上报地址列表" version="2" />
  </struct>

  <struct name="TDirErrorResp" version="1">
    <entry name="ReqCmd" type="uint16" bindmacrosgroup="TDIR_REPORT_CMD" desc="请求的命令字" />
    <entry name="ErrorCode" type="int" desc="错误码" />
    <entry name="ErrorMsg" type="string" size="TDIR_REPORT_MAX_ERR_LEN" desc="出错描述" />
  </struct>

  <struct name="TDirReportHead" version="1">
    <entry name="Magic" type="uint16" bindmacrosgroup="TDIR_REPORT_MAGIC" desc="魔数" />
    <entry name="HeadLen" type="uint32" desc="包头长度" />
    <entry name="BodyLen" type="uint32" desc="数据长度" />
    <entry name="Version" type="uint16" desc="协议版本" />
    <entry name="Command" type="uint16" bindmacrosgroup="TDIR_REPORT_CMD" desc="包类型" />
    <entry name="AsyncId" type="uint64" desc="异步请求事务ID，返回请求携带的数据" />
    <entry name="AsyncLen" type="uint16" desc="异步回调数据大小" />
    <entry name="AsyncData" type="char" count="TDIR_REPORT_MAX_ASYNCDATA_SIZE" refer="AsyncLen" desc="异步回调数据" />
  </struct>

  <union name="TDirReportBody" version="1">
    <entry name="TDirGetReportAddrReq" type="TDirGetReportAddrReq" id="TDIR_REPORT_CMD_GET_REPORT_ADDR_REQ"/>
    <entry name="TDirGetReportAddrResp" type="TDirGetReportAddrResp" id="TDIR_REPORT_CMD_GET_REPORT_ADDR_RESP"/>
    <entry name="TDirErrorResp" type="TDirErrorResp" id="TDIR_REPORT_CMD_ERROR_RESP"/>
  </union>

  <struct name="TDirReportMsg" version="1" versionindicator="Head.Version" desc="Dir同访问端的协议">
    <entry name="Head" type="TDirReportHead" sizeinfo="Head.HeadLen"/>
    <entry name="Body" type="TDirReportBody" select="Head.Command" sizeinfo="Head.BodyLen"/>
  </struct>

</metalib>
