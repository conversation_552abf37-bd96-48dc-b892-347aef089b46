<?xml version="1.0" encoding="GBK"?>

<metalib tagsetversion="1" name="dir_svr" version="10">

  <include file="dir_common.xml" name="引用公共文件" />

  <macro name="TDIR_SVR_MAX_ADDR_NUM" value="1024" desc="单个模块最大地址数" />
  <macro name="TDIR_HOST_LEN" value="64" desc="地址url最大长度" />
  <macro name="TDIR_MAX_SERVICEROUTE_NUM" value="20000" desc="最大路由数目" />
  <macro name="TDIR_MIN_APIUPDATE_INTERVAL" value="3" desc="api拉取间隔的最小值" />
  <macro name="TDIR_MIN_CALLSTATISTIC_INTERVAL" value="10" desc="统计调用成功率的时间周期的最小值" />
  <macro name="TDIR_MIN_FAILREQCOUNT_THRESHOLD" value="10" desc="失败数阈值最小值" />
  <macro name="TDIR_MIN_FAILRATE_THRESHOLD" value="1" desc="" />
  <macro name="TDIR_MAX_FAILRATE_THRESHOLD" value="1000" desc="" />
  <macro name="TDIR_MIN_REPORT_INTERVAL" value="3" desc="上报数据时间间隔最小值" />
  <macro name="TDIR_MAX_BUSSINESS_COUNT" value="1024" desc="可配置的最大业务数目" />
  <macro name="TDIR_BUSSINESS_SIG_SALT" value="10000" desc="" />

  <macro name="TDIR_BIG_ROUTE_MAX_NUM" value="2048" desc="大地址最大路由数目" />
  <macro name="TDIR_SVR_MAX_TPL_ADDR_NUM" value="2000" desc="单个模块最大地址数，限制2000是防止地址列表超过udp的最大包长64k" />
  <macro name="TDIR_TPL_NAME_LEN" value="64" desc="TPL名字最大长度" />
  <macro name="TDIR_MAX_TPL_NUM" value="256" desc="模版个数" />

  <struct name="CfgAddrList" version="1">
    <entry name="AddrCount" type="uint32" io="nooutput" desc="地址数量" />
    <entry name="Url" type="string" size="TDIR_HOST_LEN" count="TDIR_SVR_MAX_ADDR_NUM" refer="AddrCount" desc="地址url,例如tcp://***********:8888" />
  </struct>

  <struct name="Route" version="1">
    <entry name="BussinessId" type="uint32" desc="业务id" />
    <entry name="ServiceId" type="uint32" desc="服务id" />
    <entry name="AddrList" type="CfgAddrList" desc="地址列表" />
  </struct>

  <struct name="RouteList" desc="路由列表" version="1">
    <entry name="RouteCount" type="uint32" io="nooutput" desc="路由数目" />
    <entry name="Route" type="Route" count="TDIR_MAX_SERVICEROUTE_NUM" refer="RouteCount" desc="路由列表" />
  </struct>

  <struct name="Policy" desc="下发给api的策略相关数据" version="1">
    <entry name="ApiUpdateInterval" type="uint32" defaultvalue="30" desc="下发给客户端api的定时请求DirSvr的时间间隔，单位秒，不能小于3" />
    <entry name="ApiCallStatisticInterval" type="uint32" defaultvalue="60" desc="下发给客户端api的，进行调用数据统计的时间周期(统计成功率，延迟，决定是否踢掉某些地址), 单位秒，不能小于10" />
    <entry name="ApiAddrFailReqCountThreshold" type="uint32" defaultvalue="30" desc="当api在一个统计周期(ApiCallStatisticInterval)内, 发现某个地址的失败率超过ApiAddrFailRateThreshold, 并且总请求数超过ApiAddrFailReqCountThreshold，就认为该地址不可用，不能小于10" />
    <entry name="ApiAddrFailRateThreshold" type="uint32" defaultvalue="800" desc="单位千分之一，取值范围[1,1000]" />
    <entry name="ReportInterval" type="uint32" defaultvalue="60" desc="上报数据的时间间隔, 单位秒， 不能小于3" />
  </struct>

  <struct name="ServiceList" version="3">
    <entry name="ServiceCount" type="uint32" io="nooutput" desc="" />
    <entry name="ServiceId" type="uint32" count="TDIR_MAX_SERVICE_COUNT" refer="ServiceCount" desc="" />
  </struct>

  <struct name="BussinessServiceItem" version="3">
    <entry name="BussinessId" type="uint32" desc="" />
    <entry name="ServiceList" type="ServiceList" desc="" />
  </struct>

  <struct name="BussinessServiceInfo" version="3">
    <entry name="BussinessCount" type="uint32" io="nooutput" desc="业务数目" />
    <entry name="Bussiness" type="BussinessServiceItem" count="TDIR_MAX_BUSSINESS_COUNT" refer="BussinessCount" desc="" />
  </struct>

  <struct name="AddrTpl" version="1">
    <entry name="AddrTplName" type="string" size="TDIR_TPL_NAME_LEN" desc="地址模版名称" />
    <entry name="AddrListMd5" type="string" size="TDIR_MD5_VALUE_LEN" io="nooutput" desc="地址模版MD5值" />
    <entry name="AddrCount" type="uint32" io="nooutput" desc="地址数量" />
    <entry name="Url" type="string" size="TDIR_HOST_LEN" count="TDIR_SVR_MAX_TPL_ADDR_NUM" refer="AddrCount" desc="地址url,例如tcp://***********:8888" />
  </struct>

  <struct name="BigRouteInfo" version="1">
    <entry name="BussinessId" type="uint32" desc="业务id" />
    <entry name="ServiceId" type="uint32" desc="服务id" />
    <entry name="RegionId" type="uint32" desc="地区id" />
    <entry name="AddrTplName" type="string" size="TDIR_TPL_NAME_LEN" desc="地址模版名称" />
  </struct>

  <struct name="BigServiceRoute" desc="路由列表" version="1">
    <entry name="RouteCount" type="uint32" io="nooutput" desc="路由数目" />
    <entry name="BigRouteInfo" type="BigRouteInfo" count="TDIR_BIG_ROUTE_MAX_NUM" refer="RouteCount" desc="路由列表" />
  </struct>

  <struct name="AddrTplList" version="1">
    <entry name="TplCount" type="uint32" io="nooutput" desc="地址模版数量" />
    <entry name="AddrTpl" type="AddrTpl" count="TDIR_MAX_TPL_NUM" refer="TplCount" desc="模版列表" />
  </struct>

  <struct name="dir_app_cfg" version="1">
    <entry name="MaxServiceRouteCount" defaultvalue="20000" type="uint32" desc="最大支持的业务服务路由数目" />
    <entry name="Policy" type="Policy" desc="下发给客户端的相关数据配置" />
    <entry name="ServiceRoute" type="RouteList" desc="服务路由列表" />
    <entry name="BigServiceRoute" type="BigServiceRoute" desc="大地址的服务路由列表" />
    <entry name="AddrTplList" type="AddrTplList" desc="大地址模版" />
    <entry name="ReportAddrList" type="CfgAddrList" desc="上报路由列表" />
    <!--add by chadli report gdata -->
    <entry name="GdataReportAddrList" type="CfgAddrList" desc="gdata上报路由列表" version="9"/>
    <entry name="ReportTcpAddrList" type="CfgAddrList" desc="tcp上报路由列表" version="8" />
    <entry name="cfg_version" type="uint32" desc="配置文件版本号，reload更新配置文件时需要更新这个值" version="2" />
    <entry name="BussinessServiceInfo" type="BussinessServiceInfo" desc="业务注册过的服务信息" version="3" />
    <entry name="ServiceProtocol" type="ServiceInfoList" desc="服务的协议信息" version="3" />
    <entry name="ConsistentHashNodeReplicateNum" type="uint32" defaultvalue="32" desc="一致性hash每个节点的虚拟节点数量,该参数不支持reload" version="7" />
    <entry name="ServiceMaxAddrNum" type="uint32" defaultvalue="32" desc="下发给api的地址数量，该参数不支持reload" version="10" />
    <entry name="ForceAuthorize" type="uint8" defaultvalue="0" desc="强制校验密码" />
  </struct>

  <struct name="rundata_dir_app" version="1" desc="">
    <entry name="MaxServiceRouteNum" type="uint32" desc="最大可支持的路由数目" />
    <entry name="LoadedServiceRouteNum" type="uint32" desc="当前已配置的路由数目" />
    <entry name="cfg_version" type="uint32" desc="配置文件版本号，reload更新配置文件时需要更新这个值" version="2" />
  </struct>

  <struct name="cumudata_dir_app" version="1" desc="">
    <entry name="RecvMsgNum" type="int64" desc="" />
    <entry name="DiscardMsgNum" type="int64" desc="" />
    <entry name="GetConfigReqNum" type="int64" desc="" />
    <entry name="GetConfigReqFailNum" type="int64" desc="" />
    <entry name="GetConfigByBussinessReqNum" type="int64" desc="" version="3" />
    <entry name="GetConfigByBussinessReqFailNum" type="int64" desc="" version="3" />
    <entry name="GetMultiConfigReqNum" type="int64" version="4" />
    <entry name="GetMultiConfigReqFailNum" type="int64" version="4" />
    <entry name="GetReportAddrNum" type="int64" version="5" />
    <entry name="GetReportAddrFailNum" type="int64" version="5" />

    <entry name="GetBigAddrReqNum" type="int64" desc="" />
    <entry name="GetBigAddrReqFailNum" type="int64" desc="" />
  </struct>

</metalib>
