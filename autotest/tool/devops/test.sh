#!/bin/bash

result="TconndTests          CaseNum: 255  Pass: 255  Failures: 0    Errors: 0\nTconndCluster        CaseNum: 301  Pass: 297  Failures: 2    Errors: 2\nTconndWebsocket      CaseNum: 70   Pass: 69   Failures: 1    Errors: 0\nTconndTbuspp         CaseNum: 51   Pass: 48   Failures: 3    Errors: 0\n"
commands="\n\n\nBAxczxczzxczxczxczxczxczxcxzcxzcasodsfdsfdsfsdfsdfsdfsdfdsfdsfdsfdsfdsfdssdfsdfsdfsdfsdfdsfsffffffffAAxczxczzxczxczxczxczxczxcxzcxzcasodsfdsfdsfsdfsdfsdfsdfdsfdsfdsfdsfdsfdssdfsdfsdfsdfsdfdsfsffffffffAAxczxczzxczxczxczxczxczxcxzcxzcasodsfdsfdsfsdfsdfsdfsdfdsfdsfdsfdsfdsfdssdfsdfsdfsdfsdfdsfsffffffffAAxczxczzxczxczxczxczxczxcxzcxzcasodsfdsfdsfsdfsdfsdfsdfdsfdsfdsfdsfdsfdssdfsdfsdfsdfsdfdsfsffffffffAAxczxczzxczxczxczxczxczxcxzcxzcasodsfdsfdsfsdfsdfsdfsdfdsfdsfdsfdsfdsfdssdfsdfsdfsdfsdfdsfsffffffffA"
devops_pid="p-"
devops_bid="b-"
devops_eid="e-/report.html"

echo "${result//\\n/$'\n'}" | awk -v commands="${commands//\\n/$'\n'}" '
  BEGIN {
    split(commands, cmd_array, "\n")
    cmd_index = 1
  }
  NF == 0 { next }
  NF >= 9 {
    # 使用 \x1F (ASCII Unit Separator) 作为字段分隔符
    print $1 "\x1F" $3 "\x1F" $5 "\x1F" $7 "\x1F" $9 "\x1F" cmd_array[cmd_index]
    cmd_index++
  }' | jq -R -s --arg pid "$devops_pid" --arg bid "$devops_bid" --arg eid "$devops_eid" '
  split("\n") |
  map(select(. != "")) |
  map(split("\u001F") | {
    server_name: .[0],
    case_num: (.[1] | tonumber),
    pass: (.[2] | tonumber),
    failures: (.[3] | tonumber),
    errors: (.[4] | tonumber),
    test_commands: (
      .[5] as $cmd |
      if ($cmd | length) > 500 then ""
      elif ($cmd | length) == 0 then ""
      else $cmd
      end
    ),
    devops_pid: $pid,
    devops_bid: $bid,
    devops_eid: $eid
  }) |
  {
    project: "GcloudService",
    test_results: .
  }'