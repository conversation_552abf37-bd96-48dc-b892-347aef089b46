# 通过./xxx.sh的方式执行脚本. 即若脚本中未指定解释器，则使用系统默认的shell

# 旧的${}引用变量的方式已升级为${{}}，避免和bash原生方式冲突

# 通过::set-variable命令字设置/修改全局变量(在当前步骤执行完才生效)
# echo "::set-variable name=<var_name>::<value>"
# 在后续的插件表单中使用表达式${{variables.<var_name>}}引用这个变量(<var_name>替换为真实变量)
# 注意：旧的通过setEnv设置变量的方式仍然保留，但存在一些历史问题，已停止迭代，不再推荐使用

# 通过::set-output命令字设置当前步骤的输出(变量隔离，不会被覆盖)
# echo "::set-output name=<output_name>::<value>"
# 在后续的插件表单中使用表达式${{jobs.<job_id>.steps.<step_id>.outputs.<output_name>}}引用这个输出，其中job_id和step_id在对应的Job和Task上配置

# 在质量红线中创建自定义指标后，通过setGateValue函数设置指标值
# setGateValue "CodeCoverage" $myValue
# 然后在质量红线选择相应指标和阈值。若不满足，流水线在执行时将会被卡住


########################################################################
##                          记录测试统计结果                           ##
########################################################################

result=""

# ************
result+=$(echo "${{jobs.job_Jws.steps.distribute_id.outputs.sub_pipeline_variables.TestResult}}" | head -n 1)
result+="\n"
result+=$(echo "${{jobs.job_Jws.steps.apollo_dir_id.outputs.sub_pipeline_variables.TestResult}}" | head -n 1)
result+="\n"
result+=$(echo "${{jobs.job_Jws.steps.apollo_check_top_id.outputs.sub_pipeline_variables.TestResult}}" | head -n 1)
result+="\n"
result+=$(echo "${{jobs.job_Jws.steps.apollo_ranktop_service_id.outputs.sub_pipeline_variables.TestResult}}" | head -n 1)
result+="\n"

# # *************
result+=$(echo "${{jobs.job_X8E.steps.apollo_topnext_pebble_id.outputs.sub_pipeline_variables.TestResult}}" | head -n 1)
result+="\n"
result+=$(echo "${{jobs.job_X8E.steps.apollo_aps_proxy_id.outputs.sub_pipeline_variables.TestResult}}" | head -n 1)
result+="\n"
result+=$(echo "${{jobs.job_X8E.steps.apollo_face2face_id.outputs.sub_pipeline_variables.TestResult}}" | head -n 1)
result+="\n"
result+=$(echo "${{jobs.job_X8E.steps.apollo_service_agent_id.outputs.sub_pipeline_variables.TestResult}}" | head -n 1)
result+="\n"
result+=$(echo "${{jobs.job_X8E.steps.apollo_idmap_id.outputs.sub_pipeline_variables.TestResult}}" | head -n 1)
result+="\n"
result+=$(echo "${{jobs.job_X8E.steps.apollo_openid_auth_id.outputs.sub_pipeline_variables.TestResult}}" | head -n 1)
result+="\n"

# # ************ gcc7.3.1
result+=$(echo "${{jobs.job_JQS.steps.apollo_pay_service_id.outputs.sub_pipeline_variables.TestResult}}" | head -n 1)
result+="\n"
result+=$(echo "${{jobs.job_JQS.steps.apollo_intl_sns_id.outputs.sub_pipeline_variables.TestResult}}" | head -n 1)
result+="\n"
result+=$(echo "${{jobs.job_JQS.steps.apollo_itop_auth_id.outputs.sub_pipeline_variables.TestResult}}" | head -n 1)
result+="\n"
result+=$(echo "${{jobs.job_JQS.steps.apollo_swiftd_sns_id.outputs.sub_pipeline_variables.TestResult}}" | head -n 1)
result+="\n"
result+=$(echo "${{jobs.job_JQS.steps.apollo_lbs_id.outputs.sub_pipeline_variables.TestResult}}" | head -n 1)
result+="\n"

echo -e "以下变量用于企业微信发送"
echo "::set-variable name=AllTestResult::${result}"

# ************ gcc4.8.5
echo "${{jobs.job_ZmE.steps.apollo_topnext_swift_id.outputs.sub_pipeline_variables.TestResult}}"
result+=$(echo "${{jobs.job_ZmE.steps.apollo_topnext_swift_id.outputs.sub_pipeline_variables.TestResult}}" | head -n 1)

echo -e "\n\n"
echo -e  "$result"

########################################################################
##                            记录测试详情                             ##
########################################################################

TestDetail=""


########################################################################
## 上报到统一入口 btptest.woa.com，临时地址是test.pressmaster.woa.com   ##
########################################################################

devops_pid=${{ci.pipeline_id}}
devops_bid=${{ci.build_id}}
devops_eid=""

# 将 \n 转换为真实换行符，并提取字段生成JSON
echo "${result//\\n/$'\n'}" | awk '
  # 过滤所有空行（包括连续换行和末尾换行）
  NF == 0 { next } 
  
  # 验证字段完整性（必须包含9个字段）
  NF >= 9 { 
    print $1, $3, $5, $7, $9 
  }' | jq -R -s --arg pid "$devops_pid" --arg bid "$devops_bid" --arg eid "$devops_eid" '
  split("\n") |
  map(select(. != "")) |
  map(split(" ") | {
    server_name: .[0],
    case_num: (.[1] | tonumber),
    pass: (.[2] | tonumber),
    failures: (.[3] | tonumber),
    errors: (.[4] | tonumber),
    devops_pid: $pid,
    devops_bid: $bid,
    devops_eid: $eid
  }) |
  { 
    project: "GcloudService",  # 新增字段
    test_results: .     # 原有结构
  }
' | curl -X POST -H "Content-Type: application/json" -H 'X-Bkapi-Authorization: {"bk_app_code": "pressmasterapi", "bk_app_secret": "CYh3AWxJYL2yDu43FX76rUXSe2sz7XMWr0L5" }' -d @- --insecure https://press-master-gate.apigw.o.woa.com/prod/api/gcloud/report-gcloud-test-results


# 代码覆盖率上报
source ~/.bash_profile
cd ${WORKSPACE}/GCloudService/build
gcov_files="GCloudService.gcov"
# 统计行覆盖率
total_lines=$(grep "^DA:" $gcov_files | wc -l)
covered_lines=$(grep "^DA:[0-9]\+,[1-9]" $gcov_files | wc -l)
line_coverage=$(echo "scale=4; $covered_lines / $total_lines * 100" | bc)
# 计算函数覆盖率
fnf=$(grep "^FNF" GCloudService.gcov | awk -F: '{sum += $2} END {print sum}')
fnh=$(grep "^FNH" GCloudService.gcov | awk -F: '{sum += $2} END {print sum}')
function_coverage=$(echo "scale=4; $fnh / $fnf * 100" | bc)
# 输出结果
echo -e "\n\n\n"
echo "=== Code Coverage Report ==="
echo "Line Coverage:    $line_coverage% ($covered_lines/$total_lines)"
echo "Function Coverage: $function_coverage% ($fnh/$fnf)"

# 手动构建 JSON 对象
json_object=$(
  jq -n \
    --arg server_name        "GcloudService" \
    --arg covered_lines      "$covered_lines" \
    --arg total_lines        "$total_lines" \
    --arg lines_coverage     "$line_coverage" \
    --arg covered_functions  "$fnh" \
    --arg total_functions    "$fnf" \
    --arg functions_coverage "$function_coverage" \
    --arg pid         "$devops_pid" \
    --arg bid         "$devops_bid" \
    '{
      "server_name": $server_name,
      "covered_lines": ($covered_lines | tonumber),
      "total_lines": ($total_lines | tonumber),
      "lines_coverage": ($lines_coverage | tonumber),
      "covered_functions": ($covered_functions | tonumber),
      "total_functions": ($total_functions | tonumber),
      "functions_coverage": ($functions_coverage | tonumber),
      "devops_pid": $pid,
      "devops_bid": $bid
    }'
)

# 生成请求数据
json_data=$(jq -n \
  --arg project "GcloudService" \
  --argjson test_results "$json_object" \
  '{project: $project, test_results: [$test_results]}'
)

echo $json_data

# 发送请求
curl -X POST  -H "Content-Type: application/json" -H 'X-Bkapi-Authorization: {"bk_app_code": "pressmasterapi", "bk_app_secret": "CYh3AWxJYL2yDu43FX76rUXSe2sz7XMWr0L5"}' -d "$json_data" --insecure https://press-master-gate.apigw.o.woa.com/prod/api/gcloud/report-gcloud-code-coverages