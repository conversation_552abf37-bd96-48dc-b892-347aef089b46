#!/bin/bash

# 输入: .gcov 文件路径
gcov_files="GCloudService.gcov"

# 统计行覆盖率
total_lines=$(grep "^DA:" $gcov_files | wc -l)
covered_lines=$(grep "^DA:[0-9]\+,[1-9]" $gcov_files | wc -l)
line_coverage=$(echo "scale=2; $covered_lines / $total_lines * 100" | bc)

# 计算函数覆盖率
fnf=$(grep "^FNF" GCloudService.gcov | awk -F: '{sum += $2} END {print sum}')
fnh=$(grep "^FNH" GCloudService.gcov | awk -F: '{sum += $2} END {print sum}')
function_coverage=$(echo "scale=2; $fnh / $fnf * 100" | bc)

# 输出结果
echo "=== Code Coverage Report ==="
echo "Line Coverage:    $line_coverage% ($covered_lines/$total_lines)"
echo "Function Coverage: $function_coverage% ($fnh/$fnf)"