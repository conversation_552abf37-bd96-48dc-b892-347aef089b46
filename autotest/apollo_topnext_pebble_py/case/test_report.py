
# coding:utf-8
import sys
import os
case_path = os.path.abspath(os.path.join(os.path.dirname(os.path.realpath(__file__))))
sys.path.append(r"/data/landun/workspace/AutoPerf")
from AutoPerf.local_command_op import *

import unittest
import time
import requests
import json
from comm_def import *
from apollo_dir_class import *
from topnext_class import *
from proxy_class import *
from client_class import *
from apollo_agent_class import *
from tlog_class import *
from utils import *

from datetime import datetime
import random
import string

# https://qm.woa.com/itest/v2/1146/case/home/<USER>

class testPebbleTopnextReport(unittest.TestCase):

    @classmethod
    def setUpClass(cls):
        print_current_time("testPebbleTopnextReport setUpClass", "*")
        start_gcloud_env()
        time.sleep(10)
        
    @classmethod
    def tearDownClass(cls): 
        print_current_time("testPebbleTopnextReport tearDownClass", "*")
        stop_gcloud_env()

    def setUp(self):
        print_current_time("testPebbleTopnextReport setUp " + self._testMethodName, "*")
        self.core_files = get_core_files()
        self.procs = []
        self.GlobalImageIndex = random.randint(0, 8)
        print_current_time("GlobalImageIndex: {0}".format(self.GlobalImageIndex))

    def doCleanups(self):
        print_current_time("testPebbleTopnextReport doCleanups", "*")
        stop_env(self.procs)
        hwclock_to_sys()

    def tearDown(self):
        print_current_time("testPebbleTopnextReport tearDown " + self._testMethodName, "*")
        stop_env(self.procs, True)
        core_files2 = get_core_files()
        set1 = set(self.core_files)
        set2 = set(core_files2)
        set3 = set2 - set1
        self.assertTrue(len(set3) == 0, "core dump:{0}".format(set3))
        
    def client(self, idx=0):
        client = TopnextClient(idx=idx)
        self.procs.append(client)
        client.set_cfgs(dir_url="tcp://{0}:{1}".format(local_ip, dir_port))
        return client
    
    def client_tbus(self, idx=0):
        client = TopnextClientTbus(idx=idx)
        self.procs.append(client)
        client.set_cfgs(dir_url="tcp://{0}:{1}".format(local_ip, dir_port))
        return client
    
    # 基础榜单信息： 1-11 1-12 2-21 2-22

    def test_report_entry_threshold_01(self):
        # 基本用例, 上报分数时，低于门槛分，会导致不上榜, 默认为0,表示不开启
        topnexts, proxys, _ = start_env(self.procs, app_num=2, proxy_num=1, startProc=True)
        for svr in topnexts:
            svr.set_subtype({"rank_entry_threshold": 5}, type_idx=0, subtype_idx=0)
            svr.restart()
        time.sleep(3)

        # report openid={1 2 3 4 5 6 7 8 9 10}, score={1 2 3 4 5 6 7 8 9 10} to type1-11-1
        openids = [i for i in range(1, 11)]
        scores = [i for i in range(1, 11)]
        multi_report_req = {"Type":1, "sub_types":[11], "sub_instances":[1], "openids":openids, "scores":scores}
        send_multi_report_req_simple(self, 0, multi_report_req)
        ret, msg = topnexts[0].check_entry_threshold_reject_score("busi_id:10000, world_id:1, zone_id:1, type:1, instance_id:1, sub_type:11, sub_instance:1 ##user score is less than rank entry threshold(5)", [1, 2, 3, 4], [1, 2, 3, 4])
        self.assertTrue(ret, msg)

        # gettop of type1-12-1
        get_top_req = {"Type":1, "sub_type":11, "sub_instanceid":1, "From":1, "count":10, "isImage":0}
        get_top_rsp = {"Type":1, "sub_type":11, "sub_instanceid":1, "ranks":[(i, i, 11 - i) for i in range(5, 11)], "total":6}
        check_top_rank_simple(self, 1, get_top_req, get_top_rsp)

        # 更新 open_id=10,  score = 4
        multi_report_req = {"Type":1, "sub_types":[11], "sub_instances":[1], "openids":[10], "scores":[4]}
        send_multi_report_req_simple(self, 10, multi_report_req)
        ret, msg = topnexts[0].check_entry_threshold_reject_score("busi_id:10000, world_id:1, zone_id:1, type:1, instance_id:1, sub_type:11, sub_instance:1 ##user score is less than rank entry threshold(5)", [10], [4])
        self.assertTrue(ret, msg)
        # gettop of type1-12-1
        get_top_req = {"Type":1, "sub_type":11, "sub_instanceid":1, "From":1, "count":10, "isImage":0}
        get_top_rsp = {"Type":1, "sub_type":11, "sub_instanceid":1, "ranks":[(i, i, 10 - i) for i in range(5, 10)], "total":5}
        check_top_rank_simple(self, 11, get_top_req, get_top_rsp)

    def test_report_entry_threshold_02(self):
        # 支持reload/restart，如果已经在榜的会掉出榜单
        topnexts, proxys, _ = start_env(self.procs, app_num=2, proxy_num=1, startProc=True)

        # report openid={1 2 3 4 5 6 7 8 9 10}, score={1 2 3 4 5 6 7 8 9 10} to type1-11-1
        openids = [i for i in range(1, 11)]
        scores = [i for i in range(1, 11)]
        multi_report_req = {"Type":1, "sub_types":[11], "sub_instances":[1], "openids":openids, "scores":scores}
        send_multi_report_req_simple(self, 0, multi_report_req)

        for svr in topnexts:
            svr.set_subtype({"rank_entry_threshold": 3}, type_idx=0, subtype_idx=0)
            svr.reload()
        time.sleep(3)

        # gettop of type1-12-1
        get_top_req = {"Type":1, "sub_type":11, "sub_instanceid":1, "From":1, "count":10, "isImage":0}
        get_top_rsp = {"Type":1, "sub_type":11, "sub_instanceid":1, "ranks":[(i, i, 11 - i) for i in range(1, 11)], "total":10}
        check_top_rank_simple(self, 1, get_top_req, get_top_rsp)

        # report&gettop again
        openids = [i for i in range(1, 11)]
        scores = [i for i in range(1, 11)]
        multi_report_req = {"Type":1, "sub_types":[11], "sub_instances":[1], "openids":openids, "scores":scores}
        send_multi_report_req_simple(self, 2, multi_report_req)
        get_top_req = {"Type":1, "sub_type":11, "sub_instanceid":1, "From":1, "count":10, "isImage":0}
        get_top_rsp = {"Type":1, "sub_type":11, "sub_instanceid":1, "ranks":[(i, i, 11 - i) for i in range(3, 11)], "total":8}
        check_top_rank_simple(self, 3, get_top_req, get_top_rsp)

        for svr in topnexts:
            svr.set_subtype({"rank_entry_threshold": 8}, type_idx=0, subtype_idx=0)
            svr.restart()
        time.sleep(3)

        # gettop of type1-12-1
        get_top_req = {"Type":1, "sub_type":11, "sub_instanceid":1, "From":1, "count":10, "isImage":0}
        get_top_rsp = {"Type":1, "sub_type":11, "sub_instanceid":1, "ranks":[(i, i, 11 - i) for i in range(3, 11)], "total":8}
        check_top_rank_simple(self, 10, get_top_req, get_top_rsp)

        # report&gettop again
        openids = [i for i in range(1, 11)]
        scores = [i for i in range(1, 11)]
        multi_report_req = {"Type":1, "sub_types":[11], "sub_instances":[1], "openids":openids, "scores":scores}
        send_multi_report_req_simple(self, 11, multi_report_req)
        get_top_req = {"Type":1, "sub_type":11, "sub_instanceid":1, "From":1, "count":10, "isImage":0}
        get_top_rsp = {"Type":1, "sub_type":11, "sub_instanceid":1, "ranks":[(i, i, 11 - i) for i in range(8, 11)], "total":3}
        check_top_rank_simple(self, 12, get_top_req, get_top_rsp)

    def test_report_entry_threshold_03(self):
        # 低于门槛分，会导致不上榜; 历史榜单（从dump文件加载）
        topnexts, proxys, _ = start_env(self.procs, app_num=2, proxy_num=1, startProc=True, use_image=True)

        # report openid={1 2 3 4 5 6 7 8 9 10}, score={1 2 3 4 5 6 7 8 9 10} to type1-11-1
        openids = [i for i in range(1, 11)]
        scores = [i for i in range(1, 11)]
        multi_report_req = {"Type":1, "sub_types":[11], "sub_instances":[1], "openids":openids, "scores":scores}
        send_multi_report_req_simple(self, 0, multi_report_req)

        # 2-2. 手动生成镜像（3个 image_index[0~8] 内随机）
        client = self.client(100)
        send_generate_image_req(client, {"WorldId":1, "ZoneId":1, "Type":1, "InstanceId":1, "imageSequence": 10087, "imageIndex": self.GlobalImageIndex})
        exp_tab = {"OnTopNextGenerateSubRankImageRsp Result:1 1 1 1 0 0 sequence=10087, result=0": 1}
        ret, msg = client.check_keystrs_from_log_with_detail("topnextclient.log", exp_tab)
        self.assertTrue(ret, msg)

        # gettop of type1-12-1
        get_top_req = {"Type":1, "sub_type":11, "sub_instanceid":1, "From":1, "count":10, "isImage":0}
        get_top_rsp = {"Type":1, "sub_type":11, "sub_instanceid":1, "ranks":[(i, i, 11 - i) for i in range(1, 11)], "total":10}
        check_top_rank_simple(self, 1, get_top_req, get_top_rsp)

        dump_1 = TopnextDump(idx=1)
        self.procs.append(dump_1)
        dump_1.set_ini_cfg(zk_address="{0}:{1}".format(local_ip, zookeeper_port))
        dump_1.start()
        time.sleep(31)

        # wait for 3s, stop all topnexts and clean shm
        time.sleep(3)
        for svr in topnexts:
            svr.stop()
        time.sleep(3)
        for svr in topnexts:
            svr.clear_all_shm()
        time.sleep(3)

        # copy data from dump_topnext
        for i in range(1):
            svr = topnexts[0]
            svr.set_subtype({"rank_entry_threshold": 5}, type_idx=0, subtype_idx=0)
            os.system('cp -r {0}/../data/* {1}/../data/'.format(dump_1.bin_path, svr.bin_path))
            svr.start()
        # here must wait for 60 beacause topnext_proxy will not get new topnextlist till new topnext restart for 60s
        time.sleep(10)
        # 镜像也触发了这个日志
        ret, msg = topnexts[0].check_entry_threshold_reject_score("busi_id:10000, world_id:1, zone_id:1, type:1, instance_id:1, sub_type:11, sub_instance:1 ##user score is less than rank entry threshold(5)", [1, 2, 3, 4], [1, 2, 3, 4])
        self.assertTrue(ret, msg)

        # gettop of type1-12-1
        get_top_req = {"Type":1, "sub_type":11, "sub_instanceid":1, "From":1, "count":10, "isImage":0}
        get_top_rsp = {"Type":1, "sub_type":11, "sub_instanceid":1, "ranks":[(i, i, 11 - i) for i in range(5, 11)], "total":6}
        check_top_rank_simple(self, 10, get_top_req, get_top_rsp)
        # 获取镜像
        get_top_req = {"Type":1, "sub_type":11, "sub_instanceid":1, "From":1, "count":10, "isImage":1, "imageIndex": self.GlobalImageIndex}
        get_top_rsp = {"Type":1, "sub_type":11, "sub_instanceid":1, "ranks":[(i, i, 11 - i) for i in range(1, 11)], "total":10}
        check_top_rank_simple(self, 101, get_top_req, get_top_rsp)

    def test_report_entry_threshold_04(self):
        '''
            低于门槛分，会导致不上榜; 从sync同步
        '''
        topnexts, proxys, _ = start_env(self.procs, app_num=2, proxy_num=1, startProc=False, use_image=True)
        [topnext1, topnext2] = topnexts
        for svr in topnexts:
            svr.set_subtype({"n": 200}, type_idx=0, subtype_idx=0)
            svr.set_subtype({"n": 200}, type_idx=0, subtype_idx=1)

        topnext1.start()
        for proxy in proxys:
            proxy.start()
        time.sleep(2)

        # report
        openids = [ i for i in range(1, 101)]
        scores = [ i for i in range(1, 101)]
        client = self.client(0)
        multi_report_req = {"WorldId":1, "ZoneId":1, "Type":1, "InstanceId":1, "sub_types":[11, 12],
                            "sub_instances":[1, 1], "openids":openids, "scores":scores}
        send_multi_report_req(client, multi_report_req, 5)
        exp_tab = {"OnTopNextReportSubRankRsp succ result = 0": 200}
        ret, msg = client.check_keystrs_from_log_with_detail("topnextclient.log", exp_tab)
        self.assertTrue(ret, msg)

        # 2-2. 手动生成镜像（3个 image_index[0~8] 内随机）
        client = self.client(100)
        send_generate_image_req(client, {"WorldId":1, "ZoneId":1, "Type":1, "InstanceId":1, "imageSequence": 10087, "imageIndex": self.GlobalImageIndex})
        exp_tab = {"OnTopNextGenerateSubRankImageRsp Result:1 1 1 1 0 0 sequence=10087, result=0": 1}
        ret, msg = client.check_keystrs_from_log_with_detail("topnextclient.log", exp_tab)
        self.assertTrue(ret, msg)
        
        # get
        for i in range(1, 3):
            get_top_req = {"WorldId":1, "ZoneId":1, "Type":1, "InstanceId":1, "sub_type":10 + i, "sub_instanceid":1, "From":1, "count":100, "isImage": 0}
            client = self.client(i)
            send_get_top_rank(client, get_top_req)
            exp_tab = {"OnTopNextGetTopSubRankRsp succ result = 0": 100}
            ret, msg = client.check_keystrs_from_log_with_detail("topnextclient.log", exp_tab)
            self.assertTrue(ret, msg)
        
        # start a sync topnext, but entry_threshold is smaller than topnext1
        topnext2.set_cfgs(sync_data=1)
        topnext2.set_subtype({"rank_entry_threshold": 50}, type_idx=0, subtype_idx=0)
        topnext2.set_subtype({"rank_entry_threshold": 80}, type_idx=0, subtype_idx=1)
        topnext2.start()
        time.sleep(2)
        
        # stop topnext 1 and send get top again
        topnext1.stop()
        time.sleep(2)
        
        # get
        get_top_req = {"WorldId":1, "ZoneId":1, "Type":1, "InstanceId":1, "sub_type":11, "sub_instanceid":1, "From":1, "count":100, "isImage": 0}
        client = self.client(11)
        send_get_top_rank(client, get_top_req)
        exp_tab = {"OnTopNextGetTopSubRankRsp succ result = 0": 51}
        ret, msg = client.check_keystrs_from_log_with_detail("topnextclient.log", exp_tab)
        self.assertTrue(ret, msg)
        get_top_req = {"WorldId":1, "ZoneId":1, "Type":1, "InstanceId":1, "sub_type":12, "sub_instanceid":1, "From":1, "count":100, "isImage": 0}
        client = self.client(12)
        send_get_top_rank(client, get_top_req)
        exp_tab = {"OnTopNextGetTopSubRankRsp succ result = 0": 21}
        ret, msg = client.check_keystrs_from_log_with_detail("topnextclient.log", exp_tab)
        self.assertTrue(ret, msg)
        # 获取镜像 
        get_top_req = {"Type":1, "sub_type":11, "sub_instanceid":1, "From":1, "count":100, "isImage":1, "imageIndex": self.GlobalImageIndex}
        get_top_rsp = {"Type":1, "sub_type":11, "sub_instanceid":1, "ranks":[(i, i, 101 - i) for i in range(1, 101)], "total":100}
        check_top_rank_simple(self, 101, get_top_req, get_top_rsp)

        # 再次验证第二个app能不能正常工作 report 200 data
        openids = [ i for i in range(1, 101)]
        scores = [ i for i in range(1, 101)]
        client = self.client(20)
        multi_report_req = {"WorldId":1, "ZoneId":1, "Type":1, "InstanceId":1, "sub_types":[11, 12],
                            "sub_instances":[1, 1], "openids":openids, "scores":scores}
        send_multi_report_req(client, multi_report_req, 5)
        exp_tab = {"OnTopNextReportSubRankRsp succ result = 0": 200}
        ret, msg = client.check_keystrs_from_log_with_detail("topnextclient.log", exp_tab)
        self.assertTrue(ret, msg)
        openids = [ i for i in range(101, 201)]
        scores = [ i for i in range(101, 201)]
        client = self.client(21)
        multi_report_req = {"WorldId":1, "ZoneId":1, "Type":1, "InstanceId":1, "sub_types":[11, 12],
                            "sub_instances":[1, 1], "openids":openids, "scores":scores}
        send_multi_report_req(client, multi_report_req, 5)
        exp_tab = {"OnTopNextReportSubRankRsp succ result = 0": 200}
        ret, msg = client.check_keystrs_from_log_with_detail("topnextclient.log", exp_tab)
        self.assertTrue(ret, msg)
        
        # get
        get_top_req = {"WorldId":1, "ZoneId":1, "Type":1, "InstanceId":1, "sub_type":11, "sub_instanceid":1, "From":1, "count":100, "isImage": 0}
        client = self.client(31)
        send_get_top_rank(client, get_top_req)
        exp_tab = {"OnTopNextGetTopSubRankRsp succ result = 0": 100}
        ret, msg = client.check_keystrs_from_log_with_detail("topnextclient.log", exp_tab)
        self.assertTrue(ret, msg)
        get_top_req = {"WorldId":1, "ZoneId":1, "Type":1, "InstanceId":1, "sub_type":11, "sub_instanceid":1, "From":101, "count":100, "isImage": 0}
        client = self.client(32)
        send_get_top_rank(client, get_top_req)
        exp_tab = {"OnTopNextGetTopSubRankRsp succ result = 0": 51}
        ret, msg = client.check_keystrs_from_log_with_detail("topnextclient.log", exp_tab)
        self.assertTrue(ret, msg)

        get_top_req = {"WorldId":1, "ZoneId":1, "Type":1, "InstanceId":1, "sub_type":12, "sub_instanceid":1, "From":1, "count":100, "isImage": 0}
        client = self.client(33)
        send_get_top_rank(client, get_top_req)
        exp_tab = {"OnTopNextGetTopSubRankRsp succ result = 0": 100}
        ret, msg = client.check_keystrs_from_log_with_detail("topnextclient.log", exp_tab)
        self.assertTrue(ret, msg)
        get_top_req = {"WorldId":1, "ZoneId":1, "Type":1, "InstanceId":1, "sub_type":12, "sub_instanceid":1, "From":101, "count":100, "isImage": 0}
        client = self.client(34)
        send_get_top_rank(client, get_top_req)
        exp_tab = {"OnTopNextGetTopSubRankRsp succ result = 0": 21}
        ret, msg = client.check_keystrs_from_log_with_detail("topnextclient.log", exp_tab)
        self.assertTrue(ret, msg)

    def test_report_entry_threshold_05(self):
        # 门槛分,镜像,Multi镜像数据的低分数据还在
        os.system('date -s "11/06/2023 00:30:00"')
        topnexts, proxys, _ = start_env(self.procs, app_num=2, proxy_num=1, startProc=True)
        
        # image_cron=0 * * * *(image per hour)、image_cron=0 0 * * *(image per day)
        set_sub_type = {"n":10, "use_image":1, "image_cron":"0 0 * * *"}
        add_sub_type = {"type":13, "instance_num":3, "n":10, "use_image":1, "image_cron":"0 * * * *", "real_data_op_after_image":0, "rank_entry_threshold": 0}
        for svr in topnexts:
            # type = 1
            svr.set_subtype(set_sub_type, type_idx=0, subtype_idx=0)
            svr.set_subtype(set_sub_type, type_idx=0, subtype_idx=1)
            add_sub_type["type"] = 13
            svr.add_subtype(add_sub_type, type_idx=0)
            add_sub_type["type"] = 14
            svr.add_subtype(add_sub_type, type_idx=0)
            # type = 2
            set_sub_type["use_image"] = 0
            svr.set_subtype(set_sub_type, type_idx=1, subtype_idx=0)
            set_sub_type["use_image"] = 1
            svr.set_subtype(set_sub_type, type_idx=1, subtype_idx=1)
            svr.restart()
        time.sleep(3)

        # 2. report data
        openids = [i for i in range(1, 11)]
        scores = [i for i in range(1, 11)]
        multi_report_req = {"WorldId":1, "ZoneId":1, "Type":1, "InstanceId":1, "sub_types":[12, 13, 14], "sub_instances":[1, 1, 1], "openids":openids, "scores":scores}
        client = self.client(0)
        send_multi_report_req(client, multi_report_req, timeout=2)
        
        # 2-2. 手动生成镜像（3个 image_index[0~8] 内随机）
        client = self.client(1)
        send_generate_image_req(client, {"WorldId":1, "ZoneId":1, "Type":1, "InstanceId":1, "imageSequence": 10087, "imageIndex": self.GlobalImageIndex})
        exp_tab = {"OnTopNextGenerateSubRankImageRsp Result:1 1 1 1 0 0 sequence=10087, result=0": 1}
        ret, msg = client.check_keystrs_from_log_with_detail("topnextclient.log", exp_tab)
        self.assertTrue(ret, msg)
        
        # 3. 发起 GetTopSubRankReq，image_index=0、1
        get_top_req = {"WorldId":1, "ZoneId":1, "Type":1, "InstanceId":1, "sub_type":14, "sub_instanceid":1, "From":1, "count":10, "isImage":1, "imageIndex": self.GlobalImageIndex}
        ranks = [(i + 1, scores[i], 10 - i) for i in range(0, 10)]
        get_top_rsp = {"ranks":ranks, "total":10,
                       "WorldId":1, "ZoneId":1, "Type":1, "InstanceId":1, "sub_instanceid":1, "sub_type":14}
        client = self.client(2)
        ret, msg = check_top_rank(client, get_top_req, get_top_rsp, timeout=4)
        self.assertTrue(ret, msg)
        
        # 4.reload
        for svr in topnexts:
            svr.set_subtype({"rank_entry_threshold": 3}, type_idx=0, subtype_idx=3)
            svr.reload()
        time.sleep(3)

        # report & gettop again (get 实时 + 镜像)
        openids = [i for i in range(1, 11)]
        scores = [i for i in range(1, 11)]
        multi_report_req = {"Type":1, "sub_types":[14], "sub_instances":[1], "openids":openids, "scores":scores}
        send_multi_report_req_simple(self, 3, multi_report_req)
        # 实时
        get_top_req = {"Type":1, "sub_type":14, "sub_instanceid":1, "From":1, "count":10, "isImage":0}
        get_top_rsp = {"Type":1, "sub_type":14, "sub_instanceid":1, "ranks":[(i, i, 11 - i) for i in range(3, 11)], "total":8}
        check_top_rank_simple(self, 4, get_top_req, get_top_rsp)
        # 镜像
        get_top_req = {"Type":1, "sub_type":14, "sub_instanceid":1, "From":1, "count":10, "isImage":1, "imageIndex": self.GlobalImageIndex}
        get_top_rsp = {"Type":1, "sub_type":14, "sub_instanceid":1, "ranks":[(i, i, 11 - i) for i in range(1, 11)], "total":10}
        check_top_rank_simple(self, 5, get_top_req, get_top_rsp)

    def test_report_entry_threshold_06(self):
        # 门槛分,镜像, 衰减(最后一次上报时间小于周一1点的会进行衰减，固定减去ext_field2的值)
        os.system('date -s "01/01/2025 00:30:00"') # 星期三
        topnexts, proxys, _ = start_env(self.procs, app_num=2, proxy_num=1, startProc=False)
        for svr in topnexts:
            svr.set_type({"use_reduce": 1, "reduce_cron": "0 0 * * * ", "reduce_func": 4, "reduce_args": "reduce_rate=10 min_rate=70 inc_rate=1"}, type_idx=0)
            svr.set_subtype( {"use_image":1, "image_cron":"0 0 * * *", "rank_entry_threshold": 50}, type_idx=0, subtype_idx=0)
            svr.start()
        for proxy in proxys:
            proxy.start()
        time.sleep(1)

        # 2. report data
        openids = [10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
        scores = [100, 99, 98, 97, 96, 95, 94, 93, 92, 91]
        ExtField2s = [20, 18, 16, 14, 12, 10, 8, 6, 4, 2]
        multi_report_req = {"WorldId":1, "ZoneId":1, "Type":1, "InstanceId":1, "sub_types":[11, 12], "sub_instances":[1, 1], "openids":openids, "scores":scores, "ExtField2s":ExtField2s}
        client = self.client(0)
        send_multi_report_req(client, multi_report_req, timeout=2)

        # 第一轮衰减
        os.system('date -s "01/06/2025 01:14:59"') # 星期一
        time.sleep(5)
        openids = [10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
        scores = [80, 81, 82, 83, 84, 85, 86, 87, 88, 89]
        get_top_req = {"Type":1, "sub_type":11, "sub_instanceid":1, "From":1, "count":10, "isImage":0}
        get_top_rsp = {"Type":1, "sub_type":11, "sub_instanceid":1, "ranks":[(openids[i], scores[i], 10 - i) for i in range(len(openids))], "total":10}
        check_top_rank_simple(self, 10, get_top_req, get_top_rsp)

        # 第二轮衰减
        os.system('date -s "01/13/2025 01:14:59"') # 星期一
        time.sleep(5)
        openids = [10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
        scores = [60, 63, 66, 69, 72, 75, 78, 81, 84, 87]
        get_top_req = {"Type":1, "sub_type":11, "sub_instanceid":1, "From":1, "count":10, "isImage":0}
        get_top_rsp = {"Type":1, "sub_type":11, "sub_instanceid":1, "ranks":[(openids[i], scores[i], 10 - i) for i in range(len(openids))], "total":10}
        check_top_rank_simple(self, 11, get_top_req, get_top_rsp)

        # 第三轮衰减
        os.system('date -s "01/20/2025 01:14:59"') # 星期一
        time.sleep(5)
        openids = [8, 7, 6, 5, 4, 3, 2, 1]
        scores = [50, 55, 60, 65, 70, 75, 80, 85]
        get_top_req = {"Type":1, "sub_type":11, "sub_instanceid":1, "From":1, "count":10, "isImage":0}
        get_top_rsp = {"Type":1, "sub_type":11, "sub_instanceid":1, "ranks":[(openids[i], scores[i], 8 - i) for i in range(len(openids))], "total":len(openids)}
        check_top_rank_simple(self, 12, get_top_req, get_top_rsp)
        openids = [10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
        scores = [40, 45, 50, 55, 60, 65, 70, 75, 80, 85]
        get_top_req = {"Type":1, "sub_type":12, "sub_instanceid":1, "From":1, "count":10, "isImage":0}
        get_top_rsp = {"Type":1, "sub_type":12, "sub_instanceid":1, "ranks":[(openids[i], scores[i], 10 - i) for i in range(len(openids))], "total":len(openids)}
        check_top_rank_simple(self, 13, get_top_req, get_top_rsp)

        # 第四轮衰减
        os.system('date -s "01/27/2025 01:14:59"') # 星期一
        time.sleep(5)
        openids = [5, 4, 3, 2, 1]
        scores = [55, 62, 69, 76, 83]
        get_top_req = {"Type":1, "sub_type":11, "sub_instanceid":1, "From":1, "count":10, "isImage":0}
        get_top_rsp = {"Type":1, "sub_type":11, "sub_instanceid":1, "ranks":[(openids[i], scores[i], 5 - i) for i in range(len(openids))], "total":len(openids)}
        check_top_rank_simple(self, 14, get_top_req, get_top_rsp)
        openids = [10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
        scores = [20, 27, 34, 41, 48, 55, 62, 69, 76, 83]
        get_top_req = {"Type":1, "sub_type":12, "sub_instanceid":1, "From":1, "count":10, "isImage":0}
        get_top_rsp = {"Type":1, "sub_type":12, "sub_instanceid":1, "ranks":[(openids[i], scores[i], 10 - i) for i in range(len(openids))], "total":len(openids)}
        check_top_rank_simple(self, 15, get_top_req, get_top_rsp)

        # 第五轮衰减
        os.system('date -s "02/03/2025 01:14:59"') # 星期一
        time.sleep(5)
        openids = [4, 3, 2, 1]
        scores = [54, 63, 72, 81]
        get_top_req = {"Type":1, "sub_type":11, "sub_instanceid":1, "From":1, "count":10, "isImage":0}
        get_top_rsp = {"Type":1, "sub_type":11, "sub_instanceid":1, "ranks":[(openids[i], scores[i], 4 - i) for i in range(len(openids))], "total":len(openids)}
        check_top_rank_simple(self, 16, get_top_req, get_top_rsp)
        openids = [10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
        scores = [0, 9, 18, 27, 36, 45, 54, 63, 72, 81]
        get_top_req = {"Type":1, "sub_type":12, "sub_instanceid":1, "From":1, "count":10, "isImage":0}
        get_top_rsp = {"Type":1, "sub_type":12, "sub_instanceid":1, "ranks":[(openids[i], scores[i], 10 - i) for i in range(len(openids))], "total":len(openids)}
        check_top_rank_simple(self, 17, get_top_req, get_top_rsp)

        # 第六轮衰减
        os.system('date -s "02/10/2025 01:14:59"') # 星期一
        time.sleep(5)
        openids = [3, 2, 1]
        scores = [57, 68, 79]
        get_top_req = {"Type":1, "sub_type":11, "sub_instanceid":1, "From":1, "count":10, "isImage":0}
        get_top_rsp = {"Type":1, "sub_type":11, "sub_instanceid":1, "ranks":[(openids[i], scores[i], 3 - i) for i in range(len(openids))], "total":len(openids)}
        check_top_rank_simple(self, 18, get_top_req, get_top_rsp)
        openids = [10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
        scores = [0, 0, 2, 13, 24, 35, 46, 57, 68, 79]
        get_top_req = {"Type":1, "sub_type":12, "sub_instanceid":1, "From":1, "count":10, "isImage":0}
        get_top_rsp = {"Type":1, "sub_type":12, "sub_instanceid":1, "ranks":[(openids[i], scores[i], 10 - i) for i in range(len(openids))], "total":len(openids)}
        check_top_rank_simple(self, 19, get_top_req, get_top_rsp)

        # reload rank_entry_threshold
        for svr in topnexts:
            svr.set_subtype({"rank_entry_threshold": 0}, type_idx=0, subtype_idx=0)
            svr.set_subtype({"rank_entry_threshold": 60}, type_idx=0, subtype_idx=1)
            svr.reload()
        time.sleep(5)

         # 第七轮衰减
        os.system('date -s "02/17/2025 01:14:59"') # 星期一
        time.sleep(5)
        openids = [3, 2, 1]
        scores = [51, 64, 77]
        get_top_req = {"Type":1, "sub_type":11, "sub_instanceid":1, "From":1, "count":10, "isImage":0}
        get_top_rsp = {"Type":1, "sub_type":11, "sub_instanceid":1, "ranks":[(openids[i], scores[i], 3 - i) for i in range(len(openids))], "total":len(openids)}
        check_top_rank_simple(self, 20, get_top_req, get_top_rsp)
        # openids = [10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
        # scores = [0, 0, 0, 0, 12, 25, 38, 51, 64, 77]
        openids = [2, 1]
        scores = [64, 77]
        get_top_req = {"Type":1, "sub_type":12, "sub_instanceid":1, "From":1, "count":10, "isImage":0}
        get_top_rsp = {"Type":1, "sub_type":12, "sub_instanceid":1, "ranks":[(openids[i], scores[i], 2 - i) for i in range(len(openids))], "total":len(openids)}
        check_top_rank_simple(self, 21, get_top_req, get_top_rsp)





    
auto_perf = sys.path.append(r"/data/landun/workspace/AutoPerf")
sys.path.append(auto_perf)
from AutoPerf.htmlrunner import HTMLTestRunner
if __name__ == "__main__":
    
    # 测试用例存放路径
    case_path = os.path.abspath(os.path.join(os.path.dirname(os.path.realpath(__file__))))
    report_path = os.path.abspath(os.path.join(case_path, "../report"))

    if not os.path.isdir(report_path):
        os.system("mkdir -vp {0}".format(report_path))

    # 生成报告存放路径
    tTime = time.strftime('%Y-%m-%d-%H:%M:%S', time.localtime(time.time()))
    report_file = os.path.join(
        report_path,
        "apollo_topnext_pebble_test_report_" + tTime + ".html"
    )
    
    suite = unittest.TestSuite()
    tests = unittest.defaultTestLoader.discover("./", "test_report.py")
    print(type(tests))

    suite.addTest(tests)
    with open(report_file, 'w') as f:
        runner = HTMLTestRunner(stream=f, title="topnext_pebble")
        runner.run(suite)
