
# coding:utf-8
import sys
import os
case_path = os.path.abspath(os.path.join(os.path.dirname(os.path.realpath(__file__))))
sys.path.append(r"/data/landun/workspace/AutoPerf")
from AutoPerf.local_command_op import *

import unittest
import time
import requests
import json
from comm_def import *
from apollo_dir_class import *
from topnext_class import *
from proxy_class import *
from client_class import *
from apollo_agent_class import *
from tlog_class import *
from utils import *

from datetime import datetime
import random
import string

class testPebbleTopnextReduce(unittest.TestCase):

    @classmethod
    def setUpClass(cls):
        print_current_time("testPebbleTopnextReduce setUpClass", "*")
        start_gcloud_env()
        time.sleep(10)
        
    @classmethod
    def tearDownClass(cls): 
        print_current_time("testPebbleTopnextReduce tearDownClass", "*")
        stop_gcloud_env()

    def setUp(self):
        print_current_time("testPebbleTopnextReduce setUp " + self._testMethodName, "*")
        self.core_files = get_core_files()
        self.procs = []
        self.GlobalImageIndex = random.randint(0, 8)
        print_current_time("GlobalImageIndex: {0}".format(self.GlobalImageIndex))

    def doCleanups(self):
        print_current_time("testPebbleTopnextReduce doCleanups", "*")
        stop_env(self.procs)
        hwclock_to_sys()

    def tearDown(self):
        print_current_time("testPebbleTopnextReduce tearDown " + self._testMethodName, "*")
        stop_env(self.procs, True)
        core_files2 = get_core_files()
        set1 = set(self.core_files)
        set2 = set(core_files2)
        set3 = set2 - set1
        self.assertTrue(len(set3) == 0, "core dump:{0}".format(set3))
        
    def client(self, idx=0):
        client = TopnextClient(idx=idx)
        self.procs.append(client)
        client.set_cfgs(dir_url="tcp://{0}:{1}".format(local_ip, dir_port))
        return client
    
    def client_tbus(self, idx=0):
        client = TopnextClientTbus(idx=idx)
        self.procs.append(client)
        client.set_cfgs(dir_url="tcp://{0}:{1}".format(local_ip, dir_port))
        return client
    
    # https://tapd.woa.com/1002191/sparrow/test_plan/view/1001002191000697336?action_timestamp=12230713&cur_test_plan_id=1001002191000697336
    def test_reduce_type4_01(self):
        # 测试1点到1点15分的的上报不会衰减, 衰减(最后一次上报时间小于周一1点的会进行衰减，固定减去ext_field2的值)
        os.system('date -s "01/01/2025 00:30:00"') # 星期三
        topnexts, proxys, _ = start_env(self.procs, app_num=2, proxy_num=1, startProc=False)
        for svr in topnexts:
            svr.set_type({"use_reduce": 1, "reduce_cron": "0 0 * * * ", "reduce_func": 4, "reduce_args": "reduce_rate=10 min_rate=70 inc_rate=1"}, type_idx=0)
            svr.set_subtype( {"use_image":1, "image_cron":"0 0 * * *"}, type_idx=0, subtype_idx=0)
            svr.start()
        for proxy in proxys:
            proxy.start()
        time.sleep(1)

        # 2. report data
        openids = [10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
        scores = [100, 99, 98, 97, 96, 95, 94, 93, 92, 91]
        ExtField2s = [20, 18, 16, 14, 12, 10, 8, 6, 4, 2]
        multi_report_req = {"WorldId":1, "ZoneId":1, "Type":1, "InstanceId":1, "sub_types":[11, 12], "sub_instances":[1, 1], "openids":openids, "scores":scores, "ExtField2s":ExtField2s}
        client = self.client(0)
        send_multi_report_req(client, multi_report_req, timeout=2)

        # 第一轮衰减
        os.system('date -s "01/06/2025 01:14:59"') # 星期一
        time.sleep(5)
        openids = [10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
        scores = [80, 81, 82, 83, 84, 85, 86, 87, 88, 89]
        get_top_req = {"Type":1, "sub_type":11, "sub_instanceid":1, "From":1, "count":10, "isImage":0}
        get_top_rsp = {"Type":1, "sub_type":11, "sub_instanceid":1, "ranks":[(openids[i], scores[i], 10 - i) for i in range(len(openids))], "total":10}
        check_top_rank_simple(self, 10, get_top_req, get_top_rsp)

        # 3. report new data in 01:00
        os.system('date -s "01/13/2025 01:00:00"') # 星期一
        openids = [10]
        scores = [100]
        ExtField2s = [70]
        multi_report_req = {"WorldId":1, "ZoneId":1, "Type":1, "InstanceId":1, "sub_types":[11, 12], "sub_instances":[1, 1], "openids":openids, "scores":scores, "ExtField2s":ExtField2s}
        client = self.client(1)
        send_multi_report_req(client, multi_report_req, timeout=2)

        # 第二轮衰减
        os.system('date -s "01/13/2025 01:14:59"') # 星期一
        time.sleep(5)
        openids = [9, 8, 7, 6, 5, 4, 3, 2, 1, 10]
        scores = [63, 66, 69, 72, 75, 78, 81, 84, 87, 100]
        get_top_req = {"Type":1, "sub_type":11, "sub_instanceid":1, "From":1, "count":10, "isImage":0}
        get_top_rsp = {"Type":1, "sub_type":11, "sub_instanceid":1, "ranks":[(openids[i], scores[i], 10 - i) for i in range(len(openids))], "total":len(openids)}
        check_top_rank_simple(self, 11, get_top_req, get_top_rsp)

        # 第三轮衰减
        os.system('date -s "01/20/2025 01:14:59"') # 星期一
        time.sleep(5)
        openids = [10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
        scores = [30, 45, 50, 55, 60, 65, 70, 75, 80, 85]
        get_top_req = {"Type":1, "sub_type":11, "sub_instanceid":1, "From":1, "count":10, "isImage":0}
        get_top_rsp = {"Type":1, "sub_type":11, "sub_instanceid":1, "ranks":[(openids[i], scores[i], 10 - i) for i in range(len(openids))], "total":len(openids)}
        check_top_rank_simple(self, 12, get_top_req, get_top_rsp)

    def test_reduce_type4_02(self):
        # 异常用例：ext_field2 为负数、空、小数、整型, 衰减(最后一次上报时间小于周一1点的会进行衰减，固定减去ext_field2的值)
        # ext_field2 为负数（无符号），代码判断分数小于ext_field2，直接返回0
        os.system('date -s "01/01/2025 00:30:00"') # 星期三
        topnexts, proxys, _ = start_env(self.procs, app_num=2, proxy_num=1, startProc=False)
        for svr in topnexts:
            svr.set_type({"use_reduce": 1, "reduce_cron": "0 0 * * * ", "reduce_func": 4, "reduce_args": "reduce_rate=10 min_rate=70 inc_rate=1"}, type_idx=0)
            svr.set_subtype( {"use_image":1, "image_cron":"0 0 * * *"}, type_idx=0, subtype_idx=0)
            svr.start()
        for proxy in proxys:
            proxy.start()
        time.sleep(1)

        # 2. report data
        openids = [10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
        scores = [100, 99, 98, 97, 96, 95, 94, 93, 92, 91]
        ExtField2s = [-20, 0, "aa", 14, 12, 10, 8, 6, 4, 2]
        multi_report_req = {"WorldId":1, "ZoneId":1, "Type":1, "InstanceId":1, "sub_types":[11], "sub_instances":[1], "openids":openids, "scores":scores, "ExtField2s":ExtField2s}
        client = self.client(0)
        send_multi_report_req(client, multi_report_req, timeout=2)
        exp_tab = {"OnTopNextReportSubRankRsp succ result = 0": 10}
        ret, msg = client.check_keystrs_from_log_with_detail("topnextclient.log", exp_tab)
        self.assertTrue(ret, msg)

        # 第一轮衰减
        os.system('date -s "01/06/2025 01:14:59"') # 星期一
        time.sleep(5)
        openids = [10, 7, 6, 5, 4, 3, 2, 1, 8, 9]
        scores = [0, 83, 84, 85, 86, 87, 88, 89, 98, 99]
        get_top_req = {"Type":1, "sub_type":11, "sub_instanceid":1, "From":1, "count":10, "isImage":0}
        get_top_rsp = {"Type":1, "sub_type":11, "sub_instanceid":1, "ranks":[(openids[i], scores[i], 10 - i) for i in range(len(openids))], "total":10}
        check_top_rank_simple(self, 10, get_top_req, get_top_rsp)
        time.sleep(5)


    
auto_perf = sys.path.append(r"/data/landun/workspace/AutoPerf")
sys.path.append(auto_perf)
from AutoPerf.htmlrunner import HTMLTestRunner
if __name__ == "__main__":
    
    # 测试用例存放路径
    case_path = os.path.abspath(os.path.join(os.path.dirname(os.path.realpath(__file__))))
    report_path = os.path.abspath(os.path.join(case_path, "../report"))

    if not os.path.isdir(report_path):
        os.system("mkdir -vp {0}".format(report_path))

    # 生成报告存放路径
    tTime = time.strftime('%Y-%m-%d-%H:%M:%S', time.localtime(time.time()))
    report_file = os.path.join(
        report_path,
        "apollo_topnext_pebble_test_report_" + tTime + ".html"
    )
    
    suite = unittest.TestSuite()
    tests = unittest.defaultTestLoader.discover("./", "test_reduce.py")
    print(type(tests))

    suite.addTest(tests)
    with open(report_file, 'w') as f:
        runner = HTMLTestRunner(stream=f, title="topnext_pebble")
        runner.run(suite)
