#!/bin/sh
# 2.1.42 多镜像前的版本
CurPath=$(dirname $0)
cd $CurPath	
DataPath=$1
if [ ! -f $DataPath/shm*.xml ]; then
	echo "no shm file"
  exit
fi  
key_xml_file=$(ls $DataPath/shm*.xml)
echo $key_xml_file
if [[ "a"$key_xml_file != "a" ]]
then
	# xml文件多了image_index后， cut -d '=' -f 6 --> cut -d '=' -f 7
	shm_keys=$(cat $key_xml_file | grep ShmKey | cut -d '=' -f 6 | cut -d '"' -f 2 | tr "\n" " ")	
	for k in $shm_keys
	do
		echo "ShmKey=$k"
		ipcrm -M $k
	done
else 
    echo "cannot find xml file"
fi
