<?xml version="1.0" encoding="GBK" standalone="yes" ?>

<!--TDirMsg: Protocol between Dir and client-->
<TDirMsg __version="4">

    <Head>

        <!--Magic: Magic number-->
        <Magic>0x139c </Magic>

        <!--HeadLen: Header length-->
        <HeadLen>0 </HeadLen>

        <!--BodyLen: Data length-->
        <BodyLen>0 </BodyLen>

        <!--Version: Protocol version-->
        <Version>0 </Version>

        <!--Command: Packet type-->
        <Command>GET_CONFIG_RESP </Command>

        <!--AsyncId: Async request transaction ID, carries data in response-->
        <AsyncId>0 </AsyncId>

        <!--AsyncLen: Async callback data size-->
        <AsyncLen>0 </AsyncLen>

    </Head>


    <!--'Body' defines some options-->
    <!--'Command' decides which is used-->
    <Body>

        <!--'Command' : 2 [GET_CONFIG_RESP]-->
        <!--TDirGetConfigResp: -->
        <TDirGetConfigResp>

            <!--PolicyInfo: Policy related data-->
            <PolicyInfo>

                <!--GetConfigInterval: Interval for API to pull config from Dir, in seconds-->
                <GetConfigInterval>30 </GetConfigInterval>

                <!--CallStatisticInterval: Call statistics collection interval-->
                <CallStatisticInterval>30 </CallStatisticInterval>

                <!--AddrFailReqCountThreshold: Threshold for address failure call count-->
                <AddrFailReqCountThreshold>30 </AddrFailReqCountThreshold>

                <!--AddrFailRateThreshold: Threshold for address failure rate-->
                <AddrFailRateThreshold>500 </AddrFailRateThreshold>

                <!--ReportInterval: Reporting interval when service needs data reporting, in seconds-->
                <ReportInterval>60 </ReportInterval>

            </PolicyInfo>

            <!--BussinessServiceConfigCount: Number of returned configurations-->
            <BussinessServiceConfigCount>1 </BussinessServiceConfigCount>

            <!--BussinessServiceConfig: List of returned business service configurations-->
            <!--'BussinessServiceConfig' is an array of 128 elements-->
            <!--'BussinessServiceConfigCount' decides how many elements is used-->
            <BussinessServiceConfig>

                <!--BussinessService: Business ID and service ID-->
                <BussinessService>

                    <!--BussinessId: Business ID-->
                    <BussinessId>1001 </BussinessId>

                    <!--ServiceId: Service ID-->
                    <ServiceId>1 </ServiceId>

                </BussinessService>

                <!--ServiceAddrList: Service address list-->
                <ServiceAddrList>

                    <!--AddrCount: Address count-->
                    <AddrCount>1 </AddrCount>
					<AddrList>
						<Url>*******</Url>
					</AddrList>

                </ServiceAddrList>

                <!--ReportAddrList: Reporting address list-->
                <ReportAddrList>

                    <!--AddrCount: Address count-->
                    <AddrCount>1 </AddrCount>
					<AddrList>
						<Url>*******</Url>
					</AddrList>

                </ReportAddrList>

                <GdataReportAddrList>
                    <AddrCount>0 </AddrCount>
                    <AddrList>
                        <Url>udp://127.0.0.1:8888</Url>
                    </AddrList>
                </GdataReportAddrList>

            </BussinessServiceConfig>

        </TDirGetConfigResp>

        <!--'Command' : 4 [GET_CONFIG_BY_BUSSINESS_RESP]-->
        <!--TDirGetConfigByBussinessResp: -->
        <TDirGetConfigByBussinessResp>

            <!--PolicyInfo: Policy related data-->
            <PolicyInfo>

                <!--GetConfigInterval: Interval for API to pull config from Dir, in seconds-->
                <GetConfigInterval>30 </GetConfigInterval>

                <!--CallStatisticInterval: Call statistics collection interval-->
                <CallStatisticInterval>30 </CallStatisticInterval>

                <!--AddrFailReqCountThreshold: Threshold for address failure call count-->
                <AddrFailReqCountThreshold>30 </AddrFailReqCountThreshold>

                <!--AddrFailRateThreshold: Threshold for address failure rate-->
                <AddrFailRateThreshold>500 </AddrFailRateThreshold>

                <!--ReportInterval: Reporting interval when service needs data reporting, in seconds-->
                <ReportInterval>60 </ReportInterval>

            </PolicyInfo>

            <!--ServiceInfoList: Service related information-->
            <ServiceInfoList>
                <!--'ServiceInfo' is an array of 64 elements-->
                <ServiceInfo>

                    <!--ServiceId: -->
                    <ServiceId>1 </ServiceId>

                    <!--ServiceName: -->
                    <ServiceName>apollo.aps</ServiceName>

                    <!--ProtocolInfo: -->
                    <ProtocolInfo>

                        <!--MagicValue: -->
                        <MagicValue>0 </MagicValue>

                    </ProtocolInfo>

                </ServiceInfo>

            </ServiceInfoList>

            <!--BussinessServiceConfigCount: Number of returned configurations-->
            <BussinessServiceConfigCount>1 </BussinessServiceConfigCount>

            <!--BussinessServiceConfig: List of returned business service configurations-->
            <!--'BussinessServiceConfig' is an array of 128 elements-->
            <!--'BussinessServiceConfigCount' decides how many elements is used-->
            <BussinessServiceConfig>

                <!--BussinessService: Business ID and service ID-->
                <BussinessService>

                    <!--BussinessId: Business ID-->
                    <BussinessId>1001 </BussinessId>

                    <!--ServiceId: Service ID-->
                    <ServiceId>1 </ServiceId>

                </BussinessService>

                <!--ServiceAddrList: Service address list-->
                <ServiceAddrList>

                    <!--AddrCount: Address count-->
                    <AddrCount>1 </AddrCount>
					<AddrList>
						<Url>*******</Url>
					</AddrList>

                </ServiceAddrList>

                <!--ReportAddrList: Reporting address list-->
                <ReportAddrList>

                    <!--AddrCount: Address count-->
                    <AddrCount>1 </AddrCount>
					<AddrList>
						<Url>*******</Url>
					</AddrList>

                </ReportAddrList>

                <GdataReportAddrList>
                    <AddrCount>0 </AddrCount>
                    <AddrList>
                        <Url>udp://127.0.0.1:8888</Url>
                    </AddrList>
                </GdataReportAddrList>


            </BussinessServiceConfig>

        </TDirGetConfigByBussinessResp>

        <!--'Command' : 6 [GET_MULTI_CONFIG_RESP]-->
        <TDirGetMultiConfigResp>

            <!--PolicyInfo: Policy related data-->
            <PolicyInfo>

                <!--GetConfigInterval: Interval for API to pull config from Dir, in seconds-->
                <GetConfigInterval>30 </GetConfigInterval>

                <!--CallStatisticInterval: Call statistics collection interval-->
                <CallStatisticInterval>30 </CallStatisticInterval>

                <!--AddrFailReqCountThreshold: Threshold for address failure call count-->
                <AddrFailReqCountThreshold>30 </AddrFailReqCountThreshold>

                <!--AddrFailRateThreshold: Threshold for address failure rate-->
                <AddrFailRateThreshold>500 </AddrFailRateThreshold>

                <!--ReportInterval: Reporting interval when service needs data reporting, in seconds-->
                <ReportInterval>60 </ReportInterval>

            </PolicyInfo>

            <!--ServiceInfoList: Service related information-->
            <ServiceInfoList>
                <!--'ServiceInfo' is an array of 64 elements-->
                <ServiceInfo>

                    <!--ServiceId: -->
                    <ServiceId>1 </ServiceId>

                    <!--ServiceName: -->
                    <ServiceName>apollo.sns</ServiceName>

                    <!--ProtocolInfo: -->
                    <ProtocolInfo>

                        <!--MagicValue: -->
                        <MagicValue>0 </MagicValue>

                    </ProtocolInfo>

                </ServiceInfo>

            </ServiceInfoList>

            <!--BussinessServiceConfigCount: Number of returned configurations-->
            <BussinessServiceConfigCount>1 </BussinessServiceConfigCount>

            <!--BussinessServiceConfig: List of returned business service configurations-->
            <!--'BussinessServiceConfig' is an array of 128 elements-->
            <!--'BussinessServiceConfigCount' decides how many elements is used-->
            <BussinessServiceConfig>

                <!--BussinessService: Business ID and service ID-->
                <BussinessService>

                    <!--BussinessId: Business ID-->
                    <BussinessId>1001 </BussinessId>

                    <!--ServiceId: Service ID-->
                    <ServiceId>1 </ServiceId>

                </BussinessService>

                <!--ServiceAddrList: Service address list-->
                <ServiceAddrList>

                    <!--AddrCount: Address count-->
                    <AddrCount>1 </AddrCount>
					<AddrList>
						<Url>*******</Url>
					</AddrList>

                </ServiceAddrList>

                <!--ReportAddrList: Reporting address list-->
                <ReportAddrList>

                    <!--AddrCount: Address count-->
                    <AddrCount>1 </AddrCount>
					<AddrList>
						<Url>*******</Url>
					</AddrList>

                </ReportAddrList>

                <GdataReportAddrList>
                    <AddrCount>0 </AddrCount>
                    <AddrList>
                        <Url>udp://127.0.0.1:8888</Url>
                    </AddrList>
                </GdataReportAddrList>


            </BussinessServiceConfig>

        </TDirGetMultiConfigResp>

        <!--'Command' : 255 [ERROR_RESP]-->
        <!--TDirErrorResp: -->
        <TDirErrorResp>

            <!--ReqCmd: Request command-->
            <ReqCmd>INVALID </ReqCmd>

            <!--ErrorCode: Error code-->
            <ErrorCode>0 </ErrorCode>

            <!--ErrorMsg: Error description-->
            <ErrorMsg></ErrorMsg>

        </TDirErrorResp>
    </Body>

</TDirMsg>
