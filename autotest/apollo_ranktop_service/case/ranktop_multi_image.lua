#!/usr/local/bin/lua

--[[
	@Date:				2024-09-01
	@Desc:				ranktopn's auto test with tsf4gtest framework: clear 
	@Author:			chufudeng
	@Version:			1.0
	@ranktopn's version:	2.1.37
--]] require("tsf4gtest")
require("ranktop_def")
super = RanktopDef

StartBaseWithTopn = function(TopnNum)
    TopnNum = TopnNum or 2

    super.Common.StartDirstub()

    if TopnNum <= 1 then
        super.Common.StartProxy()
    else
        super.Common.StartProxyWith2Topn()
    end

    super.Common.StartRanktop(1, 1)
    if TopnNum > 1 then
        super.Common.StartRanktop(1, 2)
    end
end

-- 1000
-- 33d6548e48d4318ceb0e3916a79afc84
-- 2000
-- 831bb3dd5d09fb053fc65257e81b28f8

TestMultiImageInit = function(isWithRank, ExtraImageCount)
    isWithRank = isWithRank or 1
    ExtraImageCount = ExtraImageCount or 4
    
    if isWithRank == 1 then
        tsf4gtest.Shell.exec("cp "..AUTORUNHOME.."/ranktop/data.bak/* "..AUTORUNHOME.."/ranktop/data")
    else 
        tsf4gtest.Shell.exec("rm -rf "..AUTORUNHOME.."/ranktop/data")
    end
    tsf4gtest.Xml.load(super.Env.TRANKTOPCFG)
    tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.UseImageData", "1", "list", {super.Env.SCOREIDX+1})
    tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.GenerateType", "1", "list", {super.Env.SCOREIDX+1})
    tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.GenerateTimeOfMonth", "0", "list", {super.Env.SCOREIDX+1})
    tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.BeginTime", os.date("%Y-%m-%d %H:%M:%S", os.time()-1), "list", {super.Env.SCOREIDX+1})
    tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", ExtraImageCount, "list", {super.Env.SCOREIDX+1})
    tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.UseImageData", "1", "list", {4})
    tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.GenerateType", "1", "list", {4})
    tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.GenerateTimeOfMonth", "0", "list", {4})
    tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.BeginTime", os.date("%Y-%m-%d %H:%M:%S", os.time()-1), "list", {4})
    tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", ExtraImageCount, "list", {4})

    if isWithRank == 1 then
        tsf4gtest.Shell.exec("cp "..AUTORUNHOME.."/ranktop/data.bak/* "..AUTORUNHOME.."/ranktopA/data/")
    else
        -- 这里有问题，在teeardown 恢复
        tsf4gtest.Shell.exec("rm -rf "..AUTORUNHOME.."/ranktopA/data/*")
    end
    tsf4gtest.Xml.load(AUTORUNHOME.."/ranktopA/cfg/topn_app.xml")
    tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.UseImageData", "1", "list", {super.Env.SCOREIDX+1})
    tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.GenerateType", "1", "list", {super.Env.SCOREIDX+1})
    tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.GenerateTimeOfMonth", "0", "list", {super.Env.SCOREIDX+1})
    tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.BeginTime", os.date("%Y-%m-%d %H:%M:%S", os.time()-1), "list", {super.Env.SCOREIDX+1})
    tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", ExtraImageCount, "list", {super.Env.SCOREIDX+1})
    tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.UseImageData", "1", "list", {4})
    tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.GenerateType", "1", "list", {4})
    tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.GenerateTimeOfMonth", "0", "list", {4})
    tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.BeginTime", os.date("%Y-%m-%d %H:%M:%S", os.time()-1), "list", {4})
    tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", ExtraImageCount, "list", {4})
end

SendApiWithImageIndex = function(isImage, imageIndex, GetImageStatusRsp_seq) 

    -- 手动生成镜像
    tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"26", "TestGenerateImageReq.zoneid":"1", "TestGenerateImageReq.score_type":"1", "TestGenerateImageReq.image_sequence":%d, "TestGenerateImageReq.imageIndex":%d}]], GetImageStatusRsp_seq, imageIndex))
    super.Common.StartClient(2)
    tsf4gtest.Util.wait(5)
    -- tsf4gtest.Assert.EXPECT_LOG(AUTORUNHOME.."/client/log/rankclient.log", "GenerateImageRsp.GetResultInfo ret_=0, resultCode=0, errMsg_=,", 1)

    -- 获取镜像状态
    tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"27", "TestGetImageStatusReq.zoneid":"1" "TestGetImageStatusReq.score_type":"1", "TestGetImageStatusReq.imageIndex":%d}]], imageIndex))
    super.Common.StartClient(2)
    tsf4gtest.Util.wait(2)
    -- tsf4gtest.Assert.EXPECT_LOG(AUTORUNHOME.."/client/log/rankclient.log", string.format("GetImageStatusRsp.GetData suc. zoneid=1, score_type=1, sequence=%d, status=0, CurrentImageEnabled=open", GetImageStatusRsp_seq), 1)

    -- 1. RankGetTopRankReq     GetTopN
    tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"1", "TestGetTopRankReq.ScoreType":"1", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":%d, "TestGetTopRankReq.imageIndex":%d}]], isImage, imageIndex))
    super.Common.StartClient(2)
    tsf4gtest.Util.wait(2)
    -- super.Common.CheckBasicInfo(1, 1, 5, 5)

    -- 2. RankGetTopByUserReq     GetSpecialUserTopRank
    tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"4", "TestGetSpecialUserTopRankReq.ZoneId":"1", "TestGetSpecialUserTopRankReq.ScoreType":"1", "TestRankTopSpecialUser.OpenIdCount":"1", "TestRankTopSpecialUser.OpenIds":"1", "TestGetSpecialUserTopRankReq.UpCount":"1", "TestGetSpecialUserTopRankReq.DownCount":"1", "TestGetSpecialUserTopRankReq.DataSource":%d, "TestGetSpecialUserTopRankReq.imageIndex":%d}]], isImage, imageIndex))
    super.Common.StartClient(2)
    tsf4gtest.Util.wait(2)
    -- super.Common.CheckTopnByUserLog(1, 1, 5, 1)

    -- 3. RankGetTopByOneUserReq     GetSpecialOneUserTopRank
    tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"10", "TestGetSpecialOneUserTopRankReq.ZoneId":"1", "TestGetSpecialOneUserTopRankReq.ScoreType":"1", "TestGetSpecialOneUserTopRankReq.OpenId":"1", "TestGetSpecialOneUserTopRankReq.UpCount":"1", "TestGetSpecialOneUserTopRankReq.DownCount":"2", "TestGetSpecialOneUserTopRankReq.DataSource":%d, "TestGetSpecialOneUserTopRankReq.imageIndex":%d}]], isImage, imageIndex))
    super.Common.StartClient(2)
    tsf4gtest.Util.wait(2)
    -- super.Common.CheckTopnByOneUserLog(1, 1, 5, 1, 3)

    -- 4. RankGetTopRankByRankReq     GetSpecialRankTopRank
    tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"30", "TestGetSpecialRankTopRankReq.ZoneId":"1", "TestGetSpecialRankTopRankReq.ScoreType":"1", "TestGetSpecialRankTopRankReq.QueryCount":"1", "TestGetSpecialRankTopRankReq.QueryRank":"1", "TestGetSpecialRankTopRankReq.DataSource":%d, "TestGetSpecialRankTopRankReq.imageIndex":%d}]], isImage, imageIndex))
    super.Common.StartClient(2)
    tsf4gtest.Util.wait(2)
    -- super.Common.CheckTopnByRankLog(1, 1, 5, 1)
    
    -- 5. RankGetTopByScoreReq     GetSpecialScoreTopRank
    tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"11", "TestGetTopByScoreReq.ZoneId":"1", "TestGetTopByScoreReq.ScoreType":"1", "TestGetTopByScoreReq.UpCount":"1", "TestGetTopByScoreReq.DownCount":"2", "TestGetTopByScoreReq.TestSpecailScore.score_count":"1", "TestGetTopByScoreReq.TestSpecailScore.score":"10", "TestGetTopByScoreReq.DataSource":%d, "TestGetTopByScoreReq.imageIndex":%d}]], isImage, imageIndex))
    super.Common.StartClient(2)
    tsf4gtest.Util.wait(super.Env.WAITTIME)
    -- super.Common.CheckTopnByScoreLog(1, 1, 5, 1)
    -- super.Common.CheckClientByScoresResult({10}, {{1, 2}}, {{1, 2}}, {{10,9}})

    -- 6. TopRankLastUpdateTimeReq     RankLastUpdateTime
    tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"29", "TestTopRankLastUpdateTimeReq.ZoneId":"1", "TestTopRankLastUpdateTimeReq.ScoreType":"1", "TestTopRankLastUpdateTimeReq.DataSource":%d, "TestTopRankLastUpdateTimeReq.imageIndex":%d}]], isImage, imageIndex))
    super.Common.StartClient(2)
    tsf4gtest.Util.wait(2)
    -- super.Common.CheckTopRankLastUpdateTime(1, 1, 5, isImage)

end

-- 其他接口，在其他文件中定义，并随机imageIndex

test_multi_image = {
    Name = "test_multi_image",
    SetUp = function()
        -- 替换配置文件
        super.Suite.SetUp()
    end,

    TearDown = function()
        -- 需要把image_index还原成10086
        -- 按道理回自动还原才对
        -- tsf4gtest.Xml.load(super.Env.CLTCFG)
        -- tsf4gtest.Xml.set("TestGenerateImageReq.imageIndex", 10086)
        -- tsf4gtest.Xml.set("TestGetImageStatusReq.imageIndex", 10086)
        -- tsf4gtest.Xml.set("TestGetTopRankReq.imageIndex", 10086)
        -- tsf4gtest.Xml.set("TestGetSpecialUserTopRankReq.imageIndex", 10086)
        -- tsf4gtest.Xml.set("TestGetSpecialOneUserTopRankReq.imageIndex", 10086)
        -- tsf4gtest.Xml.set("TestGetSpecialRankTopRankReq.imageIndex", 10086)
        -- tsf4gtest.Xml.set("TestGetTopByScoreReq.imageIndex", 10086)
        -- tsf4gtest.Xml.set("TestTopRankLastUpdateTimeReq.imageIndex", 10086)

        -- 有些地方会删除了/ranktopA/data/目录，所以需要还原
        tsf4gtest.Shell.exec("cp "..AUTORUNHOME.."/ranktop/data.bak/* "..AUTORUNHOME.."/ranktopA/data/")
    end,

    -- test_base_01 = {
    --     Desc = "基础用例，查看实时数据是否存在，分开topN",
    --     Ver = "2.1.42",
    --     Run = function()
    --         TestMultiImageInit()
    --         StartBaseWithTopn()
    --         tsf4gtest.Util.wait(2)

    --         --从topn1查询镜像数据,ZoneId-1,ScoreType-1
    --         tsf4gtest.Xml.load(AUTORUNHOME.."/proxy/cfg/rankproxy_conf.xml")
    --         tsf4gtest.Xml.set("BusinessArray.ProxyBusiness.TrankTopNCfg.ZoneInfo.RankSvrInfo.QueryPermission", "disable", "list", {4,1,1,5,2})
    --         super.Common.StopProxy()
    --         tsf4gtest.Util.wait(1)
    --         super.Common.StartProxy()
    --         tsf4gtest.Util.wait(1)
	-- 		tsf4gtest.Xml.batchset(super.Env.CLTCFG, [[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"1", "TestGetTopRankReq.ScoreType":"1", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":"0"}]])
	-- 		super.Common.StopClient()
	-- 		super.Common.StartClient()
    --         tsf4gtest.Util.wait(1)
	-- 		super.Common.CheckClientResult(1, 1, {1,2,3,4,5}, {10,9,8,7,6})

    --         --从topn2查询实时数据
    --         tsf4gtest.Xml.load(AUTORUNHOME.."/proxy/cfg/rankproxy_conf.xml")
    --         tsf4gtest.Xml.set("BusinessArray.ProxyBusiness.TrankTopNCfg.ZoneInfo.RankSvrInfo.QueryPermission", "enable", "list", {4,1,1,5,2})
    --         tsf4gtest.Xml.set("BusinessArray.ProxyBusiness.TrankTopNCfg.ZoneInfo.RankSvrInfo.QueryPermission", "disable", "list", {4,1,1,4,2})
    --         super.Common.StopProxy()
    --         tsf4gtest.Util.wait(1)
    --         super.Common.StartProxy()
    --         tsf4gtest.Util.wait(1)
	-- 		tsf4gtest.Xml.batchset(super.Env.CLTCFG, [[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"2", "TestGetTopRankReq.ScoreType":"2", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":"0"}]])
	-- 		super.Common.StopClient()
    --         tsf4gtest.Shell.exec(">"..AUTORUNHOME.."/client/log/rankclient.log")
	-- 		super.Common.StartClient()
    --         tsf4gtest.Util.wait(1)
	-- 		super.Common.CheckClientResult(1, 1, {11,12,13,14,15}, {20,19,18,17,16})
    --     end
    -- },

    test_base_02 = {
        Desc = "各个镜像查询相关接口用例，查询实时镜像数据 -- UseImageData=1, isImage=0",
        Ver = "2.1.42",
        Run = function()
            TestMultiImageInit()
            StartBaseWithTopn()
            tsf4gtest.Util.wait(2)

            local isImage = 0

            -- 1. RankGetTopRankReq     GetTopN
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"1", "TestGetTopRankReq.ScoreType":"1", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":%d, "TestGetTopRankReq.imageIndex":%d}]], isImage, GlobalImageIndex))
			super.Common.StartClient()
            tsf4gtest.Util.wait(2)
            super.Common.CheckBasicInfo(1, 1, 5, 5)

            -- 2. RankGetTopByUserReq     GetSpecialUserTopRank
			tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"4", "TestGetSpecialUserTopRankReq.ZoneId":"1", "TestGetSpecialUserTopRankReq.ScoreType":"1", "TestRankTopSpecialUser.OpenIdCount":"1", "TestRankTopSpecialUser.OpenIds":"1", "TestGetSpecialUserTopRankReq.UpCount":"1", "TestGetSpecialUserTopRankReq.DownCount":"1", "TestGetSpecialUserTopRankReq.DataSource":%d, "TestGetSpecialUserTopRankReq.imageIndex":%d}]], isImage, GlobalImageIndex))
            super.Common.StartClient(2)
            tsf4gtest.Util.wait(2)
            super.Common.CheckTopnByUserLog(1, 1, 5, 1)

            -- 3. RankGetTopByOneUserReq     GetSpecialOneUserTopRank
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"10", "TestGetSpecialOneUserTopRankReq.ZoneId":"1", "TestGetSpecialOneUserTopRankReq.ScoreType":"1", "TestGetSpecialOneUserTopRankReq.OpenId":"1", "TestGetSpecialOneUserTopRankReq.UpCount":"1", "TestGetSpecialOneUserTopRankReq.DownCount":"2", "TestGetSpecialOneUserTopRankReq.DataSource":%d, "TestGetSpecialOneUserTopRankReq.imageIndex":%d}]], isImage, GlobalImageIndex))
			super.Common.StartClient(2)
			tsf4gtest.Util.wait(2)
			super.Common.CheckTopnByOneUserLog(1, 1, 5, 1, 3)

            -- 4. RankGetTopRankByRankReq     GetSpecialRankTopRank
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"30", "TestGetSpecialRankTopRankReq.ZoneId":"1", "TestGetSpecialRankTopRankReq.ScoreType":"1", "TestGetSpecialRankTopRankReq.QueryCount":"1", "TestGetSpecialRankTopRankReq.QueryRank":"1", "TestGetSpecialRankTopRankReq.DataSource":%d, "TestGetSpecialRankTopRankReq.imageIndex":%d}]], isImage, GlobalImageIndex))
            super.Common.StartClient(2)
			tsf4gtest.Util.wait(2)
            super.Common.CheckTopnByRankLog(1, 1, 5, 1)
            
            -- 5. RankGetTopByScoreReq     GetSpecialScoreTopRank
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"11", "TestGetTopByScoreReq.ZoneId":"1", "TestGetTopByScoreReq.ScoreType":"1", "TestGetTopByScoreReq.UpCount":"1", "TestGetTopByScoreReq.DownCount":"2", "TestGetTopByScoreReq.TestSpecailScore.score_count":"1", "TestGetTopByScoreReq.TestSpecailScore.score":"10", "TestGetTopByScoreReq.DataSource":%d, "TestGetTopByScoreReq.imageIndex":%d}]], isImage, GlobalImageIndex))
			super.Common.StartClient(2)
			tsf4gtest.Util.wait(super.Env.WAITTIME)
			super.Common.CheckTopnByScoreLog(1, 1, 5, 1)
			super.Common.CheckClientByScoresResult({10}, {{1, 2}}, {{1, 2}}, {{10,9}})

            -- 6. TopRankLastUpdateTimeReq     RankLastUpdateTime
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"29", "TestTopRankLastUpdateTimeReq.ZoneId":"1", "TestTopRankLastUpdateTimeReq.ScoreType":"1", "TestTopRankLastUpdateTimeReq.DataSource":%d, "TestTopRankLastUpdateTimeReq.imageIndex":%d}]], isImage, GlobalImageIndex))
            super.Common.StartClient(2)
            tsf4gtest.Util.wait(2)
            super.Common.CheckTopRankLastUpdateTime(1, 1, 5, isImage)

        end
    },

    -- test_cfg_01 = {
    --     Desc = "配置reload: UseImageData",
    --     Ver = "2.1.42",
    --     TearDown = function()
    --         super.Case.TearDown()
    --         -- 删除了/ranktopA/data/目录，所以需要还原
    --         tsf4gtest.Shell.exec("cp "..AUTORUNHOME.."/ranktop/data.bak/* "..AUTORUNHOME.."/ranktopA/data/")
    --     end,
    --     Run = function()
    --         TestMultiImageInit(2, 4)
    --         tsf4gtest.Xml.load(super.Env.TRANKTOPCFG)
    --         tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.UseImageData", "1", "list", {super.Env.SCOREIDX+1})
    --         tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.UseImageData", "0", "list", {4})

    --         StartBaseWithTopn(1)
    --         tsf4gtest.Util.wait(2)

    --         -- upload 数据
    --         tsf4gtest.Xml.batchset(super.Env.CLTCFG, [[{"Cmd":"1", "TestUploadScore.ScoreValue":"100", "TestUploadScoreReq.TestRankUser.OpenId":"100", "UploadScoreCount":"10", "ScoreIncrement":"1"}]])
	-- 		super.Common.StartClient()
	-- 		tsf4gtest.Util.wait(3)
    --         tsf4gtest.Xml.batchset(super.Env.CLTCFG, [[{"Cmd":"1", "TestUploadScore.ScoreValue":"100", "TestUploadScoreReq.TestRankUser.OpenId":"100", "UploadScoreCount":"10", "ScoreIncrement":"1", "ZoneId":"2", "TestUploadScore.ScoreType":"2"}]])
	-- 		super.Common.StartClient(2)
	-- 		tsf4gtest.Util.wait(3)

    --         --  手动生成镜像
    --         tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"26", "TestGenerateImageReq.zoneid":"1", "TestGenerateImageReq.score_type":"1", "TestGenerateImageReq.image_sequence":%d, "TestGenerateImageReq.imageIndex":%d}]], 10000, GlobalImageIndex))
    --         super.Common.StartClient(2)
    --         tsf4gtest.Util.wait(3)
    --         tsf4gtest.Assert.EXPECT_LOG(AUTORUNHOME.."/client/log/rankclient.log", "GenerateImageRsp.GetResultInfo ret_=0, resultCode=0, errMsg_=,", 1)

    --         -- 1. RankGetTopRankReq     GetTopN
    --         tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"1", "TestGetTopRankReq.ScoreType":"1", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":%d, "TestGetTopRankReq.imageIndex":%d}]], 1, GlobalImageIndex))
	-- 		super.Common.StartClient(2)
    --         tsf4gtest.Util.wait(2)
    --         super.Common.CheckBasicInfo(1, 1, 10, 10)

    --         -- reload UseImageData
    --         tsf4gtest.Xml.load(super.Env.TRANKTOPCFG)
    --         tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.UseImageData", "0", "list", {super.Env.SCOREIDX+1})
    --         tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.UseImageData", "1", "list", {4})
    --         super.Common.StartRanktop(2, 1)
    --         tsf4gtest.Util.wait(2)

    --         -- 自动生成镜像
    --         super.Common.SetSystemTime("+1 days")
    --         tsf4gtest.Util.wait(6)

    --         -- 1. RankGetTopRankReq     GetTopN
    --         tsf4gtest.Shell.exec("> ".. (super.Env.CLTLOG))
    --         tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"2", "TestGetTopRankReq.ScoreType":"2", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":%d, "TestGetTopRankReq.imageIndex":%d}]], 1, 0))
    --         super.Common.StartClient(2)
    --         tsf4gtest.Util.wait(2)
    --         super.Common.CheckBasicInfo(2, 2, 10, 10)

    --     end
    -- },

    test_base_getApi_01 = {
        Desc = "各个镜像查询相关接口用例 -- UseImageData=1, 无镜像实例，image_index = 0~8",
        Ver = "2.1.42",
        Run = function()
            TestMultiImageInit()
            StartBaseWithTopn()
            tsf4gtest.Util.wait(2)

            local isImage = 1

            -- 1. RankGetTopRankReq     GetTopN
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"1", "TestGetTopRankReq.ScoreType":"1", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":%d, "TestGetTopRankReq.imageIndex":%d}]], isImage, GlobalImageIndex))
			super.Common.StartClient()
            tsf4gtest.Util.wait(2)
            super.Common.CheckBasicInfo(1, 1, 0, 0)

            -- 2. RankGetTopByUserReq     GetSpecialUserTopRank
			tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"4", "TestGetSpecialUserTopRankReq.ZoneId":"1", "TestGetSpecialUserTopRankReq.ScoreType":"1", "TestRankTopSpecialUser.OpenIdCount":"1", "TestRankTopSpecialUser.OpenIds":"1", "TestGetSpecialUserTopRankReq.UpCount":"1", "TestGetSpecialUserTopRankReq.DownCount":"1", "TestGetSpecialUserTopRankReq.DataSource":%d, "TestGetSpecialUserTopRankReq.imageIndex":%d}]], isImage, GlobalImageIndex))
            super.Common.StartClient(2)
            tsf4gtest.Util.wait(2)
            super.Common.CheckTopnByUserLog(1, 1, 0, 1)

            -- 3. RankGetTopByOneUserReq     GetSpecialOneUserTopRank
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"10", "TestGetSpecialOneUserTopRankReq.ZoneId":"1", "TestGetSpecialOneUserTopRankReq.ScoreType":"1", "TestGetSpecialOneUserTopRankReq.OpenId":"1", "TestGetSpecialOneUserTopRankReq.UpCount":"1", "TestGetSpecialOneUserTopRankReq.DownCount":"2", "TestGetSpecialOneUserTopRankReq.DataSource":%d, "TestGetSpecialOneUserTopRankReq.imageIndex":%d}]], isImage, GlobalImageIndex))
			super.Common.StartClient(2)
			tsf4gtest.Util.wait(2)
			super.Common.CheckTopnByOneUserLog(1, 1, 0, 1, 0)

            -- 4. RankGetTopRankByRankReq     GetSpecialRankTopRank
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"30", "TestGetSpecialRankTopRankReq.ZoneId":"1", "TestGetSpecialRankTopRankReq.ScoreType":"1", "TestGetSpecialRankTopRankReq.QueryCount":"1", "TestGetSpecialRankTopRankReq.QueryRank":"1", "TestGetSpecialRankTopRankReq.DataSource":%d, "TestGetSpecialRankTopRankReq.imageIndex":%d}]], isImage, GlobalImageIndex))
            super.Common.StartClient(2)
			tsf4gtest.Util.wait(2)
            super.Common.CheckTopnByRankLog(1, 1, 0, 0)
            
            -- 5. RankGetTopByScoreReq     GetSpecialScoreTopRank
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"11", "TestGetTopByScoreReq.ZoneId":"1", "TestGetTopByScoreReq.ScoreType":"1", "TestGetTopByScoreReq.UpCount":"1", "TestGetTopByScoreReq.DownCount":"2", "TestGetTopByScoreReq.TestSpecailScore.score_count":"1", "TestGetTopByScoreReq.TestSpecailScore.score":"10", "TestGetTopByScoreReq.DataSource":%d, "TestGetTopByScoreReq.imageIndex":%d}]], isImage, GlobalImageIndex))
			super.Common.StartClient(2)
			tsf4gtest.Util.wait(super.Env.WAITTIME)
			super.Common.CheckTopnByScoreLog(1, 1, 0, 1)
			super.Common.CheckClientByScoresResult({10}, {}, {})

            -- 6. TopRankLastUpdateTimeReq     RankLastUpdateTime
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"29", "TestTopRankLastUpdateTimeReq.ZoneId":"1", "TestTopRankLastUpdateTimeReq.ScoreType":"1", "TestTopRankLastUpdateTimeReq.DataSource":%d, "TestTopRankLastUpdateTimeReq.imageIndex":%d}]], isImage, GlobalImageIndex))
            super.Common.StartClient(2)
            tsf4gtest.Util.wait(2)
            super.Common.CheckTopRankLastUpdateTime(1, 1, 0, isImage)

        end
    },

    test_base_getApi_02 = {
        Desc = "各个镜像查询相关接口用例 -- UseImageData=1, 有镜像实例，image_index = 0~8",
        Ver = "2.1.42",
        Run = function()
            TestMultiImageInit()
            StartBaseWithTopn()
            tsf4gtest.Util.wait(2)

            local isImage = 1
            local GetImageStatusRsp_seq = 10000

            if GlobalImageIndex == 0 then
                -- 自动生成镜像
                super.Common.SetSystemTime("+1 days")
                tsf4gtest.Util.wait(6)
                GetImageStatusRsp_seq = 0
            else
                -- 手动生成镜像
                tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"26", "TestGenerateImageReq.zoneid":"1", "TestGenerateImageReq.score_type":"1", "TestGenerateImageReq.image_sequence":%d, "TestGenerateImageReq.imageIndex":%d}]], GetImageStatusRsp_seq, GlobalImageIndex))
                super.Common.StartClient()
                tsf4gtest.Util.wait(5)
                super.Common.CheckGenerateImage()
            end

			tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"27", "TestGetImageStatusReq.zoneid":"1" "TestGetImageStatusReq.score_type":"1", "TestGetImageStatusReq.imageIndex":%d}]], GlobalImageIndex))
			super.Common.StartClient(2)
            tsf4gtest.Util.wait(2)
            tsf4gtest.Assert.EXPECT_LOG(AUTORUNHOME.."/client/log/rankclient.log", string.format("GetImageStatusRsp.GetData suc. zoneid=1, score_type=1, sequence=%d, status=0, CurrentImageEnabled=open", GetImageStatusRsp_seq), 1)

            -- 1. RankGetTopRankReq     GetTopN
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"1", "TestGetTopRankReq.ScoreType":"1", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":%d, "TestGetTopRankReq.imageIndex":%d}]], isImage, GlobalImageIndex))
			super.Common.StartClient(2)
            tsf4gtest.Util.wait(2)
            super.Common.CheckBasicInfo(1, 1, 5, 5)

            -- 1.1 RankGetTopRankReq     GetTopN 不会影响其他镜像
            local invalidImageIndex = (GlobalImageIndex + 1) % 5
            if GlobalImageIndex == 10086 then
                invalidImageIndex = 1
            end
            tsf4gtest.Shell.exec("> ".. (super.Env.CLTLOG))
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"1", "TestGetTopRankReq.ScoreType":"1", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":%d, "TestGetTopRankReq.imageIndex":%d}]], isImage, invalidImageIndex))
			super.Common.StartClient(2)
            tsf4gtest.Util.wait(2)
            super.Common.CheckBasicInfo(1, 1, 0, 0)

            -- 2. RankGetTopByUserReq     GetSpecialUserTopRank
			tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"4", "TestGetSpecialUserTopRankReq.ZoneId":"1", "TestGetSpecialUserTopRankReq.ScoreType":"1", "TestRankTopSpecialUser.OpenIdCount":"1", "TestRankTopSpecialUser.OpenIds":"1", "TestGetSpecialUserTopRankReq.UpCount":"1", "TestGetSpecialUserTopRankReq.DownCount":"1", "TestGetSpecialUserTopRankReq.DataSource":%d, "TestGetSpecialUserTopRankReq.imageIndex":%d}]], isImage, GlobalImageIndex))
            super.Common.StartClient(2)
            tsf4gtest.Util.wait(2)
            super.Common.CheckTopnByUserLog(1, 1, 5, 1)

            -- 3. RankGetTopByOneUserReq     GetSpecialOneUserTopRank
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"10", "TestGetSpecialOneUserTopRankReq.ZoneId":"1", "TestGetSpecialOneUserTopRankReq.ScoreType":"1", "TestGetSpecialOneUserTopRankReq.OpenId":"1", "TestGetSpecialOneUserTopRankReq.UpCount":"1", "TestGetSpecialOneUserTopRankReq.DownCount":"2", "TestGetSpecialOneUserTopRankReq.DataSource":%d, "TestGetSpecialOneUserTopRankReq.imageIndex":%d}]], isImage, GlobalImageIndex))
			super.Common.StartClient(2)
			tsf4gtest.Util.wait(2)
			super.Common.CheckTopnByOneUserLog(1, 1, 5, 1, 3)

            -- 4. RankGetTopRankByRankReq     GetSpecialRankTopRank
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"30", "TestGetSpecialRankTopRankReq.ZoneId":"1", "TestGetSpecialRankTopRankReq.ScoreType":"1", "TestGetSpecialRankTopRankReq.QueryCount":"1", "TestGetSpecialRankTopRankReq.QueryRank":"1", "TestGetSpecialRankTopRankReq.DataSource":%d, "TestGetSpecialRankTopRankReq.imageIndex":%d}]], isImage, GlobalImageIndex))
            super.Common.StartClient(2)
			tsf4gtest.Util.wait(2)
            super.Common.CheckTopnByRankLog(1, 1, 5, 1)
            
            -- 5. RankGetTopByScoreReq     GetSpecialScoreTopRank
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"11", "TestGetTopByScoreReq.ZoneId":"1", "TestGetTopByScoreReq.ScoreType":"1", "TestGetTopByScoreReq.UpCount":"1", "TestGetTopByScoreReq.DownCount":"2", "TestGetTopByScoreReq.TestSpecailScore.score_count":"1", "TestGetTopByScoreReq.TestSpecailScore.score":"10", "TestGetTopByScoreReq.DataSource":%d, "TestGetTopByScoreReq.imageIndex":%d}]], isImage, GlobalImageIndex))
			super.Common.StartClient(2)
			tsf4gtest.Util.wait(super.Env.WAITTIME)
            super.Common.CheckTopnByScoreLog(1, 1, 5, 1)
			super.Common.CheckClientByScoresResult({10}, {{1, 2}}, {{1, 2}}, {{10,9}})

            -- 6. TopRankLastUpdateTimeReq     RankLastUpdateTime
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"29", "TestTopRankLastUpdateTimeReq.ZoneId":"1", "TestTopRankLastUpdateTimeReq.ScoreType":"1", "TestTopRankLastUpdateTimeReq.DataSource":%d, "TestTopRankLastUpdateTimeReq.imageIndex":%d}]], isImage, GlobalImageIndex))
            super.Common.StartClient(2)
            tsf4gtest.Util.wait(2)
            super.Common.CheckTopRankLastUpdateTime(1, 1, 5, isImage)

        end
    },

    test_base_getApi_03 = {
        Desc = "各个镜像查询相关接口用例 -- UseImageData=0, 有额外镜像实例，image_index = 1~4",
        Ver = "2.1.42",
        Run = function()
            TestMultiImageInit()
            tsf4gtest.Xml.load(super.Env.TRANKTOPCFG)
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.UseImageData", "0", "list", {super.Env.SCOREIDX+1})
            tsf4gtest.Xml.load(AUTORUNHOME.."/ranktopA/cfg/topn_app.xml")
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.UseImageData", "0", "list", {super.Env.SCOREIDX+1})
            local isImage = 1
            local image_index = GlobalImageIndex
            if GlobalImageIndex == 10086 or GlobalImageIndex == 0 then
                image_index = 1
            else
                image_index = GlobalImageIndex
            end
            image_index = 2

            StartBaseWithTopn()
            tsf4gtest.Util.wait(2)

            local GetImageStatusRsp_seq = 10000

            -- 手动生成镜像
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"26", "TestGenerateImageReq.zoneid":"1", "TestGenerateImageReq.score_type":"1", "TestGenerateImageReq.image_sequence":%d, "TestGenerateImageReq.imageIndex":%d}]], GetImageStatusRsp_seq, image_index))
            super.Common.StartClient()
            tsf4gtest.Util.wait(5)
            super.Common.CheckGenerateImage()
            -- get image status
			tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"27", "TestGetImageStatusReq.zoneid":"1" "TestGetImageStatusReq.score_type":"1", "TestGetImageStatusReq.imageIndex":%d}]], image_index))
			super.Common.StartClient(2)
            tsf4gtest.Util.wait(2)
            tsf4gtest.Assert.EXPECT_LOG(AUTORUNHOME.."/client/log/rankclient.log", string.format("GetImageStatusRsp.GetData suc. zoneid=1, score_type=1, sequence=%d, status=0, CurrentImageEnabled=open", GetImageStatusRsp_seq), 1)

            -- 1. RankGetTopRankReq     GetTopN
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"1", "TestGetTopRankReq.ScoreType":"1", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":%d, "TestGetTopRankReq.imageIndex":%d}]], isImage, image_index))
			super.Common.StartClient(2)
            tsf4gtest.Util.wait(2)
            super.Common.CheckBasicInfo(1, 1, 5, 5)

            -- 2. RankGetTopByUserReq     GetSpecialUserTopRank
			tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"4", "TestGetSpecialUserTopRankReq.ZoneId":"1", "TestGetSpecialUserTopRankReq.ScoreType":"1", "TestRankTopSpecialUser.OpenIdCount":"1", "TestRankTopSpecialUser.OpenIds":"1", "TestGetSpecialUserTopRankReq.UpCount":"1", "TestGetSpecialUserTopRankReq.DownCount":"1", "TestGetSpecialUserTopRankReq.DataSource":%d, "TestGetSpecialUserTopRankReq.imageIndex":%d}]], isImage, image_index))
            super.Common.StartClient(2)
            tsf4gtest.Util.wait(2)
            super.Common.CheckTopnByUserLog(1, 1, 5, 1)

            -- 3. RankGetTopByOneUserReq     GetSpecialOneUserTopRank
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"10", "TestGetSpecialOneUserTopRankReq.ZoneId":"1", "TestGetSpecialOneUserTopRankReq.ScoreType":"1", "TestGetSpecialOneUserTopRankReq.OpenId":"1", "TestGetSpecialOneUserTopRankReq.UpCount":"1", "TestGetSpecialOneUserTopRankReq.DownCount":"2", "TestGetSpecialOneUserTopRankReq.DataSource":%d, "TestGetSpecialOneUserTopRankReq.imageIndex":%d}]], isImage, image_index))
			super.Common.StartClient(2)
			tsf4gtest.Util.wait(2)
			super.Common.CheckTopnByOneUserLog(1, 1, 5, 1, 3)

            -- 4. RankGetTopRankByRankReq     GetSpecialRankTopRank
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"30", "TestGetSpecialRankTopRankReq.ZoneId":"1", "TestGetSpecialRankTopRankReq.ScoreType":"1", "TestGetSpecialRankTopRankReq.QueryCount":"1", "TestGetSpecialRankTopRankReq.QueryRank":"1", "TestGetSpecialRankTopRankReq.DataSource":%d, "TestGetSpecialRankTopRankReq.imageIndex":%d}]], isImage, image_index))
            super.Common.StartClient(2)
			tsf4gtest.Util.wait(2)
            super.Common.CheckTopnByRankLog(1, 1, 5, 1)
            
            -- 5. RankGetTopByScoreReq     GetSpecialScoreTopRank
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"11", "TestGetTopByScoreReq.ZoneId":"1", "TestGetTopByScoreReq.ScoreType":"1", "TestGetTopByScoreReq.UpCount":"1", "TestGetTopByScoreReq.DownCount":"2", "TestGetTopByScoreReq.TestSpecailScore.score_count":"1", "TestGetTopByScoreReq.TestSpecailScore.score":"10", "TestGetTopByScoreReq.DataSource":%d, "TestGetTopByScoreReq.imageIndex":%d}]], isImage, image_index))
			super.Common.StartClient(2)
			tsf4gtest.Util.wait(super.Env.WAITTIME)
            super.Common.CheckTopnByScoreLog(1, 1, 5, 1)
			super.Common.CheckClientByScoresResult({10}, {{1, 2}}, {{1, 2}}, {{10,9}})

            -- 6. TopRankLastUpdateTimeReq     RankLastUpdateTime
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"29", "TestTopRankLastUpdateTimeReq.ZoneId":"1", "TestTopRankLastUpdateTimeReq.ScoreType":"1", "TestTopRankLastUpdateTimeReq.DataSource":%d, "TestTopRankLastUpdateTimeReq.imageIndex":%d}]], isImage, image_index))
            super.Common.StartClient(2)
            tsf4gtest.Util.wait(2)
            super.Common.CheckTopRankLastUpdateTime(1, 1, 5, isImage)

            -- 7. clearimage TODO image_index = 1 有问题
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, [[{"Cmd":"20", "TestClearTopReq.iDeleteAccount":"0", "TestClearTopReq.image_data":"1", "TestClearTopReq.imageIndex": ]] .. image_index .. [[}]])
            super.Common.StartClient(2)
            tsf4gtest.Util.wait(2)
            tsf4gtest.Assert.EXPECT_LOG(super.Env.CLTLOG, "OnRspClearTopRank.GetCountInfo succ, ret_=0", 1)

        end
    },

    test_reload_and_restart_01 = {
        Desc = "支持reload，重启从共享内存加载额外镜像支持 -- UseImageData=1, 有镜像实例，image_index = 0~8",
        Ver = "2.1.42",
        Run = function()
            TestMultiImageInit()
            StartBaseWithTopn()
            tsf4gtest.Util.wait(2)

            local isImage = 1
            local GetImageStatusRsp_seq = 10000

            if GlobalImageIndex == 0 then
                -- 自动生成镜像
                super.Common.SetSystemTime("+1 days")
                tsf4gtest.Util.wait(6)
                GetImageStatusRsp_seq = 0
            else
                -- 手动生成镜像
                tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"26", "TestGenerateImageReq.zoneid":"1", "TestGenerateImageReq.score_type":"1", "TestGenerateImageReq.image_sequence":%d, "TestGenerateImageReq.imageIndex":%d}]], GetImageStatusRsp_seq, GlobalImageIndex))
                super.Common.StartClient()
                tsf4gtest.Util.wait(5)
                super.Common.CheckGenerateImage()
            end
            
            -- 判断镜像已生成
			tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"27", "TestGetImageStatusReq.zoneid":"1" "TestGetImageStatusReq.score_type":"1", "TestGetImageStatusReq.imageIndex":%d}]], GlobalImageIndex))
			super.Common.StartClient(2)
            tsf4gtest.Util.wait(2)
            tsf4gtest.Assert.EXPECT_LOG(AUTORUNHOME.."/client/log/rankclient.log", string.format("GetImageStatusRsp.GetData suc. zoneid=1, score_type=1, sequence=%d, status=0, CurrentImageEnabled=open", GetImageStatusRsp_seq), 1)

            -- reload
            super.Common.StartRanktop(2, 1)
            super.Common.StartRanktop(2, 2)
            tsf4gtest.Util.wait(5)
            tsf4gtest.Assert.EXPECT_LOG_BYSTR(1, super.Env.TRANKTOPLOG, "reload app successfully")

            -- 1. RankGetTopRankReq     GetTopN
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"1", "TestGetTopRankReq.ScoreType":"1", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":%d, "TestGetTopRankReq.imageIndex":%d}]], isImage, GlobalImageIndex))
			super.Common.StartClient(2)
            tsf4gtest.Util.wait(2)
            super.Common.CheckBasicInfo(1, 1, 5, 5)

            -- restart
            super.Common.StartRanktop(3, 1)
            super.Common.StartRanktop(3, 2)
            tsf4gtest.Util.wait(5)

            -- 1. RankGetTopRankReq     GetTopN
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"1", "TestGetTopRankReq.ScoreType":"1", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":%d, "TestGetTopRankReq.imageIndex":%d}]], isImage, GlobalImageIndex))
            super.Common.StartClient(2)
            tsf4gtest.Util.wait(2)
            super.Common.CheckBasicInfo(1, 1, 5, 5)
            

        end
    },

    -- ExtraImageCount 参数0， 2， 4， 8，支持reload?，考虑额外镜像超过或是不足的情况
    test_ExtraImageCount_01 = {
        Desc = "参数0， 2， 4， 8，支持reload?当前不支持",
        Ver = "2.1.42",
        Run = function()
            TestMultiImageInit()
            StartBaseWithTopn(1)
            tsf4gtest.Util.wait(2)

            local ExtraImageCount = getRandomNum({0, 1, 2, 3, 4}, 5)
            local validImageIndex = getRandomNum({0, 1, 2, 3, 4}, ExtraImageCount + 1)
            local invalidImageIndex = ExtraImageCount + 1

            print("ExtraImageCount: " .. ExtraImageCount .. ", validImageIndex: " .. validImageIndex .. ", invalidImageIndex: " .. invalidImageIndex)

            -- reload
            tsf4gtest.Xml.load(super.Env.TRANKTOPCFG)
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", ExtraImageCount, "list", {super.Env.SCOREIDX+1})
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", ExtraImageCount, "list", {4})
            super.Common.StartRanktop(3, 1)
            tsf4gtest.Util.wait(5)

            -- 所有相关请求都得来一遍
            SendApiWithImageIndex(0, validImageIndex, 10000)
            super.Common.CheckClientErrLogEmpty()
			tsf4gtest.Assert.ASSERT_FILE_EMPTY(super.Env.TRANKTOPELOG)
            tsf4gtest.Shell.exec("> ".. (super.Env.CLTELOG))
            tsf4gtest.Shell.exec("> ".. (super.Env.CLTLOG))
            tsf4gtest.Shell.exec("> ".. (super.Env.TRANKTOPELOG))

            SendApiWithImageIndex(1, invalidImageIndex, 10000)
            if invalidImageIndex <= 4 then
                tsf4gtest.Assert.EXPECT_LOG(super.Env.CLTELOG, "image index is illegal", 6)
                tsf4gtest.Assert.EXPECT_LOG(super.Env.CLTLOG, "GenerateImageRsp.GetData suc. zoneid=1, score_type=1, sequence=10000, status=67107, detail_count=1", 1)
                tsf4gtest.Assert.EXPECT_LOG(super.Env.CLTLOG, "GetImageStatusRsp.GetData suc. zoneid=1, score_type=1, sequence=0, status=67107, CurrentImageEnabled=unknown", 1)
            else
                tsf4gtest.Assert.EXPECT_LOG(super.Env.CLTELOG, string.format("Illegal Req uiImageIndex\\[%d\\]  may be >  max \\[4\\]", invalidImageIndex), 8)

            end

            tsf4gtest.Shell.exec("> ".. (super.Env.CLTELOG))
            tsf4gtest.Shell.exec("> ".. (super.Env.CLTLOG))
            tsf4gtest.Shell.exec("> ".. (super.Env.TRANKTOPELOG))

            ExtraImageCount = 5
            -- restart
            tsf4gtest.Xml.load(super.Env.TRANKTOPCFG)
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", ExtraImageCount, "list", {super.Env.SCOREIDX+1})
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", ExtraImageCount, "list", {4})
            super.Common.StartRanktop(3, 1)
            tsf4gtest.Util.wait(5)
            -- RemoveExtraScorePool
            tsf4gtest.Assert.EXPECT_LOG(super.Env.TRANKTOPELOG, "image index\\[5\\] bigger than max num\\[4\\] or less than min num\\[1\\]", 3)
            tsf4gtest.Assert.EXPECT_LOG(super.Env.TRANKTOPELOG, "init app failed", 1)

        end
    },

    test_ExtraImageCount_02 = {
        Desc = "重启的时候, 考虑额外镜像超过ExtraImageCount(restart不会删除), 或是小于ExtraImageCount的情况, 2 - 4 - 1 - 4",
        Ver = "2.1.42",
        Run = function()
            TestMultiImageInit()
            StartBaseWithTopn(2)
            tsf4gtest.Util.wait(2)

            local GetImageStatusRsp_seq = 10000

            -- reload
            local OriExtraImageCount = 2
            tsf4gtest.Xml.load(super.Env.TRANKTOPCFG)
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", OriExtraImageCount, "list", {super.Env.SCOREIDX+1})
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", OriExtraImageCount, "list", {4})
            tsf4gtest.Xml.load(AUTORUNHOME.."/ranktopA/cfg/topn_app.xml")
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", OriExtraImageCount, "list", {super.Env.SCOREIDX+1})
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", OriExtraImageCount, "list", {4})
            super.Common.StartRanktop(3, 1)
            super.Common.StartRanktop(3, 2)
            tsf4gtest.Util.wait(5)

            for i = 0, OriExtraImageCount do
                -- 手动生成镜像
                tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"26", "TestGenerateImageReq.zoneid":"1", "TestGenerateImageReq.score_type":"1", "TestGenerateImageReq.image_sequence":%d, "TestGenerateImageReq.imageIndex":%d}]], GetImageStatusRsp_seq, i))
                super.Common.StartClient(2)
                tsf4gtest.Util.wait(3)
                super.Common.CheckGenerateImage(i+1)
            end

            -- reload
            local BigExtraImageCount = 4
            tsf4gtest.Xml.load(super.Env.TRANKTOPCFG)
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", BigExtraImageCount, "list", {super.Env.SCOREIDX+1})
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", BigExtraImageCount, "list", {4})
            tsf4gtest.Xml.load(AUTORUNHOME.."/ranktopA/cfg/topn_app.xml")
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", BigExtraImageCount, "list", {super.Env.SCOREIDX+1})
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", BigExtraImageCount, "list", {4})
            super.Common.StartRanktop(3, 1)
            super.Common.StartRanktop(3, 2)
            tsf4gtest.Util.wait(5)

            for i = 0, BigExtraImageCount do
                tsf4gtest.Shell.exec("> ".. (super.Env.CLTLOG))
                -- RankGetTopRankReq     GetTopN
                tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"1", "TestGetTopRankReq.ScoreType":"1", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":%d, "TestGetTopRankReq.imageIndex":%d}]], 1, i))
                super.Common.StartClient(2)
                tsf4gtest.Util.wait(2)
                if i <= OriExtraImageCount then
                    super.Common.CheckBasicInfo(1, 1, 5, 5)
                else
                    super.Common.CheckBasicInfo(1, 1, 0, 0)
                end
            end

            -- reload
            local SmallExtraImageCount = 1
            tsf4gtest.Xml.load(super.Env.TRANKTOPCFG)
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", SmallExtraImageCount, "list", {super.Env.SCOREIDX+1})
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", SmallExtraImageCount, "list", {4})
            tsf4gtest.Xml.load(AUTORUNHOME.."/ranktopA/cfg/topn_app.xml")
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", SmallExtraImageCount, "list", {super.Env.SCOREIDX+1})
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", SmallExtraImageCount, "list", {4})
            super.Common.StartRanktop(3, 1)
            super.Common.StartRanktop(3, 2)
            tsf4gtest.Util.wait(5)

            for i = 0, BigExtraImageCount do
                tsf4gtest.Shell.exec("> ".. (super.Env.CLTLOG))
                tsf4gtest.Shell.exec("> ".. (super.Env.CLTELOG))
                -- RankGetTopRankReq     GetTopN
                tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"1", "TestGetTopRankReq.ScoreType":"1", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":%d, "TestGetTopRankReq.imageIndex":%d}]], 1, i))
                super.Common.StartClient(2)
                tsf4gtest.Util.wait(2)
                if i <= SmallExtraImageCount then
                    super.Common.CheckBasicInfo(1, 1, 5, 5)
                else
                    tsf4gtest.Assert.EXPECT_LOG(super.Env.CLTELOG, "image index is illegal", 1)
                    tsf4gtest.Assert.EXPECT_LOG(super.Env.TRANKTOPELOG, string.format("image index\\[%d\\] bigger than extra image count\\[%d\\] or bigger than max num\\[4\\]", i, SmallExtraImageCount), 1)
                end
            end

             -- reload 重回大的BigExtraImageCount
            tsf4gtest.Xml.load(super.Env.TRANKTOPCFG)
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", BigExtraImageCount, "list", {super.Env.SCOREIDX+1})
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", BigExtraImageCount, "list", {4})
            tsf4gtest.Xml.load(AUTORUNHOME.."/ranktopA/cfg/topn_app.xml")
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", BigExtraImageCount, "list", {super.Env.SCOREIDX+1})
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", BigExtraImageCount, "list", {4})
            super.Common.StartRanktop(3, 1)
            super.Common.StartRanktop(3, 2)
            tsf4gtest.Util.wait(5)

            for i = 0, BigExtraImageCount do
                tsf4gtest.Shell.exec("> ".. (super.Env.CLTLOG))
                -- RankGetTopRankReq     GetTopN
                tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"1", "TestGetTopRankReq.ScoreType":"1", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":%d, "TestGetTopRankReq.imageIndex":%d}]], 1, i))
                super.Common.StartClient(2)
                tsf4gtest.Util.wait(2)
                if i <= OriExtraImageCount then
                    super.Common.CheckBasicInfo(1, 1, 5, 5)
                else
                    super.Common.CheckBasicInfo(1, 1, 0, 0)
                end
            end

        end
    },

    test_ExtraImageCount_03 = {
        Desc = "reload的时候, 考虑额外镜像超过ExtraImageCount(reload会删除), 或是小于ExtraImageCount的情况, 2 - 4 - 1 - 4",
        Ver = "2.1.42",
        Run = function()
            TestMultiImageInit()
            StartBaseWithTopn(2)
            tsf4gtest.Util.wait(2)

            local GetImageStatusRsp_seq = 10000

            -- reload
            local OriExtraImageCount = 2
            tsf4gtest.Xml.load(super.Env.TRANKTOPCFG)
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", OriExtraImageCount, "list", {super.Env.SCOREIDX+1})
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", OriExtraImageCount, "list", {4})
            tsf4gtest.Xml.load(AUTORUNHOME.."/ranktopA/cfg/topn_app.xml")
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", OriExtraImageCount, "list", {super.Env.SCOREIDX+1})
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", OriExtraImageCount, "list", {4})
            super.Common.StartRanktop(2, 1)
            super.Common.StartRanktop(2, 2)
            tsf4gtest.Util.wait(5)

            for i = 0, OriExtraImageCount do
                -- 手动生成镜像
                tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"26", "TestGenerateImageReq.zoneid":"1", "TestGenerateImageReq.score_type":"1", "TestGenerateImageReq.image_sequence":%d, "TestGenerateImageReq.imageIndex":%d}]], GetImageStatusRsp_seq, i))
                super.Common.StartClient(2)
                tsf4gtest.Util.wait(3)
                super.Common.CheckGenerateImage(i+1)
            end

            -- reload
            local BigExtraImageCount = 4
            tsf4gtest.Xml.load(super.Env.TRANKTOPCFG)
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", BigExtraImageCount, "list", {super.Env.SCOREIDX+1})
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", BigExtraImageCount, "list", {4})
            tsf4gtest.Xml.load(AUTORUNHOME.."/ranktopA/cfg/topn_app.xml")
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", BigExtraImageCount, "list", {super.Env.SCOREIDX+1})
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", BigExtraImageCount, "list", {4})
            super.Common.StartRanktop(2, 1)
            super.Common.StartRanktop(2, 2)
            tsf4gtest.Util.wait(5)

            for i = 0, BigExtraImageCount do
                tsf4gtest.Shell.exec("> ".. (super.Env.CLTLOG))
                -- RankGetTopRankReq     GetTopN
                tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"1", "TestGetTopRankReq.ScoreType":"1", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":%d, "TestGetTopRankReq.imageIndex":%d}]], 1, i))
                super.Common.StartClient(2)
                tsf4gtest.Util.wait(2)
                if i <= OriExtraImageCount then
                    super.Common.CheckBasicInfo(1, 1, 5, 5)
                else
                    super.Common.CheckBasicInfo(1, 1, 0, 0)
                end
            end

            -- reload
            local SmallExtraImageCount = 1
            tsf4gtest.Xml.load(super.Env.TRANKTOPCFG)
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", SmallExtraImageCount, "list", {super.Env.SCOREIDX+1})
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", SmallExtraImageCount, "list", {4})
            tsf4gtest.Xml.load(AUTORUNHOME.."/ranktopA/cfg/topn_app.xml")
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", SmallExtraImageCount, "list", {super.Env.SCOREIDX+1})
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", SmallExtraImageCount, "list", {4})
            super.Common.StartRanktop(2, 1)
            super.Common.StartRanktop(2, 2)
            tsf4gtest.Util.wait(5)

            for i = 0, BigExtraImageCount do
                tsf4gtest.Shell.exec("> ".. (super.Env.CLTLOG))
                tsf4gtest.Shell.exec("> ".. (super.Env.CLTELOG))
                -- RankGetTopRankReq     GetTopN
                tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"1", "TestGetTopRankReq.ScoreType":"1", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":%d, "TestGetTopRankReq.imageIndex":%d}]], 1, i))
                super.Common.StartClient(2)
                tsf4gtest.Util.wait(2)
                if i <= SmallExtraImageCount then
                    super.Common.CheckBasicInfo(1, 1, 5, 5)
                else
                    tsf4gtest.Assert.EXPECT_LOG(super.Env.CLTELOG, "image index is illegal", 1)
                    tsf4gtest.Assert.EXPECT_LOG(super.Env.TRANKTOPELOG, string.format("image index\\[%d\\] bigger than extra image count\\[%d\\] or bigger than max num\\[4\\]", i, SmallExtraImageCount), 1)
                end
            end

             -- reload 重回大的BigExtraImageCount
            tsf4gtest.Xml.load(super.Env.TRANKTOPCFG)
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", BigExtraImageCount, "list", {super.Env.SCOREIDX+1})
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", BigExtraImageCount, "list", {4})
            tsf4gtest.Xml.load(AUTORUNHOME.."/ranktopA/cfg/topn_app.xml")
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", BigExtraImageCount, "list", {super.Env.SCOREIDX+1})
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.ExtraImageCount", BigExtraImageCount, "list", {4})
            super.Common.StartRanktop(2, 1)
            super.Common.StartRanktop(2, 2)
            tsf4gtest.Util.wait(5)

            for i = 0, BigExtraImageCount do
                tsf4gtest.Shell.exec("> ".. (super.Env.CLTLOG))
                -- RankGetTopRankReq     GetTopN
                tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"1", "TestGetTopRankReq.ScoreType":"1", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":%d, "TestGetTopRankReq.imageIndex":%d}]], 1, i))
                super.Common.StartClient(2)
                tsf4gtest.Util.wait(2)
                if i <= SmallExtraImageCount then
                    super.Common.CheckBasicInfo(1, 1, 5, 5)
                else
                    super.Common.CheckBasicInfo(1, 1, 0, 0)
                end
            end

        end
    },

    test_sync_01 = {
        Desc = "sync同步，两个topn的参数都一致",
        TearDown = function()
            super.Case.TearDown()
            -- 删除了/ranktopA/data/目录，所以需要还原
            tsf4gtest.Shell.exec("cp "..AUTORUNHOME.."/ranktop/data.bak/* "..AUTORUNHOME.."/ranktopA/data/")
        end,
        Run = function()
            TestMultiImageInit(2, 4)
            -- 修改ReferPullExpectedDuration为20s， 迁移预期时长，在这个时长内不要有榜单正在生成镜像，默认前后各半小时
            tsf4gtest.Xml.load(super.Env.TRANKTOPCFG)
            tsf4gtest.Xml.set("ReferPullExpectedDuration", 20)
            tsf4gtest.Xml.load(AUTORUNHOME.."/ranktopA/cfg/topn_app.xml")
			tsf4gtest.Xml.set("ReferPullExpectedDuration", 20)

            StartBaseWithTopn(1)
            tsf4gtest.Util.wait(2)
            
            local imageIndexs = {0, 1, 2, 4}

            -- upload 数据
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, [[{"Cmd":"1", "TestUploadScore.ScoreValue":"100", "TestUploadScoreReq.TestRankUser.OpenId":"100", "UploadScoreCount":"10", "ScoreIncrement":"1"}]])
			super.Common.StartClient()
			tsf4gtest.Util.wait(3)

            -- 自动生成镜像
            super.Common.SetSystemTime("+1 days")
            tsf4gtest.Util.wait(6)
            -- 手动生成镜像
            for i, v in ipairs(imageIndexs) do
                if v~= 0 then
                    tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"26", "TestGenerateImageReq.zoneid":"1", "TestGenerateImageReq.score_type":"1", "TestGenerateImageReq.image_sequence":%d, "TestGenerateImageReq.imageIndex":%d}]], 10000, v))
                    super.Common.StartClient(2)
                    tsf4gtest.Util.wait(3)
                    tsf4gtest.Assert.EXPECT_LOG(AUTORUNHOME.."/client/log/rankclient.log", "GenerateImageRsp.GetResultInfo ret_=0, resultCode=0, errMsg_=,", i - 1)
                end
            end

            -- 检查镜像
            for i, v in ipairs(imageIndexs) do
                tsf4gtest.Shell.exec("> ".. (super.Env.CLTLOG))
                -- RankGetTopRankReq     GetTopN
                tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"1", "TestGetTopRankReq.ScoreType":"1", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":%d, "TestGetTopRankReq.imageIndex":%d}]], 1, v))
                super.Common.StartClient(2)
                tsf4gtest.Util.wait(2)
                super.Common.CheckBasicInfo(1, 1, 10, 10)
            end

            -- 开始sync
            local Aip = "tcp://"..super.Env.MYADDR..":"..tostring(RANKTOPPORT)
			tsf4gtest.Xml.load(AUTORUNHOME.."/ranktopA/cfg/topn_app.xml")
			tsf4gtest.Xml.set("ReferMachineAddr", Aip)

            super.Common.StartRanktop(1, 2)
			tsf4gtest.Util.wait(2)
			-- tsf4gtest.Assert.EXPECT_LOG(AUTORUNHOME .. "/ranktopA/log/topn_app.log", "all sync msg recv from refer machine, set svr ready", 1)
            tsf4gtest.Assert.EXPECT_LOG(AUTORUNHOME .. "/ranktopA/log/topn_app.log", " business\\[2000\\], zone\\[1\\], score type\\[1\\] sync complete, syncNum:10, total_top_count:10", 1 + 4)

			super.Common.StopRanktop(1)
			tsf4gtest.Util.wait(2)

			tsf4gtest.Xml.load(super.Env.PROXYCFG)
			tsf4gtest.Xml.set("RankSvrAddr", "tbus://********", "list")
			super.Common.StopProxy()
			tsf4gtest.Util.wait(1)
			super.Common.StartProxy()
			tsf4gtest.Util.wait(1)

            -- 检查镜像
            for i, v in ipairs(imageIndexs) do
                tsf4gtest.Shell.exec("> ".. (super.Env.CLTLOG))
                -- RankGetTopRankReq     GetTopN
                tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"1", "TestGetTopRankReq.ScoreType":"1", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":%d, "TestGetTopRankReq.imageIndex":%d}]], 1, v))
                super.Common.StartClient(2)
                tsf4gtest.Util.wait(2)
                super.Common.CheckBasicInfo(1, 1, 10, 10)
            end
            
        end
    },

    test_dump_01 = {
        Desc = "sync同步，两个topn的参数都一致",
        TearDown = function()
            super.Case.TearDown()
            -- 删除了/ranktopA/data/目录，所以需要还原
            tsf4gtest.Shell.exec("cp "..AUTORUNHOME.."/ranktop/data.bak/* "..AUTORUNHOME.."/ranktopA/data/")
        end,
        Run = function()
            TestMultiImageInit(2, 4)
            StartBaseWithTopn(1)

            local imageIndexs = {0, 1, 2, 4}

            -- upload 数据
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, [[{"Cmd":"1", "TestUploadScore.ScoreValue":"100", "TestUploadScoreReq.TestRankUser.OpenId":"100", "UploadScoreCount":"10", "ScoreIncrement":"1"}]])
			super.Common.StartClient()
			tsf4gtest.Util.wait(3)

            -- 生成镜像 & 并检查
            super.Common.SetSystemTime("+1 days")
            tsf4gtest.Util.wait(6)
            for i, v in ipairs(imageIndexs) do
                if v~= 0 then
                    tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"26", "TestGenerateImageReq.zoneid":"1", "TestGenerateImageReq.score_type":"1", "TestGenerateImageReq.image_sequence":%d, "TestGenerateImageReq.imageIndex":%d}]], 10000, v))
                    super.Common.StartClient(2)
                    tsf4gtest.Util.wait(3)
                    tsf4gtest.Assert.EXPECT_LOG(AUTORUNHOME.."/client/log/rankclient.log", "GenerateImageRsp.GetResultInfo ret_=0, resultCode=0, errMsg_=,", i - 1)
                end
            end
            for i, v in ipairs(imageIndexs) do
                tsf4gtest.Shell.exec("> ".. (super.Env.CLTLOG))
                -- RankGetTopRankReq     GetTopN
                tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"1", "TestGetTopRankReq.ScoreType":"1", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":%d, "TestGetTopRankReq.imageIndex":%d}]], 1, v))
                super.Common.StartClient(2)
                tsf4gtest.Util.wait(2)
                super.Common.CheckBasicInfo(1, 1, 10, 10)
            end

            -- start topn_dump 
            tsf4gtest.Xml.batchset(super.Env.TOPNDUMP_CFG, [[{"TopNBusinessCfg.TopNBusiness.BusinessId":"2000", "TopNBusinessCfg.TopNBusiness.TopNScoreCfg.ZoneId":"1", "TopNBusinessCfg.TopNBusiness.TopNScoreCfg.ScoreType":"1", "TopNBusinessCfg.TopNBusiness.TopNScoreCfg.UseImageData":"1", "TopNBusinessCfg.TopNBusiness.TopNScoreCfg.ExtraImageCount":"4", "GetTopNumPerReq":"5"}]])
            -- topn_dump 需和app的一致，表示已经生成过镜像了
            tsf4gtest.Xml.set("TopNBusinessCfg.TopNBusiness.TopNScoreCfg.BeginTime", os.date("%Y-%m-%d %H:%M:%S", os.time() - 10000))
            -- tsf4gtest.Xml.set("TopNBusinessCfg.TopNBusiness.TopNScoreCfg.BeginTime", os.date("%Y-%m-%d %H:%M:%S", os.time() + 10000))
            super.Common.ControlProc(super.Env.TOPNDUMP_BIN, "start")
			tsf4gtest.Util.wait(super.Env.WAITTIME)
			tsf4gtest.Assert.EXPECT_LOG_BYSTR("true", super.Env.TOPNDUMP_LOG, "enter into backup file status")
            super.Common.ControlProc(super.Env.TOPNDUMP_BIN, "stop")

            -- 停止app1&清理榜单信息, 启动app1, 检查榜单数据为空
            super.Common.StopRanktop(1)
            super.Common.ClearShm()
            tsf4gtest.Util.wait(2)
            super.Common.StartRanktop(1, 1)
            tsf4gtest.Util.wait(2)
            for i, v in ipairs(imageIndexs) do
                tsf4gtest.Shell.exec("> ".. (super.Env.CLTLOG))
                -- RankGetTopRankReq     GetTopN
                tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"1", "TestGetTopRankReq.ScoreType":"1", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":%d, "TestGetTopRankReq.imageIndex":%d}]], 1, v))
                super.Common.StartClient(2)
                tsf4gtest.Util.wait(2)
                super.Common.CheckBasicInfo(1, 1, 0, 0)
            end
            super.Common.StopRanktop(1)
            super.Common.ClearShm()
            tsf4gtest.Util.wait(2)

            -- copy dump数据 & 重启app1， 检查镜像
            tsf4gtest.Shell.exec("cp "..AUTORUNHOME.."/topn_dump/data/*  "..AUTORUNHOME.."/ranktop/data/")
            tsf4gtest.Xml.load(super.Env.TRANKTOPCFG)
            -- 确保不触发自动镜像
            tsf4gtest.Xml.set("TopNBusiness.TopNScoreCfg.ImageData.BeginTime", os.date("%Y-%m-%d %H:%M:%S", os.time() + 10000), "list", {super.Env.SCOREIDX+1})
            super.Common.StartRanktop(1, 1)
            tsf4gtest.Util.wait(5)

            -- tsf4gtest.Util.pause()
            tsf4gtest.Shell.exec("> ".. (super.Env.CLTLOG))
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, [[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"1", "TestGetTopRankReq.ScoreType":"1", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":0}]])
            super.Common.StartClient(2)
            tsf4gtest.Util.wait(2)
            super.Common.CheckBasicInfo(1, 1, 10, 10)
            for i, v in ipairs(imageIndexs) do
                tsf4gtest.Shell.exec("> ".. (super.Env.CLTLOG))
                -- RankGetTopRankReq     GetTopN
                -- TODO image_index=0的情况, 配置要跟app一致, 计算上次生成镜像的时间(>=当前时间) , 但image_index=0没到时间也可以手动生成，这种情况可能就dump不了image_index=0的了
                tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"1", "TestGetTopRankReq.ScoreType":"1", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":%d, "TestGetTopRankReq.imageIndex":%d}]], 1, v))
                super.Common.StartClient(2)
                tsf4gtest.Util.wait(2)
                super.Common.CheckBasicInfo(1, 1, 10, 10)
            end
            
        end
    },

    -- 升级还有哪些需要注意的场景
    test_upgrade_01 = {
        Desc = "升级兼容（不换proxy），旧版本：TSF4G_APOLLO_SERVICE_INTERNAL-2.1.37.b392b8361_X86_64_Release",
        TearDown = function()
            super.Common.StopAll()
            -- case exec fail, env backup
            if (tsf4gtest.GlobalVar.case_ispass == false) then
                local dst = super.Env.TRANKTOPFAIL .. "/" .. tsf4gtest.GlobalVar.case_name .. "/"
                tsf4gtest.Util.backup_failenv(dst, AUTORUNHOME .. "/*")
            end
            tsf4gtest.Util.restore_files(super.Env.NEED2BACKUP_FILES)
            tsf4gtest.Util.backup_files(super.Env.NEED2BACKUP_FILES)

            tsf4gtest.Shell.exec(AUTORUNHOME .. "/tool/clear_shm_old.sh  " .. AUTORUNHOME ..
                                     "/ranktop/data > ../run_env/tool/clear1_old.txt")
            tsf4gtest.Shell.exec(AUTORUNHOME .. "/tool/clear_shm_old.sh  " .. AUTORUNHOME ..
                                        "/ranktopA/data > ../run_env/tool/clear2_old.txt")
            tsf4gtest.Shell.exec(AUTORUNHOME .. "/tool/clear_shm.sh  " .. AUTORUNHOME ..
                                    "/ranktop/data > ../run_env/tool/clear1.txt")
            tsf4gtest.Shell.exec(AUTORUNHOME .. "/tool/clear_shm.sh  " .. AUTORUNHOME ..
                                    "/ranktopA/data > ../run_env/tool/clear2.txt")
            tsf4gtest.Util.RmFiles(AUTORUNHOME .. "/ranktop/data/*")
            tsf4gtest.Util.RmFiles(AUTORUNHOME .. "/ranktopA/data/*.xml")
            tsf4gtest.Util.RmFiles(AUTORUNHOME .. "/ranktopA/data.bak/*.xml")
                                     
            super.Common.RestoreSystemTimeToHardWareTime()

            -- 删除了/ranktopA/data/目录，所以需要还原
            tsf4gtest.Shell.exec("cp "..AUTORUNHOME.."/ranktop/data.bak/* "..AUTORUNHOME.."/ranktopA/data/")
        end,
        Run = function()
            TestMultiImageInit(2, 4)
            super.Common.StartDirstub()
            super.Common.StartProxy(1, "2_1_37")
            -- super.Common.StartProxy()
            super.Common.StartRanktop(1, "2_1_37_1")
            -- super.Common.StartRanktop(1)
            tsf4gtest.Util.wait(2)

            -- upload 数据
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, [[{"Cmd":"1", "TestUploadScore.ScoreValue":"100", "TestUploadScoreReq.TestRankUser.OpenId":"100", "UploadScoreCount":"10", "ScoreIncrement":"1"}]])
			super.Common.StartClient(1, "2_1_37")
            -- super.Common.StartClient(1)
			tsf4gtest.Util.wait(3)

            -- 自动生成镜像
            super.Common.SetSystemTime("+1 days")
            tsf4gtest.Util.wait(6)
            -- 手动生成镜像
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"26", "TestGenerateImageReq.zoneid":"1", "TestGenerateImageReq.score_type":"1", "TestGenerateImageReq.image_sequence":%d, "TestGenerateImageReq.imageIndex":%d}]], 10000, 0))
            super.Common.StartClient(2, "2_1_37")
            tsf4gtest.Util.wait(3)
            tsf4gtest.Assert.EXPECT_LOG(AUTORUNHOME.."/client/log/rankclient.log", "GenerateImageRsp.GetResultInfo ret_=0, resultCode=0, errMsg_=,", 1)

            -- 检查镜像
            -- RankGetTopRankReq     GetTopN
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"1", "TestGetTopRankReq.ScoreType":"1", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":%d, "TestGetTopRankReq.imageIndex":%d}]], 1, 0))
            super.Common.StartClient(2, "2_1_37")
            tsf4gtest.Util.wait(2)
            super.Common.CheckBasicInfo(1, 1, 10, 10)
            super.Common.StopClient("2_1_37")

            -- 开始升级
            super.Common.StopRanktop("2_1_37_1")
            tsf4gtest.Util.wait(2)
            super.Common.StartRanktop(1)
			tsf4gtest.Util.wait(2)
            tsf4gtest.Shell.exec(">"..AUTORUNHOME.."/client/log/rankclient.log")

            -- 检查实时榜单
            -- RankGetTopRankReq     GetTopN
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"1", "TestGetTopRankReq.ScoreType":"1", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":%d, "TestGetTopRankReq.imageIndex":%d}]], 0, 0))
            super.Common.StartClient(2)
            tsf4gtest.Util.wait(2)
            super.Common.CheckBasicInfo(1, 1, 10, 10)
            tsf4gtest.Shell.exec(">"..AUTORUNHOME.."/client/log/rankclient.log")

            -- 检查镜像
            -- RankGetTopRankReq     GetTopN
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"1", "TestGetTopRankReq.ScoreType":"1", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":%d, "TestGetTopRankReq.imageIndex":%d}]], 1, 0))
            super.Common.StartClient(2)
            tsf4gtest.Util.wait(2)
            super.Common.CheckBasicInfo(1, 1, 10, 10)
            
        end
    },
    -- 老api兼容，手动执行，替换旧api

    test_abnormal_01 = {
        Desc = "异常用例： 各个接口image_index 有错",
        Ver = "2.1.42",
        Run = function()
            TestMultiImageInit()
            StartBaseWithTopn(1)
            tsf4gtest.Util.wait(2)

            -- local invalidImageIndex = getRandomNum({-1, 5, 100, 10000}, 4)
            local invalidImageIndex = 5

            -- TODO, 接口的image_index>4 和>8的表现不一致，>8是会在SetPara时校验不通过

            print("invalidImageIndex: " .. invalidImageIndex)

            SendApiWithImageIndex(0, invalidImageIndex, 10000)
            tsf4gtest.Assert.EXPECT_LOG(super.Env.CLTELOG, "may be >  max \\[4\\] ", 8)

            SendApiWithImageIndex(1, invalidImageIndex, 10000)
            tsf4gtest.Assert.EXPECT_LOG(super.Env.CLTELOG, "may be >  max \\[4\\] ", 16)

            tsf4gtest.Assert.ASSERT_FILE_EMPTY(super.Env.TRANKTOPELOG)
        end
    },

    test_abnormal_02 = {
        Desc = "异常用例： 各个镜像相关接口用例 -- 无榜单实例",
        Ver = "2.1.42",
        TearDown = function()
            super.Case.TearDown()
            -- 删除了/ranktopA/data/目录，所以需要还原
            tsf4gtest.Shell.exec("cp "..AUTORUNHOME.."/ranktop/data.bak/* "..AUTORUNHOME.."/ranktopA/data/")
        end,
        Run = function()
            TestMultiImageInit(2)
            StartBaseWithTopn(1)
            tsf4gtest.Util.wait(2)

            local GetImageStatusRsp_seq = 10000

            SendApiWithImageIndex(0, GlobalImageIndex, GetImageStatusRsp_seq)
            tsf4gtest.Assert.EXPECT_LOG(AUTORUNHOME.."/client/log/rankclient.log", "GenerateImageRsp.GetResultInfo ret_=0, resultCode=0, errMsg_=,", 1)
            tsf4gtest.Assert.EXPECT_LOG(AUTORUNHOME.."/client/log/rankclient.log", string.format("GetImageStatusRsp.GetData suc. zoneid=1, score_type=1, sequence=%d, status=0, CurrentImageEnabled=open", GetImageStatusRsp_seq), 1)
            super.Common.CheckBasicInfo(1, 1, 0, 0)
            super.Common.CheckTopnByUserLog(1, 1, 0, 1)
            super.Common.CheckTopnByOneUserLog(1, 1, 0, 1, 0)
            super.Common.CheckTopnByRankLog(1, 1, 0, 0)
            super.Common.CheckTopnByScoreLog(1, 1, 0, 1)
			super.Common.CheckClientByScoresResult({10}, {}, {})
            super.Common.CheckTopRankLastUpdateTime(1, 1, 0, 0)
            super.Common.CheckClientErrLogEmpty()

            tsf4gtest.Shell.exec("> ".. (super.Env.CLTLOG))
            tsf4gtest.Shell.exec("> ".. (super.Env.CLTELOG))

            SendApiWithImageIndex(1, GlobalImageIndex, GetImageStatusRsp_seq)
            tsf4gtest.Assert.EXPECT_LOG(AUTORUNHOME.."/client/log/rankclient.log", "GenerateImageRsp.GetResultInfo ret_=0, resultCode=0, errMsg_=,", 1)
            tsf4gtest.Assert.EXPECT_LOG(AUTORUNHOME.."/client/log/rankclient.log", string.format("GetImageStatusRsp.GetData suc. zoneid=1, score_type=1, sequence=%d, status=0, CurrentImageEnabled=open", GetImageStatusRsp_seq), 1)
            super.Common.CheckBasicInfo(1, 1, 0, 0)
            super.Common.CheckTopnByUserLog(1, 1, 0, 1)
            super.Common.CheckTopnByOneUserLog(1, 1, 0, 1, 0)
            super.Common.CheckTopnByRankLog(1, 1, 0, 0)
            super.Common.CheckTopnByScoreLog(1, 1, 0, 1)
			super.Common.CheckClientByScoresResult({10}, {}, {})
            super.Common.CheckTopRankLastUpdateTime(1, 1, 0, 1)
            super.Common.CheckClientErrLogEmpty()

        end
    },

    test_abnormal_03 = {
        Desc = "异常用例： 各个镜像相关接口用例 -- 无榜单",
        Ver = "2.1.42",
        TearDown = function()
            super.Case.TearDown()
            -- 删除了/ranktopA/data/目录，所以需要还原
            tsf4gtest.Shell.exec("cp "..AUTORUNHOME.."/ranktop/data.bak/* "..AUTORUNHOME.."/ranktopA/data/")
        end,
        Run = function()
            TestMultiImageInit(2)
            StartBaseWithTopn(1)
            tsf4gtest.Util.wait(2)

            local isImage = 1
            local imageIndex = 2
            local GetImageStatusRsp_seq = 10000

            -- 1. RankGetTopRankReq     GetTopN
            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"10", "TestGetTopRankReq.ScoreType":"1", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":%d, "TestGetTopRankReq.imageIndex":%d}]], isImage, imageIndex))
            super.Common.StartClient(2)
            tsf4gtest.Util.wait(2)
            tsf4gtest.Assert.EXPECT_LOG(super.Env.CLTLOG, "GetTopRank_fail. iRetCode\\[-100\\] RetMsg\\[The businessid\\[2000\\] zone\\[10\\] is not configure in the server]", 1)

            tsf4gtest.Xml.batchset(super.Env.CLTCFG, string.format([[{"Cmd":"3", "TestGetTopRankReq.ZoneId":"1", "TestGetTopRankReq.ScoreType":"99999", "TestGetTopRankReq.RankFrom":"1", "TestGetTopRankReq.RankCount":"100", "TestGetTopRankReq.DataSource":%d, "TestGetTopRankReq.imageIndex":%d}]], isImage, imageIndex))
            super.Common.StartClient(2)
            tsf4gtest.Util.wait(2)
            tsf4gtest.Assert.EXPECT_LOG(super.Env.CLTLOG, "GetTopRank_fail. iRetCode\\[-25\\] RetMsg\\[business info not configed in topn svr\\]", 1)


        end
    },


}

tsf4gtest.Setmetatable(super, {test_multi_image})
