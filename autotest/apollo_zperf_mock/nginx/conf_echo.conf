
#user  nobody;
worker_processes 4;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
error_log  logs/error.log  error;

#pid        logs/nginx.pid;


events {
    worker_connections  2000;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  60;
    keepalive_requests 10000;

    #gzip  on;
    init_by_lua '
        package.path = package.path .. ";" .. ngx.config.prefix() .. "/lua/?.lua"
    ';

    lua_shared_dict count_flag 1m;

    server {
        listen 8080;  # 监听的端口号
        server_name _;  # 服务器名称

        location / {
            # 返回请求的URI和请求方法
            return 200 "Request URI: $request_uri\nRequest Method: $request_method";
        }
    }
}
