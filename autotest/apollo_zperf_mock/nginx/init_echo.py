import os
import sys

# import commands py2 / py3 compatible
import subprocess
def getstatusoutput(cmd):
    try:
        output = subprocess.check_output(cmd, shell=True, stderr=subprocess.STDOUT)
        status = 0
    except subprocess.CalledProcessError as e:
        output = e.output
        status = e.returncode
    if sys.version_info[0] == 3:
        output = output.decode('utf-8')
    return status, output

cur = os.path.dirname(os.path.realpath(__file__))
#to install openresty
if os.path.isdir(os.path.join(cur, "nginx"))==False:
    print("openresty has not installed yet. now start to install ...")
    init_cmds = ["tar -zxvf openresty-1.17.8.2.tar.gz",
                 "cd {0}/openresty-1.17.8.2 && ./configure --prefix={0}".format(cur),
                 "cd {0}/openresty-1.17.8.2 && make && make install".format(cur)
                ]
    for cmd in init_cmds:
        status,output = getstatusoutput(cmd)
        if status != 0:
            print("init failed for: %s" % cmd)
            sys.exit(1)
#to update cfg
cmds = [
        "cp {0}/conf_echo.conf {0}/nginx/conf/nginx.conf".format(cur)
       ]
for cmd in cmds:
    status, output = getstatusoutput(cmd)
    if status != 0:
        print("init failed when for:%s" % output)
        sys.exit(1)