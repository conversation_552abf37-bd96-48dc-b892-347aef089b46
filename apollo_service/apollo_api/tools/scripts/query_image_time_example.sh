gettop=true
maxrate=8000
maxgrouprate=1500
bizid=10249
bizkey=AAAAA
dir=tcp://127.0.0.1:9050

./process_subrank_image_time_async -business_id=$bizid -business_key=$bizkey -dir=$dir -input_dir=./input -output_dir=./output -max_rate=$maxrate -max_group_rate=$maxgrouprate -start_time="2025-06-23 00:00:00" -get_top=$gettop | tee /dev/tty | grep --line-buffered "image not genarated" | grep -oP 'groupid:\K\d+' | awk '{printf "%s,", $0}' >> ungenarated.log &