#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import csv
import subprocess
import re
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Tuple, NamedTuple, Optional
from datetime import datetime

class SubRankInfo(NamedTuple):
    world_id: str
    instance_id: str
    sub_type: str
    sub_instance_id: str

def extract_sub_info(log_file: str) -> List[SubRankInfo]:
    """
    Extract world_id, instance_id, sub_type and sub_instance_id from CSV log file
    """
    combinations = []
    with open(log_file, 'r') as f:
        # Skip header line if exists
        next(f, None)
        for line in f:
            # Split by comma and strip whitespace
            parts = [part.strip() for part in line.split(',')]
            if len(parts) >= 5:  # We need at least 5 fields: world_id,instance_id,query_from,sub_type,sub_instance_id
                world_id = parts[0]
                instance_id = parts[1]
                # Skip query_from (parts[2])
                sub_type = parts[3]
                sub_instance_id = parts[4]
                if (world_id.isdigit() and instance_id.isdigit() and
                    sub_type.isdigit() and sub_instance_id.isdigit()):
                    combinations.append(SubRankInfo(world_id, instance_id, sub_type, sub_instance_id))
    return combinations

def extract_image_generate_ts(output: str) -> Optional[str]:
    """
    Extract Image_Generate_ts value from command output
    """
    # Look for pattern like "Image_Generate_ts: 1750004067563"
    pattern = r'Image_Generate_ts:\s*(\d+)'
    match = re.search(pattern, output)
    if match:
        return match.group(1)
    return None

def execute_command(world_id: str, instance_id: str, sub_type: str, sub_instance_id: str, query_from: int, output_file: str) -> Optional[dict]:
    """
    Execute the command with given world_id, instance_id, sub_type, sub_instance_id and query_from value
    Returns a dict with result data if successful, None otherwise
    """
    cmd = [
        './gettop_topnext',
        '-dir=tcp://30.41.104.211:9050',
        '-business_id=10249',
        '-business_key=5cd3027c07873cf6aa691a42320d86f5',
        f'-world_id={world_id}',
        '-type=100',
        f'-instance_id={instance_id}',
        f'-sub_rank_info={sub_type}-{sub_instance_id}',
        '-is_query_image=1',
        f'-query_from={query_from}',
        '-query_count=1'  # 添加query_count=1参数
    ]
    
    print(f"Executing command for world_id={world_id}, instance_id={instance_id}, sub_type={sub_type}, sub_instance_id={sub_instance_id}, query_from={query_from}, query_count=1")
    
    try:
        result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)

        # Extract Image_Generate_ts from output
        image_generate_ts = None
        if result.returncode == 0 and result.stdout:
            image_generate_ts = extract_image_generate_ts(result.stdout)

        # Append result to the output file
        with open(output_file, 'a') as f:
            f.write(f"\n{'='*80}\n")
            f.write(f"World ID: {world_id}\n")
            f.write(f"Instance ID: {instance_id}\n")
            f.write(f"Sub Type: {sub_type}\n")
            f.write(f"Sub Instance ID: {sub_instance_id}\n")
            f.write(f"Query from: {query_from}\n")
            f.write(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            if image_generate_ts:
                f.write(f"Image_Generate_ts: {image_generate_ts}\n")
            f.write("\n=== STDOUT ===\n")
            f.write(result.stdout)
            f.write("\n=== STDERR ===\n")
            f.write(result.stderr)
            f.write(f"\nReturn code: {result.returncode}\n")

        if result.returncode == 0:
            if image_generate_ts:
                print(f"[Thread-{threading.current_thread().ident}] Command executed successfully. Image_Generate_ts: {image_generate_ts}")
                # Convert timestamp to readable format
                image_generate_datetime = convert_timestamp_to_datetime(image_generate_ts)
                # Return result data for CSV writing
                return {
                    'world_id': world_id,
                    'instance_id': instance_id,
                    'sub_type': sub_type,
                    'sub_instance_id': sub_instance_id,
                    'image_generate_ts': image_generate_ts,
                    'image_generate_datetime': image_generate_datetime,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            else:
                print(f"[Thread-{threading.current_thread().ident}] Command executed successfully. No Image_Generate_ts found.")
        else:
            print(f"[Thread-{threading.current_thread().ident}] Command failed with error: {result.stderr}")

        return None
    except Exception as e:
        print(f"[Thread-{threading.current_thread().ident}] Error executing command: {str(e)}")
        # Append error to output file
        with open(output_file, 'a') as f:
            f.write(f"\n{'='*80}\n")
            f.write(f"World ID: {world_id}\n")
            f.write(f"Instance ID: {instance_id}\n")
            f.write(f"Sub Type: {sub_type}\n")
            f.write(f"Sub Instance ID: {sub_instance_id}\n")
            f.write(f"Query from: {query_from}\n")
            f.write(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Error: {str(e)}\n")

        return None

def convert_timestamp_to_datetime(timestamp_str: str) -> str:
    """
    Convert timestamp (milliseconds) to readable datetime format
    """
    try:
        # Convert milliseconds to seconds
        timestamp_seconds = int(timestamp_str) / 1000
        # Convert to datetime and format
        dt = datetime.fromtimestamp(timestamp_seconds)
        return dt.strftime('%Y-%m-%d %H:%M:%S')
    except (ValueError, OSError) as e:
        print(f"Error converting timestamp {timestamp_str}: {str(e)}")
        return "Invalid timestamp"

def process_single_combination(info: SubRankInfo, output_dir: str, query_from: int = 1) -> Optional[dict]:
    """
    Process a single combination in a thread-safe manner
    """
    try:
        # Create output file for this combination
        output_file = os.path.join(output_dir, f"results_{info.world_id}_{info.instance_id}_{info.sub_type}_{info.sub_instance_id}.txt")

        # Write header to the output file (thread-safe file creation)
        with open(output_file, 'w') as f:
            f.write(f"Results for world_id: {info.world_id}, instance_id: {info.instance_id}, sub_type: {info.sub_type}, sub_instance_id: {info.sub_instance_id}\n")
            f.write(f"Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Query from: {query_from}\n")
            f.write("="*80 + "\n")

        # Execute command and return result
        return execute_command(info.world_id, info.instance_id, info.sub_type, info.sub_instance_id, query_from, output_file)

    except Exception as e:
        print(f"[Thread-{threading.current_thread().ident}] Error processing combination {info}: {str(e)}")
        return None

def main():
    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)
    
    # Create output directory with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join('results', timestamp)
    os.makedirs(output_dir, exist_ok=True)
    print(f"Results will be saved to: {output_dir}")

    # Create CSV file for Image_Generate_ts values
    csv_file = os.path.join(output_dir, 'image_generate_ts.csv')
    csv_fieldnames = ['world_id', 'instance_id', 'sub_type', 'sub_instance_id', 'image_generate_ts', 'image_generate_datetime', 'timestamp']
    
    # Process all log files in the logs directory
    log_files = [f for f in os.listdir('logs') if f.startswith('subrank_list_') and f.endswith('.csv')]
    
    if not log_files:
        print("No log files found in logs directory")
        return
    
    # 只查询一次，query_from=1, query_count=1
    query_from = 1

    # Collect all combinations from all log files
    all_combinations = []
    for log_file in log_files:
        log_path = os.path.join('logs', log_file)
        print(f"Loading combinations from: {log_file}")
        combinations = extract_sub_info(log_path)
        all_combinations.extend(combinations)

    total_combinations = len(all_combinations)
    print(f"\nTotal combinations to process: {total_combinations}")

    if total_combinations == 0:
        print("No combinations found to process.")
        return

    # Use ThreadPoolExecutor for concurrent processing
    max_workers = 10
    print(f"Starting processing with {max_workers} threads...")

    # Open CSV file for writing Image_Generate_ts values
    csv_results = []
    csv_lock = threading.Lock()

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_info = {
            executor.submit(process_single_combination, info, output_dir, query_from): info
            for info in all_combinations
        }

        completed = 0
        # Process completed tasks
        for future in as_completed(future_to_info):
            info = future_to_info[future]
            completed += 1

            try:
                result = future.result()
                if result:
                    with csv_lock:
                        csv_results.append(result)
                    print(f"[{completed}/{total_combinations}] ✓ Completed: world_id={info.world_id}, instance_id={info.instance_id}, sub_type={info.sub_type}, sub_instance_id={info.sub_instance_id}")
                else:
                    print(f"[{completed}/{total_combinations}] ✗ Failed: world_id={info.world_id}, instance_id={info.instance_id}, sub_type={info.sub_type}, sub_instance_id={info.sub_instance_id}")
            except Exception as e:
                print(f"[{completed}/{total_combinations}] ✗ Exception for {info}: {str(e)}")

    # Write all CSV results at once
    with open(csv_file, 'w', newline='') as csvf:
        csv_writer = csv.DictWriter(csvf, fieldnames=csv_fieldnames)
        csv_writer.writeheader()
        csv_writer.writerows(csv_results)

    print(f"\nProcessing completed!")
    print(f"Total combinations processed: {total_combinations}")
    print(f"Successful extractions: {len(csv_results)}")
    print(f"Image_Generate_ts values saved to: {csv_file}")

if __name__ == "__main__":
    main() 
