#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import csv
import subprocess
import argparse
from datetime import datetime
from typing import List, Dict, Tuple
from collections import defaultdict

def parse_rank_instance_file(file_path: str) -> List[Tuple[int, int, int, int, int]]:
    """
    Parse the rank_instance.txt file and extract world_id, zone_id, rank_id, ranktype_id, and group_id
    Returns a list of tuples (world_id, zone_id, rank_id, ranktype_id, group_id)
    """
    instances = []
    try:
        with open(file_path, 'r') as f:
            for line in f:
                line = line.strip()
                # Skip header lines and separator lines
                if line.startswith('+') or line.startswith('|') and 'world_id' in line:
                    continue
                # Parse data lines
                if line.startswith('|') and line.endswith('|'):
                    parts = [part.strip() for part in line.split('|')]
                    if len(parts) >= 6:  # Need at least 6 parts to get group_id
                        try:
                            world_id = int(parts[1])
                            zone_id = int(parts[2])   # zone_id from the file
                            rank_id = int(parts[3])   # rank_id will be used as instance_id
                            ranktype_id = int(parts[4])  # ranktype_id will be used as type
                            group_id = int(parts[5])  # group_id for grouping results
                            instances.append((world_id, zone_id, rank_id, ranktype_id, group_id))
                        except ValueError:
                            # Skip lines that can't be parsed as integers
                            continue
    except FileNotFoundError:
        print(f"Error: File {file_path} not found")
        return []
    except Exception as e:
        print(f"Error reading file {file_path}: {str(e)}")
        return []

    return instances


def parse_output(output: str, world_id: int, zone_id: int, instance_id: int, ranktype_id: int) -> Tuple[List[Dict[str, str]], int]:
    """
    Parse the command output and extract sub_type, sub_instance_id, and total count
    Returns: (results_list, total_count)
    """
    results = []
    total_count = 0

    for line in output.split('\n'):
        line = line.strip()

        # Parse total count line
        if line.startswith('total count:'):
            try:
                total_count = int(line.split(':')[1].strip().rstrip(','))
            except (ValueError, IndexError):
                total_count = 0
            continue

        # Parse data lines
        if line and '|' in line and not line.startswith('+'):
            parts = [part.strip() for part in line.split('|')]
            if len(parts) >= 3:  # At least 3 parts (including empty ones from start/end |)
                # Skip header line
                if 'sub_type' in line or 'sub_instance_id' in line:
                    continue

                # Remove empty parts from start and end (caused by leading/trailing |)
                while parts and not parts[0]:
                    parts.pop(0)
                while parts and not parts[-1]:
                    parts.pop()

                # Look for sub_type and sub_instance_id in the last columns
                if len(parts) >= 2:
                    sub_type = parts[-2] if len(parts) >= 2 else ''
                    sub_instance_id = parts[-1] if len(parts) >= 1 else ''

                    # Only add if both sub_type and sub_instance_id are non-empty and not header-like
                    if (sub_type and sub_instance_id and
                        sub_type not in ['sub_type', 'col6', 'col7'] and
                        sub_instance_id not in ['sub_instance_id', 'col7', 'col8']):
                        result = {
                            'world_id': str(world_id),
                            'zone_id': str(zone_id),
                            'instance_id': str(instance_id),
                            'type': str(ranktype_id),
                            'sub_type': sub_type,
                            'sub_instance_id': sub_instance_id
                        }
                        results.append(result)

    return results, total_count


def execute_query(world_id: int, zone_id: int, instance_id: int, ranktype_id: int, query_from: int, business_id: str, business_key: str, dir_param: str, filter_sub_types: set, log_file: str, csv_file_handle, writer: csv.DictWriter) -> Tuple[int, int, int]:
    """
    Execute the query command and log the output
    Returns: (original_records_found, filtered_records_saved, total_count)
    """
    cmd = [
        './getsubranklist_topnext',
        f'-dir={dir_param}',
        f'-business_id={business_id}',
        f'-business_key={business_key}',
        f'-world_id={world_id}',
        f'-zone_id={zone_id}',
        f'-type={ranktype_id}',
        f'-instance_id={instance_id}',
        '-is_query_image=1',
        f'-query_from={query_from}'
    ]
    
    print(f"Executing query for world_id={world_id}, zone_id={zone_id}, instance_id={instance_id}, ranktype_id={ranktype_id}, query_from={query_from}")

    try:
        # Execute command and capture output
        result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)

        # Write to log file
        with open(log_file, 'a') as f:
            f.write(f"\n{'='*40}\n")
            f.write(f"Query executed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Command: {' '.join(cmd)}\n")
            f.write(f"World ID: {world_id}\n")
            f.write(f"Zone ID: {zone_id}\n")
            f.write(f"Instance ID: {instance_id}\n")
            f.write(f"Ranktype ID: {ranktype_id}\n")
            f.write(f"Query from: {query_from}\n")
            f.write(f"Return code: {result.returncode}\n")
            
            if result.stdout:
                f.write("\nSTDOUT:\n")
                f.write(result.stdout)
            
            if result.stderr:
                f.write("\nSTDERR:\n")
                f.write(result.stderr)
            
            f.write(f"\n{'='*40}\n")
        
        if result.returncode == 0:
            # Parse the output and write to CSV
            results, total_count = parse_output(result.stdout, world_id, zone_id, instance_id, ranktype_id)
            original_records_found = len(results) if results else 0

            if results:
                # Apply sub_type filter if specified
                if filter_sub_types:
                    original_count = len(results)
                    results = [r for r in results if r['sub_type'] in filter_sub_types]
                    filtered_count = len(results)
                    print(f"Filtered {original_count} records to {filtered_count} records based on sub_type filter")
                    with open(log_file, 'a') as f:
                        f.write(f"Filtered {original_count} records to {filtered_count} records based on sub_type filter: {filter_sub_types}\n")
                        f.flush()

                if results:  # Only write if we have results after filtering
                    writer.writerows(results)
                    csv_file_handle.flush()  # 强制刷新CSV文件缓冲区
                    print(f"Saved {len(results)} records to CSV, total count: {total_count}")
                    with open(log_file, 'a') as f:
                        f.write(f"Successfully saved {len(results)} records to CSV, total count: {total_count}\n")
                        f.flush()  # 强制刷新日志文件缓冲区
                    return original_records_found, len(results), total_count
                else:
                    print(f"No records remaining after filtering, but {original_records_found} original records found, total count: {total_count}")
                    with open(log_file, 'a') as f:
                        f.write(f"No records remaining after filtering, but {original_records_found} original records found, total count: {total_count}\n")
                        f.flush()
                    return original_records_found, 0, total_count
            else:
                print(f"No records found in output, total count: {total_count}")
                with open(log_file, 'a') as f:
                    f.write(f"No records found in output, total count: {total_count}\n")
                    f.flush()  # 强制刷新日志文件缓冲区
                return 0, 0, total_count
        else:
            error_msg = f"Query failed with error: {result.stderr}"
            print(error_msg)
            with open(log_file, 'a') as f:
                f.write(f"{error_msg}\n")
                f.flush()  # 强制刷新日志文件缓冲区
            return 0, 0, 0
    except Exception as e:
        error_msg = f"Error executing query: {str(e)}"
        print(error_msg)
        with open(log_file, 'a') as f:
            f.write(f"\n{'='*40}\n")
            f.write(f"Error executing query for world_id={world_id}, zone_id={zone_id}, instance_id={instance_id}, ranktype_id={ranktype_id}, query_from={query_from}: {str(e)}\n")
            f.write(f"{'='*40}\n")
            f.flush()  # 强制刷新日志文件缓冲区
        return 0, 0, 0

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Execute subrank list queries')
    parser.add_argument('--business_id', required=True, help='Business ID for the query')
    parser.add_argument('--business_key', required=True, help='Business key for the query')
    parser.add_argument('--dir', required=True, help='Directory parameter for the query (e.g., tcp://*************:9050)')
    parser.add_argument('--filter_sub_types', help='Comma-separated list of sub_type values to filter (e.g., "1,2,3"). If not specified, all sub_types will be saved.')
    parser.add_argument('--rank_instance_file', default='rank_instance.txt', help='Path to rank instance file (default: rank_instance.txt)')

    args = parser.parse_args()

    # Parse filter_sub_types parameter
    filter_sub_types = set()
    if args.filter_sub_types:
        filter_sub_types = set(args.filter_sub_types.split(','))
        filter_sub_types = {sub_type.strip() for sub_type in filter_sub_types if sub_type.strip()}

    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)

    # Parse rank_instance.txt file
    instances = parse_rank_instance_file(args.rank_instance_file)

    if not instances:
        print(f"No instances found in {args.rank_instance_file}. Exiting.")
        return

    print(f"Found {len(instances)} instances to process")
    print(f"Using business_id: {args.business_id}")
    print(f"Using business_key: {args.business_key}")
    print(f"Using dir: {args.dir}")
    if filter_sub_types:
        print(f"Filtering sub_types: {filter_sub_types}")
    else:
        print("No sub_type filtering applied - all sub_types will be saved")

    # Generate timestamp for log and CSV files
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join('logs', f'subrank_list_{timestamp}.log')
    csv_file = os.path.join('logs', f'subrank_list_{timestamp}.csv')

    # Log script start
    with open(log_file, 'w') as f:
        f.write(f"{'='*40}\n")
        f.write(f"Script started at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Business ID: {args.business_id}\n")
        f.write(f"Business Key: {args.business_key}\n")
        f.write(f"Dir: {args.dir}\n")
        f.write(f"Filter sub_types: {filter_sub_types if filter_sub_types else 'None (all sub_types)'}\n")
        f.write(f"Rank instance file: {args.rank_instance_file}\n")
        f.write(f"Log file: {log_file}\n")
        f.write(f"CSV file: {csv_file}\n")
        f.write(f"Total instances to process: {len(instances)}\n")
        f.write(f"{'='*40}\n")

    print(f"Script started. Log file: {log_file}")
    print(f"CSV file: {csv_file}")

    # Define CSV fieldnames
    fieldnames = ['world_id', 'zone_id', 'instance_id', 'type', 'sub_type', 'sub_instance_id']

    # Group instances by group_id
    groups = defaultdict(list)
    for world_id, zone_id, instance_id, ranktype_id, group_id in instances:
        groups[group_id].append((world_id, zone_id, instance_id, ranktype_id))
    
    print(f"Found {len(groups)} groups to process: {list(groups.keys())}")
    
    # Process each group separately
    for group_id, group_instances in groups.items():
        print(f"\n{'='*50}")
        print(f"Processing Group {group_id} with {len(group_instances)} instances")
        print(f"{'='*50}")

        # Create group-specific files
        group_log_file = os.path.join('logs', f'subrank_list_group_{group_id}_{timestamp}.log')
        group_csv_file = os.path.join('logs', f'subrank_list_group_{group_id}_{timestamp}.csv')
        
        # Log group start
        with open(group_log_file, 'w') as group_log:
            group_log.write(f"{'='*40}\n")
            group_log.write(f"Group {group_id} processing started at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            group_log.write(f"Business ID: {args.business_id}\n")
            group_log.write(f"Business Key: {args.business_key}\n")
            group_log.write(f"Dir: {args.dir}\n")
            group_log.write(f"Filter sub_types: {filter_sub_types if filter_sub_types else 'None (all sub_types)'}\n")
            group_log.write(f"Rank instance file: {args.rank_instance_file}\n")
            group_log.write(f"Group Log file: {group_log_file}\n")
            group_log.write(f"Group CSV file: {group_csv_file}\n")
            group_log.write(f"Total instances in group: {len(group_instances)}\n")
            group_log.write(f"{'='*40}\n")
        
        print(f"Group {group_id} - Log file: {group_log_file}")
        print(f"Group {group_id} - CSV file: {group_csv_file}")
        
        # Open group-specific CSV file and create writer
        with open(group_csv_file, 'w', newline='') as group_csv:
            group_writer = csv.DictWriter(group_csv, fieldnames=fieldnames)
            group_writer.writeheader()
            group_csv.flush()
            
            # Execute queries for each instance in the group
            group_total_queries = 0
            for world_id, zone_id, instance_id, ranktype_id in group_instances:
                print(f"\n  Processing instance: world_id={world_id}, zone_id={zone_id}, instance_id={instance_id}, ranktype_id={ranktype_id}")
                # First query to get total count
                group_total_queries += 1
                print(f"    Initial query {group_total_queries}: query_from=1")
                original_records_found, filtered_records_saved, total_count = execute_query(world_id, zone_id, instance_id, ranktype_id, 1, args.business_id, args.business_key, args.dir, filter_sub_types, group_log_file, group_csv, group_writer)
                if total_count <= 100:
                    # If total count is 100 or less, first query already got all data
                    print(f"    Total count ({total_count}) <= 100, no additional queries needed")
                    continue
                # If total count > 100, continue with additional queries
                print(f"    Total count: {total_count}, continuing with additional queries...")
                query_from = 101  # Start from 101 since we already got 1-100
                while query_from <= total_count:
                    group_total_queries += 1
                    print(f"    Processing query {group_total_queries}: query_from={query_from}")
                    original_records_found, filtered_records_saved, _ = execute_query(world_id, zone_id, instance_id, ranktype_id, query_from, args.business_id, args.business_key, args.dir, filter_sub_types, group_log_file, group_csv, group_writer)
                    if original_records_found == 0:
                        # No more original records from API, break the loop
                        print(f"    No more records found from API, stopping queries for this instance")
                        break
                    query_from += 100  # Move to next batch
            
            # Log group completion
            with open(group_log_file, 'a') as group_log:
                group_log.write(f"\n{'='*40}\n")
                group_log.write(f"Group {group_id} processing completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                group_log.write(f"Total instances processed in group: {len(group_instances)}\n")
                group_log.write(f"Total queries executed in group: {group_total_queries}\n")
                group_log.write(f"{'='*40}\n")
            
            print(f"\nGroup {group_id} completed.")
            print(f"Group {group_id} - Total instances processed: {len(group_instances)}")
            print(f"Group {group_id} - Total queries executed: {group_total_queries}")
            print(f"Group {group_id} - Log file: {group_log_file}")
            print(f"Group {group_id} - CSV file: {group_csv_file}")
    
    # Log script completion
    with open(log_file, 'a') as f:
        f.write(f"\n{'='*40}\n")
        f.write(f"Script completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total instances processed: {len(instances)}\n")
        f.write(f"Total groups processed: {len(groups)}\n")
        f.write(f"Groups: {list(groups.keys())}\n")
        f.write(f"{'='*40}\n")

    print(f"\n{'='*50}")
    print(f"Script completed.")
    print(f"Total instances processed: {len(instances)}")
    print(f"Total groups processed: {len(groups)}")
    print(f"Groups: {list(groups.keys())}")
    print(f"Main log file: {log_file}")
    print(f"Group-specific files saved in logs/ directory")
    print(f"{'='*50}")

if __name__ == "__main__":
    main() 