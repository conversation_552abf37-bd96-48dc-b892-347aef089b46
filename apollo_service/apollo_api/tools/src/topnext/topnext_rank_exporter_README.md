# TopNext 排行榜查询导出工具 (topnext_rank_exporter)

这是一个用于查询和导出 TopNext 排行榜数据的命令行工具。支持查询子榜列表、子榜数据，并提供数据导出功能。

## 功能特点

- 查询子榜列表
- 查询子榜详细数据
- 支持分数过滤（最小分数和最大分数）
- 支持数据导出为 CSV 格式
- 支持分页查询（子榜列表和子榜数据都支持分页）
- 支持镜像查询

## 编译方法

```bash
cd apollo_service/apollo_api/tools
make
```

## 使用方法

### 基本用法

```bash
./topnext_rank_exporter [参数]
```

### 参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--dir` | 服务器地址 | tcp://10.123.16.138:13001 |
| `--business_id` | 业务ID | 10000 |
| `--business_key` | 业务密钥 | d9798cdf31c02d86b8b81cc119d94836 |
| `--world_id` | 世界ID | 1 |
| `--zone_id` | 区域ID | 1 |
| `--type` | 类型 | 1 |
| `--instance_id` | 实例ID | 1 |
| `--query_count` | 每页查询数量 | 100 |
| `--is_query_image` | 是否查询镜像 | 0 |
| `--image_index` | 镜像索引(0-8) | 0 |
| `--encrypt_key` | 加密密钥 | "" |
| `--min_score` | 最小分数过滤 | 0 |
| `--max_score` | 最大分数过滤 | 0 |
| `--export_dir` | CSV导出目录 | "" |

### 使用示例

1. 基本查询：

```bash
./topnext_rank_exporter --world_id=1 --zone_id=1 --type=1 --instance_id=1
```

2. 带分数过滤的查询：

```bash
./topnext_rank_exporter --min_score=1000 --max_score=2000
```

3. 导出数据到CSV：

```bash
./topnext_rank_exporter --export_dir=./export_data
```

4. 完整示例：

```bash
./topnext_rank_exporter --world_id=1 --zone_id=1 --type=1 --instance_id=1 --min_score=1000 --max_score=2000 --export_dir=./export_data
```

## 分页查询说明

工具支持两种分页查询：

1. 子榜列表分页查询
   - 自动获取所有页面的子榜列表数据
   - 每页默认显示100条记录
   - 可以通过 `--query_count` 参数调整每页记录数

2. 子榜数据分页查询
   - 对每个子榜自动进行分页查询
   - 每页默认显示100条记录
   - 可以通过 `--query_count` 参数调整每页记录数
   - 显示每个子榜的总记录数和总页数
   - 显示当前查询进度

## 输出说明

### 控制台输出

工具会在控制台输出以下信息：

- 子榜列表汇总信息
- 子榜列表详细数据（表格形式）
- 子榜数据汇总信息
- 子榜详细数据（表格形式）
- 导出文件信息（如果启用了导出功能）

### CSV导出

如果指定了导出目录，工具会生成以下文件：

1. `sub_rank_list.csv`：包含所有子榜的基本信息
   - 列：business_id, world_id, zone_id, type, instance_id, sub_type, sub_instance_id

2. `sub_rank_[type]_[instance_id]_[序号].csv`：每个子榜的详细数据
   - 列：rank, score, openid, timestamp, sort_field1, sort_field2, sort_field3, sort_field4, sort_field5, ext_len, ext_md5

## 注意事项

1. 确保有足够的权限创建导出目录
2. 分数过滤条件会同时应用于控制台输出和CSV导出
3. 如果导出目录已存在，工具会直接使用该目录
4. 如果导出目录不存在，工具会自动创建
5. 分页查询会自动获取所有页面的数据，可能需要一定时间

## 错误处理

工具会在以下情况输出错误信息：

- 初始化日志失败
- 初始化TopNext驱动失败
- 创建导出目录失败
- 打开文件写入失败
- 查询数据失败
- 分页查询失败

## 依赖项

- gflags
- tbus
- apollo_service_api
