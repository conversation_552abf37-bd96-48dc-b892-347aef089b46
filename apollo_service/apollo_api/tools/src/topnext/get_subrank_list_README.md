# Get Subrank List Tool

## 概述

`get_subrank_list.py` 是一个用于批量查询子榜列表数据的Python脚本。该脚本可以读取排行榜实例配置文件，执行多个查询请求，并将结果保存为CSV格式，同时支持子榜类型过滤功能。

## 功能特性

- **批量查询**: 支持从配置文件读取多个排行榜实例并批量执行查询
- **分组处理**: 按group_id对实例进行分组，每组生成独立的日志和CSV文件
- **分页查询**: 自动处理大量数据的分页查询（每页100条记录）
- **子榜过滤**: 支持指定特定的子榜类型，只保存匹配的记录到CSV
- **详细日志**: 生成详细的执行日志，包括查询命令、结果统计等
- **错误处理**: 完善的错误处理和异常捕获机制

## 依赖要求

- Python 3.6+
- 标准库模块：`os`, `csv`, `subprocess`, `argparse`, `datetime`, `typing`, `collections`
- 可执行文件：`./getsubranklist_topnext`（需要在同一目录下）

## 输入文件格式

### rank_instance.txt

脚本需要一个名为 `rank_instance.txt` 的配置文件，格式如下：

通过`select world_id, zone_id, rank_id, ranktype_id, group_id from t_topnext_rank_list where business_id = 10000;`从db中导出

```
+----------+----------+----------+----------+----------+
| world_id | zone_id  | rank_id  |ranktype_id| group_id |
+----------+----------+----------+----------+----------+
|    1     |    100   |    1001  |    5     |    1     |
|    1     |    100   |    1002  |    5     |    1     |
|    2     |    200   |    2001  |    6     |    2     |
+----------+----------+----------+----------+----------+
```

字段说明：
- `world_id`: 世界ID
- `zone_id`: 区域ID  
- `rank_id`: 排行榜ID（作为instance_id使用）
- `ranktype_id`: 排行榜类型ID（作为type使用）
- `group_id`: 分组ID，相同group_id的实例会被分组处理

## 命令行参数

### 必需参数

- `--business_id`: 业务ID，用于API认证
- `--business_key`: 业务密钥，用于API认证  
- `--dir`: 服务器地址，TCP端点格式（如：tcp://*************:9050）

### 可选参数

- `--filter_sub_types`: 子榜类型过滤器，逗号分隔的列表（如："1,2,3"）
- `--rank_instance_file`: 配置文件路径（默认：rank_instance.txt）

## 使用方法

### 基本用法（保存所有子榜类型）

```bash
python3 get_subrank_list.py \
    --business_id=your_business_id \
    --business_key=your_business_key \
    --dir=tcp://*************:9050
```

### 使用子榜类型过滤

```bash
# 只保存子榜类型为1,2,3的记录
python3 get_subrank_list.py \
    --business_id=your_business_id \
    --business_key=your_business_key \
    --dir=tcp://*************:9050 \
    --filter_sub_types="1,2,3"

# 只保存子榜类型为5的记录  
python3 get_subrank_list.py \
    --business_id=your_business_id \
    --business_key=your_business_key \
    --dir=tcp://*************:9050 \
    --filter_sub_types="5"
```

### 指定自定义配置文件

```bash
python3 get_subrank_list.py \
    --business_id=your_business_id \
    --business_key=your_business_key \
    --dir=tcp://*************:9050 \
    --rank_instance_file=custom_instances.txt
```

## 输出文件

脚本会在 `logs/` 目录下生成以下文件：

### 主日志文件
- `subrank_list_YYYYMMDD_HHMMSS.log`: 主执行日志

### 分组文件
- `subrank_list_group_{group_id}_YYYYMMDD_HHMMSS.log`: 每个组的详细日志
- `subrank_list_group_{group_id}_YYYYMMDD_HHMMSS.csv`: 每个组的查询结果

### CSV文件格式

输出的CSV文件包含以下字段：

```csv
world_id,zone_id,instance_id,type,sub_type,sub_instance_id
1,100,1001,5,1,10001
1,100,1001,5,2,10002
```

字段说明：
- `world_id`: 世界ID
- `zone_id`: 区域ID
- `instance_id`: 实例ID（来自rank_id）
- `type`: 类型（来自ranktype_id）
- `sub_type`: 子榜类型
- `sub_instance_id`: 子实例ID

## 工作流程

1. **参数解析**: 解析命令行参数和过滤设置
2. **文件读取**: 读取并解析rank_instance.txt配置文件
3. **分组处理**: 按group_id对实例进行分组
4. **查询执行**: 
   - 对每个实例执行初始查询（query_from=1）
   - 如果总数超过100，继续分页查询
   - 应用子榜类型过滤（如果指定）
5. **结果保存**: 将过滤后的结果写入CSV文件
6. **日志记录**: 记录详细的执行信息和统计数据

## 错误处理

- **文件不存在**: 如果配置文件不存在，脚本会报错并退出
- **查询失败**: 查询失败时会记录错误信息并继续处理下一个实例
- **解析错误**: 配置文件格式错误时会跳过无效行
- **网络错误**: 网络连接问题会被捕获并记录

## 性能特性

- **内存过滤**: 子榜类型过滤在内存中进行，避免不必要的磁盘写入
- **分页处理**: 自动处理大数据集的分页查询
- **缓冲刷新**: 及时刷新文件缓冲区，确保数据实时写入
- **分组并行**: 不同组的处理相互独立，便于并行优化

## 注意事项

1. 确保 `./getsubranklist_topnext` 可执行文件存在且有执行权限
2. 确保有足够的磁盘空间存储日志和CSV文件
3. 网络连接稳定，避免查询中断
4. 子榜类型过滤区分大小写，请确保输入正确的值
5. 大量数据查询可能需要较长时间，请耐心等待

## 故障排除

### 常见问题

1. **"No instances found"错误**
   - 检查rank_instance.txt文件是否存在
   - 检查文件格式是否正确

2. **"Query failed"错误**  
   - 检查网络连接
   - 验证business_id和business_key是否正确
   - 确认dir参数格式正确

3. **"Permission denied"错误**
   - 检查getsubranklist_topnext文件权限
   - 确保logs目录可写

### 调试建议

- 查看详细日志文件了解具体错误信息
- 使用小数据集测试配置是否正确
- 检查命令行参数格式和值的正确性

## 示例输出

### 控制台输出示例

```
Found 4 instances to process
Using business_id: your_business_id
Using business_key: your_business_key
Using dir: tcp://*************:9050
Filtering sub_types: {'1', '2', '3'}
Script started. Log file: logs/subrank_list_20231201_143022.log
CSV file: logs/subrank_list_20231201_143022.csv
Found 2 groups to process: [1, 2]

==================================================
Processing Group 1 with 2 instances
==================================================
Group 1 - Log file: logs/subrank_list_group_1_20231201_143022.log
Group 1 - CSV file: logs/subrank_list_group_1_20231201_143022.csv

  Processing instance: world_id=1, zone_id=100, instance_id=1001, ranktype_id=5
    Initial query 1: query_from=1
Filtered 150 records to 45 records based on sub_type filter
Found 45 records, total count: 150
```

### 日志文件示例

```
========================================
Script started at 2023-12-01 14:30:22
Business ID: your_business_id
Business Key: your_business_key
Dir: tcp://*************:9050
Filter sub_types: {'1', '2', '3'}
Rank instance file: rank_instance.txt
Log file: logs/subrank_list_20231201_143022.log
CSV file: logs/subrank_list_20231201_143022.csv
Total instances to process: 4
========================================
```
