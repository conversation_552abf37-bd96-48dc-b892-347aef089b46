/*!
 * Copyright (c) 2015 Tencent all rights reserved
 *
 * TopNext 排行榜查询导出工具
 * 用于查询和导出 TopNext 排行榜的子榜数据
 */

#include "apollo_service.h"
#include "md5.h"
#include "tloghelp/tlogload.h"
#include "tool_common.h"

#include <errno.h>
#include <fcntl.h>
#include <gflags/gflags.h>
#include <sys/stat.h>
#include <tbus/tbus.h>
#include <unistd.h>
#include <atomic>
#include <chrono>
#include <cstdio>
#include <cstdlib>
#include <cstring>
#include <ctime>
#include <fstream>
#include <iostream>
#include <map>
#include <memory>
#include <set>
#include <sstream>
#include <string>
#include <thread>
#include <vector>

using std::atomic;
using std::cerr;
using std::cout;
using std::endl;
using std::map;
using std::ofstream;
using std::set;
using std::string;
using std::stringstream;
using std::unique_ptr;
using std::vector;
using namespace apollo_service_api;

DEFINE_string(dir, "tcp://*************:13001", "Dir server ip port");
DEFINE_int32(business_id, 10000, "Business Id");
DEFINE_string(business_key, "d9798cdf31c02d86b8b81cc119d94836", "Business Key");
DEFINE_int32(world_id, 1, "World ID");
DEFINE_int32(zone_id, 1, "Zone ID");
DEFINE_int32(type, 1, "Type");
DEFINE_int32(instance_id, 1, "Instance ID");
DEFINE_int32(query_count, 100, "Number of items to query");
DEFINE_int32(is_query_image, 0, "Whether to query image");
DEFINE_int32(image_index, 0, "Image index (0-8)");
DEFINE_string(encrypt_key, "", "Encrypt key");
DEFINE_int32(min_score, 0, "Minimum score to filter players");
DEFINE_int32(max_score, 0, "Maximum score to filter players (0 means no limit)");
DEFINE_string(export_dir, "", "Directory to export CSV files (empty means no export)");
DEFINE_bool(auto_clear_users, false, "Automatically clear users from sub ranks after exporting");
DEFINE_string(clear_user_list, "", "Comma-separated list of openids to clear (empty means clear all users)");

// 辅助函数定义（需要在 FLAGS 定义之后）
namespace {

// 系统配置常量
constexpr int kMaxErrorBufferSize = 1024;
constexpr int kMaxCallbackBufferSize = 1024;
constexpr int kDefaultTimeoutMs = 3000;
constexpr int kSleepIntervalMs = 10;
constexpr int kDefaultCallbackId = 0x1234;
constexpr int kDefaultPageSize = 100;

// 表格显示常量
constexpr int kRankColumnWidth = 8;
constexpr int kScoreColumnWidth = 10;
constexpr int kOpenIdColumnWidth = 20;
constexpr int kTimeColumnWidth = 20;
constexpr int kFieldColumnWidth = 12;
constexpr int kExtLenColumnWidth = 10;
constexpr int kExtMd5ColumnWidth = 50;

// 参数验证常量
constexpr int kMaxPageSize = 100;
constexpr int kMinImageIndex = 0;
constexpr int kMaxImageIndex = 8;

// C++11 compatible make_unique implementation
template <typename T, typename... Args>
std::unique_ptr<T> make_unique(Args&&... args) {
  return std::unique_ptr<T>(new T(std::forward<Args>(args)...));
}

// 分数过滤辅助函数
bool IsScoreInRange(uint32_t score) {
  bool min_check = (FLAGS_min_score <= 0) || (score >= static_cast<uint32_t>(FLAGS_min_score));
  bool max_check = (FLAGS_max_score <= 0) || (score <= static_cast<uint32_t>(FLAGS_max_score));
  return min_check && max_check;
}

// 时间戳格式化辅助函数
string FormatTimestamp(uint64_t timestamp_ms) {
  time_t timestamp = timestamp_ms / 1000;
  struct tm tm_info;
  localtime_r(&timestamp, &tm_info);
  char time_str[32] = {0};
  strftime(time_str, sizeof(time_str), "%Y-%m-%d %H:%M:%S", &tm_info);
  return string(time_str);
}

// 创建表格分隔线
string CreateTableSeparator() {
  return "+" + string(kRankColumnWidth + 2, '-') + "+" + string(kScoreColumnWidth + 2, '-') + "+" +
         string(kOpenIdColumnWidth + 2, '-') + "+" + string(kTimeColumnWidth + 2, '-') + "+" +
         string(kFieldColumnWidth + 2, '-') + "+" + string(kFieldColumnWidth + 2, '-') + "+" +
         string(kFieldColumnWidth + 2, '-') + "+" + string(kFieldColumnWidth + 2, '-') + "+" +
         string(kFieldColumnWidth + 2, '-') + "+" + string(kExtLenColumnWidth + 2, '-') + "+" +
         string(kExtMd5ColumnWidth + 2, '-') + "+";
}

// 参数验证辅助函数
bool ValidatePageParameters(int page_num, int page_size) {
  return page_num > 0 && page_size > 0 && page_size <= kMaxPageSize;
}

bool ValidateImageIndex(int image_index) { return image_index >= kMinImageIndex && image_index <= kMaxImageIndex; }

// 显示分数过滤条件
void PrintScoreFilterInfo() {
  if (FLAGS_min_score > 0 || FLAGS_max_score > 0) {
    cout << "分数过滤: ";
    if (FLAGS_min_score > 0) {
      cout << ">= " << FLAGS_min_score;
    }
    if (FLAGS_min_score > 0 && FLAGS_max_score > 0) {
      cout << " 且 ";
    }
    if (FLAGS_max_score > 0) {
      cout << "<= " << FLAGS_max_score;
    }
    cout << endl;
  }
}

std::string CalcMD5(const unsigned char* ext, uint ext_len) {
  if (ext_len == 0) {
    return "";
  }
  char output[255] = {0};
  unsigned char hash_buf[APS_MD5_DIGEST_LENGTH];
  aps_md5hash_buffer(hash_buf, ext, ext_len);
  aps_md5hash2str(hash_buf, output, sizeof(output));
  return output;
}

}  // namespace

TLOGCATEGORYINST* g_logger;
std::atomic<int> g_status{0};
int g_callback_id = kDefaultCallbackId;

const string LOG_CONFIG = R"(
<?xml version="1.0" encoding="GBK" standalone="yes" ?>
<TLOGConf version="2">
    <Magic>1548 </Magic>
    <PriorityHigh>NULL </PriorityHigh>
    <PriorityLow>DEBUG </PriorityLow>
    <DelayInit>0 </DelayInit>
    <SuppressError>1 </SuppressError>
    <Count>6 </Count>
    <CategoryList type="TLOGCategory">
        <Name>text</Name>
        <PriorityHigh>INFO </PriorityHigh>
        <PriorityLow>TRACE </PriorityLow>
        <Filter type="TLOGFilterVec">
            <Count>1 </Count>
            <Filters type="TLOGFilter">
                <IDFilter type="IntFilter">
                    <Start>0 </Start>
                    <Count>100000000 </Count>
                    <Mod>0 </Mod>
                    <ModStart>0 </ModStart>
                    <ModCount>0 </ModCount>
                </IDFilter>
                <ClsFilter type="IntFilter">
                    <Start>0 </Start>
                    <Count>100 </Count>
                    <Mod>0 </Mod>
                    <ModStart>0 </ModStart>
                    <ModCount>0 </ModCount>
                </ClsFilter>
            </Filters>
        </Filter>
        <LevelDispatch>0 </LevelDispatch>
        <MustSucc>0 </MustSucc>
        <MaxMsgSize>102400 </MaxMsgSize>
        <Format></Format>
        <ForwardCat>texttrace</ForwardCat>
        <Device type="TLOGDevAny">
            <Type>NO </Type>
        </Device>
    </CategoryList>
    <CategoryList type="TLOGCategory">
        <Name>texttrace</Name>
        <PriorityHigh>NULL </PriorityHigh>
        <PriorityLow>TRACE </PriorityLow>
        <Filter type="TLOGFilterVec">
            <Count>0 </Count>
        </Filter>
        <LevelDispatch>0 </LevelDispatch>
        <MustSucc>0 </MustSucc>
        <MaxMsgSize>102400 </MaxMsgSize>
        <Format>[%d.%u][%h][(%f:%l) (%F)][%M][%p] %m%n</Format>
        <ForwardCat>texterr</ForwardCat>
        <Device type="TLOGDevAny">
            <Type>FILE </Type>
            <Device type="TLOGDevSelector">
                <File type="TLOGDevFile">
                    <Pattern>./log/test_service.log</Pattern>
                    <BuffSize>0 </BuffSize>
                    <SizeLimit>10485760 </SizeLimit>
                    <Precision>1 </Precision>
                    <MaxRotate>2 </MaxRotate>
                    <SyncTime>0 </SyncTime>
                    <NoFindLatest>0 </NoFindLatest>
                    <RotateStick>0 </RotateStick>
                </File>
            </Device>
        </Device>
    </CategoryList>
    <CategoryList type="TLOGCategory">
        <Name>texterr</Name>
        <PriorityHigh>NULL </PriorityHigh>
        <PriorityLow>ERROR </PriorityLow>
        <Filter type="TLOGFilterVec">
            <Count>0 </Count>
        </Filter>
        <LevelDispatch>0 </LevelDispatch>
        <MustSucc>0 </MustSucc>
        <MaxMsgSize>102400 </MaxMsgSize>
        <Format>[%d.%u][%h][(%f:%l) (%F)][%M][%p] %m%n</Format>
        <ForwardCat></ForwardCat>
        <Device type="TLOGDevAny">
            <Type>FILE </Type>
            <Device type="TLOGDevSelector">
                <File type="TLOGDevFile">
                    <Pattern>./log/test_service.error</Pattern>
                    <BuffSize>0 </BuffSize>
                    <SizeLimit>10485760 </SizeLimit>
                    <Precision>1 </Precision>
                    <MaxRotate>2 </MaxRotate>
                    <SyncTime>0 </SyncTime>
                    <NoFindLatest>0 </NoFindLatest>
                    <RotateStick>0 </RotateStick>
                </File>
            </Device>
        </Device>
    </CategoryList>
</TLOGConf>
)";

struct SubRankListResult {
  uint32_t total_count{0};
  uint32_t current_count{0};
  std::vector<TopNextSubRankInfo> items;
};

// 添加子榜数据结构
struct SubRankData {
  uint32_t total_count{0};
  uint32_t current_count{0};
  uint32_t sub_type{0};         // 添加子榜类型
  uint32_t sub_instance_id{0};  // 添加子榜实例ID
  std::vector<TopNextUserRankInfo> items;
};

// 添加清理用户结果结构
struct ClearUserResult {
  bool success{false};
  uint32_t sub_type{0};
  uint32_t sub_instance_id{0};
  string openid;
  string error_message;
};

class TopNextResponseHandler : public TopNextResponseListener {
 public:
  TopNextResponseHandler() = default;
  ~TopNextResponseHandler() override = default;

  int OnTopNextGetSubRankListSubRankRsp(TopNextGetSubRankListSubRankRsp& response) override {
    g_status = 0;
    int result = -1;
    char error_buffer[kMaxErrorBufferSize] = {0};
    char callback_buffer[kMaxCallbackBufferSize] = {0};  // 初始化缓冲区
    uint32_t callback_len = sizeof(callback_buffer);

    int ret = response.GetResultInfo(&result, error_buffer, sizeof(error_buffer), callback_buffer, &callback_len);
    if (APOLLO_NO_ERR != ret) {
      cerr << "Failed to get result info: ret=" << ret << ", error=" << error_buffer
           << ", callback_len=" << callback_len << endl;
      return -1;
    }

    // 添加callback_len边界检查
    if (callback_len >= sizeof(callback_buffer)) {
      cerr << "Warning: callback data truncated, len=" << callback_len << endl;
      callback_len = sizeof(callback_buffer) - 1;
      callback_buffer[callback_len] = '\0';
    }

    if (0 != result) {
      cerr << "Response failed: result=" << result << ", error=" << error_buffer << ", callback_len=" << callback_len
           << endl;
      return -1;
    }

    TopNextGetSubRankListSubRankResult rank_result;
    ret = response.GetData(rank_result);
    if (0 != ret) {
      cerr << "Failed to get data: ret=" << ret << endl;
      return -1;
    }

    // 更新结果
    current_result_.total_count = rank_result.uiTotalCount;
    current_result_.current_count = rank_result.uiResultCount;
    current_result_.items.clear();
    current_result_.items.reserve(rank_result.uiResultCount);

    // 添加数组边界检查
    for (uint32_t i = 0; i < rank_result.uiResultCount; ++i) {
      if (i >= sizeof(rank_result.astResult) / sizeof(rank_result.astResult[0])) {
        cerr << "Warning: Result count exceeds array bounds, truncating at " << i << endl;
        current_result_.current_count = i;
        break;
      }
      current_result_.items.push_back(rank_result.astResult[i].stSubRankInfo);
    }

    return 0;
  }

  int OnTopNextGetTopSubRankRsp(TopNextGetTopSubRankRsp& response) override {
    g_status = 0;
    int result = -1;
    char error_buffer[kMaxErrorBufferSize] = {0};
    char callback_buffer[kMaxCallbackBufferSize] = {0};  // 初始化缓冲区
    uint32_t callback_len = sizeof(callback_buffer);

    int ret = response.GetResultInfo(&result, error_buffer, sizeof(error_buffer), callback_buffer, &callback_len);
    if (APOLLO_NO_ERR != ret) {
      cerr << "Failed to get result info: ret=" << ret << ", error=" << error_buffer
           << ", callback_len=" << callback_len << endl;
      return -1;
    }

    // 添加callback_len边界检查
    if (callback_len >= sizeof(callback_buffer)) {
      cerr << "Warning: callback data truncated, len=" << callback_len << endl;
      callback_len = sizeof(callback_buffer) - 1;
      callback_buffer[callback_len] = '\0';
    }

    if (0 != result) {
      cerr << "Response failed: result=" << result << ", error=" << error_buffer << ", callback_len=" << callback_len
           << endl;
      return -1;
    }

    TopNextGetTopSubRankResult rank_result;
    ret = response.GetData(rank_result);
    if (0 != ret) {
      cerr << "Failed to get data: ret=" << ret << endl;
      return -1;
    }

    // 更新子榜数据结果
    current_rank_data_.total_count = rank_result.uiTotalCount;
    current_rank_data_.current_count = rank_result.uiResultCount;
    current_rank_data_.items.clear();
    current_rank_data_.items.reserve(rank_result.uiResultCount);

    // 添加数组边界检查
    for (uint32_t i = 0; i < rank_result.uiResultCount; ++i) {
      if (i >= sizeof(rank_result.astResult) / sizeof(rank_result.astResult[0])) {
        cerr << "Warning: Result count exceeds array bounds, truncating at " << i << endl;
        current_rank_data_.current_count = i;
        break;
      }
      current_rank_data_.items.push_back(rank_result.astResult[i].stUserRankInfo);
    }

    return 0;
  }

  int OnTopNextClearOneUserSubRankRsp(TopNextClearOneUserSubRankRsp& response) override {
    g_status = 0;
    int result = -1;
    char error_buffer[kMaxErrorBufferSize] = {0};
    char callback_buffer[kMaxCallbackBufferSize] = {0};
    uint32_t callback_len = sizeof(callback_buffer);

    int ret = response.GetResultInfo(&result, error_buffer, sizeof(error_buffer), callback_buffer, &callback_len);
    if (APOLLO_NO_ERR != ret) {
      cerr << "Failed to get clear user result info: ret=" << ret << ", error=" << error_buffer << endl;
      current_clear_result_.success = false;
      current_clear_result_.error_message = "Failed to get result info";
      return -1;
    }

    if (0 != result) {
      cerr << "Clear user response failed: result=" << result << ", error=" << error_buffer << endl;
      current_clear_result_.success = false;
      current_clear_result_.error_message = error_buffer;
      return -1;
    }

    TopNextClearOneUserSubRankResult clear_result;
    ret = response.GetData(clear_result);
    if (0 != ret) {
      cerr << "Failed to get clear user data: ret=" << ret << endl;
      current_clear_result_.success = false;
      current_clear_result_.error_message = "Failed to get clear data";
      return -1;
    }

    current_clear_result_.success = true;
    current_clear_result_.error_message.clear();

    cout << "Clear user operation completed successfully. Cleared from " << clear_result.uiResultCount << " sub ranks."
         << endl;
    for (uint32_t i = 0; i < clear_result.uiResultCount; ++i) {
      const auto& op_result = clear_result.astResult[i];
      cout << "  Sub rank type=" << op_result.stSubRankInfo.uiType
           << ", instance_id=" << op_result.stSubRankInfo.uiInstanceId << ", result=" << op_result.iResult << endl;
    }

    return 0;
  }

  int OnTopNextResponseTimeOuted(const char* data, uint32_t len) override {
    cerr << "Request timeout" << endl;
    g_status = 0;
    return 0;
  }

  const SubRankListResult& GetCurrentResult() const { return current_result_; }
  const SubRankData& GetCurrentRankData() const { return current_rank_data_; }
  const ClearUserResult& GetCurrentClearResult() const { return current_clear_result_; }

 private:
  SubRankListResult current_result_;
  SubRankData current_rank_data_;
  ClearUserResult current_clear_result_;
};

class TopnextDriver {
 public:
  explicit TopnextDriver(TLOGCATEGORYINST* logger) : logger_(logger) {}
  ~TopnextDriver() = default;

  bool Initialize(const string& service_url, int business_id, const string& business_key,
                  const string& encrypt_key = "") {
    apollo_api_ = make_unique<ApolloService>(logger_);
    if (!apollo_api_) {
      cerr << "Failed to create ApolloService instance" << endl;
      return false;
    }

    int ret = apollo_api_->AddServiceURL(service_url.c_str());
    if (ret != 0) {
      cerr << "Failed to add service URL: ret=" << ret << endl;
      return false;
    }

    ret = apollo_api_->SetTopNextCallBack(&response_handler_);
    if (ret != 0) {
      cerr << "Failed to set callback: ret=" << ret << endl;
      return false;
    }

    ret = apollo_api_->Init(business_id, business_key.c_str(), APOLLO_SERVICE_API_VERSION, kDefaultTimeoutMs,
                            encrypt_key.c_str());
    if (ret != 0) {
      cerr << "Failed to initialize ApolloService: ret=" << ret << endl;
      return false;
    }

    return true;
  }

  bool SendSubRankListRequest(int world_id, int zone_id, int type, int instance_id, int page_num = 1,
                              int page_size = kDefaultPageSize, bool query_image = false, int image_index = 0) {
    if (!apollo_api_) {
      cerr << "ApolloService not initialized" << endl;
      return false;
    }

    // 参数验证
    if (!ValidatePageParameters(page_num, page_size)) {
      cerr << "Invalid parameters: page_num=" << page_num << ", page_size=" << page_size << " (page_size should be 1-"
           << kMaxPageSize << ")" << endl;
      return false;
    }

    if (!ValidateImageIndex(image_index)) {
      cerr << "Invalid image_index: " << image_index << " (should be " << kMinImageIndex << "-" << kMaxImageIndex << ")"
           << endl;
      return false;
    }

    TopNextService* topnext_api = apollo_api_->GetTopNextService();
    if (!topnext_api) {
      cerr << "Failed to get TopNextService" << endl;
      return false;
    }

    TopNextGetSubRankListSubRankReq* req = topnext_api->Get_TopNextGetSubRankListSubRankReq(
        reinterpret_cast<char*>(&g_callback_id), sizeof(g_callback_id));
    if (!req) {
      cerr << "Failed to create request" << endl;
      return false;
    }

    TopNextRankInfo rank_info;
    rank_info.uiWorldId = world_id;
    rank_info.uiZoneId = zone_id;
    rank_info.uiType = type;
    rank_info.uiInstanceId = instance_id;

    int query_from = (page_num - 1) * page_size + 1;
    int ret = req->SetPara(rank_info, query_from, page_size, query_image, image_index);
    if (ret != 0) {
      cerr << "Failed to set request parameters: ret=" << ret << endl;
      return false;
    }

    ret = topnext_api->SendReq(req);
    if (ret != 0) {
      cerr << "Failed to send request: ret=" << ret << endl;
      return false;
    }

    return true;
  }

  bool SendTopSubRankRequest(int world_id, int zone_id, int type, int instance_id, int sub_type, int sub_instance_id,
                             int page_num = 1, int page_size = kDefaultPageSize, bool query_image = false,
                             int image_index = 0) {
    if (!apollo_api_) {
      cerr << "ApolloService not initialized" << endl;
      return false;
    }

    // 参数验证
    if (!ValidatePageParameters(page_num, page_size)) {
      cerr << "Invalid parameters: page_num=" << page_num << ", page_size=" << page_size << " (page_size should be 1-"
           << kMaxPageSize << ")" << endl;
      return false;
    }

    if (!ValidateImageIndex(image_index)) {
      cerr << "Invalid image_index: " << image_index << " (should be " << kMinImageIndex << "-" << kMaxImageIndex << ")"
           << endl;
      return false;
    }

    TopNextService* topnext_api = apollo_api_->GetTopNextService();
    if (!topnext_api) {
      cerr << "Failed to get TopNextService" << endl;
      return false;
    }

    TopNextGetTopSubRankReq* req =
        topnext_api->Get_TopNextGetTopSubRankReq(reinterpret_cast<char*>(&g_callback_id), sizeof(g_callback_id));
    if (!req) {
      cerr << "Failed to create request" << endl;
      return false;
    }

    TopNextRankInfo rank_info;
    rank_info.uiWorldId = world_id;
    rank_info.uiZoneId = zone_id;
    rank_info.uiType = type;
    rank_info.uiInstanceId = instance_id;

    TopNextSubRankInfo sub_rank_info;
    sub_rank_info.uiType = sub_type;
    sub_rank_info.uiInstanceId = sub_instance_id;

    int query_from = (page_num - 1) * page_size + 1;
    int ret = req->SetPara(rank_info, sub_rank_info, query_from, page_size, query_image, image_index);
    if (ret != 0) {
      cerr << "Failed to set request parameters: ret=" << ret << endl;
      return false;
    }

    ret = topnext_api->SendReq(req);
    if (ret != 0) {
      cerr << "Failed to send request: ret=" << ret << endl;
      return false;
    }

    return true;
  }

  bool SendClearOneUserSubRankRequest(int world_id, int zone_id, int type, int instance_id, const string& openid,
                                      const vector<TopNextSubRankInfo>& sub_ranks) {
    if (!apollo_api_) {
      cerr << "ApolloService not initialized" << endl;
      return false;
    }

    // 添加输入参数验证
    if (openid.empty()) {
      cerr << "OpenID cannot be empty" << endl;
      return false;
    }

    if (sub_ranks.empty()) {
      cerr << "Sub rank list cannot be empty" << endl;
      return false;
    }

    TopNextService* topnext_api = apollo_api_->GetTopNextService();
    if (!topnext_api) {
      cerr << "Failed to get TopNextService" << endl;
      return false;
    }

    TopNextClearOneUserSubRankReq* req =
        topnext_api->Get_TopNextClearOneUserSubRankReq(reinterpret_cast<char*>(&g_callback_id), sizeof(g_callback_id));
    if (!req) {
      cerr << "Failed to create clear user request" << endl;
      return false;
    }

    TopNextRankInfo rank_info;
    rank_info.uiWorldId = world_id;
    rank_info.uiZoneId = zone_id;
    rank_info.uiType = type;
    rank_info.uiInstanceId = instance_id;

    // 创建子榜信息集合
    TopNextSubRankInfoCollection sub_rank_collection;
    for (const auto& sub_rank : sub_ranks) {
      sub_rank_collection.Add(sub_rank);
    }

    int ret = req->SetPara(rank_info, openid.c_str(), sub_rank_collection);
    if (ret != 0) {
      cerr << "Failed to set clear user request parameters: ret=" << ret << endl;
      return false;
    }

    ret = topnext_api->SendReq(req);
    if (ret != 0) {
      cerr << "Failed to send clear user request: ret=" << ret << endl;
      return false;
    }

    return true;
  }

  const SubRankListResult& GetCurrentResult() const { return response_handler_.GetCurrentResult(); }
  const SubRankData& GetCurrentRankData() const { return response_handler_.GetCurrentRankData(); }
  const ClearUserResult& GetCurrentClearResult() const { return response_handler_.GetCurrentClearResult(); }

  void Update() {
    if (apollo_api_) {
      apollo_api_->Update();
    }
  }

  bool IsRunning() const { return g_status != 0; }

 private:
  TLOGCATEGORYINST* logger_;
  unique_ptr<ApolloService> apollo_api_;
  TopNextResponseHandler response_handler_;
};

int InitLog() {
  if (access("./topnext_log.xml", F_OK) != 0) {
    int fd = open("./topnext_log.xml", O_CREAT | O_RDWR | O_APPEND, 0666);
    if (fd < 0) {
      printf("open file[%s] in append mode failed!\n", "./topnext_log.xml");
      return -1;
    }
    int ret = write(fd, LOG_CONFIG.c_str(), LOG_CONFIG.length());
    if (ret != static_cast<int>(LOG_CONFIG.length())) {
      cerr << "write record fail!datalen[" << LOG_CONFIG.length() << "] return[" << ret << "]" << endl;
      close(fd);  // 关闭文件描述符
      return -5;
    }
    close(fd);  // 关闭文件描述符
    cout << "create log file[./topnext_log.xml] succ!" << endl;
  }
  LPTLOGCTX logctx = tlog_init_from_file("./topnext_log.xml");
  if (NULL == logctx) {
    printf("tlog_init_from_file ./topnext_log.xml failed\n");
    return -1;
  }
  g_logger = tlog_get_category(logctx, "text");
  if (NULL == g_logger) {
    printf("tlog_get_category  logctx failed\n");
    return -1;
  }
  return 0;
}

// 添加获取所有页面数据的函数
vector<SubRankListResult> GetAllSubRankList(TopnextDriver& driver, int world_id, int zone_id, int type, int instance_id,
                                            int page_size = kDefaultPageSize, bool query_image = false,
                                            int image_index = 0) {
  vector<SubRankListResult> all_results;

  // 添加输入参数验证
  if (page_size <= 0 || page_size > 100) {
    cerr << "Invalid page_size: " << page_size << " (should be 1-100)" << endl;
    return all_results;
  }

  // 设置状态为运行中
  g_status = 1;

  // 发送第一页请求
  if (!driver.SendSubRankListRequest(world_id, zone_id, type, instance_id, 1, page_size, query_image, image_index)) {
    cerr << "Failed to send first page request" << endl;
    return all_results;
  }

  // 等待第一页响应
  // 添加超时机制防止无限等待
  auto start_time = std::chrono::steady_clock::now();
  const auto timeout_duration = std::chrono::milliseconds(kDefaultTimeoutMs);

  while (driver.IsRunning()) {
    std::this_thread::sleep_for(std::chrono::milliseconds(kSleepIntervalMs));
    driver.Update();

    auto current_time = std::chrono::steady_clock::now();
    if (current_time - start_time > timeout_duration) {
      cerr << "Timeout waiting for first page response" << endl;
      return all_results;
    }
  }

  // 获取第一页结果
  const SubRankListResult& first_page = driver.GetCurrentResult();
  all_results.push_back(first_page);

  // 计算总页数
  uint32_t total_count = first_page.total_count;
  // 防止除零错误
  if (page_size <= 0) {
    cerr << "Invalid page_size: " << page_size << endl;
    return all_results;
  }
  int total_pages = (total_count + page_size - 1) / page_size;

  cout << "Total items: " << total_count << ", Total pages: " << total_pages << endl;

  // 获取剩余页面
  for (int page = 2; page <= total_pages; ++page) {
    cout << "Getting page " << page << " of " << total_pages << "..." << endl;

    // 重置状态为运行中
    g_status = 1;

    // 发送请求
    if (!driver.SendSubRankListRequest(world_id, zone_id, type, instance_id, page, page_size, query_image,
                                       image_index)) {
      cerr << "Failed to send page " << page << " request" << endl;
      break;
    }

    // 等待响应
    // 添加超时机制防止无限等待
    start_time = std::chrono::steady_clock::now();
    while (driver.IsRunning()) {
      std::this_thread::sleep_for(std::chrono::milliseconds(kSleepIntervalMs));
      driver.Update();

      auto current_time = std::chrono::steady_clock::now();
      if (current_time - start_time > timeout_duration) {
        cerr << "Timeout waiting for page " << page << " response" << endl;
        break;
      }
    }

    // 保存结果
    all_results.push_back(driver.GetCurrentResult());
  }

  return all_results;
}

// 添加打印所有结果的函数
void PrintAllResults(const vector<SubRankListResult>& results) {
  if (results.empty()) {
    cout << "No results to display" << endl;
    return;
  }

  cout << "\n=== 查询结果汇总 ===" << endl;
  cout << "总记录数: " << results[0].total_count << endl;
  cout << "总页数: " << results.size() << endl;

  // 计算实际获取的记录数
  uint32_t actual_count = 0;
  for (const auto& page_result : results) {
    actual_count += page_result.current_count;
  }
  cout << "实际获取记录数: " << actual_count << endl;
  cout << "\n=== 详细数据 ===" << endl;

  // 定义列宽
  const int id_width = 12;
  const int type_width = 12;
  const int sub_instance_width = 15;  // 增加sub_instance_id的宽度

  // 构建分隔线
  string separator = "+" + string(id_width + 2, '-') + "+" + string(id_width + 2, '-') + "+" +
                     string(id_width + 2, '-') + "+" + string(type_width + 2, '-') + "+" + string(id_width + 2, '-') +
                     "+" + string(type_width + 2, '-') + "+" + string(sub_instance_width + 2, '-') + "+";

  // 构建格式字符串
  string header_format = "| %-" + std::to_string(id_width) + "s |" + " %-" + std::to_string(id_width) + "s |" + " %-" +
                         std::to_string(id_width) + "s |" + " %-" + std::to_string(type_width) + "s |" + " %-" +
                         std::to_string(id_width) + "s |" + " %-" + std::to_string(type_width) + "s |" + " %-" +
                         std::to_string(sub_instance_width) + "s |";

  string data_format = "| %-" + std::to_string(id_width) + "d |" + " %-" + std::to_string(id_width) + "d |" + " %-" +
                       std::to_string(id_width) + "d |" + " %-" + std::to_string(type_width) + "d |" + " %-" +
                       std::to_string(id_width) + "d |" + " %-" + std::to_string(type_width) + "d |" + " %-" +
                       std::to_string(sub_instance_width) + "d |";

  // 打印表头边框
  cout << separator << endl;

  // 打印表头
  printf(header_format.c_str(), "business_id", "world_id", "zone_id", "type", "instance_id", "sub_type",
         "sub_instance_id");
  cout << endl;

  // 打印表头下方边框
  cout << separator << endl;

  // 打印数据行
  for (const auto& page_result : results) {
    for (const auto& item : page_result.items) {
      printf(data_format.c_str(), FLAGS_business_id, FLAGS_world_id, FLAGS_zone_id, FLAGS_type, FLAGS_instance_id,
             item.uiType, item.uiInstanceId);
      cout << endl;
    }
  }

  // 打印表格底部边框
  cout << separator << endl;
}

// 添加获取子榜数据的函数
vector<SubRankData> GetAllSubRankData(TopnextDriver& driver, const vector<SubRankListResult>& sub_rank_list,
                                      int page_size = kDefaultPageSize) {
  vector<SubRankData> all_sub_rank_data;

  cout << "\n开始获取子榜数据..." << endl;

  for (const auto& page_result : sub_rank_list) {
    for (const auto& sub_rank : page_result.items) {
      cout << "获取子榜数据: type=" << sub_rank.uiType << ", instance_id=" << sub_rank.uiInstanceId << endl;

      // 创建一个新的SubRankData对象来存储这个子榜的所有数据
      SubRankData sub_rank_data;
      sub_rank_data.total_count = 0;
      sub_rank_data.current_count = 0;
      sub_rank_data.sub_type = sub_rank.uiType;
      sub_rank_data.sub_instance_id = sub_rank.uiInstanceId;
      sub_rank_data.items.clear();

      // 设置状态为运行中
      g_status = 1;

      // 发送第一页请求
      if (!driver.SendTopSubRankRequest(FLAGS_world_id, FLAGS_zone_id, FLAGS_type, FLAGS_instance_id, sub_rank.uiType,
                                        sub_rank.uiInstanceId, 1, page_size, FLAGS_is_query_image == 1,
                                        FLAGS_image_index)) {
        cerr << "Failed to send first page request for sub rank" << endl;
        continue;
      }

      // 等待响应
      while (driver.IsRunning()) {
        std::this_thread::sleep_for(std::chrono::milliseconds(kSleepIntervalMs));
        driver.Update();
      }

      // 获取第一页结果
      const SubRankData& first_page = driver.GetCurrentRankData();
      sub_rank_data.total_count = first_page.total_count;
      sub_rank_data.items.insert(sub_rank_data.items.end(), first_page.items.begin(), first_page.items.end());

      // 计算总页数
      uint32_t total_count = first_page.total_count;
      int total_pages = (total_count + page_size - 1) / page_size;

      cout << "总记录数: " << total_count << ", 总页数: " << total_pages << endl;

      // 获取剩余页面
      for (int page = 2; page <= total_pages; ++page) {
        cout << "获取子榜数据第 " << page << "/" << total_pages << " 页..." << endl;

        // 重置状态为运行中
        g_status = 1;

        // 发送请求
        if (!driver.SendTopSubRankRequest(FLAGS_world_id, FLAGS_zone_id, FLAGS_type, FLAGS_instance_id, sub_rank.uiType,
                                          sub_rank.uiInstanceId, page, page_size, FLAGS_is_query_image == 1,
                                          FLAGS_image_index)) {
          cerr << "Failed to send page " << page << " request" << endl;
          break;
        }

        // 等待响应
        while (driver.IsRunning()) {
          std::this_thread::sleep_for(std::chrono::milliseconds(kSleepIntervalMs));
          driver.Update();
        }

        // 合并数据
        const SubRankData& page_data = driver.GetCurrentRankData();
        if (page_data.items.empty()) {
          cerr << "Warning: Page " << page << " returned empty data" << endl;
          continue;
        }
        sub_rank_data.items.insert(sub_rank_data.items.end(), page_data.items.begin(), page_data.items.end());

        // 打印进度
        cout << "已获取 " << sub_rank_data.items.size() << "/" << total_count << " 条记录" << endl;
      }

      // 更新当前记录数
      sub_rank_data.current_count = sub_rank_data.items.size();

      // 验证数据完整性
      if (sub_rank_data.current_count != total_count) {
        cerr << "Warning: Data incomplete. Expected " << total_count << " records, but got "
             << sub_rank_data.current_count << " records" << endl;
      }

      // 将这个子榜的完整数据添加到结果中
      all_sub_rank_data.push_back(sub_rank_data);

      cout << "子榜数据获取完成，共 " << sub_rank_data.current_count << " 条记录" << endl;
    }
  }

  return all_sub_rank_data;
}

// 解析用户列表字符串
vector<string> ParseUserList(const string& user_list_str) {
  vector<string> users;
  if (user_list_str.empty()) {
    return users;
  }

  stringstream ss(user_list_str);
  string user;
  while (getline(ss, user, ',')) {
    // 去除前后空格
    user.erase(0, user.find_first_not_of(" \t"));
    user.erase(user.find_last_not_of(" \t") + 1);
    if (!user.empty()) {
      users.push_back(user);
    }
  }
  return users;
}

// 从子榜数据中提取所有用户的openid（支持分数过滤）
vector<string> ExtractAllUsers(const vector<SubRankData>& sub_rank_data) {
  set<string> unique_users;

  for (const auto& data : sub_rank_data) {
    for (const auto& item : data.items) {
      // 使用辅助函数进行分数过滤
      if (!IsScoreInRange(item.stUserInfo.uiScore)) {
        continue;
      }

      string openid = item.stUserInfo.szOpenId;
      if (!openid.empty()) {
        unique_users.insert(openid);
      }
    }
  }

  return vector<string>(unique_users.begin(), unique_users.end());
}

// 清理用户函数
bool ClearUsersFromSubRanks(TopnextDriver& driver, const vector<SubRankListResult>& sub_rank_list,
                            const vector<string>& users_to_clear) {
  if (!FLAGS_auto_clear_users || users_to_clear.empty() || sub_rank_list.empty()) {
    return true;
  }

  cout << "\n开始清理用户..." << endl;
  cout << "需要清理的用户数量: " << users_to_clear.size() << endl;

  // 收集所有子榜信息
  vector<TopNextSubRankInfo> all_sub_ranks;
  for (const auto& page_result : sub_rank_list) {
    for (const auto& sub_rank : page_result.items) {
      TopNextSubRankInfo sub_rank_info;
      sub_rank_info.uiType = sub_rank.uiType;
      sub_rank_info.uiInstanceId = sub_rank.uiInstanceId;
      all_sub_ranks.push_back(sub_rank_info);
    }
  }

  cout << "需要清理的子榜数量: " << all_sub_ranks.size() << endl;

  bool all_success = true;
  int cleared_count = 0;

  for (const auto& openid : users_to_clear) {
    cout << "清理用户: " << openid << endl;

    // 设置状态为运行中
    g_status = 1;

    // 发送清理请求
    if (!driver.SendClearOneUserSubRankRequest(FLAGS_world_id, FLAGS_zone_id, FLAGS_type, FLAGS_instance_id, openid,
                                               all_sub_ranks)) {
      cerr << "Failed to send clear request for user: " << openid << endl;
      all_success = false;
      continue;
    }

    // 等待响应
    auto start_time = std::chrono::steady_clock::now();
    const auto timeout_duration = std::chrono::milliseconds(kDefaultTimeoutMs);

    while (driver.IsRunning()) {
      std::this_thread::sleep_for(std::chrono::milliseconds(kSleepIntervalMs));
      driver.Update();

      auto current_time = std::chrono::steady_clock::now();
      if (current_time - start_time > timeout_duration) {
        cerr << "Timeout waiting for clear response for user: " << openid << endl;
        all_success = false;
        break;
      }
    }

    // 检查清理结果
    const ClearUserResult& clear_result = driver.GetCurrentClearResult();
    if (clear_result.success) {
      cleared_count++;
      cout << "用户 " << openid << " 清理成功" << endl;
    } else {
      cerr << "用户 " << openid << " 清理失败: " << clear_result.error_message << endl;
      all_success = false;
    }

    // 添加短暂延迟避免请求过于频繁
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
  }

  cout << "\n清理操作完成!" << endl;
  cout << "成功清理用户数: " << cleared_count << "/" << users_to_clear.size() << endl;

  return all_success;
}

// 添加打印子榜数据的函数
void PrintSubRankData(const vector<SubRankData>& sub_rank_data) {
  if (sub_rank_data.empty()) {
    cout << "没有子榜数据可显示" << endl;
    return;
  }

  cout << "\n=== 子榜数据汇总 ===" << endl;
  cout << "子榜数量: " << sub_rank_data.size() << endl;

  // 显示分数过滤条件
  PrintScoreFilterInfo();

  for (const auto& data : sub_rank_data) {
    cout << "\n子榜信息: sub_type=" << data.sub_type << ", sub_instance_id=" << data.sub_instance_id << endl;
    cout << "总记录数: " << data.total_count << endl;
    cout << "当前页记录数: " << data.current_count << endl;

    if (!data.items.empty()) {
      cout << "\n=== 详细数据 ===" << endl;

      // 使用预定义的表格分隔线
      string separator = CreateTableSeparator();

      // 构建格式字符串
      string header_format =
          "| %-" + std::to_string(kRankColumnWidth) + "s |" + " %-" + std::to_string(kScoreColumnWidth) + "s |" +
          " %-" + std::to_string(kOpenIdColumnWidth) + "s |" + " %-" + std::to_string(kTimeColumnWidth) + "s |" +
          " %-" + std::to_string(kFieldColumnWidth) + "s |" + " %-" + std::to_string(kFieldColumnWidth) + "s |" +
          " %-" + std::to_string(kFieldColumnWidth) + "s |" + " %-" + std::to_string(kFieldColumnWidth) + "s |" +
          " %-" + std::to_string(kFieldColumnWidth) + "s |" + " %-" + std::to_string(kExtLenColumnWidth) + "s |" +
          " %-" + std::to_string(kExtMd5ColumnWidth) + "s |";

      string data_format =
          "| %-" + std::to_string(kRankColumnWidth) + "d |" + " %-" + std::to_string(kScoreColumnWidth) + "d |" +
          " %-" + std::to_string(kOpenIdColumnWidth) + "s |" + " %-" + std::to_string(kTimeColumnWidth) + "s |" +
          " %-" + std::to_string(kFieldColumnWidth) + "d |" + " %-" + std::to_string(kFieldColumnWidth) + "d |" +
          " %-" + std::to_string(kFieldColumnWidth) + "d |" + " %-" + std::to_string(kFieldColumnWidth) + "d |" +
          " %-" + std::to_string(kFieldColumnWidth) + "d |" + " %-" + std::to_string(kExtLenColumnWidth) + "d |" +
          " %-" + std::to_string(kExtMd5ColumnWidth) + "s |";

      // 打印表头边框
      cout << separator << endl;

      // 打印表头
      printf(header_format.c_str(), "rank", "score", "openid", "timestamp", "sort_field1", "sort_field2", "sort_field3",
             "sort_field4", "sort_field5", "ext_len", "ext_md5");
      cout << endl;

      // 打印表头下方边框
      cout << separator << endl;

      // 打印数据行（支持分数过滤）
      int filtered_count = 0;
      for (const auto& item : data.items) {
        // 使用辅助函数进行分数过滤
        if (!IsScoreInRange(item.stUserInfo.uiScore)) {
          continue;
        }
        filtered_count++;

        // 使用辅助函数格式化时间戳
        string formatted_time = FormatTimestamp(item.ullTimeStamp);

        printf(data_format.c_str(), item.uiRankNo, item.stUserInfo.uiScore, item.stUserInfo.szOpenId,
               formatted_time.c_str(), item.stUserInfo.uiSortField1, item.stUserInfo.uiSortField2,
               item.stUserInfo.uiSortField3, item.stUserInfo.uiSortField4, item.stUserInfo.uiSortField5,
               item.stUserInfo.uiExtDataLength, 
               CalcMD5(item.stUserInfo.bExtDataInfo, item.stUserInfo.uiExtDataLength).c_str());
        cout << endl;
      }

      // 打印表格底部边框
      cout << separator << endl;

      // 如果进行了过滤，显示过滤后的统计信息
      if (FLAGS_min_score > 0 || FLAGS_max_score > 0) {
        cout << "过滤后记录数: " << filtered_count << " (原始记录数: " << data.items.size() << ")" << endl;
      } else {
        cout << "实际打印记录数: " << filtered_count << " (总记录数: " << data.total_count << ")" << endl;
      }
    }
  }
}

// 添加导出CSV的函数
void ExportSubRankListToCSV(const vector<SubRankListResult>& results, const string& export_dir) {
  if (export_dir.empty()) {
    return;
  }

  // 添加输入验证
  if (results.empty()) {
    cerr << "No results to export" << endl;
    return;
  }

  // 确保导出目录存在
  if (mkdir(export_dir.c_str(), 0755) != 0 && errno != EEXIST) {
    cerr << "Failed to create export directory: " << export_dir << ", error: " << strerror(errno) << endl;
    return;
  }

  string filename = export_dir + "/sub_rank_list.csv";
  ofstream outfile(filename);
  if (!outfile.is_open()) {
    cerr << "Failed to open file for writing: " << filename << endl;
    return;
  }

  // 写入CSV头
  outfile << "business_id,world_id,zone_id,type,instance_id,sub_type,sub_instance_id" << endl;

  // 写入数据
  for (const auto& page_result : results) {
    for (const auto& item : page_result.items) {
      outfile << FLAGS_business_id << "," << FLAGS_world_id << "," << FLAGS_zone_id << "," << FLAGS_type << ","
              << FLAGS_instance_id << "," << item.uiType << "," << item.uiInstanceId << endl;
    }
  }

  outfile.close();
  cout << "子榜列表已导出到: " << filename << endl;
}

void ExportSubRankDataToCSV(const vector<SubRankData>& sub_rank_data, const string& export_dir) {
  if (export_dir.empty()) {
    return;
  }

  // 添加输入验证
  if (sub_rank_data.empty()) {
    cerr << "No sub rank data to export" << endl;
    return;
  }

  // 确保导出目录存在
  if (mkdir(export_dir.c_str(), 0755) != 0 && errno != EEXIST) {
    cerr << "Failed to create export directory: " << export_dir << ", error: " << strerror(errno) << endl;
    return;
  }

  for (const auto& data : sub_rank_data) {
    if (data.items.empty()) {
      continue;
    }

    // 使用子榜的type和instance_id作为文件名
    string filename =
        export_dir + "/sub_rank_" + std::to_string(data.sub_type) + "_" + std::to_string(data.sub_instance_id) + ".csv";

    ofstream outfile(filename);
    if (!outfile.is_open()) {
      cerr << "Failed to open file for writing: " << filename << endl;
      continue;
    }

    // 写入CSV头
    outfile << "rank,score,openid,timestamp,sort_field1,sort_field2,sort_field3,sort_field4,sort_field5,ext_len,ext_md5"
            << endl;

    // 写入数据（支持分数过滤）
    for (const auto& item : data.items) {
      if (!IsScoreInRange(item.stUserInfo.uiScore)) {
        continue;
      }

      // 使用辅助函数格式化时间戳
      string formatted_time = FormatTimestamp(item.ullTimeStamp);

      outfile << item.uiRankNo << "," << item.stUserInfo.uiScore << "," << item.stUserInfo.szOpenId << ","
              << formatted_time << "," << item.stUserInfo.uiSortField1 << "," << item.stUserInfo.uiSortField2 << ","
              << item.stUserInfo.uiSortField3 << "," << item.stUserInfo.uiSortField4 << ","
              << item.stUserInfo.uiSortField5 << "," << item.stUserInfo.uiExtDataLength << ","
              << CalcMD5(item.stUserInfo.bExtDataInfo, item.stUserInfo.uiExtDataLength) << endl;
    }

    outfile.close();
    cout << "子榜数据已导出到: " << filename << endl;
  }
}

int main(int argc, char** argv) {
  google::ParseCommandLineFlags(&argc, &argv, true);

  // 添加基本的参数验证
  if (FLAGS_business_id <= 0) {
    cerr << "Invalid business_id: " << FLAGS_business_id << endl;
    return -1;
  }

  if (FLAGS_business_key.empty()) {
    cerr << "business_key cannot be empty" << endl;
    return -1;
  }

  if (!ValidatePageParameters(1, FLAGS_query_count)) {
    cerr << "Invalid query_count: " << FLAGS_query_count << " (should be 1-" << kMaxPageSize << ")" << endl;
    return -1;
  }

  if (InitLog() != 0) {
    cerr << "Failed to initialize logging" << endl;
    return -1;
  }

  TopnextDriver driver(g_logger);
  if (!driver.Initialize(FLAGS_dir, FLAGS_business_id, FLAGS_business_key, FLAGS_encrypt_key)) {
    cerr << "Failed to initialize TopnextDriver" << endl;
    return -1;
  }

  cout << "开始获取子榜列表..." << endl;

  // 获取所有子榜列表数据
  vector<SubRankListResult> all_results =
      GetAllSubRankList(driver, FLAGS_world_id, FLAGS_zone_id, FLAGS_type, FLAGS_instance_id, FLAGS_query_count,
                        FLAGS_is_query_image == 1, FLAGS_image_index);

  if (all_results.empty()) {
    cerr << "未获取到子榜列表数据" << endl;
    return -1;
  }

  // 打印子榜列表
  PrintAllResults(all_results);

  // 导出子榜列表到CSV
  ExportSubRankListToCSV(all_results, FLAGS_export_dir);

  // 获取所有子榜的数据
  vector<SubRankData> all_sub_rank_data = GetAllSubRankData(driver, all_results, FLAGS_query_count);

  // 打印子榜数据
  PrintSubRankData(all_sub_rank_data);

  // 导出子榜数据到CSV
  ExportSubRankDataToCSV(all_sub_rank_data, FLAGS_export_dir);

  // 执行用户清理操作
  if (FLAGS_auto_clear_users) {
    vector<string> users_to_clear;

    if (!FLAGS_clear_user_list.empty()) {
      // 使用指定的用户列表
      users_to_clear = ParseUserList(FLAGS_clear_user_list);
      cout << "\n使用指定的用户列表进行清理，用户数量: " << users_to_clear.size() << endl;
    } else {
      // 清理所有导出的用户
      users_to_clear = ExtractAllUsers(all_sub_rank_data);
      cout << "\n将清理所有导出的用户，用户数量: " << users_to_clear.size() << endl;
    }

    if (!users_to_clear.empty()) {
      bool clear_success = ClearUsersFromSubRanks(driver, all_results, users_to_clear);
      if (clear_success) {
        cout << "所有用户清理操作完成!" << endl;
      } else {
        cerr << "部分用户清理操作失败，请检查日志" << endl;
      }
    } else {
      cout << "没有需要清理的用户" << endl;
    }
  }

  return 0;
}
