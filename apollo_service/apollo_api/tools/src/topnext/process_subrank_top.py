#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import csv
import subprocess
from typing import List, Tuple
from datetime import datetime

def extract_sub_info(csv_file: str) -> List[Tuple[str, str, str, str]]:
    """
    Extract world_id, instance_id, sub_type and sub_instance_id from CSV file
    """
    combinations = []
    with open(csv_file, 'r') as f:
        # Skip header line
        next(f, None)
        for line in f:
            line = line.strip()
            if not line:  # Skip empty lines
                continue
            # Split by comma and strip whitespace
            parts = [part.strip() for part in line.split(',')]
            if len(parts) >= 4:  # We need at least 4 fields: world_id, instance_id, sub_type, sub_instance_id
                world_id = parts[0]
                instance_id = parts[1]
                sub_type = parts[2]
                sub_instance_id = parts[3]
                if world_id.isdigit() and instance_id.isdigit() and sub_type.isdigit() and sub_instance_id.isdigit():
                    combinations.append((world_id, instance_id, sub_type, sub_instance_id))
    return combinations

def execute_command(world_id: str, instance_id: str, sub_type: str, sub_instance: str, query_from: int, output_file: str) -> None:
    """
    Execute the command with given world_id, instance_id, sub_type, sub_instance and query_from value
    """
    cmd = [
        './gettop_topnext',
        '-dir=tcp://*************:9050',
        '-business_id=10249',
        '-business_key=5cd3027c07873cf6aa691a42320d86f5',
        f'-world_id={world_id}',
        '-type=100',
        f'-instance_id={instance_id}',
        f'-sub_rank_info={sub_type}-{sub_instance}',
        '-is_query_image=1',
        f'-query_from={query_from}'
    ]

    print(f"Executing command for world_id={world_id}, instance_id={instance_id}, sub_type={sub_type}, sub_instance={sub_instance}, query_from={query_from}")
    
    try:
        result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
        
        # Append result to the output file
        with open(output_file, 'a') as f:
            f.write(f"\n{'='*80}\n")
            f.write(f"Query from: {query_from}\n")
            f.write(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("\n=== STDOUT ===\n")
            f.write(result.stdout)
            f.write("\n=== STDERR ===\n")
            f.write(result.stderr)
            f.write(f"\nReturn code: {result.returncode}\n")
        
        if result.returncode == 0:
            print("Command executed successfully")
        else:
            print(f"Command failed with error: {result.stderr}")
    except Exception as e:
        print(f"Error executing command: {str(e)}")
        # Append error to output file
        with open(output_file, 'a') as f:
            f.write(f"\n{'='*80}\n")
            f.write(f"Query from: {query_from}\n")
            f.write(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Error: {str(e)}\n")

def main():
    # Create output directory with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join('results', timestamp)
    os.makedirs(output_dir, exist_ok=True)
    print(f"Results will be saved to: {output_dir}")

    # Process the image_generate_after_00_10_00.csv file
    csv_file = 'image_generate_after_00_10_00.csv'

    if not os.path.exists(csv_file):
        print(f"CSV file not found: {csv_file}")
        return

    print(f"\nProcessing CSV file: {csv_file}")

    # Extract combinations
    combinations = extract_sub_info(csv_file)
    total = len(combinations)
    current = 0

    # Define query_from values to process
    query_from_values = [1, 101]

    print(f"Found {total} combinations to process")

    # Process each combination
    for world_id, instance_id, sub_type, sub_instance in combinations:
        current += 1
        print(f"\nProcessing combination {current}/{total}: world_id={world_id}, instance_id={instance_id}, sub_type={sub_type}, sub_instance={sub_instance}")

        # Create output file for this combination
        output_file = os.path.join(output_dir, f"results_{world_id}_{instance_id}_{sub_type}_{sub_instance}.txt")

        # Write header to the output file
        with open(output_file, 'w') as f:
            f.write(f"Results for world_id: {world_id}, instance_id: {instance_id}, sub_type: {sub_type}, sub_instance: {sub_instance}\n")
            f.write(f"Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Query from values: {query_from_values}\n")
            f.write("="*80 + "\n")

        # Execute commands for each query_from value
        for query_from in query_from_values:
            execute_command(world_id, instance_id, sub_type, sub_instance, query_from, output_file)

if __name__ == "__main__":
    main() 