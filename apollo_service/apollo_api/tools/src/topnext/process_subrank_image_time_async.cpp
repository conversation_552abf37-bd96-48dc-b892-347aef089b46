/*!
 * Copyright (c) 2015 Tencent all rights reserved
 *
 */

#include <dirent.h>
#include <gflags/gflags.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <sys/wait.h>
#include <tbus/tbus.h>
#include <time.h>
#include <unistd.h>
#include <fstream>
#include <future>
#include <iostream>
#include <map>
#include <memory>
#include <mutex>
#include <regex>
#include <sstream>
#include <string>
#include <thread>
#include <vector>
#include "apollo_service.h"
#include "tloghelp/tlogload.h"
#include "tool_common.h"

using std::string;
using namespace apollo_service_api;

// 命令行参数定义
DEFINE_string(dir, "tcp://127.0.0.1:6200", "dir server ip:port, tcp://127.0.0.1:6200");
DEFINE_int32(business_id, 10000, "Business Id");
DEFINE_string(business_key, "d9798cdf31c02d86b8b81cc119d94836", "Business Key");
DEFINE_string(input_dir, "./input", "sub rank csv input dir");
DEFINE_string(output_dir, "./output", "sub rank image genarate time result output dir");
DEFINE_string(start_time, "2025-06-23 00:00:00", "calculate start time");
DEFINE_int32(max_rate, 5000, "max request rate per second");
DEFINE_int32(max_group_rate, 1500, "max request rate per second per group");
DEFINE_bool(get_top, true, "use get top for image ts");
DEFINE_string(encryptkey, "", "Encrypt key");

// 常量定义
namespace {
constexpr int kDefaultTimeout = 3000;
constexpr int kSleepIntervalUs = 10;
constexpr int kLogFileSizeLimit = 10485760;  // 10MB
constexpr int kLogMaxRotate = 2;
constexpr const char* kLogFileName = "./log/topnext_tool.log";
constexpr const char* kLogErrorFileName = "./log/topnext_tool.error";
constexpr const char* kLogConfigFile = "./topnext_tool_log.xml";
constexpr const char* kSuccRawFile = "raw_succ.txt";  // 响应成功
constexpr const char* kFailRawFile = "raw_fail.txt";  // 请求/响应失败
constexpr const char* kAggrerateFile = "result.txt";  // 聚合结果
constexpr const char* kRawTitle = "world_id,zone_id,instance_id,type,sub_type,sub_instance_id";
constexpr const char* kAggrerateTitle = "Category,Count";
}  // namespace

// 子排行榜信息结构
struct SubRankInfo {
  string world_id;
  string zone_id;
  string type;
  string instance_id;
  string sub_type;
  string sub_instance_id;

  SubRankInfo() {}
  SubRankInfo(const string& w, const string& z, const string& t, const string& i, const string& st, const string& si)
      : world_id(w), zone_id(z), type(t), instance_id(i), sub_type(st), sub_instance_id(si) {}
};

struct ResultCnt {
  int request = 0;
  int response = 0;
  bool analyze = false;
  bool all_send = false;
};

#define NS_PER_SECOND 1000000000
#define NS_PER_USECOND 1000

uint64_t NanoSeconds() {
  timeval tnow;
  gettimeofday(&tnow, NULL);
  return (static_cast<uint64_t>(tnow.tv_sec) * 1000000000 + tnow.tv_usec * 1000);
}

class RateLimiter {
 public:
  RateLimiter() : last_feeded_(0), stored_permits_(0), max_permits_(0), time_unit_(0), feed_times_(0) {}

  virtual ~RateLimiter() {}

  // Set limit speed,parameters over writed when called
  void Init(uint32_t permits_per_second) {
    if (permits_per_second > NS_PER_SECOND) {
      permits_per_second = NS_PER_SECOND;
    }

    max_permits_ = permits_per_second;
    // Calculate how long(nano seconds) to add one permit to bucket
    time_unit_ = NS_PER_SECOND / max_permits_;
    // Must set to current time,or the first second will be incorrect,not smooth
    // should not reset last_feeded_ when reload
    last_feeded_ = NanoSeconds();
  }

  // Acquire one permit,return true if acquired,return false means QPS/RPS reach
  // limit
  bool Acquire() {
    // According to the length of time in the past, fill up the permits that need
    // to be added in the past time
    FeedPermits();
    if (stored_permits_ == 0) {
      return false;
    }
    // Reduce one permit in bucket if acquired
    TakePermits(1);
    return true;
  }

  // Feed permits times.can be used to do some statistics
  uint32_t feed_times() const { return feed_times_; }
  void set_feed_times(uint32_t feed_times) { feed_times_ = feed_times; }
  uint32_t max_permits() const { return max_permits_; }
  uint32_t stored_permits() const { return stored_permits_; }

 private:
  // Add permits to bucket
  void FeedPermits() {
    uint64_t now = NanoSeconds();
    // Calculate how many permits should be add to bucket
    // Allow time go back
    uint64_t permits = (now - last_feeded_) / time_unit_;
    if (permits == 0) {
      return;
    }

    ++feed_times_;
    uint32_t last_permits = stored_permits_;
    stored_permits_ += permits;

    // permits in bucket should not great than max allowed(Max QPS)
    if (stored_permits_ > max_permits_) {
      stored_permits_ = max_permits_;
      last_feeded_ = now;
    } else {
      uint32_t added_permits = stored_permits_ - last_permits;
      last_feeded_ += added_permits * time_unit_;
    }
  }

  void TakePermits(uint32_t cost) {
    if (stored_permits_ >= cost) {
      stored_permits_ -= cost;
    }
  }

  // Last feed permits timestamp,milli seconds
  uint64_t last_feeded_;
  // Permits remain in bucket
  uint32_t stored_permits_;
  // Max allowed permits in bucket
  uint32_t max_permits_;
  // How long to feed one permits,nano seconds
  uint64_t time_unit_;
  // FeedPermits called times
  uint32_t feed_times_;
};

class TopNextQueryTool {
 public:
  TopNextQueryTool() : logger_(nullptr), apollo_api_(nullptr) {}

  ~TopNextQueryTool() {
    if (apollo_api_) {
      delete apollo_api_;
    }
  }

  int Initialize() {
    // 初始化日志
    if (initializeLogger() != 0) {
      fprintf(stderr, "Failed to initialize logger\n");
      return -1;
    }

    tlog_error(logger_, 0, 0, "Start!!!");

    auto updates = 1 * 1000 * 1000 / kSleepIntervalUs;  // 10w
    if (FLAGS_max_rate < 1 || FLAGS_max_rate >= updates) {
      fprintf(stderr, "invalid rate:%d\n", FLAGS_max_rate);
      return -1;
    }
    limiter.Init(FLAGS_max_rate);

    for (int i = 0; i < 64; i++) {
      group_limiters[i].Init(FLAGS_max_group_rate);
    }

    // 创建Apollo服务实例
    apollo_api_ = new ApolloService(logger_);
    if (!apollo_api_) {
      tlog_error(logger_, 0, 0, "Failed to create ApolloService instance");
      return -1;
    }

    // 添加服务地址
    int ret = apollo_api_->AddServiceURL(FLAGS_dir.c_str());
    if (ret != 0) {
      tlog_error(logger_, 0, 0, "Failed to add service URL: %s, ret=%d", FLAGS_dir.c_str(), ret);
      return -1;
    }

    // 设置回调处理器
    ret = apollo_api_->SetTopNextCallBack(&response_handler_);
    if (ret != 0) {
      tlog_error(logger_, 0, 0, "Failed to set TopNext callback, ret=%d", ret);
      return -1;
    }

    // 初始化API
    ret = apollo_api_->Init(FLAGS_business_id, FLAGS_business_key.c_str(), APOLLO_SERVICE_API_VERSION, kDefaultTimeout,
                            FLAGS_encryptkey.c_str());
    if (ret != 0) {
      tlog_error(logger_, 0, 0, "Failed to initialize API, ret=%d", ret);
      return -1;
    }

    tlog_info(logger_, 0, 0, "TopNext query tool initialized successfully");
    return 0;
  }

  void Stat() {
    timeval tnow;
    gettimeofday(&tnow, NULL);
    uint64_t now = static_cast<uint64_t>(tnow.tv_sec) * 1000 + tnow.tv_usec / 1000;
    if (now - last_ts_ >= 1000) {
      // printf("rate: %d/s\n", send_cnt_);
      send_cnt_ = 0;
      last_ts_ = now;
    }
  }

  int NextGroup(int group_size, int process_group_index) {
    int next = process_group_index + 1;
    if (next < group_size) {
      return next;
    } else {
      return 0;
    }
  }

  // 读取列表，发送请求
  int Process() {
    // 创建结果目录
    this->output_dir = createOutputDirectory();
    if (this->output_dir.empty()) {
      printf("Failed to create output directory\n");
      return -1;
    }

    printf("Results will be saved to dir: %s\n", this->output_dir.c_str());

    // 读取所有CSV文件中的组合
    std::vector<std::vector<SubRankInfo>> all_combinations;
    std::vector<int> all_groups;
    if (loadAllCombinations(all_combinations, all_groups) != 0) {
      printf("Failed to load subrank from CSV files\n");
      return -1;
    }

    if (all_combinations.empty()) {
      printf("No subrank found to process.\n");
      return 0;
    }

    int group_size = all_combinations.size();
    std::vector<size_t> index;
    for (int i = 0; i < group_size; i++) {
      index.push_back(0);
      subrank_cnt += all_combinations[i].size();
      ResultCnt& rc = group_results[all_groups[i]];
      rc.request = 0;
      rc.response = 0;
      rc.analyze = false;
      rc.all_send = false;
    }

    printf("Total subrank to process: %d\n", subrank_cnt);
    printf("Total group to process: %d\n", group_size);

    int cursor = 0;
    int process_group_index = 0;
    while (cursor < subrank_cnt) {
      Stat();
      apollo_api_->Update();
      Analyze();
      usleep(kSleepIntervalUs);

      if (limiter.Acquire()) {
        auto k = index[process_group_index];  // 当前group的index
        if (k >= all_combinations[process_group_index].size()) {
          // 该group已经处理完
          group_results[all_groups[process_group_index]].all_send = true;
          process_group_index = NextGroup(group_size, process_group_index);
          k = index[process_group_index];
        }

        // 发group_max_rate个请求
        if (group_limiters[process_group_index].Acquire()) {
          if (k < all_combinations[process_group_index].size()) {
            if (FLAGS_get_top) {
              RequestByGetTop(all_combinations, all_groups, process_group_index, k, cursor, index);
            } else {
              RequestByImageStatus(all_combinations, all_groups, process_group_index, k, cursor, index);
            }
          }
        } else {
          // 处理下一个group
          process_group_index = NextGroup(group_size, process_group_index);
        }
      }
    }

    group_results[all_groups[process_group_index]].all_send = true;

    while (true) {
      apollo_api_->Update();
      Analyze();
      usleep(kSleepIntervalUs);
      if (total_send_count <= (err_cnt + succ_cnt + timeout_cnt)) {
        break;
      }
    }

    Analyze();

    return 0;
  }

  void Analyze() {
    for (auto it = group_results.begin(); it != group_results.end(); ++it) {
      // 未完成
      if (!it->second.all_send) {
        continue;
      }

      if (it->second.analyze) {
        continue;
      }

      if (it->second.request > it->second.response) {
        continue;
      }

      int groupid = it->first;
      std::string filename = output_dir + "/" + std::to_string(groupid) + "_" + kSuccRawFile;

      uint64_t timestamp = ToTS(FLAGS_start_time.c_str());
      if (timestamp == 0) {
        return;
      }
      // fprintf(stderr, "start time:%s, timestamp:%lu\n", FLAGS_start_time.c_str(), timestamp);

      std::ifstream file(filename.c_str());
      if (!file.is_open()) {
        printf("Failed to open file: %s, %s\n", filename.c_str(), strerror(errno));
        return;
      }

      string line;
      // bool first_line = true;

      int min1 = 0;
      int min2 = 0;
      int min3 = 0;
      int min4 = 0;
      int min5 = 0;
      int min6 = 0;
      int min7 = 0;
      int min8 = 0;
      int min9 = 0;
      int min10 = 0;
      int min10plus = 0;
      int ungen = 0;
      int noimage = 0;

      while (std::getline(file, line)) {
        // 跳过表头
        // if (first_line) {
        //   first_line = false;
        //   continue;
        // }

        // 解析CSV行
        std::vector<string> parts;
        std::stringstream ss(line);
        string part;

        while (std::getline(ss, part, ',')) {
          // 去除前后空格
          size_t start = part.find_first_not_of(" \t\r\n");
          size_t end = part.find_last_not_of(" \t\r\n");
          if (start != string::npos && end != string::npos) {
            parts.push_back(part.substr(start, end - start + 1));
          } else {
            parts.push_back("");
          }
        }

        // 检查字段数量：world_id,zone_id,instance_id,type,sub_type,sub_instance_id,timestamp
        if (parts.size() >= 7) {
          string ts = parts[6];

          if (isDigits(ts)) {
            uint64_t last_ts = uint64_t(atoi(ts.c_str()));
            if (last_ts == 0) {
              noimage++;
            } else if (last_ts < timestamp) {
              ungen++;
            } else if (last_ts - timestamp <= 60) {
              min1++;
            } else if (last_ts - timestamp <= 2 * 60) {
              min2++;
            } else if (last_ts - timestamp <= 3 * 60) {
              min3++;
            } else if (last_ts - timestamp <= 4 * 60) {
              min4++;
            } else if (last_ts - timestamp <= 5 * 60) {
              min5++;
            } else if (last_ts - timestamp <= 6 * 60) {
              min6++;
            } else if (last_ts - timestamp <= 7 * 60) {
              min7++;
            } else if (last_ts - timestamp <= 8 * 60) {
              min8++;
            } else if (last_ts - timestamp <= 9 * 60) {
              min9++;
            } else if (last_ts - timestamp <= 10 * 60) {
              min10++;
            } else {
              min10plus++;
            }
          } else {
            printf("invalid result:%s\n", line.c_str());
          }
        } else {
          printf("invalid result:%s\n", line.c_str());
        }
      }

      file.close();

      std::string result = output_dir + "/" + std::to_string(groupid) + "_" + kAggrerateFile;
      std::ofstream resultfile(result.c_str(), std::ios::app);
      if (!resultfile.is_open()) {
        printf("Failed to open output file: %s\n", result.c_str());
        return;
      }

      if (min1 > 0) {
        resultfile << "<= 1min" << "," << min1 << "\n";
      }
      if (min2 > 0) {
        resultfile << "1min - 2min" << "," << min2 << "\n";
      }
      if (min3 > 0) {
        resultfile << "2min - 3min" << "," << min3 << "\n";
      }
      if (min4 > 0) {
        resultfile << "3min - 4min" << "," << min4 << "\n";
      }
      if (min5 > 0) {
        resultfile << "4min - 5min" << "," << min5 << "\n";
      }
      if (min6 > 0) {
        resultfile << "5min - 6min" << "," << min6 << "\n";
      }
      if (min7 > 0) {
        resultfile << "6min - 7min" << "," << min7 << "\n";
      }
      if (min8 > 0) {
        resultfile << "7min - 8min" << "," << min8 << "\n";
      }
      if (min9 > 0) {
        resultfile << "8min - 9min" << "," << min9 << "\n";
      }
      if (min10 > 0) {
        resultfile << "9min - 10min" << "," << min10 << "\n";
      }
      if (min10plus > 0) {
        resultfile << "> 10min" << "," << min10plus << "\n";
      }
      if (ungen > 0) {
        resultfile << "image not genareted" << "," << ungen << "\n";
        printf("groupid:%d, image not genarated: %d\n", groupid, ungen);
      }
      if (noimage > 0) {
        resultfile << "never generated" << "," << noimage << "\n";
      }

      resultfile.close();

      it->second.analyze = true;

      printf("Final result in: %s\n\n", result.c_str());
    }
  }

  uint64_t ToTS(const char* str) {
    const char* format = "%Y-%m-%d %H:%M:%S";
    struct tm tm_time = {0};
    time_t timestamp;

    // 解析字符串到struct tm
    if (strptime(str, format, &tm_time) == NULL) {
      fprintf(stderr, "start time format invalid:%s\n", str);
      return 0;
    }

    // 转换为时间戳（本地时间）
    timestamp = mktime(&tm_time);
    if (timestamp == (time_t)-1) {
      fprintf(stderr, "format start time failed\n");
      return 0;
    }

    return (uint64_t)timestamp;
  }

  void RequestByGetTop(const std::vector<std::vector<SubRankInfo>>& all_combinations,
                       const std::vector<int>& all_groups, int process_group_index, size_t& k, int& cursor,
                       std::vector<size_t>& index) {
    // 构造一个请求
    std::string callback;
    std::string rankinfo_str;
    const SubRankInfo& info = all_combinations[process_group_index][k];
    TopNextRankInfo rank_info;
    rank_info.uiWorldId = atoi(info.world_id.c_str());
    rank_info.uiZoneId = atoi(info.zone_id.c_str());
    rank_info.uiType = atoi(info.type.c_str());
    rank_info.uiInstanceId = atoi(info.instance_id.c_str());
    TopNextSubRankInfo sub_rank_info;
    sub_rank_info.uiType = atoi(info.sub_type.c_str());
    sub_rank_info.uiInstanceId = atoi(info.sub_instance_id.c_str());

    rankinfo_str = info.world_id + "," + info.zone_id + "," + info.instance_id + "," + info.type + "," + info.sub_type +
                   "," + info.sub_instance_id;

    k++;
    cursor++;

    // groupid 放在callback第一位
    callback = std::to_string(all_groups[process_group_index]) + "," + rankinfo_str;
    if (sendGetTop(rank_info, sub_rank_info, callback.c_str(), 1024) == 0) {
      send_succ_cnt++;
      group_results[all_groups[process_group_index]].request++;
    } else {
      writeFailResultToFile(all_groups[process_group_index], rankinfo_str.c_str());
      err_cnt++;
    }

    send_cnt_++;
    total_send_count++;

    index[process_group_index] = k;  // 保存index
  }

  void RequestByImageStatus(const std::vector<std::vector<SubRankInfo>>& all_combinations,
                            const std::vector<int>& all_groups, int process_group_index, size_t& k, int& cursor,
                            std::vector<size_t>& index) {
    // 构造一个请求
    std::string callback;
    std::string rankinfo_str;
    const SubRankInfo& info = all_combinations[process_group_index][k];
    TopNextRankInfo rank_info;
    rank_info.uiWorldId = atoi(info.world_id.c_str());
    rank_info.uiZoneId = atoi(info.zone_id.c_str());
    rank_info.uiType = atoi(info.type.c_str());
    rank_info.uiInstanceId = atoi(info.instance_id.c_str());
    TopNextSubRankInfoCollection sub_rank_collection;
    sub_rank_collection.Add(atoi(info.sub_type.c_str()), atoi(info.sub_instance_id.c_str()));

    rankinfo_str = info.world_id + "," + info.zone_id + "," + info.instance_id + "," + info.type + "," + info.sub_type +
                   "," + info.sub_instance_id;

    int collection_cnt = 1;
    k++;
    cursor++;
    while (k < all_combinations[process_group_index].size() && collection_cnt < 8) {
      // 相同榜单实例最多一次查8个
      const SubRankInfo& info = all_combinations[process_group_index][k];
      if (rank_info.uiWorldId == uint32_t(atoi(info.world_id.c_str())) && rank_info.uiZoneId == uint32_t(atoi(info.zone_id.c_str())) &&
          rank_info.uiType == uint32_t(atoi(info.type.c_str())) && rank_info.uiInstanceId == uint32_t(atoi(info.instance_id.c_str()))) {
        sub_rank_collection.Add(atoi(info.sub_type.c_str()), atoi(info.sub_instance_id.c_str()));
        rankinfo_str += ("\n" + info.world_id + "," + info.zone_id + "," + info.instance_id + "," + info.type + "," +
                         info.sub_type + "," + info.sub_instance_id);
        collection_cnt++;
        k++;
        cursor++;
      } else {
        break;
      }
    }

    // groupid 放在callback第一位
    callback = std::to_string(all_groups[process_group_index]) + "," + rankinfo_str;

    if (sendImageStatus(rank_info, sub_rank_collection, callback.c_str(), 1024) == 0) {
      send_succ_cnt++;
      group_results[all_groups[process_group_index]].request++;
    } else {
      writeFailResultToFile(all_groups[process_group_index], rankinfo_str.c_str());
      err_cnt++;
    }

    send_cnt_++;
    total_send_count++;

    index[process_group_index] = k;  // 保存index
  }

  int GetGroupID(const string& filename) {
    // FLAGS_input_dir + "/" + subrank_list_group_175_20250620_124106.csv
    size_t start = filename.find("group_", filename.find_last_of("/"));
    if (start == std::string::npos) {
      return 0;
    }
    start += 6;  // "group_" 的长度是6，所以数字的起始位置是start+6
    // 然后从start位置开始查找下一个下划线
    size_t end = filename.find('_', start);  // 从start位置开始找第一个'_'
    if (end == std::string::npos) {
      return 0;
    }
    // 截取数字子串
    std::string num_str = filename.substr(start, end - start);
    return std::stoi(num_str);
  }

 private:
  // 响应处理类
  class TopNextResponseHandler : public TopNextResponseListener {
   public:
    TopNextResponseHandler(TopNextQueryTool* tool) : tool_(tool) {}

    int OnTopNextGetTopSubRankRsp(TopNextGetTopSubRankRsp& stRsp) override {  // 获取响应结果信息
      int result = -1;
      char error_msg[1024] = {0};
      char callback[1024] = {0};
      uint32_t callback_len = sizeof(callback);

      std::string writted_callback;
      int groupid = 0;

      int ret = stRsp.GetResultInfo(&result, error_msg, sizeof(error_msg), callback, &callback_len);

      if (callback[0] != '\0') {
        const char* comma_pos = strchr(callback, ',');  // 查找第一个逗号
        if (comma_pos != NULL) {
          // 计算子串长度
          int len = comma_pos - callback;
          char num_str[20];
          strncpy(num_str, callback, len);  // 复制子串
          num_str[len] = '\0';              // 添加结束符
          groupid = atoi(num_str);
          writted_callback.assign(comma_pos + 1);
        }
      }

      auto& rc = tool_->group_results[groupid];
      rc.response++;

      if (ret != APOLLO_NO_ERR) {
        tlog_error(tool_->logger_, 0, 0, "Failed to get result info, ret=%d, error=%s", ret, error_msg);
        tool_->err_cnt++;
        tool_->writeFailResultToFile(groupid, writted_callback.c_str());
        return -1;
      }

      if (result != 0) {
        tlog_error(tool_->logger_, 0, 0, "Query failed: result=%d, error=%s", result, error_msg);
        tool_->writeFailResultToFile(groupid, writted_callback.c_str());
        tool_->err_cnt++;
        return -1;
      }

      // 获取数据
      TopNextGetTopSubRankResult rank_result;
      ret = stRsp.GetData(rank_result);
      if (ret != 0) {
        tlog_error(tool_->logger_, 0, 0, "Failed to get response data, ret=%d", ret);
        tool_->writeFailResultToFile(groupid, writted_callback.c_str());
        tool_->err_cnt++;
        return -1;
      }

      tool_->succ_cnt++;
      tool_->writeSuccResultToFile(groupid, writted_callback.c_str(), rank_result.last_image_ts / 1000);  // ms转s

      return 0;
    }

    int OnTopNextGetImageAndReduceStatRsp(TopNextGetImageAndReduceStatRsp& stRsp) override {
      // 获取响应结果信息
      int result = -1;
      char error_msg[1024] = {0};
      char callback[1024] = {0};
      uint32_t callback_len = sizeof(callback);

      std::string writted_callback;
      int groupid = 0;

      int ret = stRsp.GetResultInfo(&result, error_msg, sizeof(error_msg), callback, &callback_len);

      if (callback[0] != '\0') {
        const char* comma_pos = strchr(callback, ',');  // 查找第一个逗号
        if (comma_pos != NULL) {
          // 计算子串长度
          int len = comma_pos - callback;
          char num_str[20];
          strncpy(num_str, callback, len);  // 复制子串
          num_str[len] = '\0';              // 添加结束符
          groupid = atoi(num_str);
          writted_callback.assign(comma_pos + 1);
        }
      }

      auto& rc = tool_->group_results[groupid];
      rc.response++;

      if (ret != APOLLO_NO_ERR) {
        tlog_error(tool_->logger_, 0, 0, "Failed to get result info, ret=%d, error=%s", ret, error_msg);
        tool_->err_cnt++;
        tool_->writeFailResultToFile(groupid, writted_callback.c_str());
        return -1;
      }

      if (result != 0) {
        tlog_error(tool_->logger_, 0, 0, "Query failed: result=%d, error=%s", result, error_msg);
        tool_->writeFailResultToFile(groupid, writted_callback.c_str());
        tool_->err_cnt++;
        return -1;
      }

      // 获取数据
      ImageAndReduceStatResult rank_result;
      ret = stRsp.GetData(rank_result);
      if (ret != 0) {
        tlog_error(tool_->logger_, 0, 0, "Failed to get response data, ret=%d", ret);
        tool_->writeFailResultToFile(groupid, writted_callback.c_str());
        tool_->err_cnt++;
        return -1;
      }

      tool_->succ_cnt++;

      for (uint32_t i = 0; i < rank_result.result_count; i++) {
        if (rank_result.astResult[i].iResult == 0) {
          tool_->writeSuccResultToFile(groupid, rank_result.astResult[i].rank_info,
                                       rank_result.astResult[i].sub_rank_info, rank_result.astResult[i].last_image_ts);
        } else {
          tool_->writeFailResultToFile(groupid, writted_callback.c_str());
        }
      }

      return 0;
    }

    int OnTopNextResponseTimeOuted(const char* callback, uint32_t len) override {
      tlog_error(tool_->logger_, 0, 0, "%s Request timeout", callback);
      tool_->timeout_cnt++;

      std::string writted_callback;
      int groupid = 0;

      if (callback[0] != '\0') {
        const char* comma_pos = strchr(callback, ',');  // 查找第一个逗号
        if (comma_pos != NULL) {
          // 计算子串长度
          int len = comma_pos - callback;
          char num_str[20];
          strncpy(num_str, callback, len);  // 复制子串
          num_str[len] = '\0';              // 添加结束符
          groupid = atoi(num_str);
          writted_callback.assign(comma_pos + 1);
        }
      }

      auto& rc = tool_->group_results[groupid];
      rc.response++;

      tool_->writeFailResultToFile(groupid, writted_callback.c_str());
      return 0;
    }

   private:
    string formatTimestamp(time_t ts) {
      struct tm tm;
      localtime_r(&ts, &tm);
      char buf[32] = {0};
      strftime(buf, sizeof(buf), "%Y-%m-%d %H:%M:%S", &tm);
      return string(buf);
    }

    TopNextQueryTool* tool_;
  };

  int initializeLogger() {
    // 初始化日志上下文
    LPTLOGCTX log_ctx = tlog_init_from_file(kLogConfigFile);
    if (!log_ctx) {
      fprintf(stderr, "Failed to initialize log from file: %s\n", kLogConfigFile);
      return -1;
    }

    // 获取日志类别
    logger_ = tlog_get_category(log_ctx, "text");
    if (!logger_) {
      fprintf(stderr, "Failed to get log category\n");
      return -1;
    }

    return 0;
  }

  int generateLogConfig() {
    // 检查配置文件是否已存在
    if (access(kLogConfigFile, F_OK) == 0) {
      return 0;
    }

    // 创建日志目录
    if (access("./log", F_OK) != 0) {
      if (mkdir("./log", 0755) != 0) {
        fprintf(stderr, "Failed to create log directory\n");
        return -1;
      }
    }

    // 生成简化的日志配置
    string log_config = generateLogConfigContent();

    // 写入配置文件
    int fd = open(kLogConfigFile, O_CREAT | O_RDWR | O_TRUNC, 0666);
    if (fd < 0) {
      fprintf(stderr, "Failed to create log config file: %s\n", kLogConfigFile);
      return -1;
    }

    ssize_t written = write(fd, log_config.c_str(), log_config.length());
    close(fd);

    if (written != static_cast<ssize_t>(log_config.length())) {
      fprintf(stderr, "Failed to write log config\n");
      return -1;
    }

    return 0;
  }

  string generateLogConfigContent() {
    char config[4096];
    snprintf(config, sizeof(config),
             "<?xml version=\"1.0\" encoding=\"GBK\" standalone=\"yes\" ?>\n"
             "<TLOGConf version=\"2\">\n"
             "    <Magic>1548 </Magic>\n"
             "    <PriorityHigh>NULL </PriorityHigh>\n"
             "    <PriorityLow>ERROR </PriorityLow>\n"
             "    <DelayInit>0 </DelayInit>\n"
             "    <SuppressError>1 </SuppressError>\n"
             "    <Count>6 </Count>\n"
             "    <CategoryList type=\"TLOGCategory\">\n"
             "        <Name>text</Name>\n"
             "        <PriorityHigh>INFO </PriorityHigh>\n"
             "        <PriorityLow>TRACE </PriorityLow>\n"
             "        <Filter type=\"TLOGFilterVec\">\n"
             "            <Count>1 </Count>\n"
             "            <Filters type=\"TLOGFilter\">\n"
             "                <IDFilter type=\"IntFilter\">\n"
             "                    <Start>0 </Start>\n"
             "                    <Count>100000000 </Count>\n"
             "                    <Mod>0 </Mod>\n"
             "                    <ModStart>0 </ModStart>\n"
             "                    <ModCount>0 </ModCount>\n"
             "                </IDFilter>\n"
             "                <ClsFilter type=\" IntFilter\">\n"
             "                    <Start>0 </Start>\n"
             "                    <Count>100 </Count>\n"
             "                    <Mod>0 </Mod>\n"
             "                    <ModStart>0 </ModStart>\n"
             "                    <ModCount>0 </ModCount>\n"
             "                </ClsFilter>\n"
             "            </Filters>\n"
             "        </Filter>\n"
             "        <LevelDispatch>0 </LevelDispatch>\n"
             "        <MustSucc>0 </MustSucc>\n"
             "        <MaxMsgSize>102400 </MaxMsgSize>\n"
             "        <Format></Format>\n"
             "        <ForwardCat>texttrace</ForwardCat>\n"
             "        <Device type=\"TLOGDevAny\">\n"
             "            <Type>NO </Type>\n"
             "        </Device>\n"
             "    </CategoryList>\n"
             "    <CategoryList type=\"TLOGCategory\">\n"
             "         <Name>texttrace</Name>\n"
             "         <PriorityHigh>NULL </PriorityHigh>\n"
             "         <PriorityLow>TRACE </PriorityLow>\n"
             "         <Filter type=\"TLOGFilterVec\">\n"
             "             <Count>0 </Count>\n"
             "         </Filter>\n"
             "         <LevelDispatch>0 </LevelDispatch>\n"
             "         <MustSucc>0 </MustSucc>\n"
             "         <MaxMsgSize>102400 </MaxMsgSize>\n"
             "         <Format>[%%d.%%u][%%h][(%%f:%%l) (%%F)][%%M][%%p] %%m%%n</Format>\n"
             "         <ForwardCat>texterr</ForwardCat>\n"
             "         <Device type=\"TLOGDevAny\">\n"
             "             <Type>FILE </Type>\n"
             "             <Device type=\"TLOGDevSelector\">\n"
             "                 <File type=\"TLOGDevFile\">\n"
             "                     <Pattern>%s</Pattern>\n"
             "                     <BuffSize>0 </BuffSize>\n"
             "                     <SizeLimit>10485760 </SizeLimit>\n"
             "                     <Precision>1 </Precision>\n"
             "                     <MaxRotate>2 </MaxRotate>\n"
             "                     <SyncTime>0 </SyncTime>\n"
             "                     <NoFindLatest>0 </NoFindLatest>\n"
             "                     <RotateStick>0 </RotateStick>\n"
             "                 </File>\n"
             "             </Device>\n"
             "         </Device>\n"
             "     </CategoryList>\n"
             "     <CategoryList type=\"TLOGCategory\">\n"
             "         <Name>texterr</Name>\n"
             "         <PriorityHigh>NULL </PriorityHigh>\n"
             "         <PriorityLow>ERROR </PriorityLow>\n"
             "         <Filter type=\"TLOGFilterVec\">\n"
             "             <Count>0 </Count>\n"
             "         </Filter>\n"
             "         <LevelDispatch>0 </LevelDispatch>\n"
             "         <MustSucc>0 </MustSucc>\n"
             "         <MaxMsgSize>102400 </MaxMsgSize>\n"
             "         <Format>[%%d.%%u][%%h][(%%f:%%l) (%%F)][%%M][%%p] %%m%%n</Format>\n"
             "         <ForwardCat></ForwardCat>\n"
             "         <Device type=\"TLOGDevAny\">\n"
             "             <Type>FILE </Type>\n"
             "             <Device type=\"TLOGDevSelector\">\n"
             "                 <File type=\"TLOGDevFile\">\n"
             "                     <Pattern>%s</Pattern>\n"
             "                     <BuffSize>0 </BuffSize>\n"
             "                     <SizeLimit>10485760 </SizeLimit>\n"
             "                     <Precision>1 </Precision>\n"
             "                     <MaxRotate>2 </MaxRotate>\n"
             "                     <SyncTime>0 </SyncTime>\n"
             "                     <NoFindLatest>0 </NoFindLatest>\n"
             "                     <RotateStick>0 </RotateStick>\n"
             "                 </File>\n"
             "             </Device>\n"
             "         </Device>\n"
             " </CategoryList>\n"
             " </TLOGConf>\n",
             kLogFileName, kLogErrorFileName);

    return string(config);
  }

  int sendImageStatus(TopNextRankInfo& rank_info, TopNextSubRankInfoCollection& sub_rank_collection,
                      const char* callback, uint32_t callbacklen) {
    TopNextService* topnext_api = apollo_api_->GetTopNextService();
    if (!topnext_api) {
      tlog_error(logger_, 0, 0, "Failed to get TopNextService");
      return -1;
    }

    // 创建请求
    auto req = topnext_api->Get_TopNextGetImageAndReduceStatReq(callback, callbacklen);
    if (!req) {
      tlog_error(logger_, 0, 0, "Failed to create Get_TopNextGetImageAndReduceStatReq");
      return -1;
    }

    // 设置请求参数
    int ret = req->SetPara(rank_info, sub_rank_collection, true);
    if (ret != 0) {
      tlog_error(logger_, 0, 0, "Failed to set request parameters, ret=%d", ret);
      return -1;
    }

    // 发送请求
    ret = topnext_api->SendReq(req);
    if (ret != 0) {
      tlog_error(logger_, 0, 0, "Failed to send request, ret=%d", ret);
      return -1;
    }

    return 0;
  }

  int sendGetTop(TopNextRankInfo& rank_info, TopNextSubRankInfo& sub_rank_info, const char* callback,
                 uint32_t callbacklen) {
    TopNextService* topnext_api = apollo_api_->GetTopNextService();
    if (!topnext_api) {
      tlog_error(logger_, 0, 0, "Failed to get TopNextService");
      return -1;
    }

    // 创建请求
    auto req = topnext_api->Get_TopNextGetTopSubRankReq(callback, callbacklen);
    if (!req) {
      tlog_error(logger_, 0, 0, "Failed to create Get_TopNextGetTopSubRankReq");
      return -1;
    }

    // 设置请求参数
    int ret = req->SetPara(rank_info, sub_rank_info, 1, 1, true);
    if (ret != 0) {
      tlog_error(logger_, 0, 0, "Failed to set request parameters, ret=%d", ret);
      return -1;
    }

    // 发送请求
    ret = topnext_api->SendReq(req);
    if (ret != 0) {
      tlog_error(logger_, 0, 0, "Failed to send request, ret=%d", ret);
      return -1;
    }

    return 0;
  }

  // 创建输出目录
  string createOutputDirectory() {
    // 创建results目录
    if (access(FLAGS_output_dir.c_str(), F_OK) != 0) {
      if (mkdir(FLAGS_output_dir.c_str(), 0755) != 0) {
        tlog_error(logger_, 0, 0, "Failed to create %s directory", FLAGS_output_dir.c_str());
        return "";
      }
    }

    // 创建带时间戳的子目录
    time_t now = time(nullptr);
    struct tm tm;
    localtime_r(&now, &tm);
    char timestamp[32];
    strftime(timestamp, sizeof(timestamp), "%Y%m%d_%H%M%S", &tm);

    string output_dir = FLAGS_output_dir + "/" + timestamp;
    if (mkdir(output_dir.c_str(), 0755) != 0) {
      tlog_error(logger_, 0, 0, "Failed to create output directory: %s", output_dir.c_str());
      return "";
    }

    return output_dir;
  }

  int loadAllCombinations(std::vector<std::vector<SubRankInfo>>& all_combinations, std::vector<int>& all_groups) {
    DIR* dir = opendir(FLAGS_input_dir.c_str());
    if (!dir) {
      tlog_error(logger_, 0, 0, "Failed to open logs directory");
      return -1;
    }

    struct dirent* entry;
    std::vector<string> csv_files;

    // 查找所有subrank_list_*.csv文件
    while ((entry = readdir(dir)) != nullptr) {
      string filename = entry->d_name;
      if (filename.find("subrank_list_") == 0 && filename.find(".csv") != string::npos) {
        csv_files.push_back(FLAGS_input_dir + "/" + filename);
      } else {
        tlog_error(logger_, 0, 0, "invalid filename: %s", filename.c_str());
      }
    }
    closedir(dir);

    if (csv_files.empty()) {
      tlog_error(logger_, 0, 0, "No log files found in %s directory", FLAGS_input_dir.c_str());
      return -1;
    }

    // 处理每个CSV文件
    for (const string& csv_file : csv_files) {
      tlog_info(logger_, 0, 0, "Loading combinations from: %s", csv_file.c_str());
      std::vector<SubRankInfo> combinations;
      if (extractSubInfo(csv_file, combinations) != 0) {
        printf("Failed to extract info from: %s\n", csv_file.c_str());
        continue;
      }
      all_groups.push_back(GetGroupID(csv_file));
      all_combinations.emplace_back(combinations);
    }

    return 0;
  }

  // 从CSV文件中提取子排行榜信息
  int extractSubInfo(const string& log_file, std::vector<SubRankInfo>& combinations) {
    std::ifstream file(log_file.c_str());
    if (!file.is_open()) {
      printf("Failed to open file: %s\n", log_file.c_str());
      return -1;
    }

    string line;
    bool first_line = true;

    int linecnt = 0;

    while (std::getline(file, line)) {
      // 跳过表头
      if (first_line) {
        first_line = false;
        continue;
      }

      linecnt++;

      // 解析CSV行
      std::vector<string> parts;
      std::stringstream ss(line);
      string part;

      while (std::getline(ss, part, ',')) {
        // 去除前后空格
        size_t start = part.find_first_not_of(" \t\r\n");
        size_t end = part.find_last_not_of(" \t\r\n");
        if (start != string::npos && end != string::npos) {
          parts.push_back(part.substr(start, end - start + 1));
        } else {
          parts.push_back("");
        }
      }

      // 检查字段数量：world_id,zone_id,instance_id,type,sub_type,sub_instance_id
      if (parts.size() >= 6) {
        string world_id = parts[0];
        string zone_id = parts[1];
        string instance_id = parts[2];
        string type = parts[3];
        string sub_type = parts[4];
        string sub_instance_id = parts[5];

        // 验证所有字段都是数字
        if (isDigits(world_id) && isDigits(zone_id) && isDigits(instance_id) && isDigits(type) && isDigits(sub_type) &&
            isDigits(sub_instance_id)) {
          combinations.push_back(SubRankInfo(world_id, zone_id, type, instance_id, sub_type, sub_instance_id));
        } else {
          tlog_error(logger_, 0, 0, "invalid field, not digit");
        }
      } else {
        tlog_error(logger_, 0, 0, "not enough fields: %s", line.c_str());
      }
    }

    file.close();
    if (linecnt == 0) {
      return -1;
    }
    return 0;
  }

  // 检查字符串是否全为数字
  bool isDigits(const string& str) {
    if (str.empty()) return false;
    for (char c : str) {
      if (!isdigit(c)) return false;
    }
    return true;
  }

  // 将查询结果写入文件
  void writeSuccResultToFile(int groupid, TopNextRankInfo rank_info, TopNextSubRankInfo sub_rank_info,
                             int64_t last_image_ts) {
    if (output_dir.empty()) return;
    std::string output_file = output_dir + "/" + std::to_string(groupid) + "_" + kSuccRawFile;

    std::ofstream file(output_file.c_str(), std::ios::app);
    if (!file.is_open()) {
      tlog_error(logger_, 0, 0, "Failed to open output file: %s", output_file.c_str());
      return;
    }

    file << rank_info.uiWorldId << "," << rank_info.uiZoneId << "," << rank_info.uiInstanceId << "," << rank_info.uiType
         << "," << sub_rank_info.uiType << "," << sub_rank_info.uiInstanceId << "," << last_image_ts << "\n";
    file.close();
  }

  void writeSuccResultToFile(int groupid, const char* rank_info, int64_t last_image_ts) {
    if (output_dir.empty()) return;
    std::string output_file = output_dir + "/" + std::to_string(groupid) + "_" + kSuccRawFile;

    std::ofstream file(output_file.c_str(), std::ios::app);
    if (!file.is_open()) {
      tlog_error(logger_, 0, 0, "Failed to open output file: %s", output_file.c_str());
      return;
    }

    file << rank_info << "," << last_image_ts << "\n";
    file.close();
  }

  void writeFailResultToFile(int groupid, const char* rankinfo) {
    if (output_dir.empty()) return;
    std::string output_file = output_dir + "/" + std::to_string(groupid) + "_" + kFailRawFile;

    std::ofstream file(output_file.c_str(), std::ios::app);
    if (!file.is_open()) {
      tlog_error(logger_, 0, 0, "Failed to open output file: %s", output_file.c_str());
      return;
    }

    file << rankinfo << "\n";
    file.close();
  }

 public:
  int subrank_cnt = 0;
  int total_send_count = 0;
  int send_succ_cnt = 0;
  int succ_cnt = 0;
  int err_cnt = 0;
  int timeout_cnt = 0;
  string output_dir;
  TLOGCATEGORYINST* logger_;
  std::map<int, ResultCnt> group_results;  // key:groupid

 private:
  ApolloService* apollo_api_;
  TopNextResponseHandler response_handler_{this};
  int send_cnt_ = 0;
  uint64_t last_ts_ = 0;
  RateLimiter limiter;
  RateLimiter group_limiters[64];
};

int main(int argc, char** argv) {
  auto start = NanoSeconds();
  // 解析命令行参数
  google::ParseCommandLineFlags(&argc, &argv, true);

  // 创建查询工具实例
  TopNextQueryTool query_tool;

  // 初始化
  if (query_tool.Initialize() != 0) {
    fprintf(stderr, "Failed to initialize TopNext query tool\n");
    return -1;
  }

  query_tool.Process();

  printf("\ntotal subrank:%d\n", query_tool.subrank_cnt);
  printf("total send:%d\n", query_tool.total_send_count);
  printf("send succ:%d\n", query_tool.send_succ_cnt);
  printf("succ:%d\n", query_tool.succ_cnt);
  printf("err:%d\n", query_tool.err_cnt);
  printf("timeout:%d\n", query_tool.timeout_cnt);

  auto end = NanoSeconds();
  printf("\nTime Cost: %lu Seconds\n", (end - start) / 1000000000);

  return 0;
}
