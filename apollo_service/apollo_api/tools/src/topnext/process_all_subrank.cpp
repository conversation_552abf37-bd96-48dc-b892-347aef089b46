/*!
 * Copyright (c) 2015 Tencent all rights reserved
 *
 */

#include <errno.h>
#include <fcntl.h>
#include <gflags/gflags.h>
#include <inttypes.h>
#include <stdarg.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <tbus/tbus.h>
#include <time.h>
#include <unistd.h>
#include <fstream>
#include <iostream>
#include <memory>
#include <sstream>
#include <string>
#include <vector>
#include "apollo_service.h"
#include "tloghelp/tlogload.h"
#include "tool_common.h"

using std::string;
using namespace apollo_service_api;

// 命令行参数定义
DEFINE_string(dir, "tcp://127.0.0.1:6200", "dir server ip:port, tcp://127.0.0.1:6200");
DEFINE_int32(business_id, 10000, "Business Id");
DEFINE_string(business_key, "d9798cdf31c02d86b8b81cc119d94836", "Business Key");
DEFINE_int32(world_id, 1, "World ID");
DEFINE_int32(zone_id, 1, "Zone ID");
DEFINE_int32(type, 1, "Rank type");
DEFINE_int32(instance_id, 1, "Instance ID");
DEFINE_string(sub_rank_info, "1-1", "Sub rank info (type-instance)");
DEFINE_int32(query_from, 1, "Query start position");
DEFINE_int32(query_count, 100, "Query count");
DEFINE_int32(is_query_image, 0, "Whether to query image (0/1)");
DEFINE_int32(image_index, 0, "Image index (0-8)");
DEFINE_string(encryptkey, "", "Encrypt key");
DEFINE_string(csv_file, "sub_rank_list.csv", "CSV file to process");
DEFINE_bool(batch_mode, false, "Enable batch processing mode");
DEFINE_bool(optimized_batch, false, "Enable optimized batch processing mode (like mainv1.cpp)");

// 常量定义
namespace {
constexpr int DEFAULT_TIMEOUT = 3000;
constexpr int SLEEP_INTERVAL_MS = 10;
constexpr int MAX_IMAGE_INDEX = 8;
constexpr int LOG_FILE_SIZE_LIMIT = 10485760;  // 10MB
constexpr int LOG_MAX_ROTATE = 2;
constexpr const char* LOG_FILE_NAME = "./log/topnext_tool.log";
constexpr const char* LOG_ERROR_FILE_NAME = "./log/topnext_tool.error";
constexpr const char* LOG_CONFIG_FILE = "./topnext_log.xml";
}  // namespace

// CSV数据结构
struct SubRankCombination {
  int world_id;
  int instance_id;
  int sub_type;
  int sub_instance_id;

  SubRankCombination(int w, int i, int st, int si) : world_id(w), instance_id(i), sub_type(st), sub_instance_id(si) {}
};

class TopNextQueryTool {
 public:
  TopNextQueryTool()
      : status_(1), logger_(nullptr), apollo_api_(nullptr), total_requests_sent_(0), total_responses_received_(0) {}

  ~TopNextQueryTool() {
    if (apollo_api_) {
      delete apollo_api_;
    }
  }

  int Initialize() {
    // 初始化日志
    if (InitializeLogger() != 0) {
      fprintf(stderr, "Failed to initialize logger\n");
      return -1;
    }

    // 创建Apollo服务实例
    apollo_api_ = new ApolloService(logger_);
    if (!apollo_api_) {
      LogError("Failed to create ApolloService instance");
      return -1;
    }

    // 添加服务地址
    int ret = apollo_api_->AddServiceURL(FLAGS_dir.c_str());
    if (ret != 0) {
      LogError("Failed to add service URL: %s, ret=%d", FLAGS_dir.c_str(), ret);
      return -1;
    }

    // 设置回调处理器
    ret = apollo_api_->SetTopNextCallBack(&response_handler_);
    if (ret != 0) {
      LogError("Failed to set TopNext callback, ret=%d", ret);
      return -1;
    }

    // 初始化API
    ret = apollo_api_->Init(FLAGS_business_id, FLAGS_business_key.c_str(), APOLLO_SERVICE_API_VERSION, DEFAULT_TIMEOUT,
                            FLAGS_encryptkey.c_str());
    if (ret != 0) {
      LogError("Failed to initialize API, ret=%d", ret);
      return -1;
    }

    LogInfo("TopNext query tool initialized successfully");
    return 0;
  }

  int ExecuteQuery() {
    if (!apollo_api_) {
      LogError("Apollo API not initialized");
      return -1;
    }

    // 发送查询请求
    int ret = SendGetTopSubRankRequest();
    if (ret != 0) {
      LogError("Failed to send query request, ret=%d", ret);
      return -1;
    }

    // 等待响应
    while (status_ != 0) {
      usleep(SLEEP_INTERVAL_MS * 1000);
      apollo_api_->Update();
    }

    return 0;
  }

  int ExecuteBatchQuery() {
    if (!apollo_api_) {
      LogError("Apollo API not initialized");
      return -1;
    }

    // 读取CSV文件
    std::vector<SubRankCombination> combinations;
    if (ExtractSubInfo(FLAGS_csv_file, combinations) != 0) {
      LogError("Failed to extract sub info from CSV file: %s", FLAGS_csv_file.c_str());
      return -1;
    }

    if (combinations.empty()) {
      LogError("No valid combinations found in CSV file");
      return -1;
    }

    // 创建结果目录
    string output_dir = CreateOutputDirectory();
    if (output_dir.empty()) {
      LogError("Failed to create output directory");
      return -1;
    }

    LogInfo("Results will be saved to: %s", output_dir.c_str());
    LogInfo("Found %zu combinations to process", combinations.size());

    // 重置计数器
    ResetCounters();

    // 处理每个组合
    std::vector<int> query_from_values = {1, 101};

    for (size_t i = 0; i < combinations.size(); ++i) {
      const SubRankCombination& combo = combinations[i];

      printf("\nProcessing combination %zu/%zu: world_id=%d, instance_id=%d, sub_type=%d, sub_instance=%d\n", i + 1,
             combinations.size(), combo.world_id, combo.instance_id, combo.sub_type, combo.sub_instance_id);

      // 创建输出文件
      string output_file = CreateOutputFile(output_dir, combo);
      if (output_file.empty()) {
        LogError("Failed to create output file for combination");
        continue;
      }

      // 对每个query_from值执行查询
      for (int query_from : query_from_values) {
        if (ExecuteSingleQuery(combo, query_from, output_file) != 0) {
          LogError("Failed to execute query for query_from=%d", query_from);
        }
        // ExecuteSingleQuery内部会处理请求发送和响应接收的计数
      }
    }

    // 在所有请求发送完成后，继续调用Update()确保所有响应都被处理
    // 参考mainv1.cpp中Drive()方法的实现模式，持续调用apollo_api_->Update()
    // 来确保所有发送的请求都能收到响应
    LogInfo("All requests sent (%d), ensuring all responses are processed...", GetRequestsSent());

    // 持续调用Update()，根据请求数和响应数判断是否退出
    int max_wait_cycles = 5000;  // 最大等待周期
    int wait_cycles = 0;

    while (!AllResponsesReceived() && wait_cycles < max_wait_cycles) {
      apollo_api_->Update();
      usleep(SLEEP_INTERVAL_MS * 1000);
      wait_cycles++;

      // 每50个周期输出一次进度
      if (wait_cycles % 50 == 0) {
        LogInfo("Waiting for responses... Sent: %d, Received: %d", GetRequestsSent(), GetResponsesReceived());
      }
    }

    if (AllResponsesReceived()) {
      LogInfo("Batch processing completed successfully. Total requests: %d, responses: %d", GetRequestsSent(),
              GetResponsesReceived());
    } else {
      LogError("Timeout in batch processing. Sent: %d, Received: %d, Wait cycles: %d", GetRequestsSent(),
               GetResponsesReceived(), wait_cycles);
    }

    return 0;
  }

  // 新增：优化的批量查询方法，参考mainv1.cpp的实现模式
  int ExecuteBatchQueryOptimized() {
    if (!apollo_api_) {
      LogError("Apollo API not initialized");
      return -1;
    }

    // 读取CSV文件
    std::vector<SubRankCombination> combinations;
    if (ExtractSubInfo(FLAGS_csv_file, combinations) != 0) {
      LogError("Failed to extract sub info from CSV file: %s", FLAGS_csv_file.c_str());
      return -1;
    }

    if (combinations.empty()) {
      LogError("No valid combinations found in CSV file");
      return -1;
    }

    // 创建结果目录
    string output_dir = CreateOutputDirectory();
    if (output_dir.empty()) {
      LogError("Failed to create output directory");
      return -1;
    }

    LogInfo("Results will be saved to: %s", output_dir.c_str());
    LogInfo("Found %zu combinations to process", combinations.size());

    // 使用优化的批量处理模式
    return ExecuteBatchWithContinuousUpdate(combinations, output_dir);
  }

  // 实现类似mainv1.cpp中的批量发送和持续Update模式
  int ExecuteBatchWithContinuousUpdate(const std::vector<SubRankCombination>& combinations, const string& output_dir) {
    std::vector<int> query_from_values = {1, 101};
    int total_requests_to_send = combinations.size() * query_from_values.size();

    // 重置计数器
    ResetCounters();

    LogInfo("Starting batch processing with continuous update for %d total requests", total_requests_to_send);

    // 批量发送所有请求，同时持续调用Update()
    for (size_t i = 0; i < combinations.size(); ++i) {
      const SubRankCombination& combo = combinations[i];

      printf("\nProcessing combination %zu/%zu: world_id=%d, instance_id=%d, sub_type=%d, sub_instance=%d\n", i + 1,
             combinations.size(), combo.world_id, combo.instance_id, combo.sub_type, combo.sub_instance_id);

      // 创建输出文件
      string output_file = CreateOutputFile(output_dir, combo);
      if (output_file.empty()) {
        LogError("Failed to create output file for combination");
        continue;
      }

      // 对每个query_from值发送请求，但不等待响应
      for (int query_from : query_from_values) {
        if (SendBatchRequest(combo, query_from, output_file) == 0) {
          IncrementRequestsSent();
        }

        // 在发送请求的同时调用Update()，类似mainv1.cpp中的Drive()方法
        apollo_api_->Update();
      }
    }

    // 所有请求发送完成后，继续调用Update()直到所有响应都被处理
    LogInfo("All %d requests sent, waiting for responses...", GetRequestsSent());

    // 持续调用Update()确保所有响应都被处理，根据请求数和响应数判断是否退出
    int max_wait_cycles = 10000;  // 最大等待周期，防止无限等待
    int wait_cycles = 0;

    while (!AllResponsesReceived() && wait_cycles < max_wait_cycles) {
      apollo_api_->Update();
      usleep(SLEEP_INTERVAL_MS * 1000);
      wait_cycles++;

      // 每100个周期输出一次进度
      if (wait_cycles % 100 == 0) {
        LogInfo("Waiting for responses... Sent: %d, Received: %d", GetRequestsSent(), GetResponsesReceived());
      }
    }

    if (AllResponsesReceived()) {
      LogInfo("All responses received successfully. Total requests: %d, responses: %d", GetRequestsSent(),
              GetResponsesReceived());
    } else {
      LogError("Timeout waiting for responses. Sent: %d, Received: %d, Wait cycles: %d", GetRequestsSent(),
               GetResponsesReceived(), wait_cycles);
    }
    return 0;
  }

  // 发送单个批量请求，不等待响应
  int SendBatchRequest(const SubRankCombination& combo, int query_from, const string& output_file) {
    // 临时保存原始参数
    int orig_world_id = FLAGS_world_id;
    int orig_instance_id = FLAGS_instance_id;
    string orig_sub_rank_info = FLAGS_sub_rank_info;
    int orig_query_from = FLAGS_query_from;

    // 设置新参数
    FLAGS_world_id = combo.world_id;
    FLAGS_instance_id = combo.instance_id;
    FLAGS_query_from = query_from;

    // 构造sub_rank_info字符串
    char sub_rank_buffer[64];
    snprintf(sub_rank_buffer, sizeof(sub_rank_buffer), "%d-%d", combo.sub_type, combo.sub_instance_id);
    FLAGS_sub_rank_info = sub_rank_buffer;

    // 设置响应处理器的输出信息
    response_handler_.SetOutputInfo(output_file, query_from);

    // 发送查询请求（不等待响应）
    int ret = SendGetTopSubRankRequest();

    // 恢复原始参数
    FLAGS_world_id = orig_world_id;
    FLAGS_instance_id = orig_instance_id;
    FLAGS_sub_rank_info = orig_sub_rank_info;
    FLAGS_query_from = orig_query_from;

    if (ret != 0) {
      LogError("Failed to send batch request, ret=%d", ret);
      return -1;
    }

    return 0;
  }

  // 计数器管理方法
  void ResetCounters() {
    total_requests_sent_ = 0;
    total_responses_received_ = 0;
  }

  void IncrementRequestsSent() { total_requests_sent_++; }

  void IncrementResponsesReceived() { total_responses_received_++; }

  int GetRequestsSent() const { return total_requests_sent_; }

  int GetResponsesReceived() const { return total_responses_received_; }

  bool AllResponsesReceived() const {
    return total_responses_received_ >= total_requests_sent_ && total_requests_sent_ > 0;
  }

 private:
  // 响应处理类
  class TopNextResponseHandler : public TopNextResponseListener {
   public:
    TopNextResponseHandler(TopNextQueryTool* tool) : tool_(tool), current_output_file_(""), current_query_from_(0) {}

    void SetOutputInfo(const string& output_file, int query_from) {
      current_output_file_ = output_file;
      current_query_from_ = query_from;
    }

    int OnTopNextGetTopSubRankRsp(TopNextGetTopSubRankRsp& stRsp) override {
      if (!tool_) return -1;

      tool_->status_ = 0;
      tool_->IncrementResponsesReceived();  // 增加响应计数

      // 获取响应结果信息
      int result = -1;
      char error_msg[1024] = {0};
      char callback[1024] = {0};
      uint32_t callback_len = sizeof(callback);

      int ret = stRsp.GetResultInfo(&result, error_msg, sizeof(error_msg), callback, &callback_len);
      if (ret != APOLLO_NO_ERR) {
        tool_->LogError("Failed to get result info, ret=%d, error=%s", ret, error_msg);
        return -1;
      }

      if (result != 0) {
        tool_->LogError("Query failed: result=%d, error=%s", result, error_msg);
        return -1;
      }

      // 获取数据
      TopNextGetTopSubRankResult rank_result;
      ret = stRsp.GetData(rank_result);
      if (ret != 0) {
        tool_->LogError("Failed to get response data, ret=%d", ret);
        return -1;
      }

      // 输出结果
      tool_->LogInfo("Query successful: total_count=%u, image_generate_ts=%lu", rank_result.uiTotalCount,
                     rank_result.last_image_ts);

      // 如果有输出文件，写入文件；否则输出到控制台
      if (!current_output_file_.empty()) {
        OutputRankResultToFile(rank_result);
      } else {
        OutputRankResult(rank_result);
      }
      return 0;
    }

    int OnTopNextResponseTimeOuted(const char* data, uint32_t len) override {
      if (!tool_) return -1;

      tool_->LogError("Request timeout");
      tool_->status_ = 0;
      tool_->IncrementResponsesReceived();  // 超时也算作响应
      return 0;
    }

   private:
    void OutputRankResult(const TopNextGetTopSubRankResult& result) {
      printf("Total Count: %u, Image Generate Time: %lu\n", result.uiTotalCount, result.last_image_ts);

      // 输出表头
      printf(
          "%-6s %-11s %-10s %-20s %-6s %-6s %-6s %-6s %-6s "
          "%-14s %-14s %-14s %-6s %-6s %-6s\n",
          "Rank", "OpenID", "Score", "Timestamp", "SF1", "SF2", "SF3", "SF4", "SF5", "Ext1", "Ext2", "Ext3", "Red1",
          "Red2", "ExtLen");

      printf(
          "%-6s %-11s %-10s %-20s %-6s %-6s %-6s %-6s %-6s "
          "%-14s %-14s %-14s %-6s %-6s %-6s\n",
          "----", "------", "-----", "---------", "---", "---", "---", "---", "---", "----", "----", "----", "----",
          "----", "------");

      // 输出数据行
      for (uint32_t i = 0; i < result.uiResultCount; ++i) {
        const auto& rank_info = result.astResult[i].stUserRankInfo;
        const auto& user_info = rank_info.stUserInfo;

        string time_str = FormatTimestamp(rank_info.ullTimeStamp / 1000);

        printf(
            "%-6u %-11s %-10u %-20s %-6u %-6u %-6u %-6u %-6u "
            "%-14" PRIu64 " %-14" PRIu64 " %-14" PRIu64 " %-6u %-6u %-6u\n",
            rank_info.uiRankNo, user_info.szOpenId, user_info.uiScore, time_str.c_str(), user_info.uiSortField1,
            user_info.uiSortField2, user_info.uiSortField3, user_info.uiSortField4, user_info.uiSortField5,
            user_info.ullExtField1, user_info.ullExtField2, user_info.ullExtField3, user_info.uiReduceField1,
            user_info.uiReduceField2, user_info.uiExtDataLength);
      }
    }

    void OutputRankResultToFile(const TopNextGetTopSubRankResult& result) {
      if (current_output_file_.empty()) return;

      // 如果结果为空，不输出文件
      if (result.uiResultCount == 0) {
        return;
      }

      // 检查文件是否存在，如果不存在则创建并写入表头
      bool file_exists = (access(current_output_file_.c_str(), F_OK) == 0);

      std::ofstream file(current_output_file_.c_str(), std::ios::app);
      if (!file.is_open()) {
        return;
      }

      // 如果是新文件，先写入CSV表头
      if (!file_exists) {
        file << "No,OpenID,Score,Timestamp,F1,F2,F3,F4,F5,E1,E2,E3,R1,R2,R3,ExtLen\n";
      }

      // 写入CSV数据行
      for (uint32_t i = 0; i < result.uiResultCount; ++i) {
        const auto& rank_info = result.astResult[i].stUserRankInfo;
        const auto& user_info = rank_info.stUserInfo;

        // 格式化时间戳为 YYYY-MM-DD HH:MM:SS 格式
        string time_str_data = FormatTimestamp(rank_info.ullTimeStamp / 1000);

        // 写入CSV格式的数据行
        file << rank_info.uiRankNo << "," << user_info.szOpenId << "," << user_info.uiScore << "," << time_str_data
             << "," << user_info.uiSortField1 << "," << user_info.uiSortField2 << "," << user_info.uiSortField3 << ","
             << user_info.uiSortField4 << "," << user_info.uiSortField5 << "," << user_info.ullExtField1 << ","
             << user_info.ullExtField2 << "," << user_info.ullExtField3 << "," << user_info.uiReduceField1 << ","
             << user_info.uiReduceField2 << "," << user_info.uiReduceField3 << "," << user_info.uiExtDataLength << "\n";
      }

      file.close();
    }

    string FormatTimestamp(time_t ts) {
      struct tm tm;
      localtime_r(&ts, &tm);
      char buf[32] = {0};
      strftime(buf, sizeof(buf), "%Y-%m-%d %H:%M:%S", &tm);
      return string(buf);
    }

    TopNextQueryTool* tool_;
    string current_output_file_;
    int current_query_from_;
  };

  int InitializeLogger() {
    // 生成日志配置
    if (GenerateLogConfig() != 0) {
      return -1;
    }

    // 初始化日志上下文
    LPTLOGCTX log_ctx = tlog_init_from_file(LOG_CONFIG_FILE);
    if (!log_ctx) {
      fprintf(stderr, "Failed to initialize log from file: %s\n", LOG_CONFIG_FILE);
      return -1;
    }

    // 获取日志类别
    logger_ = tlog_get_category(log_ctx, "text");
    if (!logger_) {
      fprintf(stderr, "Failed to get log category\n");
      return -1;
    }

    return 0;
  }

  int GenerateLogConfig() {
    // 检查配置文件是否已存在
    if (access(LOG_CONFIG_FILE, F_OK) == 0) {
      return 0;
    }

    // 创建日志目录
    if (access("./log", F_OK) != 0) {
      if (mkdir("./log", 0755) != 0) {
        fprintf(stderr, "Failed to create log directory\n");
        return -1;
      }
    }

    // 生成简化的日志配置
    string log_config = GenerateLogConfigContent();

    // 写入配置文件
    int fd = open(LOG_CONFIG_FILE, O_CREAT | O_RDWR | O_TRUNC, 0666);
    if (fd < 0) {
      fprintf(stderr, "Failed to create log config file: %s\n", LOG_CONFIG_FILE);
      return -1;
    }

    ssize_t written = write(fd, log_config.c_str(), log_config.length());
    close(fd);

    if (written != static_cast<ssize_t>(log_config.length())) {
      fprintf(stderr, "Failed to write log config\n");
      return -1;
    }

    return 0;
  }

  string GenerateLogConfigContent() {
    char config[4096];
    snprintf(config, sizeof(config),
             "<?xml version=\"1.0\" encoding=\"GBK\" standalone=\"yes\" ?>\n"
             "<TLOGConf version=\"2\">\n"
             "  <Magic>1548</Magic>\n"
             "  <PriorityHigh>NULL</PriorityHigh>\n"
             "  <PriorityLow>DEBUG</PriorityLow>\n"
             "  <SuppressError>1</SuppressError>\n"
             "  <Count>3</Count>\n"
             "  <CategoryList type=\"TLOGCategory\">\n"
             "    <Name>text</Name>\n"
             "    <PriorityHigh>INFO</PriorityHigh>\n"
             "    <PriorityLow>TRACE</PriorityLow>\n"
             "    <Device type=\"TLOGDevAny\">\n"
             "      <Type>FILE</Type>\n"
             "      <Device type=\"TLOGDevSelector\">\n"
             "        <File type=\"TLOGDevFile\">\n"
             "          <Pattern>%s</Pattern>\n"
             "          <SizeLimit>%d</SizeLimit>\n"
             "          <MaxRotate>%d</MaxRotate>\n"
             "        </File>\n"
             "      </Device>\n"
             "    </Device>\n"
             "  </CategoryList>\n"
             "</TLOGConf>\n",
             LOG_FILE_NAME, LOG_FILE_SIZE_LIMIT, LOG_MAX_ROTATE);

    return string(config);
  }

  int SendGetTopSubRankRequest() {
    TopNextService* topnext_api = apollo_api_->GetTopNextService();
    if (!topnext_api) {
      LogError("Failed to get TopNextService");
      return -1;
    }

    // 创建请求
    int callback = 0x1234;
    TopNextGetTopSubRankReq* req =
        topnext_api->Get_TopNextGetTopSubRankReq(reinterpret_cast<char*>(&callback), sizeof(callback));
    if (!req) {
      LogError("Failed to create TopNextGetTopSubRankReq");
      return -1;
    }

    // 设置排行榜信息
    TopNextRankInfo rank_info;
    rank_info.uiWorldId = FLAGS_world_id;
    rank_info.uiZoneId = FLAGS_zone_id;
    rank_info.uiType = FLAGS_type;
    rank_info.uiInstanceId = FLAGS_instance_id;

    // 解析子排行榜信息
    TopNextSubRankInfoCollection collection;
    ToolCommon::ParseSubRankInfo(FLAGS_sub_rank_info, collection);

    TopNextSubRankInfo sub_rank_info;
    if (collection.Count() > 0) {
      sub_rank_info = *(collection.Info());
    }

    // 设置请求参数
    int ret = req->SetPara(rank_info, sub_rank_info, FLAGS_query_from, FLAGS_query_count, FLAGS_is_query_image == 1,
                           FLAGS_image_index);
    if (ret != 0) {
      LogError("Failed to set request parameters, ret=%d", ret);
      return -1;
    }

    // 发送请求
    ret = topnext_api->SendReq(req);
    if (ret != 0) {
      LogError("Failed to send request, ret=%d", ret);
      return -1;
    }

    LogInfo(
        "Query request sent: world=%d, zone=%d, type=%d, instance=%d, "
        "from=%d, count=%d",
        FLAGS_world_id, FLAGS_zone_id, FLAGS_type, FLAGS_instance_id, FLAGS_query_from, FLAGS_query_count);

    return 0;
  }

  // 批量处理相关的辅助方法
  int ExtractSubInfo(const string& csv_file, std::vector<SubRankCombination>& combinations) {
    std::ifstream file(csv_file.c_str());
    if (!file.is_open()) {
      LogError("Failed to open CSV file: %s", csv_file.c_str());
      return -1;
    }

    string line;
    bool first_line = true;

    while (std::getline(file, line)) {
      // 跳过标题行
      if (first_line) {
        first_line = false;
        continue;
      }

      // 跳过空行
      if (line.empty()) {
        continue;
      }

      // 解析CSV行
      std::vector<string> parts;
      std::stringstream ss(line);
      string part;

      while (std::getline(ss, part, ',')) {
        // 去除空格
        size_t start = part.find_first_not_of(" \t");
        size_t end = part.find_last_not_of(" \t");
        if (start != string::npos && end != string::npos) {
          parts.push_back(part.substr(start, end - start + 1));
        } else if (start != string::npos) {
          parts.push_back(part.substr(start));
        } else {
          parts.push_back("");
        }
      }

      // 检查是否有足够的字段
      if (parts.size() >= 4) {
        // 验证所有字段都是数字
        bool all_digits = true;
        for (int i = 0; i < 4; ++i) {
          if (parts[i].empty()) {
            all_digits = false;
            break;
          }
          for (char c : parts[i]) {
            if (!isdigit(c)) {
              all_digits = false;
              break;
            }
          }
          if (!all_digits) break;
        }

        if (all_digits) {
          int world_id = atoi(parts[0].c_str());
          int instance_id = atoi(parts[1].c_str());
          int sub_type = atoi(parts[2].c_str());
          int sub_instance_id = atoi(parts[3].c_str());

          combinations.push_back(SubRankCombination(world_id, instance_id, sub_type, sub_instance_id));
        }
      }
    }

    file.close();
    LogInfo("Extracted %zu combinations from CSV file", combinations.size());
    return 0;
  }

  string CreateOutputDirectory() {
    // 获取当前时间戳
    time_t now = time(0);
    struct tm* tm_info = localtime(&now);
    char timestamp[32];
    strftime(timestamp, sizeof(timestamp), "%Y%m%d_%H%M%S", tm_info);

    // 创建结果目录路径
    string output_dir = string("results/") + timestamp;

    // 创建results目录（如果不存在）
    if (mkdir("results", 0755) != 0 && errno != EEXIST) {
      LogError("Failed to create results directory");
      return "";
    }

    // 创建时间戳目录
    if (mkdir(output_dir.c_str(), 0755) != 0) {
      LogError("Failed to create output directory: %s", output_dir.c_str());
      return "";
    }

    return output_dir;
  }

  string CreateOutputFile(const string& output_dir, const SubRankCombination& combo) {
    char filename[256];
    snprintf(filename, sizeof(filename), "processed_results_%d_%d_%d_%d.csv", combo.world_id, combo.instance_id,
             combo.sub_type, combo.sub_instance_id);

    string output_file = output_dir + "/" + filename;

    // 只返回文件路径，不预先创建文件
    // 文件将在首次有数据时创建并写入表头
    return output_file;
  }

  int ExecuteSingleQuery(const SubRankCombination& combo, int query_from, const string& output_file) {
    // 重置状态
    status_ = 1;

    // 临时保存原始参数
    int orig_world_id = FLAGS_world_id;
    int orig_instance_id = FLAGS_instance_id;
    string orig_sub_rank_info = FLAGS_sub_rank_info;
    int orig_query_from = FLAGS_query_from;

    // 设置新参数
    FLAGS_world_id = combo.world_id;
    FLAGS_instance_id = combo.instance_id;
    FLAGS_query_from = query_from;

    // 构造sub_rank_info字符串
    char sub_rank_buffer[64];
    snprintf(sub_rank_buffer, sizeof(sub_rank_buffer), "%d-%d", combo.sub_type, combo.sub_instance_id);
    FLAGS_sub_rank_info = sub_rank_buffer;

    printf("Executing query for world_id=%d, instance_id=%d, sub_type=%d, sub_instance=%d, query_from=%d\n",
           combo.world_id, combo.instance_id, combo.sub_type, combo.sub_instance_id, query_from);

    // 设置响应处理器的输出信息
    response_handler_.SetOutputInfo(output_file, query_from);

    // 发送查询请求
    int ret = SendGetTopSubRankRequest();
    if (ret != 0) {
      LogError("Failed to send query request, ret=%d", ret);
      AppendErrorToFile(output_file, query_from, "Failed to send query request");

      // 恢复原始参数
      FLAGS_world_id = orig_world_id;
      FLAGS_instance_id = orig_instance_id;
      FLAGS_sub_rank_info = orig_sub_rank_info;
      FLAGS_query_from = orig_query_from;
      return -1;
    }

    // 增加请求计数
    IncrementRequestsSent();

    // 等待响应
    while (status_ != 0) {
      usleep(SLEEP_INTERVAL_MS * 1);
      apollo_api_->Update();
    }

    // 恢复原始参数
    FLAGS_world_id = orig_world_id;
    FLAGS_instance_id = orig_instance_id;
    FLAGS_sub_rank_info = orig_sub_rank_info;
    FLAGS_query_from = orig_query_from;

    return 0;
  }

  void AppendErrorToFile(const string& /* output_file */, int query_from, const string& error_msg) {
    // 对于CSV格式，我们不需要在文件中记录错误信息
    // 错误信息会通过日志系统记录
    LogError("Query failed for query_from=%d: %s", query_from, error_msg.c_str());
  }

  // 日志辅助函数
  void LogInfo(const char* format, ...) {
    if (!logger_) return;

    va_list args;
    va_start(args, format);
    char buffer[1024];
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);

    tlog_info(logger_, 0, 0, "%s", buffer);
  }

  void LogError(const char* format, ...) {
    va_list args;
    va_start(args, format);
    char buffer[1024];
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);

    fprintf(stderr, "[ERROR] %s\n", buffer);
    if (logger_) {
      tlog_error(logger_, 0, 0, "%s", buffer);
    }
  }

 private:
  volatile int status_;
  TLOGCATEGORYINST* logger_;
  ApolloService* apollo_api_;
  TopNextResponseHandler response_handler_{this};

  // 批量处理的请求和响应计数器
  volatile int total_requests_sent_;
  volatile int total_responses_received_;
};

// 显示帮助信息
void ShowHelp(const char* program_name) {
  printf("Usage: %s [OPTIONS]\n", program_name);
  printf("\nOptions:\n");
  printf("  --dir              Dir server address (default: tcp://127.0.0.1:6200)\n");
  printf("  --business_id      Business ID (default: 10000)\n");
  printf("  --business_key     Business key\n");
  printf("  --world_id         World ID (default: 1)\n");
  printf("  --zone_id          Zone ID (default: 1)\n");
  printf("  --type             Rank type (default: 1)\n");
  printf("  --instance_id      Instance ID (default: 1)\n");
  printf("  --sub_rank_info    Sub rank info format: type-instance (default: 1-1)\n");
  printf("  --query_from       Query start position (default: 1)\n");
  printf("  --query_count      Query count (default: 100)\n");
  printf("  --is_query_image   Query image flag (0/1, default: 0)\n");
  printf("  --image_index      Image index (0-8, default: 0)\n");
  printf("  --encryptkey       Encryption key (optional)\n");
  printf("  --csv_file         CSV file to process in batch mode (default: image_generate_after_00_10_00.csv)\n");
  printf("  --batch_mode       Enable batch processing mode (default: false)\n");
  printf("  --optimized_batch  Enable optimized batch processing mode like mainv1.cpp (default: false)\n");
  printf("  --help             Show this help message\n");
  printf("\n");
  printf("Batch Mode:\n");
  printf("  When --batch_mode=true, the tool will read combinations from the specified CSV file\n");
  printf("  and execute queries for each combination with query_from values [1, 101].\n");
  printf("  Results will be saved to timestamped files in the results/ directory.\n");
  printf("\n");
  printf("Optimized Batch Mode:\n");
  printf("  When --optimized_batch=true, the tool uses a pattern similar to mainv1.cpp\n");
  printf("  where requests are sent and apollo_api->Update() is called continuously\n");
  printf("  to accelerate sending and receiving responses.\n");
  printf("\n");
}

int main(int argc, char** argv) {
  // 解析命令行参数
  google::ParseCommandLineFlags(&argc, &argv, true);

  // 创建查询工具实例
  TopNextQueryTool query_tool;

  // 初始化
  if (query_tool.Initialize() != 0) {
    fprintf(stderr, "Failed to initialize TopNext query tool\n");
    return -1;
  }

  // 根据模式执行查询
  if (FLAGS_optimized_batch) {
    printf("Running in optimized batch mode (like mainv1.cpp) with CSV file: %s\n", FLAGS_csv_file.c_str());
    if (query_tool.ExecuteBatchQueryOptimized() != 0) {
      fprintf(stderr, "Failed to execute optimized batch query\n");
      return -1;
    }
  } else if (FLAGS_batch_mode) {
    printf("Running in standard batch mode with CSV file: %s\n", FLAGS_csv_file.c_str());
    if (query_tool.ExecuteBatchQuery() != 0) {
      fprintf(stderr, "Failed to execute batch query\n");
      return -1;
    }
  } else {
    printf("Running in single query mode\n");
    if (query_tool.ExecuteQuery() != 0) {
      fprintf(stderr, "Failed to execute query\n");
      return -1;
    }
  }

  return 0;
}
