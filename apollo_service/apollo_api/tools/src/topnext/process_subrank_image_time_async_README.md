# TopNext 批量查询镜像时间 (process_subrank_image_time_async)

用于批量快速查询 TopNext 排行榜镜像生成时间的工具。支持查询镜像生成时间，以及统计生成时间的范围分布。


## 使用方法

### 基本用法

使用`get_subrank_list`工具预先导出子榜列表，文件格式为`subrank_list_group_$groupid_$exporttime.csv`, 将子榜列表的文件存放在指定目录, 例如(input)。目录结构如下：

```bash
.
├── input
│   ├── subrank_list_group_178_20250619_210506.csv
│   ├── subrank_list_group_180_20250619_210506.csv
│   ├── subrank_list_group_183_20250619_210506.csv
│   ├── subrank_list_group_186_20250619_210506.csv
│   └── subrank_list_group_187_20250619_210506.csv
└── process_subrank_image_time_async
```

`subrank_list_group_$groupid_$exporttime.csv`文件内容格式为：

```csv
world_id,zone_id,instance_id,type,sub_type,sub_instance_id
62,1,10069,100,1003,653131
62,1,10069,100,1003,653130
```

运行：

```bash
./process_subrank_image_time_async [参数]
```

输出结果存放在指定目录中，例如(output)，每次运行以时间戳生成一个子目录，每个输入的csv文件，会产出三个文件:

`$groupid_raw_succ.txt` : 所有子榜的镜像生成时间<br/>
  - 列：world_id,zone_id,instance_id,type,sub_type,sub_instance_id,timestamp<br/>

`$groupid_raw_fail.txt` : 处理失败的子榜<br/>
  - 列：world_id,zone_id,instance_id,type,sub_type,sub_instance_id<br/>

`$groupid_result.txt` : 所有子榜的镜像生成时间分布统计
  - 列：时间范围, 总数<br/>


```bash
 output
│   ├── 20250623_000434
│   │   ├── 178_raw_succ.txt
│   │   ├── 178_raw_fail.txt
│   │   ├── 178_result.txt
│   │   ├── 180_raw_succ.txt
│   │   ├── 180_raw_fail.txt
│   │   ├── 187_raw_succ.txt
│   │   ├── 187_raw_fail.txt
│   │   └── 187_result.txt
```

### 参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `-dir` | 服务器地址 | tcp://10.123.16.138:13001 |
| `-business_id` | 业务ID | 10000 |
| `-business_key` | 业务密钥 | d9798cdf31c02d86b8b81cc119d94836 |
| `-encryptkey` | 加密key | "" |
| `-get_top` | 使用使用get_top接口查询，结果准确，速度相对慢。否则使用image_status接口，速度快，随机查询主备svr，结果不准确 | true |
| `-input_dir` | 子榜列表csv文件存放目录 | ./input |
| `-output_dir` | 结果输出目录 | ./output |
| `-start_time` | 指定统计的起始时间，格式为"2025-06-23 00:00:00" | “2025-06-23 00:00:00” |
| `-max_group_rate` | 每个group的请求频率限制 | 1500 |
| `-max_rate` | 工具总的请求频率限制| 5000 |

### 使用示例

1. 基本查询：

```bash
./process_subrank_image_time_async -business_id=$bizid -business_key=$bizkey -dir=$dir -input_dir=./input -output_dir=./output -max_rate=5000 -max_group_rate=1500 -start_time="2025-06-23 00:00:00" -get_top=true
```

2. 脚本批量查询

示例脚本见`query_image_time_example.sh`, 可以输出**存在未生成镜像的**groupid列表

```bash
./process_subrank_image_time_async -business_id=$bizid -business_key=$bizkey -dir=$dir -input_dir=./input -output_dir=./output -max_rate=$maxrate -max_group_rate=$maxgrouprate -start_time="2025-06-23 00:00:00" -get_top=$gettop | tee /dev/tty | grep --line-buffered "image not genarated" | grep -oP 'groupid:\K\d+' | awk '{printf "%s,", $0}' >> ungenarated.log &
```

## 注意事项

1. 需要提前导出子榜列表，以groupid区分文件
2. 需要严格控制`max_group_rate`, 以免对现网产生影响
3. 需要严格控制`max_rate`, 避免现网proxy过载
