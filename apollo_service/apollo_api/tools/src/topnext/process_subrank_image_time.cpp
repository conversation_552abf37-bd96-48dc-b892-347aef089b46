/*!
 * Copyright (c) 2015 Tencent all rights reserved
 *
 */

#include <dirent.h>
#include <gflags/gflags.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <sys/wait.h>
#include <tbus/tbus.h>
#include <time.h>
#include <unistd.h>
#include <fstream>
#include <future>
#include <iostream>
#include <memory>
#include <mutex>
#include <regex>
#include <sstream>
#include <string>
#include <thread>
#include <vector>
#include "apollo_service.h"
#include "tloghelp/tlogload.h"
#include "tool_common.h"

using std::string;
using namespace apollo_service_api;

// 命令行参数定义
DEFINE_string(dir, "tcp://127.0.0.1:6200", "dir server ip:port, tcp://127.0.0.1:6200");
DEFINE_int32(business_id, 10000, "Business Id");
DEFINE_string(business_key, "d9798cdf31c02d86b8b81cc119d94836", "Business Key");
DEFINE_int32(world_id, 1, "World ID");
DEFINE_int32(zone_id, 1, "Zone ID");
DEFINE_int32(type, 1, "Rank type");
DEFINE_int32(instance_id, 1, "Instance ID");
DEFINE_string(sub_rank_info, "1-1", "Sub rank info (type-instance)");
DEFINE_int32(query_from, 1, "Query start position");
DEFINE_int32(query_count, 100, "Query count");
DEFINE_int32(is_query_image, 0, "Whether to query image (0/1)");
DEFINE_int32(image_index, 0, "Image index (0-8)");
DEFINE_string(encryptkey, "", "Encrypt key");
DEFINE_bool(batch_mode, false, "Enable batch processing mode to process CSV files");

// 常量定义
namespace {
constexpr int kDefaultTimeout = 3000;
constexpr int kSleepIntervalMs = 10;
constexpr int kMaxImageIndex = 8;
constexpr int kLogFileSizeLimit = 10485760;  // 10MB
constexpr int kLogMaxRotate = 2;
constexpr const char* kLogFileName = "./log/topnext_tool.log";
constexpr const char* kLogErrorFileName = "./log/topnext_tool.error";
constexpr const char* kLogConfigFile = "./topnext_tool_log.xml";
}  // namespace

// 子排行榜信息结构
struct SubRankInfo {
  string world_id;
  string zone_id;
  string type;
  string instance_id;
  string sub_type;
  string sub_instance_id;

  SubRankInfo() {}
  SubRankInfo(const string& w, const string& z, const string& t, const string& i, const string& st, const string& si)
      : world_id(w), zone_id(z), type(t), instance_id(i), sub_type(st), sub_instance_id(si) {}
};

// 查询结果结构
struct QueryResult {
  string world_id;
  string zone_id;
  string type;
  string instance_id;
  string sub_type;
  string sub_instance_id;
  string image_generate_ts;
  string image_generate_datetime;
  string timestamp;
  bool success;

  QueryResult() : success(false) {}
};

class TopNextQueryTool {
 public:
  TopNextQueryTool() : status_(1), logger_(nullptr), apollo_api_(nullptr) {}

  ~TopNextQueryTool() {
    if (apollo_api_) {
      delete apollo_api_;
    }
  }

  int Initialize() {
    // 初始化日志
    if (initializeLogger() != 0) {
      fprintf(stderr, "Failed to initialize logger\n");
      return -1;
    }

    // 创建Apollo服务实例
    apollo_api_ = new ApolloService(logger_);
    if (!apollo_api_) {
      logError("Failed to create ApolloService instance");
      return -1;
    }

    // 添加服务地址
    int ret = apollo_api_->AddServiceURL(FLAGS_dir.c_str());
    if (ret != 0) {
      logError("Failed to add service URL: %s, ret=%d", FLAGS_dir.c_str(), ret);
      return -1;
    }

    // 设置回调处理器
    ret = apollo_api_->SetTopNextCallBack(&response_handler_);
    if (ret != 0) {
      logError("Failed to set TopNext callback, ret=%d", ret);
      return -1;
    }

    // 初始化API
    ret = apollo_api_->Init(FLAGS_business_id, FLAGS_business_key.c_str(), APOLLO_SERVICE_API_VERSION, kDefaultTimeout,
                            FLAGS_encryptkey.c_str());
    if (ret != 0) {
      logError("Failed to initialize API, ret=%d", ret);
      return -1;
    }

    logInfo("TopNext query tool initialized successfully");
    return 0;
  }

  int ExecuteQuery() {
    if (!apollo_api_) {
      logError("Apollo API not initialized");
      return -1;
    }

    // 发送查询请求
    int ret = sendGetTopSubRankRequest();
    if (ret != 0) {
      logError("Failed to send query request, ret=%d", ret);
      return -1;
    }

    // 等待响应
    while (status_ != 0) {
      usleep(kSleepIntervalMs * 1000);
      apollo_api_->Update();
    }

    return 0;
  }

  // 批量处理主函数
  int ProcessBatch() {
    // 创建结果目录
    string output_dir = createOutputDirectory();
    if (output_dir.empty()) {
      logError("Failed to create output directory");
      return -1;
    }

    printf("Results will be saved to: %s\n", output_dir.c_str());

    // 读取所有CSV文件中的组合
    std::vector<SubRankInfo> all_combinations;
    if (loadAllCombinations(all_combinations) != 0) {
      logError("Failed to load combinations from CSV files");
      return -1;
    }

    if (all_combinations.empty()) {
      printf("No combinations found to process.\n");
      return 0;
    }

    printf("Total combinations to process: %zu\n", all_combinations.size());

    // 创建CSV结果文件
    string csv_file = output_dir + "/image_generate_ts.csv";
    std::vector<QueryResult> csv_results;

    // 逐个处理组合
    for (size_t i = 0; i < all_combinations.size(); ++i) {
      const SubRankInfo& info = all_combinations[i];

      printf(
          "[%zu/%zu] Processing: world_id=%s, zone_id=%s, type=%s, instance_id=%s, sub_type=%s, sub_instance_id=%s\n",
          i + 1, all_combinations.size(), info.world_id.c_str(), info.zone_id.c_str(), info.type.c_str(),
          info.instance_id.c_str(), info.sub_type.c_str(), info.sub_instance_id.c_str());

      QueryResult result = processSingleCombination(info, output_dir);
      if (result.success) {
        csv_results.push_back(result);
        printf("✓ Success: Image_Generate_ts=%s\n", result.image_generate_ts.c_str());
      } else {
        printf("✗ Failed\n");
      }
    }

    // 写入CSV结果
    if (writeCsvResults(csv_file, csv_results) != 0) {
      logError("Failed to write CSV results");
      return -1;
    }

    printf("\nProcessing completed!\n");
    printf("Total combinations processed: %zu\n", all_combinations.size());
    printf("Successful extractions: %zu\n", csv_results.size());
    printf("Image_Generate_ts values saved to: %s\n", csv_file.c_str());

    return 0;
  }

 private:
  // 响应处理类
  class TopNextResponseHandler : public TopNextResponseListener {
   public:
    TopNextResponseHandler(TopNextQueryTool* tool) : tool_(tool) {}

    int OnTopNextGetTopSubRankRsp(TopNextGetTopSubRankRsp& stRsp) override {
      if (!tool_) return -1;

      tool_->status_ = 0;

      // 获取响应结果信息
      int result = -1;
      char error_msg[1024] = {0};
      char callback[1024] = {0};
      uint32_t callback_len = sizeof(callback);

      int ret = stRsp.GetResultInfo(&result, error_msg, sizeof(error_msg), callback, &callback_len);
      if (ret != APOLLO_NO_ERR) {
        tool_->logError("Failed to get result info, ret=%d, error=%s", ret, error_msg);
        return -1;
      }

      if (result != 0) {
        tool_->logError("Query failed: result=%d, error=%s", result, error_msg);
        return -1;
      }

      // 获取数据
      TopNextGetTopSubRankResult rank_result;
      ret = stRsp.GetData(rank_result);
      if (ret != 0) {
        tool_->logError("Failed to get response data, ret=%d", ret);
        return -1;
      }

      // 输出结果
      tool_->logInfo("Query successful: total_count=%u, image_generate_ts=%lu", rank_result.uiTotalCount,
                     rank_result.last_image_ts);

      // 保存Image_Generate_ts到当前结果
      if (rank_result.last_image_ts > 0) {
        char ts_str[32];
        snprintf(ts_str, sizeof(ts_str), "%lu", rank_result.last_image_ts);
        tool_->current_result_.image_generate_ts = ts_str;
        tool_->current_result_.image_generate_datetime = tool_->convertTimestampToDateTime(rank_result.last_image_ts);
        tool_->current_result_.success = true;
      }

      // 将结果写入文件
      tool_->writeResultToFile(rank_result);

      outputRankResult(rank_result);
      return 0;
    }

    int OnTopNextResponseTimeOuted(const char* data, uint32_t len) override {
      if (!tool_) return -1;

      tool_->logError("Request timeout");
      tool_->status_ = 0;
      return 0;
    }

   private:
    void outputRankResult(const TopNextGetTopSubRankResult& result) {
      printf("Total Count: %u, Image Generate Time: %lu\n", result.uiTotalCount, result.last_image_ts);

      // 输出表头
      printf(
          "%-6s %-11s %-10s %-20s %-6s %-6s %-6s %-6s %-6s "
          "%-14s %-14s %-14s %-6s %-6s %-6s\n",
          "Rank", "OpenID", "Score", "Timestamp", "SF1", "SF2", "SF3", "SF4", "SF5", "Ext1", "Ext2", "Ext3", "Red1",
          "Red2", "ExtLen");

      printf(
          "%-6s %-11s %-10s %-20s %-6s %-6s %-6s %-6s %-6s "
          "%-14s %-14s %-14s %-6s %-6s %-6s\n",
          "----", "------", "-----", "---------", "---", "---", "---", "---", "---", "----", "----", "----", "----",
          "----", "------");

      // 输出数据行
      for (uint32_t i = 0; i < result.uiResultCount; ++i) {
        const auto& rank_info = result.astResult[i].stUserRankInfo;
        const auto& user_info = rank_info.stUserInfo;

        string time_str = formatTimestamp(rank_info.ullTimeStamp / 1000);

        printf(
            "%-6u %-11s %-10u %-20s %-6u %-6u %-6u %-6u %-6u "
            "%-14" PRIu64 " %-14" PRIu64 " %-14" PRIu64 " %-6u %-6u %-6u\n",
            rank_info.uiRankNo, user_info.szOpenId, user_info.uiScore, time_str.c_str(), user_info.uiSortField1,
            user_info.uiSortField2, user_info.uiSortField3, user_info.uiSortField4, user_info.uiSortField5,
            user_info.ullExtField1, user_info.ullExtField2, user_info.ullExtField3, user_info.uiReduceField1,
            user_info.uiReduceField2, user_info.uiExtDataLength);
      }
    }

    string formatTimestamp(time_t ts) {
      struct tm tm;
      localtime_r(&ts, &tm);
      char buf[32] = {0};
      strftime(buf, sizeof(buf), "%Y-%m-%d %H:%M:%S", &tm);
      return string(buf);
    }

    TopNextQueryTool* tool_;
  };

  int initializeLogger() {
    // 生成日志配置
    if (generateLogConfig() != 0) {
      return -1;
    }

    // 初始化日志上下文
    LPTLOGCTX log_ctx = tlog_init_from_file(kLogConfigFile);
    if (!log_ctx) {
      fprintf(stderr, "Failed to initialize log from file: %s\n", kLogConfigFile);
      return -1;
    }

    // 获取日志类别
    logger_ = tlog_get_category(log_ctx, "text");
    if (!logger_) {
      fprintf(stderr, "Failed to get log category\n");
      return -1;
    }

    return 0;
  }

  int generateLogConfig() {
    // 检查配置文件是否已存在
    if (access(kLogConfigFile, F_OK) == 0) {
      return 0;
    }

    // 创建日志目录
    if (access("./log", F_OK) != 0) {
      if (mkdir("./log", 0755) != 0) {
        fprintf(stderr, "Failed to create log directory\n");
        return -1;
      }
    }

    // 生成简化的日志配置
    string log_config = generateLogConfigContent();

    // 写入配置文件
    int fd = open(kLogConfigFile, O_CREAT | O_RDWR | O_TRUNC, 0666);
    if (fd < 0) {
      fprintf(stderr, "Failed to create log config file: %s\n", kLogConfigFile);
      return -1;
    }

    ssize_t written = write(fd, log_config.c_str(), log_config.length());
    close(fd);

    if (written != static_cast<ssize_t>(log_config.length())) {
      fprintf(stderr, "Failed to write log config\n");
      return -1;
    }

    return 0;
  }

  string generateLogConfigContent() {
    char config[4096];
    snprintf(config, sizeof(config),
             "<?xml version=\"1.0\" encoding=\"GBK\" standalone=\"yes\" ?>\n"
             "<TLOGConf version=\"2\">\n"
             "  <Magic>1548</Magic>\n"
             "  <PriorityHigh>NULL</PriorityHigh>\n"
             "  <PriorityLow>ERROR</PriorityLow>\n"
             "  <SuppressError>1</SuppressError>\n"
             "  <Count>3</Count>\n"
             "  <CategoryList type=\"TLOGCategory\">\n"
             "    <Name>text</Name>\n"
             "    <PriorityHigh>INFO</PriorityHigh>\n"
             "    <PriorityLow>TRACE</PriorityLow>\n"
             "    <Device type=\"TLOGDevAny\">\n"
             "      <Type>FILE</Type>\n"
             "      <Device type=\"TLOGDevSelector\">\n"
             "        <File type=\"TLOGDevFile\">\n"
             "          <Pattern>%s</Pattern>\n"
             "          <SizeLimit>%d</SizeLimit>\n"
             "          <MaxRotate>%d</MaxRotate>\n"
             "        </File>\n"
             "      </Device>\n"
             "    </Device>\n"
             "  </CategoryList>\n"
             "</TLOGConf>\n",
             kLogFileName, kLogFileSizeLimit, kLogMaxRotate);

    return string(config);
  }

  int sendGetTopSubRankRequest() {
    TopNextService* topnext_api = apollo_api_->GetTopNextService();
    if (!topnext_api) {
      logError("Failed to get TopNextService");
      return -1;
    }

    // 创建请求
    int callback = 0x1234;
    TopNextGetTopSubRankReq* req =
        topnext_api->Get_TopNextGetTopSubRankReq(reinterpret_cast<char*>(&callback), sizeof(callback));
    if (!req) {
      logError("Failed to create TopNextGetTopSubRankReq");
      return -1;
    }

    // 设置排行榜信息
    TopNextRankInfo rank_info;
    rank_info.uiWorldId = FLAGS_world_id;
    rank_info.uiZoneId = FLAGS_zone_id;
    rank_info.uiType = FLAGS_type;
    rank_info.uiInstanceId = FLAGS_instance_id;

    // 解析子排行榜信息
    TopNextSubRankInfoCollection collection;
    ToolCommon::ParseSubRankInfo(FLAGS_sub_rank_info, collection);

    TopNextSubRankInfo sub_rank_info;
    if (collection.Count() > 0) {
      sub_rank_info = *(collection.Info());
    }

    // 设置请求参数
    int ret = req->SetPara(rank_info, sub_rank_info, FLAGS_query_from, FLAGS_query_count, FLAGS_is_query_image == 1,
                           FLAGS_image_index);
    if (ret != 0) {
      logError("Failed to set request parameters, ret=%d", ret);
      return -1;
    }

    // 发送请求
    ret = topnext_api->SendReq(req);
    if (ret != 0) {
      logError("Failed to send request, ret=%d", ret);
      return -1;
    }

    logInfo(
        "Query request sent: world=%d, zone=%d, type=%d, instance=%d, "
        "from=%d, count=%d",
        FLAGS_world_id, FLAGS_zone_id, FLAGS_type, FLAGS_instance_id, FLAGS_query_from, FLAGS_query_count);

    return 0;
  }

  // 创建输出目录
  string createOutputDirectory() {
    // 创建logs目录
    if (access("./logs", F_OK) != 0) {
      if (mkdir("./logs", 0755) != 0) {
        logError("Failed to create logs directory");
        return "";
      }
    }

    // 创建results目录
    if (access("./results", F_OK) != 0) {
      if (mkdir("./results", 0755) != 0) {
        logError("Failed to create results directory");
        return "";
      }
    }

    // 创建带时间戳的子目录
    time_t now = time(nullptr);
    struct tm tm;
    localtime_r(&now, &tm);
    char timestamp[32];
    strftime(timestamp, sizeof(timestamp), "%Y%m%d_%H%M%S", &tm);

    string output_dir = string("./results/") + timestamp;
    if (mkdir(output_dir.c_str(), 0755) != 0) {
      logError("Failed to create output directory: %s", output_dir.c_str());
      return "";
    }

    return output_dir;
  }

  // 时间戳转换函数
  string convertTimestampToDateTime(uint64_t timestamp_ms) {
    time_t timestamp_seconds = timestamp_ms / 1000;
    struct tm tm;
    localtime_r(&timestamp_seconds, &tm);
    char buf[32] = {0};
    strftime(buf, sizeof(buf), "%Y-%m-%d %H:%M:%S", &tm);
    return string(buf);
  }

  // 加载所有CSV文件中的组合
  int loadAllCombinations(std::vector<SubRankInfo>& combinations) {
    DIR* dir = opendir("./logs");
    if (!dir) {
      logError("Failed to open logs directory");
      return -1;
    }

    struct dirent* entry;
    std::vector<string> csv_files;

    // 查找所有subrank_list_*.csv文件
    while ((entry = readdir(dir)) != nullptr) {
      string filename = entry->d_name;
      if (filename.find("subrank_list_") == 0 && filename.find(".csv") != string::npos) {
        csv_files.push_back("./logs/" + filename);
      }
    }
    closedir(dir);

    if (csv_files.empty()) {
      printf("No log files found in logs directory\n");
      return -1;
    }

    // 处理每个CSV文件
    for (const string& csv_file : csv_files) {
      printf("Loading combinations from: %s\n", csv_file.c_str());
      if (extractSubInfo(csv_file, combinations) != 0) {
        logError("Failed to extract info from: %s", csv_file.c_str());
        continue;
      }
    }

    return 0;
  }

  // 从CSV文件中提取子排行榜信息
  int extractSubInfo(const string& log_file, std::vector<SubRankInfo>& combinations) {
    std::ifstream file(log_file.c_str());
    if (!file.is_open()) {
      logError("Failed to open file: %s", log_file.c_str());
      return -1;
    }

    string line;
    bool first_line = true;

    while (std::getline(file, line)) {
      // 跳过表头
      if (first_line) {
        first_line = false;
        continue;
      }

      // 解析CSV行
      std::vector<string> parts;
      std::stringstream ss(line);
      string part;

      while (std::getline(ss, part, ',')) {
        // 去除前后空格
        size_t start = part.find_first_not_of(" \t\r\n");
        size_t end = part.find_last_not_of(" \t\r\n");
        if (start != string::npos && end != string::npos) {
          parts.push_back(part.substr(start, end - start + 1));
        } else {
          parts.push_back("");
        }
      }

      // 检查字段数量：world_id,zone_id,instance_id,type,sub_type,sub_instance_id
      if (parts.size() >= 6) {
        string world_id = parts[0];
        string zone_id = parts[1];
        string instance_id = parts[2];
        string type = parts[3];
        string sub_type = parts[4];
        string sub_instance_id = parts[5];

        // 验证所有字段都是数字
        if (isDigits(world_id) && isDigits(zone_id) && isDigits(instance_id) && isDigits(type) && isDigits(sub_type) &&
            isDigits(sub_instance_id)) {
          combinations.push_back(SubRankInfo(world_id, zone_id, type, instance_id, sub_type, sub_instance_id));
        }
      }
    }

    file.close();
    return 0;
  }

  // 检查字符串是否全为数字
  bool isDigits(const string& str) {
    if (str.empty()) return false;
    for (char c : str) {
      if (!isdigit(c)) return false;
    }
    return true;
  }

  // 处理单个组合
  QueryResult processSingleCombination(const SubRankInfo& info, const string& output_dir) {
    QueryResult result;
    result.world_id = info.world_id;
    result.zone_id = info.zone_id;
    result.type = info.type;
    result.instance_id = info.instance_id;
    result.sub_type = info.sub_type;
    result.sub_instance_id = info.sub_instance_id;

    // 获取当前时间戳
    time_t now = time(nullptr);
    struct tm tm;
    localtime_r(&now, &tm);
    char timestamp[32];
    strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", &tm);
    result.timestamp = timestamp;

    // 创建输出文件
    char filename[256];
    snprintf(filename, sizeof(filename), "%s/results_%s_%s_%s_%s_%s_%s.txt", output_dir.c_str(), info.world_id.c_str(),
             info.zone_id.c_str(), info.type.c_str(), info.instance_id.c_str(), info.sub_type.c_str(),
             info.sub_instance_id.c_str());
    current_output_file_ = filename;

    // 写入文件头
    std::ofstream file(filename);
    if (!file.is_open()) {
      logError("Failed to create output file: %s", filename);
      return result;
    }

    file << "Results for world_id: " << info.world_id << ", zone_id: " << info.zone_id << ", type: " << info.type
         << ", instance_id: " << info.instance_id << ", sub_type: " << info.sub_type
         << ", sub_instance_id: " << info.sub_instance_id << "\n";
    file << "Generated at: " << timestamp << "\n";
    file << "Query from: 1\n";
    file << "================================================================================\n";
    file.close();

    // 重置当前结果
    current_result_ = QueryResult();
    current_result_.world_id = info.world_id;
    current_result_.zone_id = info.zone_id;
    current_result_.type = info.type;
    current_result_.instance_id = info.instance_id;
    current_result_.sub_type = info.sub_type;
    current_result_.sub_instance_id = info.sub_instance_id;
    current_result_.timestamp = timestamp;

    // 设置查询参数
    FLAGS_world_id = atoi(info.world_id.c_str());
    FLAGS_zone_id = atoi(info.zone_id.c_str());
    FLAGS_type = atoi(info.type.c_str());
    FLAGS_instance_id = atoi(info.instance_id.c_str());
    FLAGS_sub_rank_info = info.sub_type + "-" + info.sub_instance_id;
    FLAGS_query_from = 1;
    FLAGS_query_count = 1;
    FLAGS_is_query_image = 1;

    // 执行查询
    status_ = 1;
    if (sendGetTopSubRankRequest() == 0) {
      // 等待响应
      while (status_ != 0) {
        usleep(kSleepIntervalMs * 1);
        apollo_api_->Update();
      }
    }

    return current_result_;
  }

  // 写入CSV结果
  int writeCsvResults(const string& csv_file, const std::vector<QueryResult>& results) {
    std::ofstream file(csv_file.c_str());
    if (!file.is_open()) {
      logError("Failed to create CSV file: %s", csv_file.c_str());
      return -1;
    }

    // 写入表头
    file << "world_id,zone_id,type,instance_id,sub_type,sub_instance_id,image_generate_ts,image_generate_datetime,"
            "timestamp\n";

    // 写入数据行
    for (const QueryResult& result : results) {
      file << result.world_id << "," << result.zone_id << "," << result.type << "," << result.instance_id << ","
           << result.sub_type << "," << result.sub_instance_id << "," << result.image_generate_ts << ","
           << result.image_generate_datetime << "," << result.timestamp << "\n";
    }

    file.close();
    return 0;
  }

  // 将查询结果写入文件
  void writeResultToFile(const TopNextGetTopSubRankResult& rank_result) {
    if (current_output_file_.empty()) return;

    std::ofstream file(current_output_file_.c_str(), std::ios::app);
    if (!file.is_open()) {
      logError("Failed to open output file: %s", current_output_file_.c_str());
      return;
    }

    file << "\n================================================================================\n";
    file << "World ID: " << current_result_.world_id << "\n";
    file << "Zone ID: " << current_result_.zone_id << "\n";
    file << "Type: " << current_result_.type << "\n";
    file << "Instance ID: " << current_result_.instance_id << "\n";
    file << "Sub Type: " << current_result_.sub_type << "\n";
    file << "Sub Instance ID: " << current_result_.sub_instance_id << "\n";
    file << "Query from: 1\n";
    file << "Timestamp: " << current_result_.timestamp << "\n";

    if (!current_result_.image_generate_ts.empty()) {
      file << "Image_Generate_ts: " << current_result_.image_generate_ts << "\n";
    }

    file << "\n=== QUERY RESULT ===\n";
    file << "Total Count: " << rank_result.uiTotalCount << ", Image Generate Time: " << rank_result.last_image_ts
         << "\n";

    // 输出排行榜数据
    if (rank_result.uiResultCount > 0) {
      file << "\nRank Data:\n";
      file << "Rank  OpenID      Score      Timestamp            SF1   SF2   SF3   SF4   SF5   "
           << "Ext1           Ext2           Ext3           Red1  Red2  ExtLen\n";
      file << "----  ------      -----      ---------            ---   ---   ---   ---   ---   "
           << "----           ----           ----           ----  ----  ------\n";

      for (uint32_t i = 0; i < rank_result.uiResultCount; ++i) {
        const auto& rank_info = rank_result.astResult[i].stUserRankInfo;
        const auto& user_info = rank_info.stUserInfo;

        string time_str = convertTimestampToDateTime(rank_info.ullTimeStamp);

        file << rank_info.uiRankNo << "     " << user_info.szOpenId << "     " << user_info.uiScore << "     "
             << time_str << "     " << user_info.uiSortField1 << "     " << user_info.uiSortField2 << "     "
             << user_info.uiSortField3 << "     " << user_info.uiSortField4 << "     " << user_info.uiSortField5
             << "     " << user_info.ullExtField1 << "     " << user_info.ullExtField2 << "     "
             << user_info.ullExtField3 << "     " << user_info.uiReduceField1 << "     " << user_info.uiReduceField2
             << "     " << user_info.uiExtDataLength << "\n";
      }
    }

    file << "\nReturn code: 0\n";
    file.close();
  }

  // 日志辅助函数
  void logInfo(const char* format, ...) {
    if (!logger_) return;

    va_list args;
    va_start(args, format);
    char buffer[1024];
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);

    tlog_info(logger_, 0, 0, "%s", buffer);
  }

  void logError(const char* format, ...) {
    va_list args;
    va_start(args, format);
    char buffer[1024];
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);

    fprintf(stderr, "[ERROR] %s\n", buffer);
    if (logger_) {
      tlog_error(logger_, 0, 0, "%s", buffer);
    }
  }

 private:
  volatile int status_;
  TLOGCATEGORYINST* logger_;
  ApolloService* apollo_api_;
  TopNextResponseHandler response_handler_{this};

  // 批量处理相关成员
  QueryResult current_result_;
  string current_output_file_;
};

// 显示帮助信息
void showHelp(const char* program_name) {
  printf("Usage: %s [OPTIONS]\n", program_name);
  printf("\nOptions:\n");
  printf("  --dir              Dir server address (default: tcp://127.0.0.1:6200)\n");
  printf("  --business_id      Business ID (default: 10000)\n");
  printf("  --business_key     Business key\n");
  printf("  --world_id         World ID (default: 1)\n");
  printf("  --zone_id          Zone ID (default: 1)\n");
  printf("  --type             Rank type (default: 1)\n");
  printf("  --instance_id      Instance ID (default: 1)\n");
  printf("  --sub_rank_info    Sub rank info format: type-instance (default: 1-1)\n");
  printf("  --query_from       Query start position (default: 1)\n");
  printf("  --query_count      Query count (default: 100)\n");
  printf("  --is_query_image   Query image flag (0/1, default: 0)\n");
  printf("  --image_index      Image index (0-8, default: 0)\n");
  printf("  --encryptkey       Encryption key (optional)\n");
  printf("  --batch_mode       Enable batch processing mode to process CSV files (default: false)\n");
  printf("  --help             Show this help message\n");
  printf("\n");
  printf("Batch Mode:\n");
  printf("  When --batch_mode=true, the tool will:\n");
  printf("  1. Read all subrank_list_*.csv files from ./logs directory\n");
  printf("  2. Process each combination (world_id, instance_id, sub_type, sub_instance_id)\n");
  printf("  3. Extract Image_Generate_ts values and save results to ./results/timestamp/ directory\n");
  printf("  4. Generate a CSV file with all Image_Generate_ts values\n");
  printf("\n");
}

int main(int argc, char** argv) {
  // 解析命令行参数
  google::ParseCommandLineFlags(&argc, &argv, true);

  // 创建查询工具实例
  TopNextQueryTool query_tool;

  // 初始化
  if (query_tool.Initialize() != 0) {
    fprintf(stderr, "Failed to initialize TopNext query tool\n");
    return -1;
  }

  // 根据模式执行不同的操作
  if (FLAGS_batch_mode) {
    printf("Running in batch processing mode...\n");
    if (query_tool.ProcessBatch() != 0) {
      fprintf(stderr, "Failed to process batch\n");
      return -1;
    }
  } else {
    printf("Running in single query mode...\n");
    if (query_tool.ExecuteQuery() != 0) {
      fprintf(stderr, "Failed to execute query\n");
      return -1;
    }
  }

  return 0;
}
