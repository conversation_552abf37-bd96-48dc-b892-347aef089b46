/*
 * Tencent is pleased to support the open source community by making Pebble available.
 * Copyright (C) 2016 THL A29 Limited, a Tencent company. All rights reserved.
 * Licensed under the MIT License (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 * http://opensource.org/licenses/MIT
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 *
 */


#ifndef TMSG_CONF_H_
#define TMSG_CONF_H_
#include <string>
#include <string.h>

namespace tmsg {
#pragma pack(8)
class GlobalConf {
    public:
    GlobalConf();
    void Init();
    int Check(std::string* perr_info) const;
    int tmsg_mgr_index();
    int tmsg_data_index();
    int max_sub_queue_count();
    int max_queue_count();
    int max_observer_count();
    int observer_max_event_count();
    int max_event_mgr_count();
    int max_data_block_count();
    int data_block_size();
    std::string DebugString() const;
    bool operator==(const GlobalConf& right) const {
        if (tmsg_mgr_index_ != right.tmsg_mgr_index_)
            return false;
        if (tmsg_data_index_ != right.tmsg_data_index_)
            return false;
        if (mem_type_ != right.mem_type_)
            return false;
        if (mgr_shmkey_ != right.mgr_shmkey_)
            return false;
        if (data_shmkey_ != right.data_shmkey_)
            return false;
        if (max_queue_count_ != right.max_queue_count_)
            return false;
        if (max_sub_queue_count_ != right.max_sub_queue_count_)
            return false;
        if (max_observer_count_ != right.max_observer_count_)
            return false;
        if (observer_max_event_count_ != right.observer_max_event_count_)
            return false;
        if (max_event_mgr_count_ != right.max_event_mgr_count_)
            return false;
        if (max_data_block_count_ != right.max_data_block_count_)
            return false;
        if (data_block_size_ != right.data_block_size_)
            return false;
        int len1 = strlen(mgr_map_file_);
        int len2 = strlen(right.mgr_map_file_);
        if (len1 != len2)
            return false;
        if (strncmp(mgr_map_file_, right.mgr_map_file_, len1))
            return false;
        len1 = strlen(data_map_file_);
        len2 = strlen(right.data_map_file_);
        if (len1 != len2)
            return false;
        if (strncmp(data_map_file_, right.data_map_file_, len1))
            return false;
        return true;
    }
    GlobalConf& operator=(const GlobalConf& right) {
        tmsg_mgr_index_ = right.tmsg_mgr_index_;
        tmsg_data_index_ = right.tmsg_data_index_;
        mem_type_ = right.mem_type_;
        mgr_shmkey_ = right.mgr_shmkey_;
        data_shmkey_ = right.data_shmkey_;
        strncpy(mgr_map_file_, right.mgr_map_file_, sizeof(mgr_map_file_) - 1);
        mgr_map_file_[sizeof(mgr_map_file_) - 1] = 0;
        strncpy(data_map_file_, right.data_map_file_,
                sizeof(data_map_file_) - 1);
        data_map_file_[sizeof(data_map_file_) - 1] = 0;
        max_queue_count_ = right.max_queue_count_;
        max_sub_queue_count_ = right.max_sub_queue_count_;
        max_observer_count_ = right.max_observer_count_;
        observer_max_event_count_ = right.observer_max_event_count_;
        max_event_mgr_count_ = right.max_event_mgr_count_;
        max_data_block_count_ = right.max_data_block_count_;
        data_block_size_ = right.data_block_size_;
        return *this;
    }

    public:
    int tmsg_mgr_index_;  // 绠＄悊鍖� 鍏ㄥ眬鍐呭瓨鍧楃储寮�
    int tmsg_data_index_;  // 鏁版嵁鍖� 鍏ㄥ眬鍐呭瓨鍧楃储寮�
    int max_queue_count_;  // 鏈�澶ч槦鍒楁暟
    int max_sub_queue_count_;  // 鏈�澶у瓙闃熷垪鏁�
    int max_observer_count_;     // 鏈�澶ц瀵熻�呮暟锛屼笉鑳藉皬浜庢渶澶ч槦鍒楁暟
    int observer_max_event_count_;  // 瑙傚療鑰呴噷闈㈡渶澶氬灏戜釜浜嬩欢瀵硅薄
    int max_event_mgr_count_;   // tmsg鏈�澶х殑浜嬩欢瀵硅薄鏁�
    int max_data_block_count_;   // 鏈�澶х殑鏁版嵁block鏁扮洰
    int data_block_size_;     // block鐨勫ぇ灏�.
    int mem_type_;    // 鍏ㄥ眬鍐呭瓨鐨勭被鍨�
    int mgr_shmkey_;  // 绫诲瀷鏄叡浜唴瀛樻椂锛屼娇鐢�
    int data_shmkey_;    // 绫诲瀷鏄叡浜唴瀛樻椂锛屼娇鐢�
    char mgr_map_file_[256];  // 绫诲瀷鏄痬map鏃朵娇鐢�
    char data_map_file_[256];  // 绫诲瀷鏄痬map鏃朵娇鐢�
};

#pragma pack()

} // namespace tmsg

#endif
