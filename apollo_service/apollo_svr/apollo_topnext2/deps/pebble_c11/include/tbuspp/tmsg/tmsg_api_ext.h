/*
 * <PERSON><PERSON> is pleased to support the open source community by making Pebble available.
 * Copyright (C) 2016 THL A29 Limited, a Tencent company. All rights reserved.
 * Licensed under the MIT License (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 * http://opensource.org/licenses/MIT
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 *
 */
 /*
"Copyright (c) 2016, Tencent Inc. All rights reserved."
   georgeli
*/
#ifndef TMSG_API_EXT_H_
#define TMSG_API_EXT_H_
#include <stdlib.h>
#include <string>
#include "tbuspp/tmsg/queue_stddef.h"
// add --std=c++0x

namespace tmsg {

struct PkgPeekInfo {
    unsigned int pkg_sub_queue_index_;
    unsigned int pkg_offset_;
    unsigned int pkg_len_;
    unsigned int pkg_state_;
    unsigned int msg_count_;
    unsigned int bytes_count_;
};


#define MAX_PEEK_COUNT 1024

class QueueOptBase {
    public:
    QueueOptBase();
    virtual ~QueueOptBase() {}
    virtual int Init(unsigned long long qhandle);
    virtual int QueueObjInit(char* pdata_start, void* pq_mgr, void* pblock_mgr);
    unsigned long long GetQHandle() { return qhandle_; }
    unsigned int MsgCount();
    long long MsgBytes();
    unsigned int MaxPeekCount() {
        return MAX_PEEK_COUNT - 1;
    }
    void SetMemPolicy(void* pPolicy) {
        mapfile_mem_policy_ = pPolicy;
    }
    int LockQueue(QueueOpt opt, bool block = true);  // kQueueRead, kQueueWrite;
    void UnLockQueue(QueueOpt opt);
    std::string DebugString();
    void MtQueueEmptyReset(bool ispush);

    protected:
    unsigned long long qhandle_;
    char* pdata_start_;
    void* pq_mgr_;
    void* pblock_mgr_;
    int current_version_;
    void* mapfile_mem_policy_;
    bool  ext_lock_flag_[2];
};

class QueuePeek : public QueueOptBase {
    public:
    QueuePeek();
    int Init(unsigned long long qhandle) {
        int ret = QueueOptBase::Init(qhandle);
        peek_pkg_head_ = peek_pkg_tail_ = 0;
        peek_count_ = 0;
        max_peek_count_ = MAX_PEEK_COUNT - 1;
        return ret;
    }
    int QueueObjInit(char* pdata_start, void* pq_mgr, void* pblock_mgr) {
        int ret = QueueOptBase::QueueObjInit(pdata_start, pq_mgr, pblock_mgr);
        peek_pkg_head_ = peek_pkg_tail_ = 0;
        peek_count_ = 0;
        max_peek_count_ = MAX_PEEK_COUNT - 1;
        return ret;
    }
    int GetMsgInfo(int index, const char** ppmsg,
                   int* pmsg_len, PkgState* pstate, bool block = false);
    int Commit(int count, unsigned int bytes, bool block = false);
    int CommitNotify();
    unsigned int GetPeekMsgCount() { return peek_count_; }
    void SetOnceMaxPeekCount(int count) {
         max_peek_count_ = count > (MAX_PEEK_COUNT - 1) ? (MAX_PEEK_COUNT - 1) : count;
    };
    int Peeking(bool block = false);
    std::string DebugString() {
        char buff[256];
        snprintf(buff, sizeof(buff),
                 "qhandle_: %llu, peek_pkg_head_: %d, peek_pkg_tail_: %d,"
                 " peek_count_: %d, max_peek_count_: %d, msg_count: %u\n",
                 qhandle_, peek_pkg_head_, peek_pkg_tail_,
                 peek_count_, max_peek_count_, MsgCount());
        return buff;
    }

    protected:
    void GetPeekPoint(unsigned int* phead_index, unsigned int* phead) const;
    void  PeekMsgByBuff(const PkgPeekInfo& item,
                        const char** ppmsg, int* pmsg_len, PkgState* pstate);
    void  PeekMsgByBuff(unsigned int index, const char** ppmsg, int* pmsg_len, PkgState* pstate);

    protected:
    int peek_pkg_head_;
    int peek_pkg_tail_;
    int peek_count_;
    int max_peek_count_;
    PkgPeekInfo peek_[MAX_PEEK_COUNT];
};

class QueueGet : public QueuePeek {
    public:
    QueueGet() { commit_count_ = 0; }
    int GetMsg(char* pmsg, int* pmsg_len, PkgState* pstate, bool block);
    int commit_count_;
};


struct PushInfo {
    int count;
    const char* pmsgv[MAX_INPUT_ARRAY_ITEM_COUNT];
    unsigned int lenv[MAX_INPUT_ARRAY_ITEM_COUNT];
};

class QueuePush : public QueueOptBase {
    public:
    QueuePush() { enable_big_packet_ = false; }
    int Push(int count, int qsize, int head,
             const PushInfo* p_pushinfo, bool block);
    int Push(const char* pmsg, int len, bool block);
    int Push(int count,
             const char*  pmsgv[],
             unsigned int lenv[], bool block);
    int PushNotify();
    void EnableBigPack() {
        enable_big_packet_ = true;
    }
    bool enable_big_packet_;
};

/*
   进程内部使用.
   用户自定义分配queue，不使用tmsg管理的queue.
   用户自定义分配内存池.
   不能使用事件通知相关函数
*/
void*   CreateUserBlockMgr(unsigned int block_size, unsigned int block_count);
void    DestoryUserBlockMgr(void* pmgr);
void*   CreateUserQueue(int queue_size, int block_size);
void    DestoryUserQueue(void* pqueue);


} // namespace tmsg

#endif
