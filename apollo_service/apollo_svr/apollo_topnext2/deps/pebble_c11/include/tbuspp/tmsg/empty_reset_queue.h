/*
 * <PERSON><PERSON> is pleased to support the open source community by making Pebble available.
 * Copyright (C) 2016 THL A29 Limited, a Tencent company. All rights reserved.
 * Licensed under the MIT License (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 * http://opensource.org/licenses/MIT
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 *
 */


#ifndef EMPTY_RESET_QUEUE_H_
#define EMPTY_RESET_QUEUE_H_

#include <string.h>
#include <string>
#include <vector>
#include <stdio.h>
#include "tbuspp/tmsg/queue_stddef.h"


namespace tmsg {

#pragma pack(8)
class CycleQueueHead {
    public:
    CycleQueueHead();
    CycleQueueHead(int hoffset, int toffset, int q_size,
                   unsigned long long doffset);
    void Init(int hoffset, int toffset, int q_size, unsigned long long doffset);
    void Release();
    void SetStartOffset(unsigned long long offset);
    unsigned long long GetStartOffset() const;
    unsigned int GetMaxMsgLen() const;
    static int GetMinPkgLen();
    static int QHeadSize();
    int HeadOffset() const;
    int ReadHeadOffset() const;
    int WriteTailOffset() const;
    int TailOffset() const;
    unsigned long long GetDataOffset() const;
    inline int QSize() const {
        return qsize_;
    }

    void HeadAddLen(int len);
    void TailAddLen(int len, volatile unsigned int* pdst_tail);
    void TailAddLen(int len);
    int IsEnablePush(unsigned int msglen);
    unsigned int EnablePushSize(int* pmsg1_size, int* pmsg2_size);
    bool operator == (const CycleQueueHead& right) {
        if (head_ != right.head_)
            return false;
        if (tail_ != right.tail_)
            return false;
        if (qsize_ != right.qsize_)
            return false;
        return true;
    }
    int FreeDataSize() const;
    bool Empty() const;
    bool Full();
    unsigned int UsedSeriesDataSize() const;
    int FreeSeriesDataSize() const;
    unsigned int SeriesDataSize(unsigned int head, unsigned int tail,
                                unsigned int size) const;
    unsigned int MsgLenToPkgLen(unsigned int msglen) const;
    bool DebugPushMsgLen(unsigned int len);
    void DebugGetMsgLen(unsigned int len);
    void DebugReset(int head, int tail, int size);
    std::string DebugString();
    CycleQueueHead& operator=(const CycleQueueHead& right) {
        head_ = right.head_;
        tail_ = right.tail_;
        offset_ = right.offset_;
        qsize_ = right.qsize_;
        return *this;
    }
    void SetTail(unsigned int tail);
    void SetHead(unsigned int head);
    unsigned int GetTail();
    unsigned int GetHead();

    public:
    unsigned long long start_offset_;
    unsigned int head_;
    unsigned int tail_;
    unsigned int qsize_;
    unsigned long long offset_;
};
#pragma pack()

class EmptyResetQueue {
    public:
    void Attach(CycleQueueHead* phead, char* pdata);
    static bool CheckState(PkgState state);
    int Push(const char* pmsg, int len, PkgState state, int *pcount);
    int Push(unsigned int count, const char* pmsg[],
             unsigned int lenv[], PkgState state, int *pcount);
    PkgState GetMsgState(PkgState state, bool first, int len, int msg1_size,
                         int msg2_size);
    int Get(unsigned int buflen, char* pbuff, unsigned int* pmsglen,
            PkgState *pstate);
    int Get(unsigned int buflen, char** ppbuff, unsigned int* pmsglen,
            PkgState *pstate);
    int Jump(int count);
    int JumpEmptyPacket(CycleQueueHead* phead);
    bool Full();
    bool Empty();
    void SetPkgState(unsigned int* pqhead, PkgState state);
    PkgState GetPkgState(unsigned int* pqhead);
    unsigned int GetPkgLen(unsigned int* pqhead);
    std::string DebugString();
    // int EnablePushSize(CycleQueueHead& headobj);
    int EnablePushSize();
    void SetTraceString(std::string* ptrace);
    void FillData(const CycleQueueHead& headobj, const char* pmsg, int len,
                  PkgState state);
    void FillData(const CycleQueueHead& headobj,
                unsigned int fill_size,
                unsigned int count,
                const char* pmsg[],
                unsigned int lenv[],
                PkgState state);

    private:
    void GetData(const CycleQueueHead& headobj, char** ppbuff);
    int QHead(int offset, const CycleQueueHead& headobj);
    int InGet(unsigned int buflen, CycleQueueHead* phead,
              char** ppbuff, unsigned int* pmsglen,
              PkgState *pstate);

    protected:
    CycleQueueHead *pqhead_src_;
    char* pdata_;
    PkgState current_pkg_state_;
    std::string* ptrace_info_;
};




void SplitArray(unsigned int count,
                unsigned int start,
                unsigned int need_len,
                const char* pinput[],
                unsigned int input_lenv[],
                const char* pout[],
                unsigned int out_lenv[],
                unsigned int* pv_count);

} // namespace tmsg

#endif
