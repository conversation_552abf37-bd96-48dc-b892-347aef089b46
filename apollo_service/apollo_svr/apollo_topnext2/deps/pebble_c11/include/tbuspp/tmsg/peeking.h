/*
 * <PERSON><PERSON> is pleased to support the open source community by making Pebble available.
 * Copyright (C) 2016 THL A29 Limited, a Tencent company. All rights reserved.
 * Licensed under the MIT License (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 * http://opensource.org/licenses/MIT
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 *
 */
 /*
"Copyright (c) 2016, Tencent Inc. All rights reserved."
   georgeli
*/

#ifndef SOURCE_BASEAGENT_TMSG_PEEKING_H_
#define SOURCE_BASEAGENT_TMSG_PEEKING_H_
#include "tbuspp/tmsg/empty_reset_queue.h"
#include "tbuspp/tmsg/tmsg_api_ext.h"

#define kmax_peek_msg_count 4096
namespace tmsg {

class ReaderPeekTrace {
    public:
    ReaderPeekTrace();
    int AttachQueue(int qid, int ver);
    int AttachQueue(unsigned long long qhandle);
    int GetMsgInfo(unsigned int index, const char** ppmsg, unsigned int* plen,
                   PkgState* pstate);
    int Commit(int count, unsigned int bytes, bool block = false);
    int Commit(int count, bool block = false);
    int CommitNotify();
    void SetOnceMaxPeekCount(int count) {
        peek_obj_.SetOnceMaxPeekCount(count);
    }
    unsigned int MsgCount();
    int Peeking(bool block = false) {
        const char* pmsg = NULL;
        int msg_len = 0;
        PkgState state;
        return peek_obj_.GetMsgInfo(0, &pmsg, &msg_len, &state, block);
    }
    int CheckPeekInfo();
    unsigned int GetPeekMsgCount();
    unsigned int GetCommitCount();
    void SetEnd();
    int GetEnd();
    bool Invalidate();
    unsigned int MaxPeekCount() {
        return peek_obj_.MaxPeekCount();
    }
    std::string DebugString() {
        return peek_obj_.DebugString();
    }

    private:
    void Init();
    void* GetReader_Sub_Queue_Headv(int index);
    int qid_;
    int version_;
    int reader_current_get_tail_index_;
    int reader_current_base_head_index_;
    int reader_current_get_head_index_;
    int sub_queue_count_;
    // std::vector<CycleQueueHead> reader_sub_queue_headv_;
    // CycleQueueHead reader_sub_queue_headv_[kmax_peek_msg_count]; // NOLINT
    char reader_sub_queue_headv_[kmax_peek_msg_count*sizeof(CycleQueueHead)]; // NOLINT
    int reader_pack_head_indexv_[kmax_peek_msg_count];  // NOLINT
    // std::vector<int> reader_pack_head_indexv_;
    int reader_pack_len_indexv_[kmax_peek_msg_count];  // NOLINT
    // std::vector<int> reader_pack_len_indexv_;
    unsigned int reader_pack_sub_queue_headv_[kmax_peek_msg_count]; // NOLINT
    // std::vector<unsigned int> reader_pack_sub_queue_headv_;
    PkgState reader_pack_statusv_[kmax_peek_msg_count]; // NOLINT
    // std::vector<PkgState> reader_pack_statusv_;
    const char* reader_pack_start_[kmax_peek_msg_count]; // NOLINT
    // std::vector<const char*> reader_pack_start_;
    unsigned int sub_headv_count_;
    int read_pack_count_;
    int read_msg_len_;
    int ret_;
    unsigned int commit_count_;
    void* pmq_mgr_;
    int end_;
    bool invalidate_;
    QueuePeek peek_obj_;
    friend class ElasticEmptyResetQueueDataLogic;
};

} // namespace tmsg


#endif /* SOURCE_BASEAGENT_TMSG_PEEKING_H_ */
