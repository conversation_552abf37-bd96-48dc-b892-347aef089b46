/*
 * <PERSON>cent is pleased to support the open source community by making Pebble available.
 * Copyright (C) 2016 THL A29 Limited, a Tencent company. All rights reserved.
 * Licensed under the MIT License (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 * http://opensource.org/licenses/MIT
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 *
 */


#ifndef TMSG_API_H_
#define TMSG_API_H_
#include "tbuspp/tmsg/queue_stddef.h"
#include "tbuspp/tmsg/tmsg_conf.h"
#include <string>
#include "tbuspp/tmsg/peeking.h"
namespace tmsg {
// agent call
int TmsgCreate(const GlobalConf& conf, std::string* perr_info);
// agent call
int TmsgDestory(int mgr_area_index, int data_area_index,
                std::string* perr_info);

// app call
int TmsgClientInit(const GlobalConf& conf, std::string* perr_info);

int TmsgCreateEventNotify(unsigned long long *phandle, std::string* perr_info);
int TmsgDestroyEventNotify(unsigned long long handle, std::string* perr_info);

int TmsgPolling(unsigned long long evt_handle, int msec_timeout,
                unsigned long long* pqueue_handle, int* pevent,
                std::string* perr_info);

int TmsgPolling(unsigned long long evt_handle, int msec_timeout,
                unsigned long long* pqueue_handle, int* pevent,
                int* pcount, std::string* perr_info);


int TmsgSetEventObjMonitorQueueAndEventMask(unsigned long long evt_handle,
                                            unsigned long long queue_handle,
                                            int mask, std::string* perr_info);
int TmsgQueueNotEmptyAppendWriteNotify(unsigned long long queue_handle,
                                       unsigned long long evt_handle,
                                       std::string* perr_info);
int TmsgEventObjStopMonitorQueue(unsigned long long evt_handle,
                                 unsigned long long queue_handle,
                                 std::string* perr_info);
int TmsgEventObjGetQueueEventMask(unsigned long long evt_handle,
                                unsigned long long queue_handle,
                                int* pmask,
                                std::string* perr_info);

int TmsgCreateQueue(unsigned long long *phandle, std::string* perr_info);
int TmsgDestoryQueue(unsigned long long handle, std::string* perr_info);
int TmsgPush(unsigned long long handle, const char* pmsg, unsigned int len,
             std::string* perr_info);

int TmsgPush(unsigned long long handle, unsigned int count,
             const char* push_msg[], unsigned int push_lenv[],
             std::string* perr_info);

int TmsgNowEnablePushMaxSize(unsigned long long handle, unsigned int* psize,
                             std::string* perr_info);

int TmsgGet(unsigned long long handle, unsigned int bufflen, char* pbuff,
            unsigned int* pmsglen, PkgState *pstate, std::string* perr_info);

int TmsgPeeking(unsigned long long handle, unsigned int count, ReaderPeekTrace* preader_peek_info,
                std::string* perr_info);

int TmsgCommitPeekInfo(unsigned long long handle,
                       unsigned int count, ReaderPeekTrace* preader_peek_info,
                       std::string* perr_info);

int TmsgCommitPeekInfo(unsigned long long handle,
                       unsigned int count, unsigned int bytes,
                       ReaderPeekTrace* preader_peek_info,
                       std::string* perr_info);


int TmsgQueueMsgCount(unsigned long long handle, std::string* perr_info);

std::string TmsgQueueStatus(unsigned long long handle);
std::string TmsgQueueObserverStatus(unsigned long long handle);
std::string TmsgNotifyIdStatus(unsigned long long handle);
std::string TmsgDataBlockStatus();

int TmsgSetQueueExternInfo(unsigned long long handle, const std::string& extern_info);

int TmsgQueueLastAccessTime(unsigned long long handle, unsigned int *ptime, std::string* perr_info);

bool TmsgCheckQhandle(unsigned long long handle);

} // namespace tmsg

#endif
