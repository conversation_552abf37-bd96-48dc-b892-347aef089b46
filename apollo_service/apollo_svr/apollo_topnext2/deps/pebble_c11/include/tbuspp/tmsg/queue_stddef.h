/*
 * <PERSON><PERSON> is pleased to support the open source community by making Pebble available.
 * Copyright (C) 2016 THL A29 Limited, a Tencent company. All rights reserved.
 * Licensed under the MIT License (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 * http://opensource.org/licenses/MIT
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 *
 */
/*
"Copyright (c) 2016, Tencent Inc. All rights reserved."
   georgeli
*/


#ifndef   QUEUE_ERRCODE_H_
#define   QUEUE_ERRCODE_H_

#include "tbuspp/common/comm_stddef.h"

namespace tmsg {

#define TMSG_MGR_AREA_INDEX 0
#define TMSG_DATA_AREA_INDEX 1
// 1: private mem, 2: SHM TYPE 3: map_file
#define TMSG_MGR_AND_DATA_MEM_TYPE 1
#define TMSG_MGR_SHEMKEY 210001
#define TMSG_DATA_SHEMKEY 210002
#define TMSG_MGR_MAP_FILE_NAME "./tmsg_map_mem_mgr.data"
#define TMSG_DATA_MAP_FILE_NAME "./tmsg_map_mem_data.data"

#define default_max_queue_count 100
#define default_max_sub_queue_count 10
#define default_max_observer_count default_max_queue_count
#define default_observer_max_event_count 10
#define default_max_event_mgr_count (2*default_max_observer_count)

#define default_max_data_block_count ((default_max_queue_count * default_max_sub_queue_count) + 1)
#define default_data_block_size 8192


#define MAX_INPUT_ARRAY_ITEM_COUNT 1000

enum TmsgErrCode {
    kTMSG_EXIST_STATUS_OK = 2,
    kSUCC = 0,
    kEMPTY = 1,
    kELASTIC_RETURN_NOT_SPACE_1 = -1,
    kNOT_SERIES = -2,
    kNOT_SPACE_2 = -3,
    kPKGERR = -10,  // BAD DATA
    kBUFF_SHORT = -11,
    kMQ_QUEUE_NOT_SPACE = -12,
    kMSG_LEN_TOO_LONG = -13,
    kMT_QUEUE_COUNT_FULL = -14,
    kMT_QUEUE_NOT_INIT = -15,
    kQUEUE_FULL = -16,
    kPKG_STATE_ERROR = -17,
    kDATA_BLOCK_MGR_NOT_SPACE = -20,
    kQUEUE_SYS_ERROR = -21,
    kQUEUE_PARAM_ERR = -22,
    kTMSG_MGR_SIZE_ERR = -23,
    kTMSG_MEM_CONF_DIFFERENT = -24,
    kTMSG_DATA_MGR_AREA_OFFSET_ERR = -25,
    kTMSG_OBSERVER_AREA_OFFSET_ERR = -26,
    kTMSG_EVENT_AREA_OFFSET_ERR = -27,
    kTMSG_QUEUE_AREA_OFFSET_ERR = -28,
    kTMSG_OBSERVER_OBJ_OFFSET_ERR = -29,
    kTMSG_EVENT_OBJ_OFFSET_ERR = -30,
    kTMSG_ELASTER_QUEUE_OBJ_OFFSET_ERR = -31,
    kTMSG_EMPTY_RESET_QUEUE_OBJ_OFFSET_ERR = -32,
    kTMSG_MGR_MEM_DIRTY = -33,
    kEMPTY_RESET_QUEUE_NOT_ENABLE = -34,
    kEMPTY_RESET_QUEUE_ENABLE_SIZE_MIN = -35,
    kTMSG_GLOBAL_MEM_NOT_COMPLETE = -36,
    kTMSG_NOT_IDLE_EVENT_DATA = -37,
    kTMSG_NOT_INIT = -38,
    kTMSG_DEL_EVENT_NOTIFY_FAIL = -39,
    kTMSG_INIT_ERROR = -40,
    kTMSG_NOT_SOURCE = -41,
    kTMSG_OBJ_STATUS_ERR = -42,
    kTMSG_MEM_TYPE_ERR = -43,
    kTMSG_QUEUE_VERSION_DIFF = -44,
    kTMSG_GLOBAL_DATA_BLOCK_FULL = -45,
    kTMSG_CONF_ERROR = -46,
    kTMSG_PEEKING_END = -47,
    KTMSG_INPUT_MSG_ARRAY_ITEM_COUNT_ERR = -48,
    kIdleBlockNotCooling = -49,
    kReadEmptyPacket = -50,
    kQusueSubQueueFull = -51,
    kAllocSizeToolBig = -52,
    kQueueLockFail = -53,
    kPeekNoPkg = -54,
};

enum OBJSTATUS {
    kOBJ_USE = 1,
    kOBJ_IDLE = 2,
};

enum PkgState {
    kpkg_complete = 0,
    kpkg_begin = 1,
    kpkg_middle = 2,
    kpkg_end = 3,
    kpkg_empty = 4,
    kpkg_bad = 5,
};

enum QueueEvent {
    kEVENT_FULL = 1,
    kEVENT_EMPTY = 2,
    kEVENT_WRITE = 4,
    kEVENT_GET = 8,
    kEVENT_CREATE = 16,
    kEVENT_DESTORY = 32,
    kEVENT_Global_Not_space = 64
};

enum QueueOpt {
    kQueueWrite = 0,
    kQueueRead = 1,
};

const unsigned int c_qhead_len = 4;


#define AREA_GLOBAL_CONF  0
#define AREA_OBSERVER_EVENT  1

#define DATA_BLOCK_SIZE 8096

#define MAX_QUEUE_DST_ID 1024

class MapFileDataBlockPolicy;

extern MapFileDataBlockPolicy* g_pdata_block_policy;
extern int g_tmsg_sys_event_notify_index;



} // namespace tmsg

#endif
