/*
 * <PERSON><PERSON> is pleased to support the open source community by making Pebble available.
 * Copyright (C) 2016 THL A29 Limited, a Tencent company. All rights reserved.
 * Licensed under the MIT License (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 * http://opensource.org/licenses/MIT
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 *
 */


#ifndef SOURCE_BASEAGENT_TROUTE_TROUTE_STDDEF_H_
#define SOURCE_BASEAGENT_TROUTE_TROUTE_STDDEF_H_
#include "tbuspp/common/comm_stddef.h"

namespace troute {

#define TROUTE_SHEMKEY 210003
#define TROUTE_MAP_FILE_NAME "./troute_mgr.data"
#define TROUTE_MEM_TYPE 1
#define Adress<PERSON>axLength 256
#define MinPortBase 1024
#define BlockAdressCount 30
#define TableExtBlockMaxCount 400
#define MaxNameCount 100000
#define MaxAddressCount 9000000
#define MinSuccRate 0.0000001
#define MinAvgDelayMsec 1
#define TraceHistoryPeriodicCount 10
#define HashTableCount 4

#define default_global_mem_index  3
#define default_bucket_count  200
#define default_name_count    100
#define default_address_count 100000


#define default_min_server_whole_pri 0.000001
#define default_route_pri_calculating_period 60
#define default_server_io_up_limit 5000
#define default_server_io_down_limit 10
#define default_err_rate_limit 0.2
#define default_server_normal_expand_rate 0.2
#define default_min_req_down_rate 0.1
#define default_min_err_rate_down_rate 0.05
#define default_min_non_pressure_fault_periodic 2
#define default_min_non_pressure_fault_bias_ratio 0.2

#define ErrorRateAccuracy 100000

enum AdressType {
    kProcess_level,
    kOs_Instance_level,
    kInstance_level,
    kIp_port_level,
    kData_name_level,
    kLogic_name_level,
    kServer_name_level,
    kModule_name,
    kMgr_name_level1 = 8,
    kMgr_name_level2 = 9,
    kMgr_name_level3,
    kMgr_name_level4,
    kMgr_name_level5,
    kMgr_name_level6,
    kMgr_name_level7,
    kAddress_root_level,
    kNot_Complete = 100,
};

enum ConnType {
    kConnTcp = 0,
    kConnUdp = 1,
};

enum ProtocolType {
    kBaseAgent = 0,
    kHttp = 1,
    kStream = 2,
    kTbus = 3,
    kTdr = 4,
    kConnNoState = 5,
    kProtocolEnd = 6,
};

enum ErrorCode {
    kPollingEnd = 1,
    kSUCC = 0,
    kBucketIdErr = -1001,
    kKeyIdErr = -1002,
    kParamErr = -1003,
    kNameFieldExist = -1007,
    kNameTooLong = -1008,
    kFieldIsNull = -1009,
    kBuffShort = -1010,
    kIpPortFieldFormatErr = -1011,
    kIpFormatErr = -1011,
    kPortErr = -1012,
    kExceedLimits = -1013,
    kRouteStatusDisable = -1014,
    kExceedCurrentPercent = -1015,
    kTrouteConfError = -1016,
    kNodeIdNotExist = -1017,
    kNodeVsersionDiff = -1018,
    kBlockAddressError = -1019,
    kBlockDoubleFree = -1020,
    kBlockIdError = -1021,
    kAddressExist = -1022,
    kNotIdleBlock = -1023,
    kAddressFieldNull = -1024,
    kAddressNotExist = -1025,
    kAddressBlockErr = -1026,
    kBlockNotIdleItem = -1027,
    kAnyPoolError = -1028,
    kSeqNotUse = -1029,
    kReqRouteNoBlockFind = -1030,
    kRouteItemVersionDiff = -1031,
    kAddressTableIsNull = -1032,
    kServerAddressDirty = -1033,
    kTroue_mem_type_error = -1034,
    kTroute_not_init = -1035,
    kDstServerNameNotExist = -1037,
    kTablePollingEnd = -1038,
    kPacketErr = -1039,
    kNotValidAddress = -1040,
    kServerOverload = -1041,
    kFieldNameErr = -1402,
};



enum RouteState {
    kNormal = 1,
    kAlarm = 2,
    kFrozen = 3,
    kExtFrozen = 4,
    kDel = 5,
    kIdle = 6,
    kNoExist = 7,
};

enum RouteHashTableId {
    kCalledHashTableId = 0,
    kCallMeHashTableId = 1,
    kBrotherHashTableId = 2,
    kMeDependHashTableId = 3,
    kHashTableMax = 4,
};

enum ExtHashTable {
    kTstatHashTable = 5,
    kTExtEnd = 10,
};

enum InformationDimension {
    kReqCount = 0,
    kErrRate = 1,
    kTime = 2,
    kMaxlimit = 3,
    kUpperLimitReq = 4,
    kItemPercent = 5,
    kUpperSuccRate = 6,
    kDelay = 7,
    kState = 8,
    kNonPressure = 9,
    kMaxDimension = 10,
};

enum WeightDimension {
    kSuccWeight = 0,
    kDelayWeigh = 1,
    kStaticWeight = 2,
    kExtWeight = 3,
    kEndWeight = 4,
};

enum RouteFaultReason {
    kItemNormal = 0,
    kPressureFault = 1,
    kNonPressureFault = 2,
};

}  // namespace troute

#endif /* SOURCE_BASEAGENT_TROUTE_TROUTE_STDDEF_H_ */
