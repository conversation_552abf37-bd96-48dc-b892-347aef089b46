/*
 * <PERSON><PERSON> is pleased to support the open source community by making Pebble available.
 * Copyright (C) 2016 THL A29 Limited, a Tencent company. All rights reserved.
 * Licensed under the MIT License (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 * http://opensource.org/licenses/MIT
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 *
 */


#ifndef SOURCE_BASEAGENT_TROUTE_TROUTE_API_H_
#define SOURCE_BASEAGENT_TROUTE_TROUTE_API_H_
#include <vector>
#include <string>
#include "tbuspp/troute/troute_stddef.h"
#include "tbuspp/troute/troute_conf.h"
#include "tbuspp/troute/server_name_address.h"
// #include "tbuspp/troute/address_table.h"


namespace troute {

class RouteItem;

int TroueCreate(const RouteConf &conf, std::string* perror_info);
int TroueDestory(unsigned long long index, std::string* perror_info);
int TrouteClientInit(const RouteConf &conf, std::string* perror_info);
void TrouteDestoryAddressV(std::vector<ServerNameAndAddress*>* paddress_v);

int GetNoStateRoute(unsigned long long uid,
                    const ServerNameAndAddress& src_address,
                    const ServerNameAndAddress& dst_name,
                    RouteItem** ppfind,
                    unsigned long long *pfind_version);

int GetNoStateRoute(unsigned long long uid,
                    const ServerNameAndAddress& src_address,
                    const ServerNameAndAddress& dst_name,
                    std::string* pip, unsigned short *pport);

int GetNoStateRoute(unsigned long long uid,
                    const ServerNameAndAddress& src_address,
                    const ServerNameAndAddress& dst_name,
                    unsigned long long* qhandle);

int GetNoStateRouteByQuality(unsigned long long uid,
                    const ServerNameAndAddress& src_address,
                    const ServerNameAndAddress& dst_name,
                    RouteItem** ppfind,
                    unsigned long long *pfind_version);

int GetNoStateRouteByQuality(unsigned long long uid,
                             const ServerNameAndAddress& src_address,
                             const ServerNameAndAddress& dst_name,
                             std::string* pip, unsigned short *pport);

int GetNoStateRouteByQuality(unsigned long long uid,
                             const ServerNameAndAddress& src_address,
                             const ServerNameAndAddress& dst_name,
                             unsigned long long* qhandle);


int AgentUpdateResult(unsigned long long uid,
                      const ServerNameAndAddress& src_address,
                      const ServerNameAndAddress& dst_address,
                      int result, unsigned int msec);

int BusinessUpdateResult(unsigned long long uid,
                         const ServerNameAndAddress& src_address,
                         const ServerNameAndAddress& dst_address,
                         int result, unsigned int msec);

int AddRoute(const ServerNameAndAddress& address);

int DelRoute(const ServerNameAndAddress& address);

int DelRouteServerName(const ServerNameAndAddress& address);

int GetCalledRouteCount(const ServerNameAndAddress& server_name, unsigned int* pcount);

int GetCalledRouteTable(const ServerNameAndAddress& server_name,
                        std::vector<ServerNameAndAddress*>* paddress_v);

int AllCalledRouteTable(std::vector<ServerNameAndAddress*>* paddress_v);
int AllCalledRouteTableKey(std::vector<ServerNameAndAddress*>* paddress_v);

int SetExternalIndex(unsigned long long index, int version, const ServerNameAndAddress& address);
int GetAddressByIndex(unsigned long long index, int version, ServerNameAndAddress* paddress);
int GetAddressByIndex(unsigned long long index, int version,
                      const ServerNameAndAddress** ppaddress);

int SetAddressRelationKey(const ServerNameAndAddress& address,
                          unsigned long long key, int version);

int GetAddressRelationKey(const ServerNameAndAddress& address,
                          unsigned long long *pkey,
                          int *pversion);

int SetAddressState(const ServerNameAndAddress& address, RouteState state);
int DisableAddress(const ServerNameAndAddress& address);
int GetAddressState(const ServerNameAndAddress& address, RouteState *pstate);

int AddressPacketLossReport(const ServerNameAndAddress& address, unsigned int count);
int GetAddressPacketLossCount(const ServerNameAndAddress& address, unsigned int *pcount);
int ResetAddressPacketLossCount(const ServerNameAndAddress& address, unsigned int count);




//

int WhoCallMe(const ServerNameAndAddress& who, const ServerNameAndAddress& me);
int WhoNotCallMe(const ServerNameAndAddress& who, const ServerNameAndAddress& me);
int CallMeList(const ServerNameAndAddress& me, std::vector<ServerNameAndAddress*>* paddress_v);
int AllCallMeList(std::vector<ServerNameAndAddress*>* paddress_v);
int AllCallMeTableKey(std::vector<ServerNameAndAddress*>* paddress_v);
int DelCallMeList(const ServerNameAndAddress& me);

int WhoIsMyBrother(const ServerNameAndAddress& who, const ServerNameAndAddress& me);
int WhoIsNotMyBrother(const ServerNameAndAddress& who, const ServerNameAndAddress& me);
int MyBrotherList(const ServerNameAndAddress& me, std::vector<ServerNameAndAddress*>* paddress_v);
int AllBrotherList(std::vector<ServerNameAndAddress*>* paddress_v);
int AllBrotherTableKey(std::vector<ServerNameAndAddress*>* paddress_v);
int DelMyBrotherList(const ServerNameAndAddress& me);

int IDependWho(const ServerNameAndAddress& me, const ServerNameAndAddress& who);
int GetMeDependList(const ServerNameAndAddress& me, std::vector<ServerNameAndAddress*>* paddress_v);
int AllMeDependList(std::vector<ServerNameAndAddress*>* paddress_v);
int AllMeDependTableKey(std::vector<ServerNameAndAddress*>* paddress_v);
int DelMeDependList(const ServerNameAndAddress& me);

int AllHashTableRouteTableKey(RouteHashTableId id, std::vector<ServerNameAndAddress*>* paddress_v);

bool HashTableHasAddress(RouteHashTableId id, const ServerNameAndAddress& key,
                         const ServerNameAndAddress& value);

bool HashTableHasAddress(RouteHashTableId id,
                         const ServerNameAndAddress& key,
                         ServerNameAndAddress* pvalue);

std::string DumpAddressV(const std::vector<ServerNameAndAddress*>& address_v);
std::string DumpAddressVName(const std::vector<ServerNameAndAddress*>& address_v);
std::string DumpAddressVDetail(RouteHashTableId id, const std::vector<ServerNameAndAddress*>& address_v); // NOLINT
std::string DumpNameRouteDetail(ServerNameAndAddress& name); // NOLINT



std::string DumpHashTableByBucket(RouteHashTableId id, int bucket);

std::string DumpHashTableByNodeid(RouteHashTableId id, int node_id);

std::string DumpHashTableAddressTable(RouteHashTableId id,
                                      const ServerNameAndAddress& key,
                                      int* pret);

std::string DumpBlockPoolDebugString(RouteHashTableId id);

int TrouteCheck();

int BlockPoolCheck(RouteHashTableId id);

int GetHashTableBucketCount();
int GetMaxNameCount();
int GetAddressCount();
int GetAddressBlockCount();
int GetRouteTableMaxAddressCount();
int GetIdleNodeCount(RouteHashTableId id);
int GetIdleAddressBlockCount(RouteHashTableId id);
int GetHashTableRouteItemCount(RouteHashTableId id);
int GetHashTableRouteTableCount(RouteHashTableId id);

} // namespace troute


#endif /* SOURCE_BASEAGENT_TROUTE_TROUTE_API_H_ */
