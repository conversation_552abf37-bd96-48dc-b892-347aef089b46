/*
 * Tencent is pleased to support the open source community by making Pebble available.
 * Copyright (C) 2016 THL A29 Limited, a Tencent company. All rights reserved.
 * Licensed under the MIT License (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 * http://opensource.org/licenses/MIT
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 *
 */


#ifndef HTTP_PROTOCOL_API_H
#define HTTP_PROTOCOL_API_H

#include <string>

namespace troute {
    class ServerNameAndAddress;
} // namespace troute

namespace tbuspp { namespace plugin {

enum HttpProtocolApiErrorCode {
    kEnvError = -100,   ///< tbuspp环境异常
    kParseConfError,    ///< uri的配置解析错误
    kNameNotExist,      ///< 指定的名字不存在
    kNoneInstAddr,      ///< 没有有效的地址
};

/// @brief 注册http uri的路由
/// @param uri 待注册的http uri
/// @param name_path uri注册的路由的名字文件路径
/// @return 0 成功，其它则失败
/// @note uri重复注册相同的路由返回成功
/// @note uri不能重复注册不同的路由，如果需要修改则需要先删除
int32_t RegHttpUriRoute(const std::string& uri, const std::string& name_path);

/// @brief 注册http uri的路由
/// @param uri 待注册的http uri
/// @param inst_address uri注册的路由地址
/// @return 0 成功，其它则失败
/// @note uri重复注册相同的路由返回成功
/// @note uri不能重复注册不同的路由，如果需要修改则需要先删除
int32_t RegHttpUriRoute(const std::string& uri, const troute::ServerNameAndAddress* inst_address);

/// @brief 取消http uri的路由注册
/// @param uri 待取消注册的http uri
/// @return 0 成功
/// @return >0 uri对应的地址实例剩余数量，只有为0后才能成功取消注册
/// @return <0 失败
int32_t UnRegHttpUriRoute(const std::string& uri);

} // namespace plugin
} // namespace tbuspp

#endif // HTTP_PROTOCOL_API_H
