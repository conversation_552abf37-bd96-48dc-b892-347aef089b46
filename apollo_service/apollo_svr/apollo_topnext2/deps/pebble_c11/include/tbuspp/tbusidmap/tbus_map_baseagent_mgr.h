// "Copyright (c) 2016, Tencent Inc. All rights reserved.
/*
 * tbus_map_baseagent_mgr.h
 *
 *  Created on: 2016骞�4鏈�21鏃�
 *      Author: georgeli
 */
#ifndef _tbus_map_baseagent_mgr_h_
#define _tbus_map_baseagent_mgr_h_
#include <pthread.h>
#include <set>
#include <map>
#include <tbus/tbus.h>
#include "tbuspp/tbusidmap/tbus_head.h"
#include "tbuspp/baseagent_api/base_agent_stddef.h"
#include "tbuspp/baseagent_api/base_agent_client_api.h"
#include "tbuspp/baseagent_api/trans_msg.h"
#include "tbuspp/tmsg/queue_stddef.h"
#include "tbuspp/tbusidmap/busidmaptbusplus.h"
#include "tbuspp/tbusidmap/busid_map_api.h"
#include "tbuspp/tbusidmap/busid_conf.h"
#include "tbuspp/tmsg/peeking.h"
#include "tbuspp/tmsg/tmsg_api.h"

namespace baseagent {
class MsgBuff {
    public:
    MsgBuff();
    ~MsgBuff();
    void Expand(int size);
    char* pmsg_buff_;
    char default_buff_[10240];
    unsigned int msg_buff_len_;
    unsigned int msg_len_;
};
class TbusMapTbusPlusMgr {
    public:
    TbusMapTbusPlusMgr();
    ~TbusMapTbusPlusMgr();
    TbusMapTbusPlusMgr(
                       const std::string& map_template,
                       int tbuspp,
                       std::string ip,
                       int port);
    void SetServerInfo(
                       const std::string& map_template,
                       int tbuspp,
                       const std::string& ip,
                       int port) {
        map_template_ = map_template;
        tbuspp_ = tbuspp;
        ip_ = ip;
        port_ = port;
        busid_map_obj_.SetTemplateFormat(map_template);
    }
    int Listen(unsigned int iaddr);
    static int Listen(
                      const std::string& map_template,
                      unsigned int iaddr,
                      std::string ip,
                      int port);

    static int TbusAddrToBaseAgentAddr(
                                       const std::string& map_template,
                                       unsigned int iaddr,
                                       std::string ip,
                                       int port,
                                       troute::ServerNameAndAddress* p_addr);
    troute::ServerNameAndAddress* TbusAddrToBaseAgentAddr(unsigned int iaddr);
    /*static void MakeAddrPeerString(
                                   const troute::ServerNameAndAddress& src,
                                   const troute::ServerNameAndAddress& dst,
                                   std::string* paddr_str);
    bool IsSet(const std::string& peer_str);
    bool Insert(const std::string& peer_str);
    bool InsertAddrPairStr(
                           const troute::ServerNameAndAddress& src,
                           const troute::ServerNameAndAddress& dst);*/
    int ReadMsg(
                const troute::ServerNameAndAddress& src,
                MsgBuff* pmsg_buff,
                std::string* perr_info);
    int InitPeedInfo(unsigned int iaddr, tmsg::ReaderPeekTrace* p_peek_obj);
    int ReadMsg(unsigned int iaddr, MsgBuff* pmsg_buff, std::string* perr_info);
    int PeekingMsg(
                   unsigned int iaddr,
                   tmsg::ReaderPeekTrace* p_peek_obj,
                   std::string* perr_info);
    int PeekingMsg(
                   const troute::ServerNameAndAddress& src,
                   tmsg::ReaderPeekTrace* p_peek_obj,
                   std::string* perr_info);
    void TbusPlusAddrProc(
                          unsigned int iaddr,
                          int commit,
                          tmsg::ReaderPeekTrace* p_peek_obj);
    int CommitPeekInfo(
                       unsigned int iaddr,
                       int count,
                       tmsg::ReaderPeekTrace* p_peek_obj);
    int CommitPeekInfo(
                       unsigned int iaddr,
                       int count,
                       unsigned int bytes,
                       tmsg::ReaderPeekTrace* p_peek_obj);
    static troute::ServerNameAndAddress* GetTbusPlusAddrByBusId(unsigned int iaddr);
    int SendMsg(
                unsigned int isrc,
                unsigned int idest,
                int count,
                const char* pmsg_v[],
                unsigned int msg_len_v[],
                std::string* perr_info);
    static int SendMsg(
                unsigned int isrc,
                unsigned int idest,
                const std::string& map_tmp,
                int count,
                const char* pmsg_v[],
                unsigned int msg_len_v[],
                std::string* perr_info);
    int SendMsgNoTbusHead(
                          unsigned int isrc,
                          unsigned int idest,
                          int count,
                          const char* pmsg_v[],
                          unsigned int msg_len_v[],
                          std::string* perr_info);

    static int GetBusidConfCacheVer(unsigned int addr);
    static void SaveBusidConfVer(unsigned int addr);
    static void SaveBusidConfVer(unsigned int addr, int ver);
    static void BusidConfChange(unsigned int addr);
    static void ClearBusidCacheInfo(unsigned int addr);
    static void AddBusidMapInstance(unsigned int addr, troute::ServerNameAndAddress* pdest);

    public:
    static pthread_mutex_t mutex_;
    baseagent::BusIdMapServerNameAndAddress busid_map_obj_;
    // std::set<std::string> addr_conn_peer_;
    std::string map_template_;
    int tbuspp_;
    std::string ip_;
    int port_;
    troute::ServerNameAndAddress* premote_src_;
    static std::set<unsigned int> iaddr_listen_;
    static std::set<troute::ServerNameAndAddress*> base_agent_listen_;
    static std::map<unsigned int, troute::ServerNameAndAddress*> busid_to_tbusplus_addr_;
    static std::map<unsigned int, int> busid_conf_ver_;
};
int export_tbus_encode_head(
                            LPTBUSHEAD a_pstHead,
                            char *a_pszNet,
                            int *a_piLen,
                            int a_iVersion);
int export_tbus_decode_head(
                            LPTBUSHEAD a_pstHead,
                            char *a_pszNet,
                            int a_iLen,
                            int a_iVersion);
unsigned int getip(const char* ifname, char* ipbuf, int ipbuf_len);

unsigned int GetQueueMaxSizeByBusId(unsigned int addr);
unsigned int GetQueueIdleSizeByBusId(unsigned int addr);
unsigned int GetQueueLastAccessTime(unsigned int addr);
int GetDstLossPacket(unsigned int addr, unsigned int* pcount);
int ResetDstLossPacket(unsigned int addr, int count);


} // namespace baseagent

#define BASE_AGENT_SENDV_COUNT 20

#endif
