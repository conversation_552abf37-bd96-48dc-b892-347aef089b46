// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tbuspp/proto/notify_conf.proto

#ifndef PROTOBUF_source_2fbaseagent_2fproto_2fnotify_5fconf_2eproto__INCLUDED
#define PROTOBUF_source_2fbaseagent_2fproto_2fnotify_5fconf_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2006000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace notify {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fnotify_5fconf_2eproto();
void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fnotify_5fconf_2eproto();
void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fnotify_5fconf_2eproto();

class notify_conf;

// ===================================================================

class notify_conf : public ::google::protobuf::Message {
 public:
  notify_conf();
  virtual ~notify_conf();

  notify_conf(const notify_conf& from);

  inline notify_conf& operator=(const notify_conf& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const notify_conf& default_instance();

  void Swap(notify_conf* other);

  // implements Message ----------------------------------------------

  notify_conf* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const notify_conf& from);
  void MergeFrom(const notify_conf& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string tbuspp_conf = 1 [default = "/data/baseagent/conf/base_agent.conf"];
  inline bool has_tbuspp_conf() const;
  inline void clear_tbuspp_conf();
  static const int kTbusppConfFieldNumber = 1;
  inline const ::std::string& tbuspp_conf() const;
  inline void set_tbuspp_conf(const ::std::string& value);
  inline void set_tbuspp_conf(const char* value);
  inline void set_tbuspp_conf(const char* value, size_t size);
  inline ::std::string* mutable_tbuspp_conf();
  inline ::std::string* release_tbuspp_conf();
  inline void set_allocated_tbuspp_conf(::std::string* tbuspp_conf);

  // optional string stat_so_path = 2 [default = "../stat/liblocal_stat_report.so"];
  inline bool has_stat_so_path() const;
  inline void clear_stat_so_path();
  static const int kStatSoPathFieldNumber = 2;
  inline const ::std::string& stat_so_path() const;
  inline void set_stat_so_path(const ::std::string& value);
  inline void set_stat_so_path(const char* value);
  inline void set_stat_so_path(const char* value, size_t size);
  inline ::std::string* mutable_stat_so_path();
  inline ::std::string* release_stat_so_path();
  inline void set_allocated_stat_so_path(::std::string* stat_so_path);

  // optional string stat_conf_path = 3 [default = "../stat/notify_stat.conf"];
  inline bool has_stat_conf_path() const;
  inline void clear_stat_conf_path();
  static const int kStatConfPathFieldNumber = 3;
  inline const ::std::string& stat_conf_path() const;
  inline void set_stat_conf_path(const ::std::string& value);
  inline void set_stat_conf_path(const char* value);
  inline void set_stat_conf_path(const char* value, size_t size);
  inline ::std::string* mutable_stat_conf_path();
  inline ::std::string* release_stat_conf_path();
  inline void set_allocated_stat_conf_path(::std::string* stat_conf_path);

  // optional int32 route_active_detected_interval = 8 [default = 10000];
  inline bool has_route_active_detected_interval() const;
  inline void clear_route_active_detected_interval();
  static const int kRouteActiveDetectedIntervalFieldNumber = 8;
  inline ::google::protobuf::int32 route_active_detected_interval() const;
  inline void set_route_active_detected_interval(::google::protobuf::int32 value);

  // optional int32 route_check_interval = 9 [default = 3000];
  inline bool has_route_check_interval() const;
  inline void clear_route_check_interval();
  static const int kRouteCheckIntervalFieldNumber = 9;
  inline ::google::protobuf::int32 route_check_interval() const;
  inline void set_route_check_interval(::google::protobuf::int32 value);

  // optional int32 flow_ctrl_step_interval = 10 [default = 10000];
  inline bool has_flow_ctrl_step_interval() const;
  inline void clear_flow_ctrl_step_interval();
  static const int kFlowCtrlStepIntervalFieldNumber = 10;
  inline ::google::protobuf::int32 flow_ctrl_step_interval() const;
  inline void set_flow_ctrl_step_interval(::google::protobuf::int32 value);

  // optional int32 flow_ctrl_step_num = 11 [default = 3];
  inline bool has_flow_ctrl_step_num() const;
  inline void clear_flow_ctrl_step_num();
  static const int kFlowCtrlStepNumFieldNumber = 11;
  inline ::google::protobuf::int32 flow_ctrl_step_num() const;
  inline void set_flow_ctrl_step_num(::google::protobuf::int32 value);

  // optional int32 flow_ctrl_step_flow = 12 [default = 7];
  inline bool has_flow_ctrl_step_flow() const;
  inline void clear_flow_ctrl_step_flow();
  static const int kFlowCtrlStepFlowFieldNumber = 12;
  inline ::google::protobuf::int32 flow_ctrl_step_flow() const;
  inline void set_flow_ctrl_step_flow(::google::protobuf::int32 value);

  // optional int32 flow_ctrl_max_flow = 13 [default = 17];
  inline bool has_flow_ctrl_max_flow() const;
  inline void clear_flow_ctrl_max_flow();
  static const int kFlowCtrlMaxFlowFieldNumber = 13;
  inline ::google::protobuf::int32 flow_ctrl_max_flow() const;
  inline void set_flow_ctrl_max_flow(::google::protobuf::int32 value);

  // repeated string watcher_cfgs = 20;
  inline int watcher_cfgs_size() const;
  inline void clear_watcher_cfgs();
  static const int kWatcherCfgsFieldNumber = 20;
  inline const ::std::string& watcher_cfgs(int index) const;
  inline ::std::string* mutable_watcher_cfgs(int index);
  inline void set_watcher_cfgs(int index, const ::std::string& value);
  inline void set_watcher_cfgs(int index, const char* value);
  inline void set_watcher_cfgs(int index, const char* value, size_t size);
  inline ::std::string* add_watcher_cfgs();
  inline void add_watcher_cfgs(const ::std::string& value);
  inline void add_watcher_cfgs(const char* value);
  inline void add_watcher_cfgs(const char* value, size_t size);
  inline const ::google::protobuf::RepeatedPtrField< ::std::string>& watcher_cfgs() const;
  inline ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_watcher_cfgs();

  // optional int32 state_report_interval = 21 [default = 300000];
  inline bool has_state_report_interval() const;
  inline void clear_state_report_interval();
  static const int kStateReportIntervalFieldNumber = 21;
  inline ::google::protobuf::int32 state_report_interval() const;
  inline void set_state_report_interval(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:notify.notify_conf)
 private:
  inline void set_has_tbuspp_conf();
  inline void clear_has_tbuspp_conf();
  inline void set_has_stat_so_path();
  inline void clear_has_stat_so_path();
  inline void set_has_stat_conf_path();
  inline void clear_has_stat_conf_path();
  inline void set_has_route_active_detected_interval();
  inline void clear_has_route_active_detected_interval();
  inline void set_has_route_check_interval();
  inline void clear_has_route_check_interval();
  inline void set_has_flow_ctrl_step_interval();
  inline void clear_has_flow_ctrl_step_interval();
  inline void set_has_flow_ctrl_step_num();
  inline void clear_has_flow_ctrl_step_num();
  inline void set_has_flow_ctrl_step_flow();
  inline void clear_has_flow_ctrl_step_flow();
  inline void set_has_flow_ctrl_max_flow();
  inline void clear_has_flow_ctrl_max_flow();
  inline void set_has_state_report_interval();
  inline void clear_has_state_report_interval();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  static ::std::string* _default_tbuspp_conf_;
  ::std::string* tbuspp_conf_;
  static ::std::string* _default_stat_so_path_;
  ::std::string* stat_so_path_;
  static ::std::string* _default_stat_conf_path_;
  ::std::string* stat_conf_path_;
  ::google::protobuf::int32 route_active_detected_interval_;
  ::google::protobuf::int32 route_check_interval_;
  ::google::protobuf::int32 flow_ctrl_step_interval_;
  ::google::protobuf::int32 flow_ctrl_step_num_;
  ::google::protobuf::int32 flow_ctrl_step_flow_;
  ::google::protobuf::int32 flow_ctrl_max_flow_;
  ::google::protobuf::RepeatedPtrField< ::std::string> watcher_cfgs_;
  ::google::protobuf::int32 state_report_interval_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fnotify_5fconf_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fnotify_5fconf_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fnotify_5fconf_2eproto();

  void InitAsDefaultInstance();
  static notify_conf* default_instance_;
};
// ===================================================================


// ===================================================================

// notify_conf

// optional string tbuspp_conf = 1 [default = "/data/baseagent/conf/base_agent.conf"];
inline bool notify_conf::has_tbuspp_conf() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void notify_conf::set_has_tbuspp_conf() {
  _has_bits_[0] |= 0x00000001u;
}
inline void notify_conf::clear_has_tbuspp_conf() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void notify_conf::clear_tbuspp_conf() {
  if (tbuspp_conf_ != _default_tbuspp_conf_) {
    tbuspp_conf_->assign(*_default_tbuspp_conf_);
  }
  clear_has_tbuspp_conf();
}
inline const ::std::string& notify_conf::tbuspp_conf() const {
  // @@protoc_insertion_point(field_get:notify.notify_conf.tbuspp_conf)
  return *tbuspp_conf_;
}
inline void notify_conf::set_tbuspp_conf(const ::std::string& value) {
  set_has_tbuspp_conf();
  if (tbuspp_conf_ == _default_tbuspp_conf_) {
    tbuspp_conf_ = new ::std::string;
  }
  tbuspp_conf_->assign(value);
  // @@protoc_insertion_point(field_set:notify.notify_conf.tbuspp_conf)
}
inline void notify_conf::set_tbuspp_conf(const char* value) {
  set_has_tbuspp_conf();
  if (tbuspp_conf_ == _default_tbuspp_conf_) {
    tbuspp_conf_ = new ::std::string;
  }
  tbuspp_conf_->assign(value);
  // @@protoc_insertion_point(field_set_char:notify.notify_conf.tbuspp_conf)
}
inline void notify_conf::set_tbuspp_conf(const char* value, size_t size) {
  set_has_tbuspp_conf();
  if (tbuspp_conf_ == _default_tbuspp_conf_) {
    tbuspp_conf_ = new ::std::string;
  }
  tbuspp_conf_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:notify.notify_conf.tbuspp_conf)
}
inline ::std::string* notify_conf::mutable_tbuspp_conf() {
  set_has_tbuspp_conf();
  if (tbuspp_conf_ == _default_tbuspp_conf_) {
    tbuspp_conf_ = new ::std::string(*_default_tbuspp_conf_);
  }
  // @@protoc_insertion_point(field_mutable:notify.notify_conf.tbuspp_conf)
  return tbuspp_conf_;
}
inline ::std::string* notify_conf::release_tbuspp_conf() {
  clear_has_tbuspp_conf();
  if (tbuspp_conf_ == _default_tbuspp_conf_) {
    return NULL;
  } else {
    ::std::string* temp = tbuspp_conf_;
    tbuspp_conf_ = const_cast< ::std::string*>(_default_tbuspp_conf_);
    return temp;
  }
}
inline void notify_conf::set_allocated_tbuspp_conf(::std::string* tbuspp_conf) {
  if (tbuspp_conf_ != _default_tbuspp_conf_) {
    delete tbuspp_conf_;
  }
  if (tbuspp_conf) {
    set_has_tbuspp_conf();
    tbuspp_conf_ = tbuspp_conf;
  } else {
    clear_has_tbuspp_conf();
    tbuspp_conf_ = const_cast< ::std::string*>(_default_tbuspp_conf_);
  }
  // @@protoc_insertion_point(field_set_allocated:notify.notify_conf.tbuspp_conf)
}

// optional string stat_so_path = 2 [default = "../stat/liblocal_stat_report.so"];
inline bool notify_conf::has_stat_so_path() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void notify_conf::set_has_stat_so_path() {
  _has_bits_[0] |= 0x00000002u;
}
inline void notify_conf::clear_has_stat_so_path() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void notify_conf::clear_stat_so_path() {
  if (stat_so_path_ != _default_stat_so_path_) {
    stat_so_path_->assign(*_default_stat_so_path_);
  }
  clear_has_stat_so_path();
}
inline const ::std::string& notify_conf::stat_so_path() const {
  // @@protoc_insertion_point(field_get:notify.notify_conf.stat_so_path)
  return *stat_so_path_;
}
inline void notify_conf::set_stat_so_path(const ::std::string& value) {
  set_has_stat_so_path();
  if (stat_so_path_ == _default_stat_so_path_) {
    stat_so_path_ = new ::std::string;
  }
  stat_so_path_->assign(value);
  // @@protoc_insertion_point(field_set:notify.notify_conf.stat_so_path)
}
inline void notify_conf::set_stat_so_path(const char* value) {
  set_has_stat_so_path();
  if (stat_so_path_ == _default_stat_so_path_) {
    stat_so_path_ = new ::std::string;
  }
  stat_so_path_->assign(value);
  // @@protoc_insertion_point(field_set_char:notify.notify_conf.stat_so_path)
}
inline void notify_conf::set_stat_so_path(const char* value, size_t size) {
  set_has_stat_so_path();
  if (stat_so_path_ == _default_stat_so_path_) {
    stat_so_path_ = new ::std::string;
  }
  stat_so_path_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:notify.notify_conf.stat_so_path)
}
inline ::std::string* notify_conf::mutable_stat_so_path() {
  set_has_stat_so_path();
  if (stat_so_path_ == _default_stat_so_path_) {
    stat_so_path_ = new ::std::string(*_default_stat_so_path_);
  }
  // @@protoc_insertion_point(field_mutable:notify.notify_conf.stat_so_path)
  return stat_so_path_;
}
inline ::std::string* notify_conf::release_stat_so_path() {
  clear_has_stat_so_path();
  if (stat_so_path_ == _default_stat_so_path_) {
    return NULL;
  } else {
    ::std::string* temp = stat_so_path_;
    stat_so_path_ = const_cast< ::std::string*>(_default_stat_so_path_);
    return temp;
  }
}
inline void notify_conf::set_allocated_stat_so_path(::std::string* stat_so_path) {
  if (stat_so_path_ != _default_stat_so_path_) {
    delete stat_so_path_;
  }
  if (stat_so_path) {
    set_has_stat_so_path();
    stat_so_path_ = stat_so_path;
  } else {
    clear_has_stat_so_path();
    stat_so_path_ = const_cast< ::std::string*>(_default_stat_so_path_);
  }
  // @@protoc_insertion_point(field_set_allocated:notify.notify_conf.stat_so_path)
}

// optional string stat_conf_path = 3 [default = "../stat/notify_stat.conf"];
inline bool notify_conf::has_stat_conf_path() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void notify_conf::set_has_stat_conf_path() {
  _has_bits_[0] |= 0x00000004u;
}
inline void notify_conf::clear_has_stat_conf_path() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void notify_conf::clear_stat_conf_path() {
  if (stat_conf_path_ != _default_stat_conf_path_) {
    stat_conf_path_->assign(*_default_stat_conf_path_);
  }
  clear_has_stat_conf_path();
}
inline const ::std::string& notify_conf::stat_conf_path() const {
  // @@protoc_insertion_point(field_get:notify.notify_conf.stat_conf_path)
  return *stat_conf_path_;
}
inline void notify_conf::set_stat_conf_path(const ::std::string& value) {
  set_has_stat_conf_path();
  if (stat_conf_path_ == _default_stat_conf_path_) {
    stat_conf_path_ = new ::std::string;
  }
  stat_conf_path_->assign(value);
  // @@protoc_insertion_point(field_set:notify.notify_conf.stat_conf_path)
}
inline void notify_conf::set_stat_conf_path(const char* value) {
  set_has_stat_conf_path();
  if (stat_conf_path_ == _default_stat_conf_path_) {
    stat_conf_path_ = new ::std::string;
  }
  stat_conf_path_->assign(value);
  // @@protoc_insertion_point(field_set_char:notify.notify_conf.stat_conf_path)
}
inline void notify_conf::set_stat_conf_path(const char* value, size_t size) {
  set_has_stat_conf_path();
  if (stat_conf_path_ == _default_stat_conf_path_) {
    stat_conf_path_ = new ::std::string;
  }
  stat_conf_path_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:notify.notify_conf.stat_conf_path)
}
inline ::std::string* notify_conf::mutable_stat_conf_path() {
  set_has_stat_conf_path();
  if (stat_conf_path_ == _default_stat_conf_path_) {
    stat_conf_path_ = new ::std::string(*_default_stat_conf_path_);
  }
  // @@protoc_insertion_point(field_mutable:notify.notify_conf.stat_conf_path)
  return stat_conf_path_;
}
inline ::std::string* notify_conf::release_stat_conf_path() {
  clear_has_stat_conf_path();
  if (stat_conf_path_ == _default_stat_conf_path_) {
    return NULL;
  } else {
    ::std::string* temp = stat_conf_path_;
    stat_conf_path_ = const_cast< ::std::string*>(_default_stat_conf_path_);
    return temp;
  }
}
inline void notify_conf::set_allocated_stat_conf_path(::std::string* stat_conf_path) {
  if (stat_conf_path_ != _default_stat_conf_path_) {
    delete stat_conf_path_;
  }
  if (stat_conf_path) {
    set_has_stat_conf_path();
    stat_conf_path_ = stat_conf_path;
  } else {
    clear_has_stat_conf_path();
    stat_conf_path_ = const_cast< ::std::string*>(_default_stat_conf_path_);
  }
  // @@protoc_insertion_point(field_set_allocated:notify.notify_conf.stat_conf_path)
}

// optional int32 route_active_detected_interval = 8 [default = 10000];
inline bool notify_conf::has_route_active_detected_interval() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void notify_conf::set_has_route_active_detected_interval() {
  _has_bits_[0] |= 0x00000008u;
}
inline void notify_conf::clear_has_route_active_detected_interval() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void notify_conf::clear_route_active_detected_interval() {
  route_active_detected_interval_ = 10000;
  clear_has_route_active_detected_interval();
}
inline ::google::protobuf::int32 notify_conf::route_active_detected_interval() const {
  // @@protoc_insertion_point(field_get:notify.notify_conf.route_active_detected_interval)
  return route_active_detected_interval_;
}
inline void notify_conf::set_route_active_detected_interval(::google::protobuf::int32 value) {
  set_has_route_active_detected_interval();
  route_active_detected_interval_ = value;
  // @@protoc_insertion_point(field_set:notify.notify_conf.route_active_detected_interval)
}

// optional int32 route_check_interval = 9 [default = 3000];
inline bool notify_conf::has_route_check_interval() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void notify_conf::set_has_route_check_interval() {
  _has_bits_[0] |= 0x00000010u;
}
inline void notify_conf::clear_has_route_check_interval() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void notify_conf::clear_route_check_interval() {
  route_check_interval_ = 3000;
  clear_has_route_check_interval();
}
inline ::google::protobuf::int32 notify_conf::route_check_interval() const {
  // @@protoc_insertion_point(field_get:notify.notify_conf.route_check_interval)
  return route_check_interval_;
}
inline void notify_conf::set_route_check_interval(::google::protobuf::int32 value) {
  set_has_route_check_interval();
  route_check_interval_ = value;
  // @@protoc_insertion_point(field_set:notify.notify_conf.route_check_interval)
}

// optional int32 flow_ctrl_step_interval = 10 [default = 10000];
inline bool notify_conf::has_flow_ctrl_step_interval() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void notify_conf::set_has_flow_ctrl_step_interval() {
  _has_bits_[0] |= 0x00000020u;
}
inline void notify_conf::clear_has_flow_ctrl_step_interval() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void notify_conf::clear_flow_ctrl_step_interval() {
  flow_ctrl_step_interval_ = 10000;
  clear_has_flow_ctrl_step_interval();
}
inline ::google::protobuf::int32 notify_conf::flow_ctrl_step_interval() const {
  // @@protoc_insertion_point(field_get:notify.notify_conf.flow_ctrl_step_interval)
  return flow_ctrl_step_interval_;
}
inline void notify_conf::set_flow_ctrl_step_interval(::google::protobuf::int32 value) {
  set_has_flow_ctrl_step_interval();
  flow_ctrl_step_interval_ = value;
  // @@protoc_insertion_point(field_set:notify.notify_conf.flow_ctrl_step_interval)
}

// optional int32 flow_ctrl_step_num = 11 [default = 3];
inline bool notify_conf::has_flow_ctrl_step_num() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void notify_conf::set_has_flow_ctrl_step_num() {
  _has_bits_[0] |= 0x00000040u;
}
inline void notify_conf::clear_has_flow_ctrl_step_num() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void notify_conf::clear_flow_ctrl_step_num() {
  flow_ctrl_step_num_ = 3;
  clear_has_flow_ctrl_step_num();
}
inline ::google::protobuf::int32 notify_conf::flow_ctrl_step_num() const {
  // @@protoc_insertion_point(field_get:notify.notify_conf.flow_ctrl_step_num)
  return flow_ctrl_step_num_;
}
inline void notify_conf::set_flow_ctrl_step_num(::google::protobuf::int32 value) {
  set_has_flow_ctrl_step_num();
  flow_ctrl_step_num_ = value;
  // @@protoc_insertion_point(field_set:notify.notify_conf.flow_ctrl_step_num)
}

// optional int32 flow_ctrl_step_flow = 12 [default = 7];
inline bool notify_conf::has_flow_ctrl_step_flow() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void notify_conf::set_has_flow_ctrl_step_flow() {
  _has_bits_[0] |= 0x00000080u;
}
inline void notify_conf::clear_has_flow_ctrl_step_flow() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void notify_conf::clear_flow_ctrl_step_flow() {
  flow_ctrl_step_flow_ = 7;
  clear_has_flow_ctrl_step_flow();
}
inline ::google::protobuf::int32 notify_conf::flow_ctrl_step_flow() const {
  // @@protoc_insertion_point(field_get:notify.notify_conf.flow_ctrl_step_flow)
  return flow_ctrl_step_flow_;
}
inline void notify_conf::set_flow_ctrl_step_flow(::google::protobuf::int32 value) {
  set_has_flow_ctrl_step_flow();
  flow_ctrl_step_flow_ = value;
  // @@protoc_insertion_point(field_set:notify.notify_conf.flow_ctrl_step_flow)
}

// optional int32 flow_ctrl_max_flow = 13 [default = 17];
inline bool notify_conf::has_flow_ctrl_max_flow() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void notify_conf::set_has_flow_ctrl_max_flow() {
  _has_bits_[0] |= 0x00000100u;
}
inline void notify_conf::clear_has_flow_ctrl_max_flow() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void notify_conf::clear_flow_ctrl_max_flow() {
  flow_ctrl_max_flow_ = 17;
  clear_has_flow_ctrl_max_flow();
}
inline ::google::protobuf::int32 notify_conf::flow_ctrl_max_flow() const {
  // @@protoc_insertion_point(field_get:notify.notify_conf.flow_ctrl_max_flow)
  return flow_ctrl_max_flow_;
}
inline void notify_conf::set_flow_ctrl_max_flow(::google::protobuf::int32 value) {
  set_has_flow_ctrl_max_flow();
  flow_ctrl_max_flow_ = value;
  // @@protoc_insertion_point(field_set:notify.notify_conf.flow_ctrl_max_flow)
}

// repeated string watcher_cfgs = 20;
inline int notify_conf::watcher_cfgs_size() const {
  return watcher_cfgs_.size();
}
inline void notify_conf::clear_watcher_cfgs() {
  watcher_cfgs_.Clear();
}
inline const ::std::string& notify_conf::watcher_cfgs(int index) const {
  // @@protoc_insertion_point(field_get:notify.notify_conf.watcher_cfgs)
  return watcher_cfgs_.Get(index);
}
inline ::std::string* notify_conf::mutable_watcher_cfgs(int index) {
  // @@protoc_insertion_point(field_mutable:notify.notify_conf.watcher_cfgs)
  return watcher_cfgs_.Mutable(index);
}
inline void notify_conf::set_watcher_cfgs(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:notify.notify_conf.watcher_cfgs)
  watcher_cfgs_.Mutable(index)->assign(value);
}
inline void notify_conf::set_watcher_cfgs(int index, const char* value) {
  watcher_cfgs_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:notify.notify_conf.watcher_cfgs)
}
inline void notify_conf::set_watcher_cfgs(int index, const char* value, size_t size) {
  watcher_cfgs_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:notify.notify_conf.watcher_cfgs)
}
inline ::std::string* notify_conf::add_watcher_cfgs() {
  return watcher_cfgs_.Add();
}
inline void notify_conf::add_watcher_cfgs(const ::std::string& value) {
  watcher_cfgs_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:notify.notify_conf.watcher_cfgs)
}
inline void notify_conf::add_watcher_cfgs(const char* value) {
  watcher_cfgs_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:notify.notify_conf.watcher_cfgs)
}
inline void notify_conf::add_watcher_cfgs(const char* value, size_t size) {
  watcher_cfgs_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:notify.notify_conf.watcher_cfgs)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
notify_conf::watcher_cfgs() const {
  // @@protoc_insertion_point(field_list:notify.notify_conf.watcher_cfgs)
  return watcher_cfgs_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
notify_conf::mutable_watcher_cfgs() {
  // @@protoc_insertion_point(field_mutable_list:notify.notify_conf.watcher_cfgs)
  return &watcher_cfgs_;
}

// optional int32 state_report_interval = 21 [default = 300000];
inline bool notify_conf::has_state_report_interval() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
inline void notify_conf::set_has_state_report_interval() {
  _has_bits_[0] |= 0x00000400u;
}
inline void notify_conf::clear_has_state_report_interval() {
  _has_bits_[0] &= ~0x00000400u;
}
inline void notify_conf::clear_state_report_interval() {
  state_report_interval_ = 300000;
  clear_has_state_report_interval();
}
inline ::google::protobuf::int32 notify_conf::state_report_interval() const {
  // @@protoc_insertion_point(field_get:notify.notify_conf.state_report_interval)
  return state_report_interval_;
}
inline void notify_conf::set_state_report_interval(::google::protobuf::int32 value) {
  set_has_state_report_interval();
  state_report_interval_ = value;
  // @@protoc_insertion_point(field_set:notify.notify_conf.state_report_interval)
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace notify

#ifndef SWIG
namespace google {
namespace protobuf {


}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_source_2fbaseagent_2fproto_2fnotify_5fconf_2eproto__INCLUDED
