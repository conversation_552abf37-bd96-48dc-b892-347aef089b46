// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tbuspp/proto/troute.proto

#ifndef PROTOBUF_source_2fbaseagent_2fproto_2ftroute_2eproto__INCLUDED
#define PROTOBUF_source_2fbaseagent_2fproto_2ftroute_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2006000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace troute {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_source_2fbaseagent_2fproto_2ftroute_2eproto();
void protobuf_AssignDesc_source_2fbaseagent_2fproto_2ftroute_2eproto();
void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2ftroute_2eproto();

class name_field;
class server_name_and_address;
class server_name_and_address_list;

// ===================================================================

class name_field : public ::google::protobuf::Message {
 public:
  name_field();
  virtual ~name_field();

  name_field(const name_field& from);

  inline name_field& operator=(const name_field& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const name_field& default_instance();

  void Swap(name_field* other);

  // implements Message ----------------------------------------------

  name_field* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const name_field& from);
  void MergeFrom(const name_field& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint32 level = 1;
  inline bool has_level() const;
  inline void clear_level();
  static const int kLevelFieldNumber = 1;
  inline ::google::protobuf::uint32 level() const;
  inline void set_level(::google::protobuf::uint32 value);

  // optional bytes field = 2;
  inline bool has_field() const;
  inline void clear_field();
  static const int kFieldFieldNumber = 2;
  inline const ::std::string& field() const;
  inline void set_field(const ::std::string& value);
  inline void set_field(const char* value);
  inline void set_field(const void* value, size_t size);
  inline ::std::string* mutable_field();
  inline ::std::string* release_field();
  inline void set_allocated_field(::std::string* field);

  // @@protoc_insertion_point(class_scope:troute.name_field)
 private:
  inline void set_has_level();
  inline void clear_has_level();
  inline void set_has_field();
  inline void clear_has_field();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::std::string* field_;
  ::google::protobuf::uint32 level_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2ftroute_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2ftroute_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2ftroute_2eproto();

  void InitAsDefaultInstance();
  static name_field* default_instance_;
};
// -------------------------------------------------------------------

class server_name_and_address : public ::google::protobuf::Message {
 public:
  server_name_and_address();
  virtual ~server_name_and_address();

  server_name_and_address(const server_name_and_address& from);

  inline server_name_and_address& operator=(const server_name_and_address& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const server_name_and_address& default_instance();

  void Swap(server_name_and_address* other);

  // implements Message ----------------------------------------------

  server_name_and_address* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const server_name_and_address& from);
  void MergeFrom(const server_name_and_address& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .troute.name_field fields = 1;
  inline int fields_size() const;
  inline void clear_fields();
  static const int kFieldsFieldNumber = 1;
  inline const ::troute::name_field& fields(int index) const;
  inline ::troute::name_field* mutable_fields(int index);
  inline ::troute::name_field* add_fields();
  inline const ::google::protobuf::RepeatedPtrField< ::troute::name_field >&
      fields() const;
  inline ::google::protobuf::RepeatedPtrField< ::troute::name_field >*
      mutable_fields();

  // optional bytes ip = 2;
  inline bool has_ip() const;
  inline void clear_ip();
  static const int kIpFieldNumber = 2;
  inline const ::std::string& ip() const;
  inline void set_ip(const ::std::string& value);
  inline void set_ip(const char* value);
  inline void set_ip(const void* value, size_t size);
  inline ::std::string* mutable_ip();
  inline ::std::string* release_ip();
  inline void set_allocated_ip(::std::string* ip);

  // optional uint32 port = 3;
  inline bool has_port() const;
  inline void clear_port();
  static const int kPortFieldNumber = 3;
  inline ::google::protobuf::uint32 port() const;
  inline void set_port(::google::protobuf::uint32 value);

  // optional uint32 conn_type = 4;
  inline bool has_conn_type() const;
  inline void clear_conn_type();
  static const int kConnTypeFieldNumber = 4;
  inline ::google::protobuf::uint32 conn_type() const;
  inline void set_conn_type(::google::protobuf::uint32 value);

  // optional uint32 protocol_type = 5;
  inline bool has_protocol_type() const;
  inline void clear_protocol_type();
  static const int kProtocolTypeFieldNumber = 5;
  inline ::google::protobuf::uint32 protocol_type() const;
  inline void set_protocol_type(::google::protobuf::uint32 value);

  // optional uint64 extern_instance_id = 6;
  inline bool has_extern_instance_id() const;
  inline void clear_extern_instance_id();
  static const int kExternInstanceIdFieldNumber = 6;
  inline ::google::protobuf::uint64 extern_instance_id() const;
  inline void set_extern_instance_id(::google::protobuf::uint64 value);

  // optional uint64 time_usec = 7;
  inline bool has_time_usec() const;
  inline void clear_time_usec();
  static const int kTimeUsecFieldNumber = 7;
  inline ::google::protobuf::uint64 time_usec() const;
  inline void set_time_usec(::google::protobuf::uint64 value);

  // optional uint32 me_version = 8 [default = 0];
  inline bool has_me_version() const;
  inline void clear_me_version();
  static const int kMeVersionFieldNumber = 8;
  inline ::google::protobuf::uint32 me_version() const;
  inline void set_me_version(::google::protobuf::uint32 value);

  // optional bytes note = 9;
  inline bool has_note() const;
  inline void clear_note();
  static const int kNoteFieldNumber = 9;
  inline const ::std::string& note() const;
  inline void set_note(const ::std::string& value);
  inline void set_note(const char* value);
  inline void set_note(const void* value, size_t size);
  inline ::std::string* mutable_note();
  inline ::std::string* release_note();
  inline void set_allocated_note(::std::string* note);

  // @@protoc_insertion_point(class_scope:troute.server_name_and_address)
 private:
  inline void set_has_ip();
  inline void clear_has_ip();
  inline void set_has_port();
  inline void clear_has_port();
  inline void set_has_conn_type();
  inline void clear_has_conn_type();
  inline void set_has_protocol_type();
  inline void clear_has_protocol_type();
  inline void set_has_extern_instance_id();
  inline void clear_has_extern_instance_id();
  inline void set_has_time_usec();
  inline void clear_has_time_usec();
  inline void set_has_me_version();
  inline void clear_has_me_version();
  inline void set_has_note();
  inline void clear_has_note();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::troute::name_field > fields_;
  ::std::string* ip_;
  ::google::protobuf::uint32 port_;
  ::google::protobuf::uint32 conn_type_;
  ::google::protobuf::uint64 extern_instance_id_;
  ::google::protobuf::uint32 protocol_type_;
  ::google::protobuf::uint32 me_version_;
  ::google::protobuf::uint64 time_usec_;
  ::std::string* note_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2ftroute_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2ftroute_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2ftroute_2eproto();

  void InitAsDefaultInstance();
  static server_name_and_address* default_instance_;
};
// -------------------------------------------------------------------

class server_name_and_address_list : public ::google::protobuf::Message {
 public:
  server_name_and_address_list();
  virtual ~server_name_and_address_list();

  server_name_and_address_list(const server_name_and_address_list& from);

  inline server_name_and_address_list& operator=(const server_name_and_address_list& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const server_name_and_address_list& default_instance();

  void Swap(server_name_and_address_list* other);

  // implements Message ----------------------------------------------

  server_name_and_address_list* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const server_name_and_address_list& from);
  void MergeFrom(const server_name_and_address_list& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .troute.server_name_and_address list = 1;
  inline int list_size() const;
  inline void clear_list();
  static const int kListFieldNumber = 1;
  inline const ::troute::server_name_and_address& list(int index) const;
  inline ::troute::server_name_and_address* mutable_list(int index);
  inline ::troute::server_name_and_address* add_list();
  inline const ::google::protobuf::RepeatedPtrField< ::troute::server_name_and_address >&
      list() const;
  inline ::google::protobuf::RepeatedPtrField< ::troute::server_name_and_address >*
      mutable_list();

  // @@protoc_insertion_point(class_scope:troute.server_name_and_address_list)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::troute::server_name_and_address > list_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2ftroute_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2ftroute_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2ftroute_2eproto();

  void InitAsDefaultInstance();
  static server_name_and_address_list* default_instance_;
};
// ===================================================================


// ===================================================================

// name_field

// optional uint32 level = 1;
inline bool name_field::has_level() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void name_field::set_has_level() {
  _has_bits_[0] |= 0x00000001u;
}
inline void name_field::clear_has_level() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void name_field::clear_level() {
  level_ = 0u;
  clear_has_level();
}
inline ::google::protobuf::uint32 name_field::level() const {
  // @@protoc_insertion_point(field_get:troute.name_field.level)
  return level_;
}
inline void name_field::set_level(::google::protobuf::uint32 value) {
  set_has_level();
  level_ = value;
  // @@protoc_insertion_point(field_set:troute.name_field.level)
}

// optional bytes field = 2;
inline bool name_field::has_field() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void name_field::set_has_field() {
  _has_bits_[0] |= 0x00000002u;
}
inline void name_field::clear_has_field() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void name_field::clear_field() {
  if (field_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    field_->clear();
  }
  clear_has_field();
}
inline const ::std::string& name_field::field() const {
  // @@protoc_insertion_point(field_get:troute.name_field.field)
  return *field_;
}
inline void name_field::set_field(const ::std::string& value) {
  set_has_field();
  if (field_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    field_ = new ::std::string;
  }
  field_->assign(value);
  // @@protoc_insertion_point(field_set:troute.name_field.field)
}
inline void name_field::set_field(const char* value) {
  set_has_field();
  if (field_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    field_ = new ::std::string;
  }
  field_->assign(value);
  // @@protoc_insertion_point(field_set_char:troute.name_field.field)
}
inline void name_field::set_field(const void* value, size_t size) {
  set_has_field();
  if (field_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    field_ = new ::std::string;
  }
  field_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:troute.name_field.field)
}
inline ::std::string* name_field::mutable_field() {
  set_has_field();
  if (field_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    field_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:troute.name_field.field)
  return field_;
}
inline ::std::string* name_field::release_field() {
  clear_has_field();
  if (field_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = field_;
    field_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void name_field::set_allocated_field(::std::string* field) {
  if (field_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete field_;
  }
  if (field) {
    set_has_field();
    field_ = field;
  } else {
    clear_has_field();
    field_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:troute.name_field.field)
}

// -------------------------------------------------------------------

// server_name_and_address

// repeated .troute.name_field fields = 1;
inline int server_name_and_address::fields_size() const {
  return fields_.size();
}
inline void server_name_and_address::clear_fields() {
  fields_.Clear();
}
inline const ::troute::name_field& server_name_and_address::fields(int index) const {
  // @@protoc_insertion_point(field_get:troute.server_name_and_address.fields)
  return fields_.Get(index);
}
inline ::troute::name_field* server_name_and_address::mutable_fields(int index) {
  // @@protoc_insertion_point(field_mutable:troute.server_name_and_address.fields)
  return fields_.Mutable(index);
}
inline ::troute::name_field* server_name_and_address::add_fields() {
  // @@protoc_insertion_point(field_add:troute.server_name_and_address.fields)
  return fields_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::troute::name_field >&
server_name_and_address::fields() const {
  // @@protoc_insertion_point(field_list:troute.server_name_and_address.fields)
  return fields_;
}
inline ::google::protobuf::RepeatedPtrField< ::troute::name_field >*
server_name_and_address::mutable_fields() {
  // @@protoc_insertion_point(field_mutable_list:troute.server_name_and_address.fields)
  return &fields_;
}

// optional bytes ip = 2;
inline bool server_name_and_address::has_ip() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void server_name_and_address::set_has_ip() {
  _has_bits_[0] |= 0x00000002u;
}
inline void server_name_and_address::clear_has_ip() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void server_name_and_address::clear_ip() {
  if (ip_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_->clear();
  }
  clear_has_ip();
}
inline const ::std::string& server_name_and_address::ip() const {
  // @@protoc_insertion_point(field_get:troute.server_name_and_address.ip)
  return *ip_;
}
inline void server_name_and_address::set_ip(const ::std::string& value) {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  ip_->assign(value);
  // @@protoc_insertion_point(field_set:troute.server_name_and_address.ip)
}
inline void server_name_and_address::set_ip(const char* value) {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  ip_->assign(value);
  // @@protoc_insertion_point(field_set_char:troute.server_name_and_address.ip)
}
inline void server_name_and_address::set_ip(const void* value, size_t size) {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  ip_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:troute.server_name_and_address.ip)
}
inline ::std::string* server_name_and_address::mutable_ip() {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:troute.server_name_and_address.ip)
  return ip_;
}
inline ::std::string* server_name_and_address::release_ip() {
  clear_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = ip_;
    ip_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void server_name_and_address::set_allocated_ip(::std::string* ip) {
  if (ip_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete ip_;
  }
  if (ip) {
    set_has_ip();
    ip_ = ip;
  } else {
    clear_has_ip();
    ip_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:troute.server_name_and_address.ip)
}

// optional uint32 port = 3;
inline bool server_name_and_address::has_port() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void server_name_and_address::set_has_port() {
  _has_bits_[0] |= 0x00000004u;
}
inline void server_name_and_address::clear_has_port() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void server_name_and_address::clear_port() {
  port_ = 0u;
  clear_has_port();
}
inline ::google::protobuf::uint32 server_name_and_address::port() const {
  // @@protoc_insertion_point(field_get:troute.server_name_and_address.port)
  return port_;
}
inline void server_name_and_address::set_port(::google::protobuf::uint32 value) {
  set_has_port();
  port_ = value;
  // @@protoc_insertion_point(field_set:troute.server_name_and_address.port)
}

// optional uint32 conn_type = 4;
inline bool server_name_and_address::has_conn_type() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void server_name_and_address::set_has_conn_type() {
  _has_bits_[0] |= 0x00000008u;
}
inline void server_name_and_address::clear_has_conn_type() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void server_name_and_address::clear_conn_type() {
  conn_type_ = 0u;
  clear_has_conn_type();
}
inline ::google::protobuf::uint32 server_name_and_address::conn_type() const {
  // @@protoc_insertion_point(field_get:troute.server_name_and_address.conn_type)
  return conn_type_;
}
inline void server_name_and_address::set_conn_type(::google::protobuf::uint32 value) {
  set_has_conn_type();
  conn_type_ = value;
  // @@protoc_insertion_point(field_set:troute.server_name_and_address.conn_type)
}

// optional uint32 protocol_type = 5;
inline bool server_name_and_address::has_protocol_type() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void server_name_and_address::set_has_protocol_type() {
  _has_bits_[0] |= 0x00000010u;
}
inline void server_name_and_address::clear_has_protocol_type() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void server_name_and_address::clear_protocol_type() {
  protocol_type_ = 0u;
  clear_has_protocol_type();
}
inline ::google::protobuf::uint32 server_name_and_address::protocol_type() const {
  // @@protoc_insertion_point(field_get:troute.server_name_and_address.protocol_type)
  return protocol_type_;
}
inline void server_name_and_address::set_protocol_type(::google::protobuf::uint32 value) {
  set_has_protocol_type();
  protocol_type_ = value;
  // @@protoc_insertion_point(field_set:troute.server_name_and_address.protocol_type)
}

// optional uint64 extern_instance_id = 6;
inline bool server_name_and_address::has_extern_instance_id() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void server_name_and_address::set_has_extern_instance_id() {
  _has_bits_[0] |= 0x00000020u;
}
inline void server_name_and_address::clear_has_extern_instance_id() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void server_name_and_address::clear_extern_instance_id() {
  extern_instance_id_ = GOOGLE_ULONGLONG(0);
  clear_has_extern_instance_id();
}
inline ::google::protobuf::uint64 server_name_and_address::extern_instance_id() const {
  // @@protoc_insertion_point(field_get:troute.server_name_and_address.extern_instance_id)
  return extern_instance_id_;
}
inline void server_name_and_address::set_extern_instance_id(::google::protobuf::uint64 value) {
  set_has_extern_instance_id();
  extern_instance_id_ = value;
  // @@protoc_insertion_point(field_set:troute.server_name_and_address.extern_instance_id)
}

// optional uint64 time_usec = 7;
inline bool server_name_and_address::has_time_usec() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void server_name_and_address::set_has_time_usec() {
  _has_bits_[0] |= 0x00000040u;
}
inline void server_name_and_address::clear_has_time_usec() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void server_name_and_address::clear_time_usec() {
  time_usec_ = GOOGLE_ULONGLONG(0);
  clear_has_time_usec();
}
inline ::google::protobuf::uint64 server_name_and_address::time_usec() const {
  // @@protoc_insertion_point(field_get:troute.server_name_and_address.time_usec)
  return time_usec_;
}
inline void server_name_and_address::set_time_usec(::google::protobuf::uint64 value) {
  set_has_time_usec();
  time_usec_ = value;
  // @@protoc_insertion_point(field_set:troute.server_name_and_address.time_usec)
}

// optional uint32 me_version = 8 [default = 0];
inline bool server_name_and_address::has_me_version() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void server_name_and_address::set_has_me_version() {
  _has_bits_[0] |= 0x00000080u;
}
inline void server_name_and_address::clear_has_me_version() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void server_name_and_address::clear_me_version() {
  me_version_ = 0u;
  clear_has_me_version();
}
inline ::google::protobuf::uint32 server_name_and_address::me_version() const {
  // @@protoc_insertion_point(field_get:troute.server_name_and_address.me_version)
  return me_version_;
}
inline void server_name_and_address::set_me_version(::google::protobuf::uint32 value) {
  set_has_me_version();
  me_version_ = value;
  // @@protoc_insertion_point(field_set:troute.server_name_and_address.me_version)
}

// optional bytes note = 9;
inline bool server_name_and_address::has_note() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void server_name_and_address::set_has_note() {
  _has_bits_[0] |= 0x00000100u;
}
inline void server_name_and_address::clear_has_note() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void server_name_and_address::clear_note() {
  if (note_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    note_->clear();
  }
  clear_has_note();
}
inline const ::std::string& server_name_and_address::note() const {
  // @@protoc_insertion_point(field_get:troute.server_name_and_address.note)
  return *note_;
}
inline void server_name_and_address::set_note(const ::std::string& value) {
  set_has_note();
  if (note_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    note_ = new ::std::string;
  }
  note_->assign(value);
  // @@protoc_insertion_point(field_set:troute.server_name_and_address.note)
}
inline void server_name_and_address::set_note(const char* value) {
  set_has_note();
  if (note_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    note_ = new ::std::string;
  }
  note_->assign(value);
  // @@protoc_insertion_point(field_set_char:troute.server_name_and_address.note)
}
inline void server_name_and_address::set_note(const void* value, size_t size) {
  set_has_note();
  if (note_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    note_ = new ::std::string;
  }
  note_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:troute.server_name_and_address.note)
}
inline ::std::string* server_name_and_address::mutable_note() {
  set_has_note();
  if (note_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    note_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:troute.server_name_and_address.note)
  return note_;
}
inline ::std::string* server_name_and_address::release_note() {
  clear_has_note();
  if (note_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = note_;
    note_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void server_name_and_address::set_allocated_note(::std::string* note) {
  if (note_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete note_;
  }
  if (note) {
    set_has_note();
    note_ = note;
  } else {
    clear_has_note();
    note_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:troute.server_name_and_address.note)
}

// -------------------------------------------------------------------

// server_name_and_address_list

// repeated .troute.server_name_and_address list = 1;
inline int server_name_and_address_list::list_size() const {
  return list_.size();
}
inline void server_name_and_address_list::clear_list() {
  list_.Clear();
}
inline const ::troute::server_name_and_address& server_name_and_address_list::list(int index) const {
  // @@protoc_insertion_point(field_get:troute.server_name_and_address_list.list)
  return list_.Get(index);
}
inline ::troute::server_name_and_address* server_name_and_address_list::mutable_list(int index) {
  // @@protoc_insertion_point(field_mutable:troute.server_name_and_address_list.list)
  return list_.Mutable(index);
}
inline ::troute::server_name_and_address* server_name_and_address_list::add_list() {
  // @@protoc_insertion_point(field_add:troute.server_name_and_address_list.list)
  return list_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::troute::server_name_and_address >&
server_name_and_address_list::list() const {
  // @@protoc_insertion_point(field_list:troute.server_name_and_address_list.list)
  return list_;
}
inline ::google::protobuf::RepeatedPtrField< ::troute::server_name_and_address >*
server_name_and_address_list::mutable_list() {
  // @@protoc_insertion_point(field_mutable_list:troute.server_name_and_address_list.list)
  return &list_;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace troute

#ifndef SWIG
namespace google {
namespace protobuf {


}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_source_2fbaseagent_2fproto_2ftroute_2eproto__INCLUDED
