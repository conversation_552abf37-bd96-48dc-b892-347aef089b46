// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tbuspp/proto/dbconf.proto

#ifndef PROTOBUF_source_2fbaseagent_2fproto_2fdbconf_2eproto__INCLUDED
#define PROTOBUF_source_2fbaseagent_2fproto_2fdbconf_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2006000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace baseagent_comm {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fdbconf_2eproto();
void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fdbconf_2eproto();
void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fdbconf_2eproto();

class dbconf;
class dbcluster;

// ===================================================================

class dbconf : public ::google::protobuf::Message {
 public:
  dbconf();
  virtual ~dbconf();

  dbconf(const dbconf& from);

  inline dbconf& operator=(const dbconf& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const dbconf& default_instance();

  void Swap(dbconf* other);

  // implements Message ----------------------------------------------

  dbconf* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const dbconf& from);
  void MergeFrom(const dbconf& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional bool exceptions = 1 [default = false];
  inline bool has_exceptions() const;
  inline void clear_exceptions();
  static const int kExceptionsFieldNumber = 1;
  inline bool exceptions() const;
  inline void set_exceptions(bool value);

  // optional string charset = 2 [default = "utf8"];
  inline bool has_charset() const;
  inline void clear_charset();
  static const int kCharsetFieldNumber = 2;
  inline const ::std::string& charset() const;
  inline void set_charset(const ::std::string& value);
  inline void set_charset(const char* value);
  inline void set_charset(const char* value, size_t size);
  inline ::std::string* mutable_charset();
  inline ::std::string* release_charset();
  inline void set_allocated_charset(::std::string* charset);

  // optional int32 conntimeout = 3 [default = 100];
  inline bool has_conntimeout() const;
  inline void clear_conntimeout();
  static const int kConntimeoutFieldNumber = 3;
  inline ::google::protobuf::int32 conntimeout() const;
  inline void set_conntimeout(::google::protobuf::int32 value);

  // optional bool reconnect = 4 [default = true];
  inline bool has_reconnect() const;
  inline void clear_reconnect();
  static const int kReconnectFieldNumber = 4;
  inline bool reconnect() const;
  inline void set_reconnect(bool value);

  // optional int32 readtimeout = 5 [default = 5];
  inline bool has_readtimeout() const;
  inline void clear_readtimeout();
  static const int kReadtimeoutFieldNumber = 5;
  inline ::google::protobuf::int32 readtimeout() const;
  inline void set_readtimeout(::google::protobuf::int32 value);

  // optional int32 writetimeout = 6 [default = 5];
  inline bool has_writetimeout() const;
  inline void clear_writetimeout();
  static const int kWritetimeoutFieldNumber = 6;
  inline ::google::protobuf::int32 writetimeout() const;
  inline void set_writetimeout(::google::protobuf::int32 value);

  // optional string db = 7;
  inline bool has_db() const;
  inline void clear_db();
  static const int kDbFieldNumber = 7;
  inline const ::std::string& db() const;
  inline void set_db(const ::std::string& value);
  inline void set_db(const char* value);
  inline void set_db(const char* value, size_t size);
  inline ::std::string* mutable_db();
  inline ::std::string* release_db();
  inline void set_allocated_db(::std::string* db);

  // optional string host = 8;
  inline bool has_host() const;
  inline void clear_host();
  static const int kHostFieldNumber = 8;
  inline const ::std::string& host() const;
  inline void set_host(const ::std::string& value);
  inline void set_host(const char* value);
  inline void set_host(const char* value, size_t size);
  inline ::std::string* mutable_host();
  inline ::std::string* release_host();
  inline void set_allocated_host(::std::string* host);

  // optional uint32 port = 9;
  inline bool has_port() const;
  inline void clear_port();
  static const int kPortFieldNumber = 9;
  inline ::google::protobuf::uint32 port() const;
  inline void set_port(::google::protobuf::uint32 value);

  // optional string user = 10;
  inline bool has_user() const;
  inline void clear_user();
  static const int kUserFieldNumber = 10;
  inline const ::std::string& user() const;
  inline void set_user(const ::std::string& value);
  inline void set_user(const char* value);
  inline void set_user(const char* value, size_t size);
  inline ::std::string* mutable_user();
  inline ::std::string* release_user();
  inline void set_allocated_user(::std::string* user);

  // optional string password = 11;
  inline bool has_password() const;
  inline void clear_password();
  static const int kPasswordFieldNumber = 11;
  inline const ::std::string& password() const;
  inline void set_password(const ::std::string& value);
  inline void set_password(const char* value);
  inline void set_password(const char* value, size_t size);
  inline ::std::string* mutable_password();
  inline ::std::string* release_password();
  inline void set_allocated_password(::std::string* password);

  // @@protoc_insertion_point(class_scope:baseagent_comm.dbconf)
 private:
  inline void set_has_exceptions();
  inline void clear_has_exceptions();
  inline void set_has_charset();
  inline void clear_has_charset();
  inline void set_has_conntimeout();
  inline void clear_has_conntimeout();
  inline void set_has_reconnect();
  inline void clear_has_reconnect();
  inline void set_has_readtimeout();
  inline void clear_has_readtimeout();
  inline void set_has_writetimeout();
  inline void clear_has_writetimeout();
  inline void set_has_db();
  inline void clear_has_db();
  inline void set_has_host();
  inline void clear_has_host();
  inline void set_has_port();
  inline void clear_has_port();
  inline void set_has_user();
  inline void clear_has_user();
  inline void set_has_password();
  inline void clear_has_password();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  static ::std::string* _default_charset_;
  ::std::string* charset_;
  bool exceptions_;
  bool reconnect_;
  ::google::protobuf::int32 conntimeout_;
  ::google::protobuf::int32 readtimeout_;
  ::google::protobuf::int32 writetimeout_;
  ::std::string* db_;
  ::std::string* host_;
  ::std::string* user_;
  ::std::string* password_;
  ::google::protobuf::uint32 port_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fdbconf_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fdbconf_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fdbconf_2eproto();

  void InitAsDefaultInstance();
  static dbconf* default_instance_;
};
// -------------------------------------------------------------------

class dbcluster : public ::google::protobuf::Message {
 public:
  dbcluster();
  virtual ~dbcluster();

  dbcluster(const dbcluster& from);

  inline dbcluster& operator=(const dbcluster& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const dbcluster& default_instance();

  void Swap(dbcluster* other);

  // implements Message ----------------------------------------------

  dbcluster* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const dbcluster& from);
  void MergeFrom(const dbcluster& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .baseagent_comm.dbconf list = 1;
  inline int list_size() const;
  inline void clear_list();
  static const int kListFieldNumber = 1;
  inline const ::baseagent_comm::dbconf& list(int index) const;
  inline ::baseagent_comm::dbconf* mutable_list(int index);
  inline ::baseagent_comm::dbconf* add_list();
  inline const ::google::protobuf::RepeatedPtrField< ::baseagent_comm::dbconf >&
      list() const;
  inline ::google::protobuf::RepeatedPtrField< ::baseagent_comm::dbconf >*
      mutable_list();

  // @@protoc_insertion_point(class_scope:baseagent_comm.dbcluster)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::baseagent_comm::dbconf > list_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fdbconf_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fdbconf_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fdbconf_2eproto();

  void InitAsDefaultInstance();
  static dbcluster* default_instance_;
};
// ===================================================================


// ===================================================================

// dbconf

// optional bool exceptions = 1 [default = false];
inline bool dbconf::has_exceptions() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void dbconf::set_has_exceptions() {
  _has_bits_[0] |= 0x00000001u;
}
inline void dbconf::clear_has_exceptions() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void dbconf::clear_exceptions() {
  exceptions_ = false;
  clear_has_exceptions();
}
inline bool dbconf::exceptions() const {
  // @@protoc_insertion_point(field_get:baseagent_comm.dbconf.exceptions)
  return exceptions_;
}
inline void dbconf::set_exceptions(bool value) {
  set_has_exceptions();
  exceptions_ = value;
  // @@protoc_insertion_point(field_set:baseagent_comm.dbconf.exceptions)
}

// optional string charset = 2 [default = "utf8"];
inline bool dbconf::has_charset() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void dbconf::set_has_charset() {
  _has_bits_[0] |= 0x00000002u;
}
inline void dbconf::clear_has_charset() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void dbconf::clear_charset() {
  if (charset_ != _default_charset_) {
    charset_->assign(*_default_charset_);
  }
  clear_has_charset();
}
inline const ::std::string& dbconf::charset() const {
  // @@protoc_insertion_point(field_get:baseagent_comm.dbconf.charset)
  return *charset_;
}
inline void dbconf::set_charset(const ::std::string& value) {
  set_has_charset();
  if (charset_ == _default_charset_) {
    charset_ = new ::std::string;
  }
  charset_->assign(value);
  // @@protoc_insertion_point(field_set:baseagent_comm.dbconf.charset)
}
inline void dbconf::set_charset(const char* value) {
  set_has_charset();
  if (charset_ == _default_charset_) {
    charset_ = new ::std::string;
  }
  charset_->assign(value);
  // @@protoc_insertion_point(field_set_char:baseagent_comm.dbconf.charset)
}
inline void dbconf::set_charset(const char* value, size_t size) {
  set_has_charset();
  if (charset_ == _default_charset_) {
    charset_ = new ::std::string;
  }
  charset_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:baseagent_comm.dbconf.charset)
}
inline ::std::string* dbconf::mutable_charset() {
  set_has_charset();
  if (charset_ == _default_charset_) {
    charset_ = new ::std::string(*_default_charset_);
  }
  // @@protoc_insertion_point(field_mutable:baseagent_comm.dbconf.charset)
  return charset_;
}
inline ::std::string* dbconf::release_charset() {
  clear_has_charset();
  if (charset_ == _default_charset_) {
    return NULL;
  } else {
    ::std::string* temp = charset_;
    charset_ = const_cast< ::std::string*>(_default_charset_);
    return temp;
  }
}
inline void dbconf::set_allocated_charset(::std::string* charset) {
  if (charset_ != _default_charset_) {
    delete charset_;
  }
  if (charset) {
    set_has_charset();
    charset_ = charset;
  } else {
    clear_has_charset();
    charset_ = const_cast< ::std::string*>(_default_charset_);
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent_comm.dbconf.charset)
}

// optional int32 conntimeout = 3 [default = 100];
inline bool dbconf::has_conntimeout() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void dbconf::set_has_conntimeout() {
  _has_bits_[0] |= 0x00000004u;
}
inline void dbconf::clear_has_conntimeout() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void dbconf::clear_conntimeout() {
  conntimeout_ = 100;
  clear_has_conntimeout();
}
inline ::google::protobuf::int32 dbconf::conntimeout() const {
  // @@protoc_insertion_point(field_get:baseagent_comm.dbconf.conntimeout)
  return conntimeout_;
}
inline void dbconf::set_conntimeout(::google::protobuf::int32 value) {
  set_has_conntimeout();
  conntimeout_ = value;
  // @@protoc_insertion_point(field_set:baseagent_comm.dbconf.conntimeout)
}

// optional bool reconnect = 4 [default = true];
inline bool dbconf::has_reconnect() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void dbconf::set_has_reconnect() {
  _has_bits_[0] |= 0x00000008u;
}
inline void dbconf::clear_has_reconnect() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void dbconf::clear_reconnect() {
  reconnect_ = true;
  clear_has_reconnect();
}
inline bool dbconf::reconnect() const {
  // @@protoc_insertion_point(field_get:baseagent_comm.dbconf.reconnect)
  return reconnect_;
}
inline void dbconf::set_reconnect(bool value) {
  set_has_reconnect();
  reconnect_ = value;
  // @@protoc_insertion_point(field_set:baseagent_comm.dbconf.reconnect)
}

// optional int32 readtimeout = 5 [default = 5];
inline bool dbconf::has_readtimeout() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void dbconf::set_has_readtimeout() {
  _has_bits_[0] |= 0x00000010u;
}
inline void dbconf::clear_has_readtimeout() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void dbconf::clear_readtimeout() {
  readtimeout_ = 5;
  clear_has_readtimeout();
}
inline ::google::protobuf::int32 dbconf::readtimeout() const {
  // @@protoc_insertion_point(field_get:baseagent_comm.dbconf.readtimeout)
  return readtimeout_;
}
inline void dbconf::set_readtimeout(::google::protobuf::int32 value) {
  set_has_readtimeout();
  readtimeout_ = value;
  // @@protoc_insertion_point(field_set:baseagent_comm.dbconf.readtimeout)
}

// optional int32 writetimeout = 6 [default = 5];
inline bool dbconf::has_writetimeout() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void dbconf::set_has_writetimeout() {
  _has_bits_[0] |= 0x00000020u;
}
inline void dbconf::clear_has_writetimeout() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void dbconf::clear_writetimeout() {
  writetimeout_ = 5;
  clear_has_writetimeout();
}
inline ::google::protobuf::int32 dbconf::writetimeout() const {
  // @@protoc_insertion_point(field_get:baseagent_comm.dbconf.writetimeout)
  return writetimeout_;
}
inline void dbconf::set_writetimeout(::google::protobuf::int32 value) {
  set_has_writetimeout();
  writetimeout_ = value;
  // @@protoc_insertion_point(field_set:baseagent_comm.dbconf.writetimeout)
}

// optional string db = 7;
inline bool dbconf::has_db() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void dbconf::set_has_db() {
  _has_bits_[0] |= 0x00000040u;
}
inline void dbconf::clear_has_db() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void dbconf::clear_db() {
  if (db_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    db_->clear();
  }
  clear_has_db();
}
inline const ::std::string& dbconf::db() const {
  // @@protoc_insertion_point(field_get:baseagent_comm.dbconf.db)
  return *db_;
}
inline void dbconf::set_db(const ::std::string& value) {
  set_has_db();
  if (db_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    db_ = new ::std::string;
  }
  db_->assign(value);
  // @@protoc_insertion_point(field_set:baseagent_comm.dbconf.db)
}
inline void dbconf::set_db(const char* value) {
  set_has_db();
  if (db_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    db_ = new ::std::string;
  }
  db_->assign(value);
  // @@protoc_insertion_point(field_set_char:baseagent_comm.dbconf.db)
}
inline void dbconf::set_db(const char* value, size_t size) {
  set_has_db();
  if (db_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    db_ = new ::std::string;
  }
  db_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:baseagent_comm.dbconf.db)
}
inline ::std::string* dbconf::mutable_db() {
  set_has_db();
  if (db_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    db_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:baseagent_comm.dbconf.db)
  return db_;
}
inline ::std::string* dbconf::release_db() {
  clear_has_db();
  if (db_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = db_;
    db_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void dbconf::set_allocated_db(::std::string* db) {
  if (db_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete db_;
  }
  if (db) {
    set_has_db();
    db_ = db;
  } else {
    clear_has_db();
    db_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent_comm.dbconf.db)
}

// optional string host = 8;
inline bool dbconf::has_host() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void dbconf::set_has_host() {
  _has_bits_[0] |= 0x00000080u;
}
inline void dbconf::clear_has_host() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void dbconf::clear_host() {
  if (host_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    host_->clear();
  }
  clear_has_host();
}
inline const ::std::string& dbconf::host() const {
  // @@protoc_insertion_point(field_get:baseagent_comm.dbconf.host)
  return *host_;
}
inline void dbconf::set_host(const ::std::string& value) {
  set_has_host();
  if (host_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    host_ = new ::std::string;
  }
  host_->assign(value);
  // @@protoc_insertion_point(field_set:baseagent_comm.dbconf.host)
}
inline void dbconf::set_host(const char* value) {
  set_has_host();
  if (host_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    host_ = new ::std::string;
  }
  host_->assign(value);
  // @@protoc_insertion_point(field_set_char:baseagent_comm.dbconf.host)
}
inline void dbconf::set_host(const char* value, size_t size) {
  set_has_host();
  if (host_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    host_ = new ::std::string;
  }
  host_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:baseagent_comm.dbconf.host)
}
inline ::std::string* dbconf::mutable_host() {
  set_has_host();
  if (host_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    host_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:baseagent_comm.dbconf.host)
  return host_;
}
inline ::std::string* dbconf::release_host() {
  clear_has_host();
  if (host_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = host_;
    host_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void dbconf::set_allocated_host(::std::string* host) {
  if (host_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete host_;
  }
  if (host) {
    set_has_host();
    host_ = host;
  } else {
    clear_has_host();
    host_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent_comm.dbconf.host)
}

// optional uint32 port = 9;
inline bool dbconf::has_port() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void dbconf::set_has_port() {
  _has_bits_[0] |= 0x00000100u;
}
inline void dbconf::clear_has_port() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void dbconf::clear_port() {
  port_ = 0u;
  clear_has_port();
}
inline ::google::protobuf::uint32 dbconf::port() const {
  // @@protoc_insertion_point(field_get:baseagent_comm.dbconf.port)
  return port_;
}
inline void dbconf::set_port(::google::protobuf::uint32 value) {
  set_has_port();
  port_ = value;
  // @@protoc_insertion_point(field_set:baseagent_comm.dbconf.port)
}

// optional string user = 10;
inline bool dbconf::has_user() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
inline void dbconf::set_has_user() {
  _has_bits_[0] |= 0x00000200u;
}
inline void dbconf::clear_has_user() {
  _has_bits_[0] &= ~0x00000200u;
}
inline void dbconf::clear_user() {
  if (user_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    user_->clear();
  }
  clear_has_user();
}
inline const ::std::string& dbconf::user() const {
  // @@protoc_insertion_point(field_get:baseagent_comm.dbconf.user)
  return *user_;
}
inline void dbconf::set_user(const ::std::string& value) {
  set_has_user();
  if (user_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    user_ = new ::std::string;
  }
  user_->assign(value);
  // @@protoc_insertion_point(field_set:baseagent_comm.dbconf.user)
}
inline void dbconf::set_user(const char* value) {
  set_has_user();
  if (user_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    user_ = new ::std::string;
  }
  user_->assign(value);
  // @@protoc_insertion_point(field_set_char:baseagent_comm.dbconf.user)
}
inline void dbconf::set_user(const char* value, size_t size) {
  set_has_user();
  if (user_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    user_ = new ::std::string;
  }
  user_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:baseagent_comm.dbconf.user)
}
inline ::std::string* dbconf::mutable_user() {
  set_has_user();
  if (user_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    user_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:baseagent_comm.dbconf.user)
  return user_;
}
inline ::std::string* dbconf::release_user() {
  clear_has_user();
  if (user_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = user_;
    user_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void dbconf::set_allocated_user(::std::string* user) {
  if (user_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete user_;
  }
  if (user) {
    set_has_user();
    user_ = user;
  } else {
    clear_has_user();
    user_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent_comm.dbconf.user)
}

// optional string password = 11;
inline bool dbconf::has_password() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
inline void dbconf::set_has_password() {
  _has_bits_[0] |= 0x00000400u;
}
inline void dbconf::clear_has_password() {
  _has_bits_[0] &= ~0x00000400u;
}
inline void dbconf::clear_password() {
  if (password_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    password_->clear();
  }
  clear_has_password();
}
inline const ::std::string& dbconf::password() const {
  // @@protoc_insertion_point(field_get:baseagent_comm.dbconf.password)
  return *password_;
}
inline void dbconf::set_password(const ::std::string& value) {
  set_has_password();
  if (password_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    password_ = new ::std::string;
  }
  password_->assign(value);
  // @@protoc_insertion_point(field_set:baseagent_comm.dbconf.password)
}
inline void dbconf::set_password(const char* value) {
  set_has_password();
  if (password_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    password_ = new ::std::string;
  }
  password_->assign(value);
  // @@protoc_insertion_point(field_set_char:baseagent_comm.dbconf.password)
}
inline void dbconf::set_password(const char* value, size_t size) {
  set_has_password();
  if (password_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    password_ = new ::std::string;
  }
  password_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:baseagent_comm.dbconf.password)
}
inline ::std::string* dbconf::mutable_password() {
  set_has_password();
  if (password_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    password_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:baseagent_comm.dbconf.password)
  return password_;
}
inline ::std::string* dbconf::release_password() {
  clear_has_password();
  if (password_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = password_;
    password_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void dbconf::set_allocated_password(::std::string* password) {
  if (password_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete password_;
  }
  if (password) {
    set_has_password();
    password_ = password;
  } else {
    clear_has_password();
    password_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent_comm.dbconf.password)
}

// -------------------------------------------------------------------

// dbcluster

// repeated .baseagent_comm.dbconf list = 1;
inline int dbcluster::list_size() const {
  return list_.size();
}
inline void dbcluster::clear_list() {
  list_.Clear();
}
inline const ::baseagent_comm::dbconf& dbcluster::list(int index) const {
  // @@protoc_insertion_point(field_get:baseagent_comm.dbcluster.list)
  return list_.Get(index);
}
inline ::baseagent_comm::dbconf* dbcluster::mutable_list(int index) {
  // @@protoc_insertion_point(field_mutable:baseagent_comm.dbcluster.list)
  return list_.Mutable(index);
}
inline ::baseagent_comm::dbconf* dbcluster::add_list() {
  // @@protoc_insertion_point(field_add:baseagent_comm.dbcluster.list)
  return list_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::baseagent_comm::dbconf >&
dbcluster::list() const {
  // @@protoc_insertion_point(field_list:baseagent_comm.dbcluster.list)
  return list_;
}
inline ::google::protobuf::RepeatedPtrField< ::baseagent_comm::dbconf >*
dbcluster::mutable_list() {
  // @@protoc_insertion_point(field_mutable_list:baseagent_comm.dbcluster.list)
  return &list_;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace baseagent_comm

#ifndef SWIG
namespace google {
namespace protobuf {


}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_source_2fbaseagent_2fproto_2fdbconf_2eproto__INCLUDED
