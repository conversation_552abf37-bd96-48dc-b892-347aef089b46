// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tbuspp/proto/name_server.proto

#ifndef PROTOBUF_source_2fbaseagent_2fproto_2fname_5fserver_2eproto__INCLUDED
#define PROTOBUF_source_2fbaseagent_2fproto_2fname_5fserver_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2006000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace db_name_server {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();

class cluster_ip_row;
class cluster_ip_rows;
class cluster_agent_row;
class cluster_agent_rows;
class common_list_version;
class common_list_version_rows;
class x_depend_y;
class x_depend_y_rows;
class server_name_table_directory;
class cluster_server_name_rows;
class server_name_instance_list_version;
class server_name_instance_row;
class server_name_instance_rows;
class ip_field2;
class ip_field2_rows;
class agent_route_ip_field2;
class agent_route_ip_field2_rows;
class resource_status_row;
class resource_status_rows;

// ===================================================================

class cluster_ip_row : public ::google::protobuf::Message {
 public:
  cluster_ip_row();
  virtual ~cluster_ip_row();

  cluster_ip_row(const cluster_ip_row& from);

  inline cluster_ip_row& operator=(const cluster_ip_row& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const cluster_ip_row& default_instance();

  void Swap(cluster_ip_row* other);

  // implements Message ----------------------------------------------

  cluster_ip_row* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const cluster_ip_row& from);
  void MergeFrom(const cluster_ip_row& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint64 id = 1;
  inline bool has_id() const;
  inline void clear_id();
  static const int kIdFieldNumber = 1;
  inline ::google::protobuf::uint64 id() const;
  inline void set_id(::google::protobuf::uint64 value);

  // optional bytes ip = 2;
  inline bool has_ip() const;
  inline void clear_ip();
  static const int kIpFieldNumber = 2;
  inline const ::std::string& ip() const;
  inline void set_ip(const ::std::string& value);
  inline void set_ip(const char* value);
  inline void set_ip(const void* value, size_t size);
  inline ::std::string* mutable_ip();
  inline ::std::string* release_ip();
  inline void set_allocated_ip(::std::string* ip);

  // optional uint64 utime = 3;
  inline bool has_utime() const;
  inline void clear_utime();
  static const int kUtimeFieldNumber = 3;
  inline ::google::protobuf::uint64 utime() const;
  inline void set_utime(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:db_name_server.cluster_ip_row)
 private:
  inline void set_has_id();
  inline void clear_has_id();
  inline void set_has_ip();
  inline void clear_has_ip();
  inline void set_has_utime();
  inline void clear_has_utime();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::uint64 id_;
  ::std::string* ip_;
  ::google::protobuf::uint64 utime_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();

  void InitAsDefaultInstance();
  static cluster_ip_row* default_instance_;
};
// -------------------------------------------------------------------

class cluster_ip_rows : public ::google::protobuf::Message {
 public:
  cluster_ip_rows();
  virtual ~cluster_ip_rows();

  cluster_ip_rows(const cluster_ip_rows& from);

  inline cluster_ip_rows& operator=(const cluster_ip_rows& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const cluster_ip_rows& default_instance();

  void Swap(cluster_ip_rows* other);

  // implements Message ----------------------------------------------

  cluster_ip_rows* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const cluster_ip_rows& from);
  void MergeFrom(const cluster_ip_rows& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .db_name_server.cluster_ip_row rows = 1;
  inline int rows_size() const;
  inline void clear_rows();
  static const int kRowsFieldNumber = 1;
  inline const ::db_name_server::cluster_ip_row& rows(int index) const;
  inline ::db_name_server::cluster_ip_row* mutable_rows(int index);
  inline ::db_name_server::cluster_ip_row* add_rows();
  inline const ::google::protobuf::RepeatedPtrField< ::db_name_server::cluster_ip_row >&
      rows() const;
  inline ::google::protobuf::RepeatedPtrField< ::db_name_server::cluster_ip_row >*
      mutable_rows();

  // @@protoc_insertion_point(class_scope:db_name_server.cluster_ip_rows)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::db_name_server::cluster_ip_row > rows_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();

  void InitAsDefaultInstance();
  static cluster_ip_rows* default_instance_;
};
// -------------------------------------------------------------------

class cluster_agent_row : public ::google::protobuf::Message {
 public:
  cluster_agent_row();
  virtual ~cluster_agent_row();

  cluster_agent_row(const cluster_agent_row& from);

  inline cluster_agent_row& operator=(const cluster_agent_row& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const cluster_agent_row& default_instance();

  void Swap(cluster_agent_row* other);

  // implements Message ----------------------------------------------

  cluster_agent_row* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const cluster_agent_row& from);
  void MergeFrom(const cluster_agent_row& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint64 id = 1;
  inline bool has_id() const;
  inline void clear_id();
  static const int kIdFieldNumber = 1;
  inline ::google::protobuf::uint64 id() const;
  inline void set_id(::google::protobuf::uint64 value);

  // optional bytes ip = 2;
  inline bool has_ip() const;
  inline void clear_ip();
  static const int kIpFieldNumber = 2;
  inline const ::std::string& ip() const;
  inline void set_ip(const ::std::string& value);
  inline void set_ip(const char* value);
  inline void set_ip(const void* value, size_t size);
  inline ::std::string* mutable_ip();
  inline ::std::string* release_ip();
  inline void set_allocated_ip(::std::string* ip);

  // optional uint32 port = 3;
  inline bool has_port() const;
  inline void clear_port();
  static const int kPortFieldNumber = 3;
  inline ::google::protobuf::uint32 port() const;
  inline void set_port(::google::protobuf::uint32 value);

  // optional uint64 utime = 4;
  inline bool has_utime() const;
  inline void clear_utime();
  static const int kUtimeFieldNumber = 4;
  inline ::google::protobuf::uint64 utime() const;
  inline void set_utime(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:db_name_server.cluster_agent_row)
 private:
  inline void set_has_id();
  inline void clear_has_id();
  inline void set_has_ip();
  inline void clear_has_ip();
  inline void set_has_port();
  inline void clear_has_port();
  inline void set_has_utime();
  inline void clear_has_utime();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::uint64 id_;
  ::std::string* ip_;
  ::google::protobuf::uint64 utime_;
  ::google::protobuf::uint32 port_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();

  void InitAsDefaultInstance();
  static cluster_agent_row* default_instance_;
};
// -------------------------------------------------------------------

class cluster_agent_rows : public ::google::protobuf::Message {
 public:
  cluster_agent_rows();
  virtual ~cluster_agent_rows();

  cluster_agent_rows(const cluster_agent_rows& from);

  inline cluster_agent_rows& operator=(const cluster_agent_rows& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const cluster_agent_rows& default_instance();

  void Swap(cluster_agent_rows* other);

  // implements Message ----------------------------------------------

  cluster_agent_rows* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const cluster_agent_rows& from);
  void MergeFrom(const cluster_agent_rows& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .db_name_server.cluster_agent_row rows = 1;
  inline int rows_size() const;
  inline void clear_rows();
  static const int kRowsFieldNumber = 1;
  inline const ::db_name_server::cluster_agent_row& rows(int index) const;
  inline ::db_name_server::cluster_agent_row* mutable_rows(int index);
  inline ::db_name_server::cluster_agent_row* add_rows();
  inline const ::google::protobuf::RepeatedPtrField< ::db_name_server::cluster_agent_row >&
      rows() const;
  inline ::google::protobuf::RepeatedPtrField< ::db_name_server::cluster_agent_row >*
      mutable_rows();

  // @@protoc_insertion_point(class_scope:db_name_server.cluster_agent_rows)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::db_name_server::cluster_agent_row > rows_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();

  void InitAsDefaultInstance();
  static cluster_agent_rows* default_instance_;
};
// -------------------------------------------------------------------

class common_list_version : public ::google::protobuf::Message {
 public:
  common_list_version();
  virtual ~common_list_version();

  common_list_version(const common_list_version& from);

  inline common_list_version& operator=(const common_list_version& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const common_list_version& default_instance();

  void Swap(common_list_version* other);

  // implements Message ----------------------------------------------

  common_list_version* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const common_list_version& from);
  void MergeFrom(const common_list_version& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint64 id = 1;
  inline bool has_id() const;
  inline void clear_id();
  static const int kIdFieldNumber = 1;
  inline ::google::protobuf::uint64 id() const;
  inline void set_id(::google::protobuf::uint64 value);

  // optional bytes me = 2;
  inline bool has_me() const;
  inline void clear_me();
  static const int kMeFieldNumber = 2;
  inline const ::std::string& me() const;
  inline void set_me(const ::std::string& value);
  inline void set_me(const char* value);
  inline void set_me(const void* value, size_t size);
  inline ::std::string* mutable_me();
  inline ::std::string* release_me();
  inline void set_allocated_me(::std::string* me);

  // optional bytes me_packet = 3;
  inline bool has_me_packet() const;
  inline void clear_me_packet();
  static const int kMePacketFieldNumber = 3;
  inline const ::std::string& me_packet() const;
  inline void set_me_packet(const ::std::string& value);
  inline void set_me_packet(const char* value);
  inline void set_me_packet(const void* value, size_t size);
  inline ::std::string* mutable_me_packet();
  inline ::std::string* release_me_packet();
  inline void set_allocated_me_packet(::std::string* me_packet);

  // optional bytes table_name = 4;
  inline bool has_table_name() const;
  inline void clear_table_name();
  static const int kTableNameFieldNumber = 4;
  inline const ::std::string& table_name() const;
  inline void set_table_name(const ::std::string& value);
  inline void set_table_name(const char* value);
  inline void set_table_name(const void* value, size_t size);
  inline ::std::string* mutable_table_name();
  inline ::std::string* release_table_name();
  inline void set_allocated_table_name(::std::string* table_name);

  // optional uint64 who_list_version = 5;
  inline bool has_who_list_version() const;
  inline void clear_who_list_version();
  static const int kWhoListVersionFieldNumber = 5;
  inline ::google::protobuf::uint64 who_list_version() const;
  inline void set_who_list_version(::google::protobuf::uint64 value);

  // optional uint64 utime = 6;
  inline bool has_utime() const;
  inline void clear_utime();
  static const int kUtimeFieldNumber = 6;
  inline ::google::protobuf::uint64 utime() const;
  inline void set_utime(::google::protobuf::uint64 value);

  // optional bytes fingerprint = 7;
  inline bool has_fingerprint() const;
  inline void clear_fingerprint();
  static const int kFingerprintFieldNumber = 7;
  inline const ::std::string& fingerprint() const;
  inline void set_fingerprint(const ::std::string& value);
  inline void set_fingerprint(const char* value);
  inline void set_fingerprint(const void* value, size_t size);
  inline ::std::string* mutable_fingerprint();
  inline ::std::string* release_fingerprint();
  inline void set_allocated_fingerprint(::std::string* fingerprint);

  // @@protoc_insertion_point(class_scope:db_name_server.common_list_version)
 private:
  inline void set_has_id();
  inline void clear_has_id();
  inline void set_has_me();
  inline void clear_has_me();
  inline void set_has_me_packet();
  inline void clear_has_me_packet();
  inline void set_has_table_name();
  inline void clear_has_table_name();
  inline void set_has_who_list_version();
  inline void clear_has_who_list_version();
  inline void set_has_utime();
  inline void clear_has_utime();
  inline void set_has_fingerprint();
  inline void clear_has_fingerprint();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::uint64 id_;
  ::std::string* me_;
  ::std::string* me_packet_;
  ::std::string* table_name_;
  ::google::protobuf::uint64 who_list_version_;
  ::google::protobuf::uint64 utime_;
  ::std::string* fingerprint_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();

  void InitAsDefaultInstance();
  static common_list_version* default_instance_;
};
// -------------------------------------------------------------------

class common_list_version_rows : public ::google::protobuf::Message {
 public:
  common_list_version_rows();
  virtual ~common_list_version_rows();

  common_list_version_rows(const common_list_version_rows& from);

  inline common_list_version_rows& operator=(const common_list_version_rows& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const common_list_version_rows& default_instance();

  void Swap(common_list_version_rows* other);

  // implements Message ----------------------------------------------

  common_list_version_rows* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const common_list_version_rows& from);
  void MergeFrom(const common_list_version_rows& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .db_name_server.common_list_version rows = 1;
  inline int rows_size() const;
  inline void clear_rows();
  static const int kRowsFieldNumber = 1;
  inline const ::db_name_server::common_list_version& rows(int index) const;
  inline ::db_name_server::common_list_version* mutable_rows(int index);
  inline ::db_name_server::common_list_version* add_rows();
  inline const ::google::protobuf::RepeatedPtrField< ::db_name_server::common_list_version >&
      rows() const;
  inline ::google::protobuf::RepeatedPtrField< ::db_name_server::common_list_version >*
      mutable_rows();

  // @@protoc_insertion_point(class_scope:db_name_server.common_list_version_rows)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::db_name_server::common_list_version > rows_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();

  void InitAsDefaultInstance();
  static common_list_version_rows* default_instance_;
};
// -------------------------------------------------------------------

class x_depend_y : public ::google::protobuf::Message {
 public:
  x_depend_y();
  virtual ~x_depend_y();

  x_depend_y(const x_depend_y& from);

  inline x_depend_y& operator=(const x_depend_y& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const x_depend_y& default_instance();

  void Swap(x_depend_y* other);

  // implements Message ----------------------------------------------

  x_depend_y* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const x_depend_y& from);
  void MergeFrom(const x_depend_y& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint64 id = 1;
  inline bool has_id() const;
  inline void clear_id();
  static const int kIdFieldNumber = 1;
  inline ::google::protobuf::uint64 id() const;
  inline void set_id(::google::protobuf::uint64 value);

  // optional bytes me = 2;
  inline bool has_me() const;
  inline void clear_me();
  static const int kMeFieldNumber = 2;
  inline const ::std::string& me() const;
  inline void set_me(const ::std::string& value);
  inline void set_me(const char* value);
  inline void set_me(const void* value, size_t size);
  inline ::std::string* mutable_me();
  inline ::std::string* release_me();
  inline void set_allocated_me(::std::string* me);

  // optional bytes me_packet = 3;
  inline bool has_me_packet() const;
  inline void clear_me_packet();
  static const int kMePacketFieldNumber = 3;
  inline const ::std::string& me_packet() const;
  inline void set_me_packet(const ::std::string& value);
  inline void set_me_packet(const char* value);
  inline void set_me_packet(const void* value, size_t size);
  inline ::std::string* mutable_me_packet();
  inline ::std::string* release_me_packet();
  inline void set_allocated_me_packet(::std::string* me_packet);

  // optional bytes who = 4;
  inline bool has_who() const;
  inline void clear_who();
  static const int kWhoFieldNumber = 4;
  inline const ::std::string& who() const;
  inline void set_who(const ::std::string& value);
  inline void set_who(const char* value);
  inline void set_who(const void* value, size_t size);
  inline ::std::string* mutable_who();
  inline ::std::string* release_who();
  inline void set_allocated_who(::std::string* who);

  // optional bytes who_packet = 5;
  inline bool has_who_packet() const;
  inline void clear_who_packet();
  static const int kWhoPacketFieldNumber = 5;
  inline const ::std::string& who_packet() const;
  inline void set_who_packet(const ::std::string& value);
  inline void set_who_packet(const char* value);
  inline void set_who_packet(const void* value, size_t size);
  inline ::std::string* mutable_who_packet();
  inline ::std::string* release_who_packet();
  inline void set_allocated_who_packet(::std::string* who_packet);

  // optional uint64 who_list_version = 6;
  inline bool has_who_list_version() const;
  inline void clear_who_list_version();
  static const int kWhoListVersionFieldNumber = 6;
  inline ::google::protobuf::uint64 who_list_version() const;
  inline void set_who_list_version(::google::protobuf::uint64 value);

  // optional uint64 utime = 7;
  inline bool has_utime() const;
  inline void clear_utime();
  static const int kUtimeFieldNumber = 7;
  inline ::google::protobuf::uint64 utime() const;
  inline void set_utime(::google::protobuf::uint64 value);

  // optional bytes fingerprint = 8;
  inline bool has_fingerprint() const;
  inline void clear_fingerprint();
  static const int kFingerprintFieldNumber = 8;
  inline const ::std::string& fingerprint() const;
  inline void set_fingerprint(const ::std::string& value);
  inline void set_fingerprint(const char* value);
  inline void set_fingerprint(const void* value, size_t size);
  inline ::std::string* mutable_fingerprint();
  inline ::std::string* release_fingerprint();
  inline void set_allocated_fingerprint(::std::string* fingerprint);

  // @@protoc_insertion_point(class_scope:db_name_server.x_depend_y)
 private:
  inline void set_has_id();
  inline void clear_has_id();
  inline void set_has_me();
  inline void clear_has_me();
  inline void set_has_me_packet();
  inline void clear_has_me_packet();
  inline void set_has_who();
  inline void clear_has_who();
  inline void set_has_who_packet();
  inline void clear_has_who_packet();
  inline void set_has_who_list_version();
  inline void clear_has_who_list_version();
  inline void set_has_utime();
  inline void clear_has_utime();
  inline void set_has_fingerprint();
  inline void clear_has_fingerprint();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::uint64 id_;
  ::std::string* me_;
  ::std::string* me_packet_;
  ::std::string* who_;
  ::std::string* who_packet_;
  ::google::protobuf::uint64 who_list_version_;
  ::google::protobuf::uint64 utime_;
  ::std::string* fingerprint_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();

  void InitAsDefaultInstance();
  static x_depend_y* default_instance_;
};
// -------------------------------------------------------------------

class x_depend_y_rows : public ::google::protobuf::Message {
 public:
  x_depend_y_rows();
  virtual ~x_depend_y_rows();

  x_depend_y_rows(const x_depend_y_rows& from);

  inline x_depend_y_rows& operator=(const x_depend_y_rows& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const x_depend_y_rows& default_instance();

  void Swap(x_depend_y_rows* other);

  // implements Message ----------------------------------------------

  x_depend_y_rows* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const x_depend_y_rows& from);
  void MergeFrom(const x_depend_y_rows& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .db_name_server.x_depend_y rows = 1;
  inline int rows_size() const;
  inline void clear_rows();
  static const int kRowsFieldNumber = 1;
  inline const ::db_name_server::x_depend_y& rows(int index) const;
  inline ::db_name_server::x_depend_y* mutable_rows(int index);
  inline ::db_name_server::x_depend_y* add_rows();
  inline const ::google::protobuf::RepeatedPtrField< ::db_name_server::x_depend_y >&
      rows() const;
  inline ::google::protobuf::RepeatedPtrField< ::db_name_server::x_depend_y >*
      mutable_rows();

  // @@protoc_insertion_point(class_scope:db_name_server.x_depend_y_rows)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::db_name_server::x_depend_y > rows_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();

  void InitAsDefaultInstance();
  static x_depend_y_rows* default_instance_;
};
// -------------------------------------------------------------------

class server_name_table_directory : public ::google::protobuf::Message {
 public:
  server_name_table_directory();
  virtual ~server_name_table_directory();

  server_name_table_directory(const server_name_table_directory& from);

  inline server_name_table_directory& operator=(const server_name_table_directory& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const server_name_table_directory& default_instance();

  void Swap(server_name_table_directory* other);

  // implements Message ----------------------------------------------

  server_name_table_directory* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const server_name_table_directory& from);
  void MergeFrom(const server_name_table_directory& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint64 id = 1;
  inline bool has_id() const;
  inline void clear_id();
  static const int kIdFieldNumber = 1;
  inline ::google::protobuf::uint64 id() const;
  inline void set_id(::google::protobuf::uint64 value);

  // optional bytes server_name = 2;
  inline bool has_server_name() const;
  inline void clear_server_name();
  static const int kServerNameFieldNumber = 2;
  inline const ::std::string& server_name() const;
  inline void set_server_name(const ::std::string& value);
  inline void set_server_name(const char* value);
  inline void set_server_name(const void* value, size_t size);
  inline ::std::string* mutable_server_name();
  inline ::std::string* release_server_name();
  inline void set_allocated_server_name(::std::string* server_name);

  // optional bytes server_name_packet = 3;
  inline bool has_server_name_packet() const;
  inline void clear_server_name_packet();
  static const int kServerNamePacketFieldNumber = 3;
  inline const ::std::string& server_name_packet() const;
  inline void set_server_name_packet(const ::std::string& value);
  inline void set_server_name_packet(const char* value);
  inline void set_server_name_packet(const void* value, size_t size);
  inline ::std::string* mutable_server_name_packet();
  inline ::std::string* release_server_name_packet();
  inline void set_allocated_server_name_packet(::std::string* server_name_packet);

  // optional bytes table_name = 4;
  inline bool has_table_name() const;
  inline void clear_table_name();
  static const int kTableNameFieldNumber = 4;
  inline const ::std::string& table_name() const;
  inline void set_table_name(const ::std::string& value);
  inline void set_table_name(const char* value);
  inline void set_table_name(const void* value, size_t size);
  inline ::std::string* mutable_table_name();
  inline ::std::string* release_table_name();
  inline void set_allocated_table_name(::std::string* table_name);

  // optional uint64 utime = 5;
  inline bool has_utime() const;
  inline void clear_utime();
  static const int kUtimeFieldNumber = 5;
  inline ::google::protobuf::uint64 utime() const;
  inline void set_utime(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:db_name_server.server_name_table_directory)
 private:
  inline void set_has_id();
  inline void clear_has_id();
  inline void set_has_server_name();
  inline void clear_has_server_name();
  inline void set_has_server_name_packet();
  inline void clear_has_server_name_packet();
  inline void set_has_table_name();
  inline void clear_has_table_name();
  inline void set_has_utime();
  inline void clear_has_utime();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::uint64 id_;
  ::std::string* server_name_;
  ::std::string* server_name_packet_;
  ::std::string* table_name_;
  ::google::protobuf::uint64 utime_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();

  void InitAsDefaultInstance();
  static server_name_table_directory* default_instance_;
};
// -------------------------------------------------------------------

class cluster_server_name_rows : public ::google::protobuf::Message {
 public:
  cluster_server_name_rows();
  virtual ~cluster_server_name_rows();

  cluster_server_name_rows(const cluster_server_name_rows& from);

  inline cluster_server_name_rows& operator=(const cluster_server_name_rows& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const cluster_server_name_rows& default_instance();

  void Swap(cluster_server_name_rows* other);

  // implements Message ----------------------------------------------

  cluster_server_name_rows* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const cluster_server_name_rows& from);
  void MergeFrom(const cluster_server_name_rows& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .db_name_server.server_name_table_directory rows = 1;
  inline int rows_size() const;
  inline void clear_rows();
  static const int kRowsFieldNumber = 1;
  inline const ::db_name_server::server_name_table_directory& rows(int index) const;
  inline ::db_name_server::server_name_table_directory* mutable_rows(int index);
  inline ::db_name_server::server_name_table_directory* add_rows();
  inline const ::google::protobuf::RepeatedPtrField< ::db_name_server::server_name_table_directory >&
      rows() const;
  inline ::google::protobuf::RepeatedPtrField< ::db_name_server::server_name_table_directory >*
      mutable_rows();

  // @@protoc_insertion_point(class_scope:db_name_server.cluster_server_name_rows)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::db_name_server::server_name_table_directory > rows_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();

  void InitAsDefaultInstance();
  static cluster_server_name_rows* default_instance_;
};
// -------------------------------------------------------------------

class server_name_instance_list_version : public ::google::protobuf::Message {
 public:
  server_name_instance_list_version();
  virtual ~server_name_instance_list_version();

  server_name_instance_list_version(const server_name_instance_list_version& from);

  inline server_name_instance_list_version& operator=(const server_name_instance_list_version& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const server_name_instance_list_version& default_instance();

  void Swap(server_name_instance_list_version* other);

  // implements Message ----------------------------------------------

  server_name_instance_list_version* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const server_name_instance_list_version& from);
  void MergeFrom(const server_name_instance_list_version& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint64 id = 1;
  inline bool has_id() const;
  inline void clear_id();
  static const int kIdFieldNumber = 1;
  inline ::google::protobuf::uint64 id() const;
  inline void set_id(::google::protobuf::uint64 value);

  // optional bytes server_name = 2;
  inline bool has_server_name() const;
  inline void clear_server_name();
  static const int kServerNameFieldNumber = 2;
  inline const ::std::string& server_name() const;
  inline void set_server_name(const ::std::string& value);
  inline void set_server_name(const char* value);
  inline void set_server_name(const void* value, size_t size);
  inline ::std::string* mutable_server_name();
  inline ::std::string* release_server_name();
  inline void set_allocated_server_name(::std::string* server_name);

  // optional bytes server_name_packet = 3;
  inline bool has_server_name_packet() const;
  inline void clear_server_name_packet();
  static const int kServerNamePacketFieldNumber = 3;
  inline const ::std::string& server_name_packet() const;
  inline void set_server_name_packet(const ::std::string& value);
  inline void set_server_name_packet(const char* value);
  inline void set_server_name_packet(const void* value, size_t size);
  inline ::std::string* mutable_server_name_packet();
  inline ::std::string* release_server_name_packet();
  inline void set_allocated_server_name_packet(::std::string* server_name_packet);

  // optional bytes table_name = 4;
  inline bool has_table_name() const;
  inline void clear_table_name();
  static const int kTableNameFieldNumber = 4;
  inline const ::std::string& table_name() const;
  inline void set_table_name(const ::std::string& value);
  inline void set_table_name(const char* value);
  inline void set_table_name(const void* value, size_t size);
  inline ::std::string* mutable_table_name();
  inline ::std::string* release_table_name();
  inline void set_allocated_table_name(::std::string* table_name);

  // optional uint64 instance_list_version = 5;
  inline bool has_instance_list_version() const;
  inline void clear_instance_list_version();
  static const int kInstanceListVersionFieldNumber = 5;
  inline ::google::protobuf::uint64 instance_list_version() const;
  inline void set_instance_list_version(::google::protobuf::uint64 value);

  // optional uint64 utime = 6;
  inline bool has_utime() const;
  inline void clear_utime();
  static const int kUtimeFieldNumber = 6;
  inline ::google::protobuf::uint64 utime() const;
  inline void set_utime(::google::protobuf::uint64 value);

  // optional bytes fingerprint = 7;
  inline bool has_fingerprint() const;
  inline void clear_fingerprint();
  static const int kFingerprintFieldNumber = 7;
  inline const ::std::string& fingerprint() const;
  inline void set_fingerprint(const ::std::string& value);
  inline void set_fingerprint(const char* value);
  inline void set_fingerprint(const void* value, size_t size);
  inline ::std::string* mutable_fingerprint();
  inline ::std::string* release_fingerprint();
  inline void set_allocated_fingerprint(::std::string* fingerprint);

  // @@protoc_insertion_point(class_scope:db_name_server.server_name_instance_list_version)
 private:
  inline void set_has_id();
  inline void clear_has_id();
  inline void set_has_server_name();
  inline void clear_has_server_name();
  inline void set_has_server_name_packet();
  inline void clear_has_server_name_packet();
  inline void set_has_table_name();
  inline void clear_has_table_name();
  inline void set_has_instance_list_version();
  inline void clear_has_instance_list_version();
  inline void set_has_utime();
  inline void clear_has_utime();
  inline void set_has_fingerprint();
  inline void clear_has_fingerprint();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::uint64 id_;
  ::std::string* server_name_;
  ::std::string* server_name_packet_;
  ::std::string* table_name_;
  ::google::protobuf::uint64 instance_list_version_;
  ::google::protobuf::uint64 utime_;
  ::std::string* fingerprint_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();

  void InitAsDefaultInstance();
  static server_name_instance_list_version* default_instance_;
};
// -------------------------------------------------------------------

class server_name_instance_row : public ::google::protobuf::Message {
 public:
  server_name_instance_row();
  virtual ~server_name_instance_row();

  server_name_instance_row(const server_name_instance_row& from);

  inline server_name_instance_row& operator=(const server_name_instance_row& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const server_name_instance_row& default_instance();

  void Swap(server_name_instance_row* other);

  // implements Message ----------------------------------------------

  server_name_instance_row* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const server_name_instance_row& from);
  void MergeFrom(const server_name_instance_row& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint64 id = 1;
  inline bool has_id() const;
  inline void clear_id();
  static const int kIdFieldNumber = 1;
  inline ::google::protobuf::uint64 id() const;
  inline void set_id(::google::protobuf::uint64 value);

  // optional bytes server_name = 2;
  inline bool has_server_name() const;
  inline void clear_server_name();
  static const int kServerNameFieldNumber = 2;
  inline const ::std::string& server_name() const;
  inline void set_server_name(const ::std::string& value);
  inline void set_server_name(const char* value);
  inline void set_server_name(const void* value, size_t size);
  inline ::std::string* mutable_server_name();
  inline ::std::string* release_server_name();
  inline void set_allocated_server_name(::std::string* server_name);

  // optional bytes server_name_packet = 3;
  inline bool has_server_name_packet() const;
  inline void clear_server_name_packet();
  static const int kServerNamePacketFieldNumber = 3;
  inline const ::std::string& server_name_packet() const;
  inline void set_server_name_packet(const ::std::string& value);
  inline void set_server_name_packet(const char* value);
  inline void set_server_name_packet(const void* value, size_t size);
  inline ::std::string* mutable_server_name_packet();
  inline ::std::string* release_server_name_packet();
  inline void set_allocated_server_name_packet(::std::string* server_name_packet);

  // optional bytes instance_name = 4;
  inline bool has_instance_name() const;
  inline void clear_instance_name();
  static const int kInstanceNameFieldNumber = 4;
  inline const ::std::string& instance_name() const;
  inline void set_instance_name(const ::std::string& value);
  inline void set_instance_name(const char* value);
  inline void set_instance_name(const void* value, size_t size);
  inline ::std::string* mutable_instance_name();
  inline ::std::string* release_instance_name();
  inline void set_allocated_instance_name(::std::string* instance_name);

  // optional bytes instance_name_packet = 5;
  inline bool has_instance_name_packet() const;
  inline void clear_instance_name_packet();
  static const int kInstanceNamePacketFieldNumber = 5;
  inline const ::std::string& instance_name_packet() const;
  inline void set_instance_name_packet(const ::std::string& value);
  inline void set_instance_name_packet(const char* value);
  inline void set_instance_name_packet(const void* value, size_t size);
  inline ::std::string* mutable_instance_name_packet();
  inline ::std::string* release_instance_name_packet();
  inline void set_allocated_instance_name_packet(::std::string* instance_name_packet);

  // optional bytes ip = 6;
  inline bool has_ip() const;
  inline void clear_ip();
  static const int kIpFieldNumber = 6;
  inline const ::std::string& ip() const;
  inline void set_ip(const ::std::string& value);
  inline void set_ip(const char* value);
  inline void set_ip(const void* value, size_t size);
  inline ::std::string* mutable_ip();
  inline ::std::string* release_ip();
  inline void set_allocated_ip(::std::string* ip);

  // optional uint32 port = 7;
  inline bool has_port() const;
  inline void clear_port();
  static const int kPortFieldNumber = 7;
  inline ::google::protobuf::uint32 port() const;
  inline void set_port(::google::protobuf::uint32 value);

  // optional uint64 instance_list_version = 8;
  inline bool has_instance_list_version() const;
  inline void clear_instance_list_version();
  static const int kInstanceListVersionFieldNumber = 8;
  inline ::google::protobuf::uint64 instance_list_version() const;
  inline void set_instance_list_version(::google::protobuf::uint64 value);

  // optional uint64 end_instance_list_version = 9;
  inline bool has_end_instance_list_version() const;
  inline void clear_end_instance_list_version();
  static const int kEndInstanceListVersionFieldNumber = 9;
  inline ::google::protobuf::uint64 end_instance_list_version() const;
  inline void set_end_instance_list_version(::google::protobuf::uint64 value);

  // optional uint64 utime = 10;
  inline bool has_utime() const;
  inline void clear_utime();
  static const int kUtimeFieldNumber = 10;
  inline ::google::protobuf::uint64 utime() const;
  inline void set_utime(::google::protobuf::uint64 value);

  // optional bytes fingerprint = 11;
  inline bool has_fingerprint() const;
  inline void clear_fingerprint();
  static const int kFingerprintFieldNumber = 11;
  inline const ::std::string& fingerprint() const;
  inline void set_fingerprint(const ::std::string& value);
  inline void set_fingerprint(const char* value);
  inline void set_fingerprint(const void* value, size_t size);
  inline ::std::string* mutable_fingerprint();
  inline ::std::string* release_fingerprint();
  inline void set_allocated_fingerprint(::std::string* fingerprint);

  // @@protoc_insertion_point(class_scope:db_name_server.server_name_instance_row)
 private:
  inline void set_has_id();
  inline void clear_has_id();
  inline void set_has_server_name();
  inline void clear_has_server_name();
  inline void set_has_server_name_packet();
  inline void clear_has_server_name_packet();
  inline void set_has_instance_name();
  inline void clear_has_instance_name();
  inline void set_has_instance_name_packet();
  inline void clear_has_instance_name_packet();
  inline void set_has_ip();
  inline void clear_has_ip();
  inline void set_has_port();
  inline void clear_has_port();
  inline void set_has_instance_list_version();
  inline void clear_has_instance_list_version();
  inline void set_has_end_instance_list_version();
  inline void clear_has_end_instance_list_version();
  inline void set_has_utime();
  inline void clear_has_utime();
  inline void set_has_fingerprint();
  inline void clear_has_fingerprint();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::uint64 id_;
  ::std::string* server_name_;
  ::std::string* server_name_packet_;
  ::std::string* instance_name_;
  ::std::string* instance_name_packet_;
  ::std::string* ip_;
  ::google::protobuf::uint64 instance_list_version_;
  ::google::protobuf::uint64 end_instance_list_version_;
  ::google::protobuf::uint64 utime_;
  ::std::string* fingerprint_;
  ::google::protobuf::uint32 port_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();

  void InitAsDefaultInstance();
  static server_name_instance_row* default_instance_;
};
// -------------------------------------------------------------------

class server_name_instance_rows : public ::google::protobuf::Message {
 public:
  server_name_instance_rows();
  virtual ~server_name_instance_rows();

  server_name_instance_rows(const server_name_instance_rows& from);

  inline server_name_instance_rows& operator=(const server_name_instance_rows& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const server_name_instance_rows& default_instance();

  void Swap(server_name_instance_rows* other);

  // implements Message ----------------------------------------------

  server_name_instance_rows* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const server_name_instance_rows& from);
  void MergeFrom(const server_name_instance_rows& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .db_name_server.server_name_instance_row rows = 1;
  inline int rows_size() const;
  inline void clear_rows();
  static const int kRowsFieldNumber = 1;
  inline const ::db_name_server::server_name_instance_row& rows(int index) const;
  inline ::db_name_server::server_name_instance_row* mutable_rows(int index);
  inline ::db_name_server::server_name_instance_row* add_rows();
  inline const ::google::protobuf::RepeatedPtrField< ::db_name_server::server_name_instance_row >&
      rows() const;
  inline ::google::protobuf::RepeatedPtrField< ::db_name_server::server_name_instance_row >*
      mutable_rows();

  // @@protoc_insertion_point(class_scope:db_name_server.server_name_instance_rows)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::db_name_server::server_name_instance_row > rows_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();

  void InitAsDefaultInstance();
  static server_name_instance_rows* default_instance_;
};
// -------------------------------------------------------------------

class ip_field2 : public ::google::protobuf::Message {
 public:
  ip_field2();
  virtual ~ip_field2();

  ip_field2(const ip_field2& from);

  inline ip_field2& operator=(const ip_field2& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ip_field2& default_instance();

  void Swap(ip_field2* other);

  // implements Message ----------------------------------------------

  ip_field2* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ip_field2& from);
  void MergeFrom(const ip_field2& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint64 id = 1;
  inline bool has_id() const;
  inline void clear_id();
  static const int kIdFieldNumber = 1;
  inline ::google::protobuf::uint64 id() const;
  inline void set_id(::google::protobuf::uint64 value);

  // optional bytes ip = 2;
  inline bool has_ip() const;
  inline void clear_ip();
  static const int kIpFieldNumber = 2;
  inline const ::std::string& ip() const;
  inline void set_ip(const ::std::string& value);
  inline void set_ip(const char* value);
  inline void set_ip(const void* value, size_t size);
  inline ::std::string* mutable_ip();
  inline ::std::string* release_ip();
  inline void set_allocated_ip(::std::string* ip);

  // optional uint32 port = 3;
  inline bool has_port() const;
  inline void clear_port();
  static const int kPortFieldNumber = 3;
  inline ::google::protobuf::uint32 port() const;
  inline void set_port(::google::protobuf::uint32 value);

  // optional bytes table_name = 4;
  inline bool has_table_name() const;
  inline void clear_table_name();
  static const int kTableNameFieldNumber = 4;
  inline const ::std::string& table_name() const;
  inline void set_table_name(const ::std::string& value);
  inline void set_table_name(const char* value);
  inline void set_table_name(const void* value, size_t size);
  inline ::std::string* mutable_table_name();
  inline ::std::string* release_table_name();
  inline void set_allocated_table_name(::std::string* table_name);

  // optional bytes server_name = 5;
  inline bool has_server_name() const;
  inline void clear_server_name();
  static const int kServerNameFieldNumber = 5;
  inline const ::std::string& server_name() const;
  inline void set_server_name(const ::std::string& value);
  inline void set_server_name(const char* value);
  inline void set_server_name(const void* value, size_t size);
  inline ::std::string* mutable_server_name();
  inline ::std::string* release_server_name();
  inline void set_allocated_server_name(::std::string* server_name);

  // optional bytes server_name_packet = 6;
  inline bool has_server_name_packet() const;
  inline void clear_server_name_packet();
  static const int kServerNamePacketFieldNumber = 6;
  inline const ::std::string& server_name_packet() const;
  inline void set_server_name_packet(const ::std::string& value);
  inline void set_server_name_packet(const char* value);
  inline void set_server_name_packet(const void* value, size_t size);
  inline ::std::string* mutable_server_name_packet();
  inline ::std::string* release_server_name_packet();
  inline void set_allocated_server_name_packet(::std::string* server_name_packet);

  // optional bytes instance_name = 7;
  inline bool has_instance_name() const;
  inline void clear_instance_name();
  static const int kInstanceNameFieldNumber = 7;
  inline const ::std::string& instance_name() const;
  inline void set_instance_name(const ::std::string& value);
  inline void set_instance_name(const char* value);
  inline void set_instance_name(const void* value, size_t size);
  inline ::std::string* mutable_instance_name();
  inline ::std::string* release_instance_name();
  inline void set_allocated_instance_name(::std::string* instance_name);

  // optional bytes instance_name_packet = 8;
  inline bool has_instance_name_packet() const;
  inline void clear_instance_name_packet();
  static const int kInstanceNamePacketFieldNumber = 8;
  inline const ::std::string& instance_name_packet() const;
  inline void set_instance_name_packet(const ::std::string& value);
  inline void set_instance_name_packet(const char* value);
  inline void set_instance_name_packet(const void* value, size_t size);
  inline ::std::string* mutable_instance_name_packet();
  inline ::std::string* release_instance_name_packet();
  inline void set_allocated_instance_name_packet(::std::string* instance_name_packet);

  // optional uint64 utime = 9;
  inline bool has_utime() const;
  inline void clear_utime();
  static const int kUtimeFieldNumber = 9;
  inline ::google::protobuf::uint64 utime() const;
  inline void set_utime(::google::protobuf::uint64 value);

  // optional bytes fingerprint = 10;
  inline bool has_fingerprint() const;
  inline void clear_fingerprint();
  static const int kFingerprintFieldNumber = 10;
  inline const ::std::string& fingerprint() const;
  inline void set_fingerprint(const ::std::string& value);
  inline void set_fingerprint(const char* value);
  inline void set_fingerprint(const void* value, size_t size);
  inline ::std::string* mutable_fingerprint();
  inline ::std::string* release_fingerprint();
  inline void set_allocated_fingerprint(::std::string* fingerprint);

  // @@protoc_insertion_point(class_scope:db_name_server.ip_field2)
 private:
  inline void set_has_id();
  inline void clear_has_id();
  inline void set_has_ip();
  inline void clear_has_ip();
  inline void set_has_port();
  inline void clear_has_port();
  inline void set_has_table_name();
  inline void clear_has_table_name();
  inline void set_has_server_name();
  inline void clear_has_server_name();
  inline void set_has_server_name_packet();
  inline void clear_has_server_name_packet();
  inline void set_has_instance_name();
  inline void clear_has_instance_name();
  inline void set_has_instance_name_packet();
  inline void clear_has_instance_name_packet();
  inline void set_has_utime();
  inline void clear_has_utime();
  inline void set_has_fingerprint();
  inline void clear_has_fingerprint();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::uint64 id_;
  ::std::string* ip_;
  ::std::string* table_name_;
  ::std::string* server_name_;
  ::std::string* server_name_packet_;
  ::std::string* instance_name_;
  ::std::string* instance_name_packet_;
  ::google::protobuf::uint64 utime_;
  ::std::string* fingerprint_;
  ::google::protobuf::uint32 port_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();

  void InitAsDefaultInstance();
  static ip_field2* default_instance_;
};
// -------------------------------------------------------------------

class ip_field2_rows : public ::google::protobuf::Message {
 public:
  ip_field2_rows();
  virtual ~ip_field2_rows();

  ip_field2_rows(const ip_field2_rows& from);

  inline ip_field2_rows& operator=(const ip_field2_rows& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ip_field2_rows& default_instance();

  void Swap(ip_field2_rows* other);

  // implements Message ----------------------------------------------

  ip_field2_rows* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ip_field2_rows& from);
  void MergeFrom(const ip_field2_rows& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .db_name_server.ip_field2 rows = 1;
  inline int rows_size() const;
  inline void clear_rows();
  static const int kRowsFieldNumber = 1;
  inline const ::db_name_server::ip_field2& rows(int index) const;
  inline ::db_name_server::ip_field2* mutable_rows(int index);
  inline ::db_name_server::ip_field2* add_rows();
  inline const ::google::protobuf::RepeatedPtrField< ::db_name_server::ip_field2 >&
      rows() const;
  inline ::google::protobuf::RepeatedPtrField< ::db_name_server::ip_field2 >*
      mutable_rows();

  // @@protoc_insertion_point(class_scope:db_name_server.ip_field2_rows)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::db_name_server::ip_field2 > rows_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();

  void InitAsDefaultInstance();
  static ip_field2_rows* default_instance_;
};
// -------------------------------------------------------------------

class agent_route_ip_field2 : public ::google::protobuf::Message {
 public:
  agent_route_ip_field2();
  virtual ~agent_route_ip_field2();

  agent_route_ip_field2(const agent_route_ip_field2& from);

  inline agent_route_ip_field2& operator=(const agent_route_ip_field2& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const agent_route_ip_field2& default_instance();

  void Swap(agent_route_ip_field2* other);

  // implements Message ----------------------------------------------

  agent_route_ip_field2* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const agent_route_ip_field2& from);
  void MergeFrom(const agent_route_ip_field2& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint64 id = 1;
  inline bool has_id() const;
  inline void clear_id();
  static const int kIdFieldNumber = 1;
  inline ::google::protobuf::uint64 id() const;
  inline void set_id(::google::protobuf::uint64 value);

  // optional bytes ip = 2;
  inline bool has_ip() const;
  inline void clear_ip();
  static const int kIpFieldNumber = 2;
  inline const ::std::string& ip() const;
  inline void set_ip(const ::std::string& value);
  inline void set_ip(const char* value);
  inline void set_ip(const void* value, size_t size);
  inline ::std::string* mutable_ip();
  inline ::std::string* release_ip();
  inline void set_allocated_ip(::std::string* ip);

  // optional uint32 port = 3;
  inline bool has_port() const;
  inline void clear_port();
  static const int kPortFieldNumber = 3;
  inline ::google::protobuf::uint32 port() const;
  inline void set_port(::google::protobuf::uint32 value);

  // optional bytes server_name = 4;
  inline bool has_server_name() const;
  inline void clear_server_name();
  static const int kServerNameFieldNumber = 4;
  inline const ::std::string& server_name() const;
  inline void set_server_name(const ::std::string& value);
  inline void set_server_name(const char* value);
  inline void set_server_name(const void* value, size_t size);
  inline ::std::string* mutable_server_name();
  inline ::std::string* release_server_name();
  inline void set_allocated_server_name(::std::string* server_name);

  // optional bytes instance_list = 5;
  inline bool has_instance_list() const;
  inline void clear_instance_list();
  static const int kInstanceListFieldNumber = 5;
  inline const ::std::string& instance_list() const;
  inline void set_instance_list(const ::std::string& value);
  inline void set_instance_list(const char* value);
  inline void set_instance_list(const void* value, size_t size);
  inline ::std::string* mutable_instance_list();
  inline ::std::string* release_instance_list();
  inline void set_allocated_instance_list(::std::string* instance_list);

  // optional uint64 utime = 6;
  inline bool has_utime() const;
  inline void clear_utime();
  static const int kUtimeFieldNumber = 6;
  inline ::google::protobuf::uint64 utime() const;
  inline void set_utime(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:db_name_server.agent_route_ip_field2)
 private:
  inline void set_has_id();
  inline void clear_has_id();
  inline void set_has_ip();
  inline void clear_has_ip();
  inline void set_has_port();
  inline void clear_has_port();
  inline void set_has_server_name();
  inline void clear_has_server_name();
  inline void set_has_instance_list();
  inline void clear_has_instance_list();
  inline void set_has_utime();
  inline void clear_has_utime();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::uint64 id_;
  ::std::string* ip_;
  ::std::string* server_name_;
  ::std::string* instance_list_;
  ::google::protobuf::uint64 utime_;
  ::google::protobuf::uint32 port_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();

  void InitAsDefaultInstance();
  static agent_route_ip_field2* default_instance_;
};
// -------------------------------------------------------------------

class agent_route_ip_field2_rows : public ::google::protobuf::Message {
 public:
  agent_route_ip_field2_rows();
  virtual ~agent_route_ip_field2_rows();

  agent_route_ip_field2_rows(const agent_route_ip_field2_rows& from);

  inline agent_route_ip_field2_rows& operator=(const agent_route_ip_field2_rows& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const agent_route_ip_field2_rows& default_instance();

  void Swap(agent_route_ip_field2_rows* other);

  // implements Message ----------------------------------------------

  agent_route_ip_field2_rows* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const agent_route_ip_field2_rows& from);
  void MergeFrom(const agent_route_ip_field2_rows& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .db_name_server.agent_route_ip_field2 rows = 1;
  inline int rows_size() const;
  inline void clear_rows();
  static const int kRowsFieldNumber = 1;
  inline const ::db_name_server::agent_route_ip_field2& rows(int index) const;
  inline ::db_name_server::agent_route_ip_field2* mutable_rows(int index);
  inline ::db_name_server::agent_route_ip_field2* add_rows();
  inline const ::google::protobuf::RepeatedPtrField< ::db_name_server::agent_route_ip_field2 >&
      rows() const;
  inline ::google::protobuf::RepeatedPtrField< ::db_name_server::agent_route_ip_field2 >*
      mutable_rows();

  // @@protoc_insertion_point(class_scope:db_name_server.agent_route_ip_field2_rows)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::db_name_server::agent_route_ip_field2 > rows_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();

  void InitAsDefaultInstance();
  static agent_route_ip_field2_rows* default_instance_;
};
// -------------------------------------------------------------------

class resource_status_row : public ::google::protobuf::Message {
 public:
  resource_status_row();
  virtual ~resource_status_row();

  resource_status_row(const resource_status_row& from);

  inline resource_status_row& operator=(const resource_status_row& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const resource_status_row& default_instance();

  void Swap(resource_status_row* other);

  // implements Message ----------------------------------------------

  resource_status_row* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const resource_status_row& from);
  void MergeFrom(const resource_status_row& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint64 id = 1;
  inline bool has_id() const;
  inline void clear_id();
  static const int kIdFieldNumber = 1;
  inline ::google::protobuf::uint64 id() const;
  inline void set_id(::google::protobuf::uint64 value);

  // optional bytes ip = 2;
  inline bool has_ip() const;
  inline void clear_ip();
  static const int kIpFieldNumber = 2;
  inline const ::std::string& ip() const;
  inline void set_ip(const ::std::string& value);
  inline void set_ip(const char* value);
  inline void set_ip(const void* value, size_t size);
  inline ::std::string* mutable_ip();
  inline ::std::string* release_ip();
  inline void set_allocated_ip(::std::string* ip);

  // optional uint32 port = 3;
  inline bool has_port() const;
  inline void clear_port();
  static const int kPortFieldNumber = 3;
  inline ::google::protobuf::uint32 port() const;
  inline void set_port(::google::protobuf::uint32 value);

  // optional bytes resource = 4;
  inline bool has_resource() const;
  inline void clear_resource();
  static const int kResourceFieldNumber = 4;
  inline const ::std::string& resource() const;
  inline void set_resource(const ::std::string& value);
  inline void set_resource(const char* value);
  inline void set_resource(const void* value, size_t size);
  inline ::std::string* mutable_resource();
  inline ::std::string* release_resource();
  inline void set_allocated_resource(::std::string* resource);

  // optional bytes name = 5;
  inline bool has_name() const;
  inline void clear_name();
  static const int kNameFieldNumber = 5;
  inline const ::std::string& name() const;
  inline void set_name(const ::std::string& value);
  inline void set_name(const char* value);
  inline void set_name(const void* value, size_t size);
  inline ::std::string* mutable_name();
  inline ::std::string* release_name();
  inline void set_allocated_name(::std::string* name);

  // optional uint32 value_type = 6;
  inline bool has_value_type() const;
  inline void clear_value_type();
  static const int kValueTypeFieldNumber = 6;
  inline ::google::protobuf::uint32 value_type() const;
  inline void set_value_type(::google::protobuf::uint32 value);

  // optional uint64 value = 7;
  inline bool has_value() const;
  inline void clear_value();
  static const int kValueFieldNumber = 7;
  inline ::google::protobuf::uint64 value() const;
  inline void set_value(::google::protobuf::uint64 value);

  // optional bytes value_str = 8;
  inline bool has_value_str() const;
  inline void clear_value_str();
  static const int kValueStrFieldNumber = 8;
  inline const ::std::string& value_str() const;
  inline void set_value_str(const ::std::string& value);
  inline void set_value_str(const char* value);
  inline void set_value_str(const void* value, size_t size);
  inline ::std::string* mutable_value_str();
  inline ::std::string* release_value_str();
  inline void set_allocated_value_str(::std::string* value_str);

  // optional uint64 utime = 9;
  inline bool has_utime() const;
  inline void clear_utime();
  static const int kUtimeFieldNumber = 9;
  inline ::google::protobuf::uint64 utime() const;
  inline void set_utime(::google::protobuf::uint64 value);

  // optional bytes descs = 10;
  inline bool has_descs() const;
  inline void clear_descs();
  static const int kDescsFieldNumber = 10;
  inline const ::std::string& descs() const;
  inline void set_descs(const ::std::string& value);
  inline void set_descs(const char* value);
  inline void set_descs(const void* value, size_t size);
  inline ::std::string* mutable_descs();
  inline ::std::string* release_descs();
  inline void set_allocated_descs(::std::string* descs);

  // @@protoc_insertion_point(class_scope:db_name_server.resource_status_row)
 private:
  inline void set_has_id();
  inline void clear_has_id();
  inline void set_has_ip();
  inline void clear_has_ip();
  inline void set_has_port();
  inline void clear_has_port();
  inline void set_has_resource();
  inline void clear_has_resource();
  inline void set_has_name();
  inline void clear_has_name();
  inline void set_has_value_type();
  inline void clear_has_value_type();
  inline void set_has_value();
  inline void clear_has_value();
  inline void set_has_value_str();
  inline void clear_has_value_str();
  inline void set_has_utime();
  inline void clear_has_utime();
  inline void set_has_descs();
  inline void clear_has_descs();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::uint64 id_;
  ::std::string* ip_;
  ::std::string* resource_;
  ::google::protobuf::uint32 port_;
  ::google::protobuf::uint32 value_type_;
  ::std::string* name_;
  ::google::protobuf::uint64 value_;
  ::std::string* value_str_;
  ::google::protobuf::uint64 utime_;
  ::std::string* descs_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();

  void InitAsDefaultInstance();
  static resource_status_row* default_instance_;
};
// -------------------------------------------------------------------

class resource_status_rows : public ::google::protobuf::Message {
 public:
  resource_status_rows();
  virtual ~resource_status_rows();

  resource_status_rows(const resource_status_rows& from);

  inline resource_status_rows& operator=(const resource_status_rows& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const resource_status_rows& default_instance();

  void Swap(resource_status_rows* other);

  // implements Message ----------------------------------------------

  resource_status_rows* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const resource_status_rows& from);
  void MergeFrom(const resource_status_rows& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .db_name_server.resource_status_row rows = 1;
  inline int rows_size() const;
  inline void clear_rows();
  static const int kRowsFieldNumber = 1;
  inline const ::db_name_server::resource_status_row& rows(int index) const;
  inline ::db_name_server::resource_status_row* mutable_rows(int index);
  inline ::db_name_server::resource_status_row* add_rows();
  inline const ::google::protobuf::RepeatedPtrField< ::db_name_server::resource_status_row >&
      rows() const;
  inline ::google::protobuf::RepeatedPtrField< ::db_name_server::resource_status_row >*
      mutable_rows();

  // @@protoc_insertion_point(class_scope:db_name_server.resource_status_rows)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::db_name_server::resource_status_row > rows_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fname_5fserver_2eproto();

  void InitAsDefaultInstance();
  static resource_status_rows* default_instance_;
};
// ===================================================================


// ===================================================================

// cluster_ip_row

// optional uint64 id = 1;
inline bool cluster_ip_row::has_id() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void cluster_ip_row::set_has_id() {
  _has_bits_[0] |= 0x00000001u;
}
inline void cluster_ip_row::clear_has_id() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void cluster_ip_row::clear_id() {
  id_ = GOOGLE_ULONGLONG(0);
  clear_has_id();
}
inline ::google::protobuf::uint64 cluster_ip_row::id() const {
  // @@protoc_insertion_point(field_get:db_name_server.cluster_ip_row.id)
  return id_;
}
inline void cluster_ip_row::set_id(::google::protobuf::uint64 value) {
  set_has_id();
  id_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.cluster_ip_row.id)
}

// optional bytes ip = 2;
inline bool cluster_ip_row::has_ip() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void cluster_ip_row::set_has_ip() {
  _has_bits_[0] |= 0x00000002u;
}
inline void cluster_ip_row::clear_has_ip() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void cluster_ip_row::clear_ip() {
  if (ip_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_->clear();
  }
  clear_has_ip();
}
inline const ::std::string& cluster_ip_row::ip() const {
  // @@protoc_insertion_point(field_get:db_name_server.cluster_ip_row.ip)
  return *ip_;
}
inline void cluster_ip_row::set_ip(const ::std::string& value) {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  ip_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.cluster_ip_row.ip)
}
inline void cluster_ip_row::set_ip(const char* value) {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  ip_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.cluster_ip_row.ip)
}
inline void cluster_ip_row::set_ip(const void* value, size_t size) {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  ip_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.cluster_ip_row.ip)
}
inline ::std::string* cluster_ip_row::mutable_ip() {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.cluster_ip_row.ip)
  return ip_;
}
inline ::std::string* cluster_ip_row::release_ip() {
  clear_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = ip_;
    ip_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void cluster_ip_row::set_allocated_ip(::std::string* ip) {
  if (ip_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete ip_;
  }
  if (ip) {
    set_has_ip();
    ip_ = ip;
  } else {
    clear_has_ip();
    ip_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.cluster_ip_row.ip)
}

// optional uint64 utime = 3;
inline bool cluster_ip_row::has_utime() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void cluster_ip_row::set_has_utime() {
  _has_bits_[0] |= 0x00000004u;
}
inline void cluster_ip_row::clear_has_utime() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void cluster_ip_row::clear_utime() {
  utime_ = GOOGLE_ULONGLONG(0);
  clear_has_utime();
}
inline ::google::protobuf::uint64 cluster_ip_row::utime() const {
  // @@protoc_insertion_point(field_get:db_name_server.cluster_ip_row.utime)
  return utime_;
}
inline void cluster_ip_row::set_utime(::google::protobuf::uint64 value) {
  set_has_utime();
  utime_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.cluster_ip_row.utime)
}

// -------------------------------------------------------------------

// cluster_ip_rows

// repeated .db_name_server.cluster_ip_row rows = 1;
inline int cluster_ip_rows::rows_size() const {
  return rows_.size();
}
inline void cluster_ip_rows::clear_rows() {
  rows_.Clear();
}
inline const ::db_name_server::cluster_ip_row& cluster_ip_rows::rows(int index) const {
  // @@protoc_insertion_point(field_get:db_name_server.cluster_ip_rows.rows)
  return rows_.Get(index);
}
inline ::db_name_server::cluster_ip_row* cluster_ip_rows::mutable_rows(int index) {
  // @@protoc_insertion_point(field_mutable:db_name_server.cluster_ip_rows.rows)
  return rows_.Mutable(index);
}
inline ::db_name_server::cluster_ip_row* cluster_ip_rows::add_rows() {
  // @@protoc_insertion_point(field_add:db_name_server.cluster_ip_rows.rows)
  return rows_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::db_name_server::cluster_ip_row >&
cluster_ip_rows::rows() const {
  // @@protoc_insertion_point(field_list:db_name_server.cluster_ip_rows.rows)
  return rows_;
}
inline ::google::protobuf::RepeatedPtrField< ::db_name_server::cluster_ip_row >*
cluster_ip_rows::mutable_rows() {
  // @@protoc_insertion_point(field_mutable_list:db_name_server.cluster_ip_rows.rows)
  return &rows_;
}

// -------------------------------------------------------------------

// cluster_agent_row

// optional uint64 id = 1;
inline bool cluster_agent_row::has_id() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void cluster_agent_row::set_has_id() {
  _has_bits_[0] |= 0x00000001u;
}
inline void cluster_agent_row::clear_has_id() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void cluster_agent_row::clear_id() {
  id_ = GOOGLE_ULONGLONG(0);
  clear_has_id();
}
inline ::google::protobuf::uint64 cluster_agent_row::id() const {
  // @@protoc_insertion_point(field_get:db_name_server.cluster_agent_row.id)
  return id_;
}
inline void cluster_agent_row::set_id(::google::protobuf::uint64 value) {
  set_has_id();
  id_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.cluster_agent_row.id)
}

// optional bytes ip = 2;
inline bool cluster_agent_row::has_ip() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void cluster_agent_row::set_has_ip() {
  _has_bits_[0] |= 0x00000002u;
}
inline void cluster_agent_row::clear_has_ip() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void cluster_agent_row::clear_ip() {
  if (ip_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_->clear();
  }
  clear_has_ip();
}
inline const ::std::string& cluster_agent_row::ip() const {
  // @@protoc_insertion_point(field_get:db_name_server.cluster_agent_row.ip)
  return *ip_;
}
inline void cluster_agent_row::set_ip(const ::std::string& value) {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  ip_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.cluster_agent_row.ip)
}
inline void cluster_agent_row::set_ip(const char* value) {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  ip_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.cluster_agent_row.ip)
}
inline void cluster_agent_row::set_ip(const void* value, size_t size) {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  ip_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.cluster_agent_row.ip)
}
inline ::std::string* cluster_agent_row::mutable_ip() {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.cluster_agent_row.ip)
  return ip_;
}
inline ::std::string* cluster_agent_row::release_ip() {
  clear_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = ip_;
    ip_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void cluster_agent_row::set_allocated_ip(::std::string* ip) {
  if (ip_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete ip_;
  }
  if (ip) {
    set_has_ip();
    ip_ = ip;
  } else {
    clear_has_ip();
    ip_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.cluster_agent_row.ip)
}

// optional uint32 port = 3;
inline bool cluster_agent_row::has_port() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void cluster_agent_row::set_has_port() {
  _has_bits_[0] |= 0x00000004u;
}
inline void cluster_agent_row::clear_has_port() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void cluster_agent_row::clear_port() {
  port_ = 0u;
  clear_has_port();
}
inline ::google::protobuf::uint32 cluster_agent_row::port() const {
  // @@protoc_insertion_point(field_get:db_name_server.cluster_agent_row.port)
  return port_;
}
inline void cluster_agent_row::set_port(::google::protobuf::uint32 value) {
  set_has_port();
  port_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.cluster_agent_row.port)
}

// optional uint64 utime = 4;
inline bool cluster_agent_row::has_utime() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void cluster_agent_row::set_has_utime() {
  _has_bits_[0] |= 0x00000008u;
}
inline void cluster_agent_row::clear_has_utime() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void cluster_agent_row::clear_utime() {
  utime_ = GOOGLE_ULONGLONG(0);
  clear_has_utime();
}
inline ::google::protobuf::uint64 cluster_agent_row::utime() const {
  // @@protoc_insertion_point(field_get:db_name_server.cluster_agent_row.utime)
  return utime_;
}
inline void cluster_agent_row::set_utime(::google::protobuf::uint64 value) {
  set_has_utime();
  utime_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.cluster_agent_row.utime)
}

// -------------------------------------------------------------------

// cluster_agent_rows

// repeated .db_name_server.cluster_agent_row rows = 1;
inline int cluster_agent_rows::rows_size() const {
  return rows_.size();
}
inline void cluster_agent_rows::clear_rows() {
  rows_.Clear();
}
inline const ::db_name_server::cluster_agent_row& cluster_agent_rows::rows(int index) const {
  // @@protoc_insertion_point(field_get:db_name_server.cluster_agent_rows.rows)
  return rows_.Get(index);
}
inline ::db_name_server::cluster_agent_row* cluster_agent_rows::mutable_rows(int index) {
  // @@protoc_insertion_point(field_mutable:db_name_server.cluster_agent_rows.rows)
  return rows_.Mutable(index);
}
inline ::db_name_server::cluster_agent_row* cluster_agent_rows::add_rows() {
  // @@protoc_insertion_point(field_add:db_name_server.cluster_agent_rows.rows)
  return rows_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::db_name_server::cluster_agent_row >&
cluster_agent_rows::rows() const {
  // @@protoc_insertion_point(field_list:db_name_server.cluster_agent_rows.rows)
  return rows_;
}
inline ::google::protobuf::RepeatedPtrField< ::db_name_server::cluster_agent_row >*
cluster_agent_rows::mutable_rows() {
  // @@protoc_insertion_point(field_mutable_list:db_name_server.cluster_agent_rows.rows)
  return &rows_;
}

// -------------------------------------------------------------------

// common_list_version

// optional uint64 id = 1;
inline bool common_list_version::has_id() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void common_list_version::set_has_id() {
  _has_bits_[0] |= 0x00000001u;
}
inline void common_list_version::clear_has_id() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void common_list_version::clear_id() {
  id_ = GOOGLE_ULONGLONG(0);
  clear_has_id();
}
inline ::google::protobuf::uint64 common_list_version::id() const {
  // @@protoc_insertion_point(field_get:db_name_server.common_list_version.id)
  return id_;
}
inline void common_list_version::set_id(::google::protobuf::uint64 value) {
  set_has_id();
  id_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.common_list_version.id)
}

// optional bytes me = 2;
inline bool common_list_version::has_me() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void common_list_version::set_has_me() {
  _has_bits_[0] |= 0x00000002u;
}
inline void common_list_version::clear_has_me() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void common_list_version::clear_me() {
  if (me_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    me_->clear();
  }
  clear_has_me();
}
inline const ::std::string& common_list_version::me() const {
  // @@protoc_insertion_point(field_get:db_name_server.common_list_version.me)
  return *me_;
}
inline void common_list_version::set_me(const ::std::string& value) {
  set_has_me();
  if (me_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    me_ = new ::std::string;
  }
  me_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.common_list_version.me)
}
inline void common_list_version::set_me(const char* value) {
  set_has_me();
  if (me_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    me_ = new ::std::string;
  }
  me_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.common_list_version.me)
}
inline void common_list_version::set_me(const void* value, size_t size) {
  set_has_me();
  if (me_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    me_ = new ::std::string;
  }
  me_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.common_list_version.me)
}
inline ::std::string* common_list_version::mutable_me() {
  set_has_me();
  if (me_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    me_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.common_list_version.me)
  return me_;
}
inline ::std::string* common_list_version::release_me() {
  clear_has_me();
  if (me_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = me_;
    me_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void common_list_version::set_allocated_me(::std::string* me) {
  if (me_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete me_;
  }
  if (me) {
    set_has_me();
    me_ = me;
  } else {
    clear_has_me();
    me_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.common_list_version.me)
}

// optional bytes me_packet = 3;
inline bool common_list_version::has_me_packet() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void common_list_version::set_has_me_packet() {
  _has_bits_[0] |= 0x00000004u;
}
inline void common_list_version::clear_has_me_packet() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void common_list_version::clear_me_packet() {
  if (me_packet_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    me_packet_->clear();
  }
  clear_has_me_packet();
}
inline const ::std::string& common_list_version::me_packet() const {
  // @@protoc_insertion_point(field_get:db_name_server.common_list_version.me_packet)
  return *me_packet_;
}
inline void common_list_version::set_me_packet(const ::std::string& value) {
  set_has_me_packet();
  if (me_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    me_packet_ = new ::std::string;
  }
  me_packet_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.common_list_version.me_packet)
}
inline void common_list_version::set_me_packet(const char* value) {
  set_has_me_packet();
  if (me_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    me_packet_ = new ::std::string;
  }
  me_packet_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.common_list_version.me_packet)
}
inline void common_list_version::set_me_packet(const void* value, size_t size) {
  set_has_me_packet();
  if (me_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    me_packet_ = new ::std::string;
  }
  me_packet_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.common_list_version.me_packet)
}
inline ::std::string* common_list_version::mutable_me_packet() {
  set_has_me_packet();
  if (me_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    me_packet_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.common_list_version.me_packet)
  return me_packet_;
}
inline ::std::string* common_list_version::release_me_packet() {
  clear_has_me_packet();
  if (me_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = me_packet_;
    me_packet_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void common_list_version::set_allocated_me_packet(::std::string* me_packet) {
  if (me_packet_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete me_packet_;
  }
  if (me_packet) {
    set_has_me_packet();
    me_packet_ = me_packet;
  } else {
    clear_has_me_packet();
    me_packet_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.common_list_version.me_packet)
}

// optional bytes table_name = 4;
inline bool common_list_version::has_table_name() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void common_list_version::set_has_table_name() {
  _has_bits_[0] |= 0x00000008u;
}
inline void common_list_version::clear_has_table_name() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void common_list_version::clear_table_name() {
  if (table_name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    table_name_->clear();
  }
  clear_has_table_name();
}
inline const ::std::string& common_list_version::table_name() const {
  // @@protoc_insertion_point(field_get:db_name_server.common_list_version.table_name)
  return *table_name_;
}
inline void common_list_version::set_table_name(const ::std::string& value) {
  set_has_table_name();
  if (table_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    table_name_ = new ::std::string;
  }
  table_name_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.common_list_version.table_name)
}
inline void common_list_version::set_table_name(const char* value) {
  set_has_table_name();
  if (table_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    table_name_ = new ::std::string;
  }
  table_name_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.common_list_version.table_name)
}
inline void common_list_version::set_table_name(const void* value, size_t size) {
  set_has_table_name();
  if (table_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    table_name_ = new ::std::string;
  }
  table_name_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.common_list_version.table_name)
}
inline ::std::string* common_list_version::mutable_table_name() {
  set_has_table_name();
  if (table_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    table_name_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.common_list_version.table_name)
  return table_name_;
}
inline ::std::string* common_list_version::release_table_name() {
  clear_has_table_name();
  if (table_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = table_name_;
    table_name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void common_list_version::set_allocated_table_name(::std::string* table_name) {
  if (table_name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete table_name_;
  }
  if (table_name) {
    set_has_table_name();
    table_name_ = table_name;
  } else {
    clear_has_table_name();
    table_name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.common_list_version.table_name)
}

// optional uint64 who_list_version = 5;
inline bool common_list_version::has_who_list_version() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void common_list_version::set_has_who_list_version() {
  _has_bits_[0] |= 0x00000010u;
}
inline void common_list_version::clear_has_who_list_version() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void common_list_version::clear_who_list_version() {
  who_list_version_ = GOOGLE_ULONGLONG(0);
  clear_has_who_list_version();
}
inline ::google::protobuf::uint64 common_list_version::who_list_version() const {
  // @@protoc_insertion_point(field_get:db_name_server.common_list_version.who_list_version)
  return who_list_version_;
}
inline void common_list_version::set_who_list_version(::google::protobuf::uint64 value) {
  set_has_who_list_version();
  who_list_version_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.common_list_version.who_list_version)
}

// optional uint64 utime = 6;
inline bool common_list_version::has_utime() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void common_list_version::set_has_utime() {
  _has_bits_[0] |= 0x00000020u;
}
inline void common_list_version::clear_has_utime() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void common_list_version::clear_utime() {
  utime_ = GOOGLE_ULONGLONG(0);
  clear_has_utime();
}
inline ::google::protobuf::uint64 common_list_version::utime() const {
  // @@protoc_insertion_point(field_get:db_name_server.common_list_version.utime)
  return utime_;
}
inline void common_list_version::set_utime(::google::protobuf::uint64 value) {
  set_has_utime();
  utime_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.common_list_version.utime)
}

// optional bytes fingerprint = 7;
inline bool common_list_version::has_fingerprint() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void common_list_version::set_has_fingerprint() {
  _has_bits_[0] |= 0x00000040u;
}
inline void common_list_version::clear_has_fingerprint() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void common_list_version::clear_fingerprint() {
  if (fingerprint_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    fingerprint_->clear();
  }
  clear_has_fingerprint();
}
inline const ::std::string& common_list_version::fingerprint() const {
  // @@protoc_insertion_point(field_get:db_name_server.common_list_version.fingerprint)
  return *fingerprint_;
}
inline void common_list_version::set_fingerprint(const ::std::string& value) {
  set_has_fingerprint();
  if (fingerprint_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    fingerprint_ = new ::std::string;
  }
  fingerprint_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.common_list_version.fingerprint)
}
inline void common_list_version::set_fingerprint(const char* value) {
  set_has_fingerprint();
  if (fingerprint_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    fingerprint_ = new ::std::string;
  }
  fingerprint_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.common_list_version.fingerprint)
}
inline void common_list_version::set_fingerprint(const void* value, size_t size) {
  set_has_fingerprint();
  if (fingerprint_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    fingerprint_ = new ::std::string;
  }
  fingerprint_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.common_list_version.fingerprint)
}
inline ::std::string* common_list_version::mutable_fingerprint() {
  set_has_fingerprint();
  if (fingerprint_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    fingerprint_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.common_list_version.fingerprint)
  return fingerprint_;
}
inline ::std::string* common_list_version::release_fingerprint() {
  clear_has_fingerprint();
  if (fingerprint_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = fingerprint_;
    fingerprint_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void common_list_version::set_allocated_fingerprint(::std::string* fingerprint) {
  if (fingerprint_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete fingerprint_;
  }
  if (fingerprint) {
    set_has_fingerprint();
    fingerprint_ = fingerprint;
  } else {
    clear_has_fingerprint();
    fingerprint_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.common_list_version.fingerprint)
}

// -------------------------------------------------------------------

// common_list_version_rows

// repeated .db_name_server.common_list_version rows = 1;
inline int common_list_version_rows::rows_size() const {
  return rows_.size();
}
inline void common_list_version_rows::clear_rows() {
  rows_.Clear();
}
inline const ::db_name_server::common_list_version& common_list_version_rows::rows(int index) const {
  // @@protoc_insertion_point(field_get:db_name_server.common_list_version_rows.rows)
  return rows_.Get(index);
}
inline ::db_name_server::common_list_version* common_list_version_rows::mutable_rows(int index) {
  // @@protoc_insertion_point(field_mutable:db_name_server.common_list_version_rows.rows)
  return rows_.Mutable(index);
}
inline ::db_name_server::common_list_version* common_list_version_rows::add_rows() {
  // @@protoc_insertion_point(field_add:db_name_server.common_list_version_rows.rows)
  return rows_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::db_name_server::common_list_version >&
common_list_version_rows::rows() const {
  // @@protoc_insertion_point(field_list:db_name_server.common_list_version_rows.rows)
  return rows_;
}
inline ::google::protobuf::RepeatedPtrField< ::db_name_server::common_list_version >*
common_list_version_rows::mutable_rows() {
  // @@protoc_insertion_point(field_mutable_list:db_name_server.common_list_version_rows.rows)
  return &rows_;
}

// -------------------------------------------------------------------

// x_depend_y

// optional uint64 id = 1;
inline bool x_depend_y::has_id() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void x_depend_y::set_has_id() {
  _has_bits_[0] |= 0x00000001u;
}
inline void x_depend_y::clear_has_id() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void x_depend_y::clear_id() {
  id_ = GOOGLE_ULONGLONG(0);
  clear_has_id();
}
inline ::google::protobuf::uint64 x_depend_y::id() const {
  // @@protoc_insertion_point(field_get:db_name_server.x_depend_y.id)
  return id_;
}
inline void x_depend_y::set_id(::google::protobuf::uint64 value) {
  set_has_id();
  id_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.x_depend_y.id)
}

// optional bytes me = 2;
inline bool x_depend_y::has_me() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void x_depend_y::set_has_me() {
  _has_bits_[0] |= 0x00000002u;
}
inline void x_depend_y::clear_has_me() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void x_depend_y::clear_me() {
  if (me_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    me_->clear();
  }
  clear_has_me();
}
inline const ::std::string& x_depend_y::me() const {
  // @@protoc_insertion_point(field_get:db_name_server.x_depend_y.me)
  return *me_;
}
inline void x_depend_y::set_me(const ::std::string& value) {
  set_has_me();
  if (me_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    me_ = new ::std::string;
  }
  me_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.x_depend_y.me)
}
inline void x_depend_y::set_me(const char* value) {
  set_has_me();
  if (me_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    me_ = new ::std::string;
  }
  me_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.x_depend_y.me)
}
inline void x_depend_y::set_me(const void* value, size_t size) {
  set_has_me();
  if (me_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    me_ = new ::std::string;
  }
  me_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.x_depend_y.me)
}
inline ::std::string* x_depend_y::mutable_me() {
  set_has_me();
  if (me_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    me_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.x_depend_y.me)
  return me_;
}
inline ::std::string* x_depend_y::release_me() {
  clear_has_me();
  if (me_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = me_;
    me_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void x_depend_y::set_allocated_me(::std::string* me) {
  if (me_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete me_;
  }
  if (me) {
    set_has_me();
    me_ = me;
  } else {
    clear_has_me();
    me_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.x_depend_y.me)
}

// optional bytes me_packet = 3;
inline bool x_depend_y::has_me_packet() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void x_depend_y::set_has_me_packet() {
  _has_bits_[0] |= 0x00000004u;
}
inline void x_depend_y::clear_has_me_packet() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void x_depend_y::clear_me_packet() {
  if (me_packet_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    me_packet_->clear();
  }
  clear_has_me_packet();
}
inline const ::std::string& x_depend_y::me_packet() const {
  // @@protoc_insertion_point(field_get:db_name_server.x_depend_y.me_packet)
  return *me_packet_;
}
inline void x_depend_y::set_me_packet(const ::std::string& value) {
  set_has_me_packet();
  if (me_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    me_packet_ = new ::std::string;
  }
  me_packet_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.x_depend_y.me_packet)
}
inline void x_depend_y::set_me_packet(const char* value) {
  set_has_me_packet();
  if (me_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    me_packet_ = new ::std::string;
  }
  me_packet_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.x_depend_y.me_packet)
}
inline void x_depend_y::set_me_packet(const void* value, size_t size) {
  set_has_me_packet();
  if (me_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    me_packet_ = new ::std::string;
  }
  me_packet_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.x_depend_y.me_packet)
}
inline ::std::string* x_depend_y::mutable_me_packet() {
  set_has_me_packet();
  if (me_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    me_packet_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.x_depend_y.me_packet)
  return me_packet_;
}
inline ::std::string* x_depend_y::release_me_packet() {
  clear_has_me_packet();
  if (me_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = me_packet_;
    me_packet_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void x_depend_y::set_allocated_me_packet(::std::string* me_packet) {
  if (me_packet_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete me_packet_;
  }
  if (me_packet) {
    set_has_me_packet();
    me_packet_ = me_packet;
  } else {
    clear_has_me_packet();
    me_packet_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.x_depend_y.me_packet)
}

// optional bytes who = 4;
inline bool x_depend_y::has_who() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void x_depend_y::set_has_who() {
  _has_bits_[0] |= 0x00000008u;
}
inline void x_depend_y::clear_has_who() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void x_depend_y::clear_who() {
  if (who_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    who_->clear();
  }
  clear_has_who();
}
inline const ::std::string& x_depend_y::who() const {
  // @@protoc_insertion_point(field_get:db_name_server.x_depend_y.who)
  return *who_;
}
inline void x_depend_y::set_who(const ::std::string& value) {
  set_has_who();
  if (who_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    who_ = new ::std::string;
  }
  who_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.x_depend_y.who)
}
inline void x_depend_y::set_who(const char* value) {
  set_has_who();
  if (who_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    who_ = new ::std::string;
  }
  who_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.x_depend_y.who)
}
inline void x_depend_y::set_who(const void* value, size_t size) {
  set_has_who();
  if (who_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    who_ = new ::std::string;
  }
  who_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.x_depend_y.who)
}
inline ::std::string* x_depend_y::mutable_who() {
  set_has_who();
  if (who_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    who_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.x_depend_y.who)
  return who_;
}
inline ::std::string* x_depend_y::release_who() {
  clear_has_who();
  if (who_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = who_;
    who_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void x_depend_y::set_allocated_who(::std::string* who) {
  if (who_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete who_;
  }
  if (who) {
    set_has_who();
    who_ = who;
  } else {
    clear_has_who();
    who_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.x_depend_y.who)
}

// optional bytes who_packet = 5;
inline bool x_depend_y::has_who_packet() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void x_depend_y::set_has_who_packet() {
  _has_bits_[0] |= 0x00000010u;
}
inline void x_depend_y::clear_has_who_packet() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void x_depend_y::clear_who_packet() {
  if (who_packet_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    who_packet_->clear();
  }
  clear_has_who_packet();
}
inline const ::std::string& x_depend_y::who_packet() const {
  // @@protoc_insertion_point(field_get:db_name_server.x_depend_y.who_packet)
  return *who_packet_;
}
inline void x_depend_y::set_who_packet(const ::std::string& value) {
  set_has_who_packet();
  if (who_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    who_packet_ = new ::std::string;
  }
  who_packet_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.x_depend_y.who_packet)
}
inline void x_depend_y::set_who_packet(const char* value) {
  set_has_who_packet();
  if (who_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    who_packet_ = new ::std::string;
  }
  who_packet_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.x_depend_y.who_packet)
}
inline void x_depend_y::set_who_packet(const void* value, size_t size) {
  set_has_who_packet();
  if (who_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    who_packet_ = new ::std::string;
  }
  who_packet_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.x_depend_y.who_packet)
}
inline ::std::string* x_depend_y::mutable_who_packet() {
  set_has_who_packet();
  if (who_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    who_packet_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.x_depend_y.who_packet)
  return who_packet_;
}
inline ::std::string* x_depend_y::release_who_packet() {
  clear_has_who_packet();
  if (who_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = who_packet_;
    who_packet_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void x_depend_y::set_allocated_who_packet(::std::string* who_packet) {
  if (who_packet_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete who_packet_;
  }
  if (who_packet) {
    set_has_who_packet();
    who_packet_ = who_packet;
  } else {
    clear_has_who_packet();
    who_packet_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.x_depend_y.who_packet)
}

// optional uint64 who_list_version = 6;
inline bool x_depend_y::has_who_list_version() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void x_depend_y::set_has_who_list_version() {
  _has_bits_[0] |= 0x00000020u;
}
inline void x_depend_y::clear_has_who_list_version() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void x_depend_y::clear_who_list_version() {
  who_list_version_ = GOOGLE_ULONGLONG(0);
  clear_has_who_list_version();
}
inline ::google::protobuf::uint64 x_depend_y::who_list_version() const {
  // @@protoc_insertion_point(field_get:db_name_server.x_depend_y.who_list_version)
  return who_list_version_;
}
inline void x_depend_y::set_who_list_version(::google::protobuf::uint64 value) {
  set_has_who_list_version();
  who_list_version_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.x_depend_y.who_list_version)
}

// optional uint64 utime = 7;
inline bool x_depend_y::has_utime() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void x_depend_y::set_has_utime() {
  _has_bits_[0] |= 0x00000040u;
}
inline void x_depend_y::clear_has_utime() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void x_depend_y::clear_utime() {
  utime_ = GOOGLE_ULONGLONG(0);
  clear_has_utime();
}
inline ::google::protobuf::uint64 x_depend_y::utime() const {
  // @@protoc_insertion_point(field_get:db_name_server.x_depend_y.utime)
  return utime_;
}
inline void x_depend_y::set_utime(::google::protobuf::uint64 value) {
  set_has_utime();
  utime_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.x_depend_y.utime)
}

// optional bytes fingerprint = 8;
inline bool x_depend_y::has_fingerprint() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void x_depend_y::set_has_fingerprint() {
  _has_bits_[0] |= 0x00000080u;
}
inline void x_depend_y::clear_has_fingerprint() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void x_depend_y::clear_fingerprint() {
  if (fingerprint_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    fingerprint_->clear();
  }
  clear_has_fingerprint();
}
inline const ::std::string& x_depend_y::fingerprint() const {
  // @@protoc_insertion_point(field_get:db_name_server.x_depend_y.fingerprint)
  return *fingerprint_;
}
inline void x_depend_y::set_fingerprint(const ::std::string& value) {
  set_has_fingerprint();
  if (fingerprint_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    fingerprint_ = new ::std::string;
  }
  fingerprint_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.x_depend_y.fingerprint)
}
inline void x_depend_y::set_fingerprint(const char* value) {
  set_has_fingerprint();
  if (fingerprint_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    fingerprint_ = new ::std::string;
  }
  fingerprint_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.x_depend_y.fingerprint)
}
inline void x_depend_y::set_fingerprint(const void* value, size_t size) {
  set_has_fingerprint();
  if (fingerprint_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    fingerprint_ = new ::std::string;
  }
  fingerprint_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.x_depend_y.fingerprint)
}
inline ::std::string* x_depend_y::mutable_fingerprint() {
  set_has_fingerprint();
  if (fingerprint_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    fingerprint_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.x_depend_y.fingerprint)
  return fingerprint_;
}
inline ::std::string* x_depend_y::release_fingerprint() {
  clear_has_fingerprint();
  if (fingerprint_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = fingerprint_;
    fingerprint_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void x_depend_y::set_allocated_fingerprint(::std::string* fingerprint) {
  if (fingerprint_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete fingerprint_;
  }
  if (fingerprint) {
    set_has_fingerprint();
    fingerprint_ = fingerprint;
  } else {
    clear_has_fingerprint();
    fingerprint_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.x_depend_y.fingerprint)
}

// -------------------------------------------------------------------

// x_depend_y_rows

// repeated .db_name_server.x_depend_y rows = 1;
inline int x_depend_y_rows::rows_size() const {
  return rows_.size();
}
inline void x_depend_y_rows::clear_rows() {
  rows_.Clear();
}
inline const ::db_name_server::x_depend_y& x_depend_y_rows::rows(int index) const {
  // @@protoc_insertion_point(field_get:db_name_server.x_depend_y_rows.rows)
  return rows_.Get(index);
}
inline ::db_name_server::x_depend_y* x_depend_y_rows::mutable_rows(int index) {
  // @@protoc_insertion_point(field_mutable:db_name_server.x_depend_y_rows.rows)
  return rows_.Mutable(index);
}
inline ::db_name_server::x_depend_y* x_depend_y_rows::add_rows() {
  // @@protoc_insertion_point(field_add:db_name_server.x_depend_y_rows.rows)
  return rows_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::db_name_server::x_depend_y >&
x_depend_y_rows::rows() const {
  // @@protoc_insertion_point(field_list:db_name_server.x_depend_y_rows.rows)
  return rows_;
}
inline ::google::protobuf::RepeatedPtrField< ::db_name_server::x_depend_y >*
x_depend_y_rows::mutable_rows() {
  // @@protoc_insertion_point(field_mutable_list:db_name_server.x_depend_y_rows.rows)
  return &rows_;
}

// -------------------------------------------------------------------

// server_name_table_directory

// optional uint64 id = 1;
inline bool server_name_table_directory::has_id() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void server_name_table_directory::set_has_id() {
  _has_bits_[0] |= 0x00000001u;
}
inline void server_name_table_directory::clear_has_id() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void server_name_table_directory::clear_id() {
  id_ = GOOGLE_ULONGLONG(0);
  clear_has_id();
}
inline ::google::protobuf::uint64 server_name_table_directory::id() const {
  // @@protoc_insertion_point(field_get:db_name_server.server_name_table_directory.id)
  return id_;
}
inline void server_name_table_directory::set_id(::google::protobuf::uint64 value) {
  set_has_id();
  id_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.server_name_table_directory.id)
}

// optional bytes server_name = 2;
inline bool server_name_table_directory::has_server_name() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void server_name_table_directory::set_has_server_name() {
  _has_bits_[0] |= 0x00000002u;
}
inline void server_name_table_directory::clear_has_server_name() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void server_name_table_directory::clear_server_name() {
  if (server_name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_->clear();
  }
  clear_has_server_name();
}
inline const ::std::string& server_name_table_directory::server_name() const {
  // @@protoc_insertion_point(field_get:db_name_server.server_name_table_directory.server_name)
  return *server_name_;
}
inline void server_name_table_directory::set_server_name(const ::std::string& value) {
  set_has_server_name();
  if (server_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_ = new ::std::string;
  }
  server_name_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.server_name_table_directory.server_name)
}
inline void server_name_table_directory::set_server_name(const char* value) {
  set_has_server_name();
  if (server_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_ = new ::std::string;
  }
  server_name_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.server_name_table_directory.server_name)
}
inline void server_name_table_directory::set_server_name(const void* value, size_t size) {
  set_has_server_name();
  if (server_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_ = new ::std::string;
  }
  server_name_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.server_name_table_directory.server_name)
}
inline ::std::string* server_name_table_directory::mutable_server_name() {
  set_has_server_name();
  if (server_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.server_name_table_directory.server_name)
  return server_name_;
}
inline ::std::string* server_name_table_directory::release_server_name() {
  clear_has_server_name();
  if (server_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = server_name_;
    server_name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void server_name_table_directory::set_allocated_server_name(::std::string* server_name) {
  if (server_name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete server_name_;
  }
  if (server_name) {
    set_has_server_name();
    server_name_ = server_name;
  } else {
    clear_has_server_name();
    server_name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.server_name_table_directory.server_name)
}

// optional bytes server_name_packet = 3;
inline bool server_name_table_directory::has_server_name_packet() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void server_name_table_directory::set_has_server_name_packet() {
  _has_bits_[0] |= 0x00000004u;
}
inline void server_name_table_directory::clear_has_server_name_packet() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void server_name_table_directory::clear_server_name_packet() {
  if (server_name_packet_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_packet_->clear();
  }
  clear_has_server_name_packet();
}
inline const ::std::string& server_name_table_directory::server_name_packet() const {
  // @@protoc_insertion_point(field_get:db_name_server.server_name_table_directory.server_name_packet)
  return *server_name_packet_;
}
inline void server_name_table_directory::set_server_name_packet(const ::std::string& value) {
  set_has_server_name_packet();
  if (server_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_packet_ = new ::std::string;
  }
  server_name_packet_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.server_name_table_directory.server_name_packet)
}
inline void server_name_table_directory::set_server_name_packet(const char* value) {
  set_has_server_name_packet();
  if (server_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_packet_ = new ::std::string;
  }
  server_name_packet_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.server_name_table_directory.server_name_packet)
}
inline void server_name_table_directory::set_server_name_packet(const void* value, size_t size) {
  set_has_server_name_packet();
  if (server_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_packet_ = new ::std::string;
  }
  server_name_packet_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.server_name_table_directory.server_name_packet)
}
inline ::std::string* server_name_table_directory::mutable_server_name_packet() {
  set_has_server_name_packet();
  if (server_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_packet_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.server_name_table_directory.server_name_packet)
  return server_name_packet_;
}
inline ::std::string* server_name_table_directory::release_server_name_packet() {
  clear_has_server_name_packet();
  if (server_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = server_name_packet_;
    server_name_packet_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void server_name_table_directory::set_allocated_server_name_packet(::std::string* server_name_packet) {
  if (server_name_packet_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete server_name_packet_;
  }
  if (server_name_packet) {
    set_has_server_name_packet();
    server_name_packet_ = server_name_packet;
  } else {
    clear_has_server_name_packet();
    server_name_packet_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.server_name_table_directory.server_name_packet)
}

// optional bytes table_name = 4;
inline bool server_name_table_directory::has_table_name() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void server_name_table_directory::set_has_table_name() {
  _has_bits_[0] |= 0x00000008u;
}
inline void server_name_table_directory::clear_has_table_name() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void server_name_table_directory::clear_table_name() {
  if (table_name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    table_name_->clear();
  }
  clear_has_table_name();
}
inline const ::std::string& server_name_table_directory::table_name() const {
  // @@protoc_insertion_point(field_get:db_name_server.server_name_table_directory.table_name)
  return *table_name_;
}
inline void server_name_table_directory::set_table_name(const ::std::string& value) {
  set_has_table_name();
  if (table_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    table_name_ = new ::std::string;
  }
  table_name_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.server_name_table_directory.table_name)
}
inline void server_name_table_directory::set_table_name(const char* value) {
  set_has_table_name();
  if (table_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    table_name_ = new ::std::string;
  }
  table_name_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.server_name_table_directory.table_name)
}
inline void server_name_table_directory::set_table_name(const void* value, size_t size) {
  set_has_table_name();
  if (table_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    table_name_ = new ::std::string;
  }
  table_name_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.server_name_table_directory.table_name)
}
inline ::std::string* server_name_table_directory::mutable_table_name() {
  set_has_table_name();
  if (table_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    table_name_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.server_name_table_directory.table_name)
  return table_name_;
}
inline ::std::string* server_name_table_directory::release_table_name() {
  clear_has_table_name();
  if (table_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = table_name_;
    table_name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void server_name_table_directory::set_allocated_table_name(::std::string* table_name) {
  if (table_name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete table_name_;
  }
  if (table_name) {
    set_has_table_name();
    table_name_ = table_name;
  } else {
    clear_has_table_name();
    table_name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.server_name_table_directory.table_name)
}

// optional uint64 utime = 5;
inline bool server_name_table_directory::has_utime() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void server_name_table_directory::set_has_utime() {
  _has_bits_[0] |= 0x00000010u;
}
inline void server_name_table_directory::clear_has_utime() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void server_name_table_directory::clear_utime() {
  utime_ = GOOGLE_ULONGLONG(0);
  clear_has_utime();
}
inline ::google::protobuf::uint64 server_name_table_directory::utime() const {
  // @@protoc_insertion_point(field_get:db_name_server.server_name_table_directory.utime)
  return utime_;
}
inline void server_name_table_directory::set_utime(::google::protobuf::uint64 value) {
  set_has_utime();
  utime_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.server_name_table_directory.utime)
}

// -------------------------------------------------------------------

// cluster_server_name_rows

// repeated .db_name_server.server_name_table_directory rows = 1;
inline int cluster_server_name_rows::rows_size() const {
  return rows_.size();
}
inline void cluster_server_name_rows::clear_rows() {
  rows_.Clear();
}
inline const ::db_name_server::server_name_table_directory& cluster_server_name_rows::rows(int index) const {
  // @@protoc_insertion_point(field_get:db_name_server.cluster_server_name_rows.rows)
  return rows_.Get(index);
}
inline ::db_name_server::server_name_table_directory* cluster_server_name_rows::mutable_rows(int index) {
  // @@protoc_insertion_point(field_mutable:db_name_server.cluster_server_name_rows.rows)
  return rows_.Mutable(index);
}
inline ::db_name_server::server_name_table_directory* cluster_server_name_rows::add_rows() {
  // @@protoc_insertion_point(field_add:db_name_server.cluster_server_name_rows.rows)
  return rows_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::db_name_server::server_name_table_directory >&
cluster_server_name_rows::rows() const {
  // @@protoc_insertion_point(field_list:db_name_server.cluster_server_name_rows.rows)
  return rows_;
}
inline ::google::protobuf::RepeatedPtrField< ::db_name_server::server_name_table_directory >*
cluster_server_name_rows::mutable_rows() {
  // @@protoc_insertion_point(field_mutable_list:db_name_server.cluster_server_name_rows.rows)
  return &rows_;
}

// -------------------------------------------------------------------

// server_name_instance_list_version

// optional uint64 id = 1;
inline bool server_name_instance_list_version::has_id() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void server_name_instance_list_version::set_has_id() {
  _has_bits_[0] |= 0x00000001u;
}
inline void server_name_instance_list_version::clear_has_id() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void server_name_instance_list_version::clear_id() {
  id_ = GOOGLE_ULONGLONG(0);
  clear_has_id();
}
inline ::google::protobuf::uint64 server_name_instance_list_version::id() const {
  // @@protoc_insertion_point(field_get:db_name_server.server_name_instance_list_version.id)
  return id_;
}
inline void server_name_instance_list_version::set_id(::google::protobuf::uint64 value) {
  set_has_id();
  id_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.server_name_instance_list_version.id)
}

// optional bytes server_name = 2;
inline bool server_name_instance_list_version::has_server_name() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void server_name_instance_list_version::set_has_server_name() {
  _has_bits_[0] |= 0x00000002u;
}
inline void server_name_instance_list_version::clear_has_server_name() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void server_name_instance_list_version::clear_server_name() {
  if (server_name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_->clear();
  }
  clear_has_server_name();
}
inline const ::std::string& server_name_instance_list_version::server_name() const {
  // @@protoc_insertion_point(field_get:db_name_server.server_name_instance_list_version.server_name)
  return *server_name_;
}
inline void server_name_instance_list_version::set_server_name(const ::std::string& value) {
  set_has_server_name();
  if (server_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_ = new ::std::string;
  }
  server_name_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.server_name_instance_list_version.server_name)
}
inline void server_name_instance_list_version::set_server_name(const char* value) {
  set_has_server_name();
  if (server_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_ = new ::std::string;
  }
  server_name_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.server_name_instance_list_version.server_name)
}
inline void server_name_instance_list_version::set_server_name(const void* value, size_t size) {
  set_has_server_name();
  if (server_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_ = new ::std::string;
  }
  server_name_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.server_name_instance_list_version.server_name)
}
inline ::std::string* server_name_instance_list_version::mutable_server_name() {
  set_has_server_name();
  if (server_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.server_name_instance_list_version.server_name)
  return server_name_;
}
inline ::std::string* server_name_instance_list_version::release_server_name() {
  clear_has_server_name();
  if (server_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = server_name_;
    server_name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void server_name_instance_list_version::set_allocated_server_name(::std::string* server_name) {
  if (server_name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete server_name_;
  }
  if (server_name) {
    set_has_server_name();
    server_name_ = server_name;
  } else {
    clear_has_server_name();
    server_name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.server_name_instance_list_version.server_name)
}

// optional bytes server_name_packet = 3;
inline bool server_name_instance_list_version::has_server_name_packet() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void server_name_instance_list_version::set_has_server_name_packet() {
  _has_bits_[0] |= 0x00000004u;
}
inline void server_name_instance_list_version::clear_has_server_name_packet() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void server_name_instance_list_version::clear_server_name_packet() {
  if (server_name_packet_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_packet_->clear();
  }
  clear_has_server_name_packet();
}
inline const ::std::string& server_name_instance_list_version::server_name_packet() const {
  // @@protoc_insertion_point(field_get:db_name_server.server_name_instance_list_version.server_name_packet)
  return *server_name_packet_;
}
inline void server_name_instance_list_version::set_server_name_packet(const ::std::string& value) {
  set_has_server_name_packet();
  if (server_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_packet_ = new ::std::string;
  }
  server_name_packet_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.server_name_instance_list_version.server_name_packet)
}
inline void server_name_instance_list_version::set_server_name_packet(const char* value) {
  set_has_server_name_packet();
  if (server_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_packet_ = new ::std::string;
  }
  server_name_packet_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.server_name_instance_list_version.server_name_packet)
}
inline void server_name_instance_list_version::set_server_name_packet(const void* value, size_t size) {
  set_has_server_name_packet();
  if (server_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_packet_ = new ::std::string;
  }
  server_name_packet_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.server_name_instance_list_version.server_name_packet)
}
inline ::std::string* server_name_instance_list_version::mutable_server_name_packet() {
  set_has_server_name_packet();
  if (server_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_packet_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.server_name_instance_list_version.server_name_packet)
  return server_name_packet_;
}
inline ::std::string* server_name_instance_list_version::release_server_name_packet() {
  clear_has_server_name_packet();
  if (server_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = server_name_packet_;
    server_name_packet_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void server_name_instance_list_version::set_allocated_server_name_packet(::std::string* server_name_packet) {
  if (server_name_packet_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete server_name_packet_;
  }
  if (server_name_packet) {
    set_has_server_name_packet();
    server_name_packet_ = server_name_packet;
  } else {
    clear_has_server_name_packet();
    server_name_packet_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.server_name_instance_list_version.server_name_packet)
}

// optional bytes table_name = 4;
inline bool server_name_instance_list_version::has_table_name() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void server_name_instance_list_version::set_has_table_name() {
  _has_bits_[0] |= 0x00000008u;
}
inline void server_name_instance_list_version::clear_has_table_name() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void server_name_instance_list_version::clear_table_name() {
  if (table_name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    table_name_->clear();
  }
  clear_has_table_name();
}
inline const ::std::string& server_name_instance_list_version::table_name() const {
  // @@protoc_insertion_point(field_get:db_name_server.server_name_instance_list_version.table_name)
  return *table_name_;
}
inline void server_name_instance_list_version::set_table_name(const ::std::string& value) {
  set_has_table_name();
  if (table_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    table_name_ = new ::std::string;
  }
  table_name_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.server_name_instance_list_version.table_name)
}
inline void server_name_instance_list_version::set_table_name(const char* value) {
  set_has_table_name();
  if (table_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    table_name_ = new ::std::string;
  }
  table_name_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.server_name_instance_list_version.table_name)
}
inline void server_name_instance_list_version::set_table_name(const void* value, size_t size) {
  set_has_table_name();
  if (table_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    table_name_ = new ::std::string;
  }
  table_name_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.server_name_instance_list_version.table_name)
}
inline ::std::string* server_name_instance_list_version::mutable_table_name() {
  set_has_table_name();
  if (table_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    table_name_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.server_name_instance_list_version.table_name)
  return table_name_;
}
inline ::std::string* server_name_instance_list_version::release_table_name() {
  clear_has_table_name();
  if (table_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = table_name_;
    table_name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void server_name_instance_list_version::set_allocated_table_name(::std::string* table_name) {
  if (table_name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete table_name_;
  }
  if (table_name) {
    set_has_table_name();
    table_name_ = table_name;
  } else {
    clear_has_table_name();
    table_name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.server_name_instance_list_version.table_name)
}

// optional uint64 instance_list_version = 5;
inline bool server_name_instance_list_version::has_instance_list_version() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void server_name_instance_list_version::set_has_instance_list_version() {
  _has_bits_[0] |= 0x00000010u;
}
inline void server_name_instance_list_version::clear_has_instance_list_version() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void server_name_instance_list_version::clear_instance_list_version() {
  instance_list_version_ = GOOGLE_ULONGLONG(0);
  clear_has_instance_list_version();
}
inline ::google::protobuf::uint64 server_name_instance_list_version::instance_list_version() const {
  // @@protoc_insertion_point(field_get:db_name_server.server_name_instance_list_version.instance_list_version)
  return instance_list_version_;
}
inline void server_name_instance_list_version::set_instance_list_version(::google::protobuf::uint64 value) {
  set_has_instance_list_version();
  instance_list_version_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.server_name_instance_list_version.instance_list_version)
}

// optional uint64 utime = 6;
inline bool server_name_instance_list_version::has_utime() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void server_name_instance_list_version::set_has_utime() {
  _has_bits_[0] |= 0x00000020u;
}
inline void server_name_instance_list_version::clear_has_utime() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void server_name_instance_list_version::clear_utime() {
  utime_ = GOOGLE_ULONGLONG(0);
  clear_has_utime();
}
inline ::google::protobuf::uint64 server_name_instance_list_version::utime() const {
  // @@protoc_insertion_point(field_get:db_name_server.server_name_instance_list_version.utime)
  return utime_;
}
inline void server_name_instance_list_version::set_utime(::google::protobuf::uint64 value) {
  set_has_utime();
  utime_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.server_name_instance_list_version.utime)
}

// optional bytes fingerprint = 7;
inline bool server_name_instance_list_version::has_fingerprint() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void server_name_instance_list_version::set_has_fingerprint() {
  _has_bits_[0] |= 0x00000040u;
}
inline void server_name_instance_list_version::clear_has_fingerprint() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void server_name_instance_list_version::clear_fingerprint() {
  if (fingerprint_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    fingerprint_->clear();
  }
  clear_has_fingerprint();
}
inline const ::std::string& server_name_instance_list_version::fingerprint() const {
  // @@protoc_insertion_point(field_get:db_name_server.server_name_instance_list_version.fingerprint)
  return *fingerprint_;
}
inline void server_name_instance_list_version::set_fingerprint(const ::std::string& value) {
  set_has_fingerprint();
  if (fingerprint_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    fingerprint_ = new ::std::string;
  }
  fingerprint_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.server_name_instance_list_version.fingerprint)
}
inline void server_name_instance_list_version::set_fingerprint(const char* value) {
  set_has_fingerprint();
  if (fingerprint_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    fingerprint_ = new ::std::string;
  }
  fingerprint_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.server_name_instance_list_version.fingerprint)
}
inline void server_name_instance_list_version::set_fingerprint(const void* value, size_t size) {
  set_has_fingerprint();
  if (fingerprint_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    fingerprint_ = new ::std::string;
  }
  fingerprint_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.server_name_instance_list_version.fingerprint)
}
inline ::std::string* server_name_instance_list_version::mutable_fingerprint() {
  set_has_fingerprint();
  if (fingerprint_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    fingerprint_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.server_name_instance_list_version.fingerprint)
  return fingerprint_;
}
inline ::std::string* server_name_instance_list_version::release_fingerprint() {
  clear_has_fingerprint();
  if (fingerprint_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = fingerprint_;
    fingerprint_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void server_name_instance_list_version::set_allocated_fingerprint(::std::string* fingerprint) {
  if (fingerprint_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete fingerprint_;
  }
  if (fingerprint) {
    set_has_fingerprint();
    fingerprint_ = fingerprint;
  } else {
    clear_has_fingerprint();
    fingerprint_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.server_name_instance_list_version.fingerprint)
}

// -------------------------------------------------------------------

// server_name_instance_row

// optional uint64 id = 1;
inline bool server_name_instance_row::has_id() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void server_name_instance_row::set_has_id() {
  _has_bits_[0] |= 0x00000001u;
}
inline void server_name_instance_row::clear_has_id() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void server_name_instance_row::clear_id() {
  id_ = GOOGLE_ULONGLONG(0);
  clear_has_id();
}
inline ::google::protobuf::uint64 server_name_instance_row::id() const {
  // @@protoc_insertion_point(field_get:db_name_server.server_name_instance_row.id)
  return id_;
}
inline void server_name_instance_row::set_id(::google::protobuf::uint64 value) {
  set_has_id();
  id_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.server_name_instance_row.id)
}

// optional bytes server_name = 2;
inline bool server_name_instance_row::has_server_name() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void server_name_instance_row::set_has_server_name() {
  _has_bits_[0] |= 0x00000002u;
}
inline void server_name_instance_row::clear_has_server_name() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void server_name_instance_row::clear_server_name() {
  if (server_name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_->clear();
  }
  clear_has_server_name();
}
inline const ::std::string& server_name_instance_row::server_name() const {
  // @@protoc_insertion_point(field_get:db_name_server.server_name_instance_row.server_name)
  return *server_name_;
}
inline void server_name_instance_row::set_server_name(const ::std::string& value) {
  set_has_server_name();
  if (server_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_ = new ::std::string;
  }
  server_name_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.server_name_instance_row.server_name)
}
inline void server_name_instance_row::set_server_name(const char* value) {
  set_has_server_name();
  if (server_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_ = new ::std::string;
  }
  server_name_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.server_name_instance_row.server_name)
}
inline void server_name_instance_row::set_server_name(const void* value, size_t size) {
  set_has_server_name();
  if (server_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_ = new ::std::string;
  }
  server_name_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.server_name_instance_row.server_name)
}
inline ::std::string* server_name_instance_row::mutable_server_name() {
  set_has_server_name();
  if (server_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.server_name_instance_row.server_name)
  return server_name_;
}
inline ::std::string* server_name_instance_row::release_server_name() {
  clear_has_server_name();
  if (server_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = server_name_;
    server_name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void server_name_instance_row::set_allocated_server_name(::std::string* server_name) {
  if (server_name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete server_name_;
  }
  if (server_name) {
    set_has_server_name();
    server_name_ = server_name;
  } else {
    clear_has_server_name();
    server_name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.server_name_instance_row.server_name)
}

// optional bytes server_name_packet = 3;
inline bool server_name_instance_row::has_server_name_packet() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void server_name_instance_row::set_has_server_name_packet() {
  _has_bits_[0] |= 0x00000004u;
}
inline void server_name_instance_row::clear_has_server_name_packet() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void server_name_instance_row::clear_server_name_packet() {
  if (server_name_packet_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_packet_->clear();
  }
  clear_has_server_name_packet();
}
inline const ::std::string& server_name_instance_row::server_name_packet() const {
  // @@protoc_insertion_point(field_get:db_name_server.server_name_instance_row.server_name_packet)
  return *server_name_packet_;
}
inline void server_name_instance_row::set_server_name_packet(const ::std::string& value) {
  set_has_server_name_packet();
  if (server_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_packet_ = new ::std::string;
  }
  server_name_packet_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.server_name_instance_row.server_name_packet)
}
inline void server_name_instance_row::set_server_name_packet(const char* value) {
  set_has_server_name_packet();
  if (server_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_packet_ = new ::std::string;
  }
  server_name_packet_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.server_name_instance_row.server_name_packet)
}
inline void server_name_instance_row::set_server_name_packet(const void* value, size_t size) {
  set_has_server_name_packet();
  if (server_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_packet_ = new ::std::string;
  }
  server_name_packet_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.server_name_instance_row.server_name_packet)
}
inline ::std::string* server_name_instance_row::mutable_server_name_packet() {
  set_has_server_name_packet();
  if (server_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_packet_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.server_name_instance_row.server_name_packet)
  return server_name_packet_;
}
inline ::std::string* server_name_instance_row::release_server_name_packet() {
  clear_has_server_name_packet();
  if (server_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = server_name_packet_;
    server_name_packet_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void server_name_instance_row::set_allocated_server_name_packet(::std::string* server_name_packet) {
  if (server_name_packet_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete server_name_packet_;
  }
  if (server_name_packet) {
    set_has_server_name_packet();
    server_name_packet_ = server_name_packet;
  } else {
    clear_has_server_name_packet();
    server_name_packet_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.server_name_instance_row.server_name_packet)
}

// optional bytes instance_name = 4;
inline bool server_name_instance_row::has_instance_name() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void server_name_instance_row::set_has_instance_name() {
  _has_bits_[0] |= 0x00000008u;
}
inline void server_name_instance_row::clear_has_instance_name() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void server_name_instance_row::clear_instance_name() {
  if (instance_name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    instance_name_->clear();
  }
  clear_has_instance_name();
}
inline const ::std::string& server_name_instance_row::instance_name() const {
  // @@protoc_insertion_point(field_get:db_name_server.server_name_instance_row.instance_name)
  return *instance_name_;
}
inline void server_name_instance_row::set_instance_name(const ::std::string& value) {
  set_has_instance_name();
  if (instance_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    instance_name_ = new ::std::string;
  }
  instance_name_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.server_name_instance_row.instance_name)
}
inline void server_name_instance_row::set_instance_name(const char* value) {
  set_has_instance_name();
  if (instance_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    instance_name_ = new ::std::string;
  }
  instance_name_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.server_name_instance_row.instance_name)
}
inline void server_name_instance_row::set_instance_name(const void* value, size_t size) {
  set_has_instance_name();
  if (instance_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    instance_name_ = new ::std::string;
  }
  instance_name_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.server_name_instance_row.instance_name)
}
inline ::std::string* server_name_instance_row::mutable_instance_name() {
  set_has_instance_name();
  if (instance_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    instance_name_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.server_name_instance_row.instance_name)
  return instance_name_;
}
inline ::std::string* server_name_instance_row::release_instance_name() {
  clear_has_instance_name();
  if (instance_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = instance_name_;
    instance_name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void server_name_instance_row::set_allocated_instance_name(::std::string* instance_name) {
  if (instance_name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete instance_name_;
  }
  if (instance_name) {
    set_has_instance_name();
    instance_name_ = instance_name;
  } else {
    clear_has_instance_name();
    instance_name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.server_name_instance_row.instance_name)
}

// optional bytes instance_name_packet = 5;
inline bool server_name_instance_row::has_instance_name_packet() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void server_name_instance_row::set_has_instance_name_packet() {
  _has_bits_[0] |= 0x00000010u;
}
inline void server_name_instance_row::clear_has_instance_name_packet() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void server_name_instance_row::clear_instance_name_packet() {
  if (instance_name_packet_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    instance_name_packet_->clear();
  }
  clear_has_instance_name_packet();
}
inline const ::std::string& server_name_instance_row::instance_name_packet() const {
  // @@protoc_insertion_point(field_get:db_name_server.server_name_instance_row.instance_name_packet)
  return *instance_name_packet_;
}
inline void server_name_instance_row::set_instance_name_packet(const ::std::string& value) {
  set_has_instance_name_packet();
  if (instance_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    instance_name_packet_ = new ::std::string;
  }
  instance_name_packet_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.server_name_instance_row.instance_name_packet)
}
inline void server_name_instance_row::set_instance_name_packet(const char* value) {
  set_has_instance_name_packet();
  if (instance_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    instance_name_packet_ = new ::std::string;
  }
  instance_name_packet_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.server_name_instance_row.instance_name_packet)
}
inline void server_name_instance_row::set_instance_name_packet(const void* value, size_t size) {
  set_has_instance_name_packet();
  if (instance_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    instance_name_packet_ = new ::std::string;
  }
  instance_name_packet_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.server_name_instance_row.instance_name_packet)
}
inline ::std::string* server_name_instance_row::mutable_instance_name_packet() {
  set_has_instance_name_packet();
  if (instance_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    instance_name_packet_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.server_name_instance_row.instance_name_packet)
  return instance_name_packet_;
}
inline ::std::string* server_name_instance_row::release_instance_name_packet() {
  clear_has_instance_name_packet();
  if (instance_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = instance_name_packet_;
    instance_name_packet_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void server_name_instance_row::set_allocated_instance_name_packet(::std::string* instance_name_packet) {
  if (instance_name_packet_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete instance_name_packet_;
  }
  if (instance_name_packet) {
    set_has_instance_name_packet();
    instance_name_packet_ = instance_name_packet;
  } else {
    clear_has_instance_name_packet();
    instance_name_packet_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.server_name_instance_row.instance_name_packet)
}

// optional bytes ip = 6;
inline bool server_name_instance_row::has_ip() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void server_name_instance_row::set_has_ip() {
  _has_bits_[0] |= 0x00000020u;
}
inline void server_name_instance_row::clear_has_ip() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void server_name_instance_row::clear_ip() {
  if (ip_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_->clear();
  }
  clear_has_ip();
}
inline const ::std::string& server_name_instance_row::ip() const {
  // @@protoc_insertion_point(field_get:db_name_server.server_name_instance_row.ip)
  return *ip_;
}
inline void server_name_instance_row::set_ip(const ::std::string& value) {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  ip_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.server_name_instance_row.ip)
}
inline void server_name_instance_row::set_ip(const char* value) {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  ip_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.server_name_instance_row.ip)
}
inline void server_name_instance_row::set_ip(const void* value, size_t size) {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  ip_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.server_name_instance_row.ip)
}
inline ::std::string* server_name_instance_row::mutable_ip() {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.server_name_instance_row.ip)
  return ip_;
}
inline ::std::string* server_name_instance_row::release_ip() {
  clear_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = ip_;
    ip_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void server_name_instance_row::set_allocated_ip(::std::string* ip) {
  if (ip_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete ip_;
  }
  if (ip) {
    set_has_ip();
    ip_ = ip;
  } else {
    clear_has_ip();
    ip_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.server_name_instance_row.ip)
}

// optional uint32 port = 7;
inline bool server_name_instance_row::has_port() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void server_name_instance_row::set_has_port() {
  _has_bits_[0] |= 0x00000040u;
}
inline void server_name_instance_row::clear_has_port() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void server_name_instance_row::clear_port() {
  port_ = 0u;
  clear_has_port();
}
inline ::google::protobuf::uint32 server_name_instance_row::port() const {
  // @@protoc_insertion_point(field_get:db_name_server.server_name_instance_row.port)
  return port_;
}
inline void server_name_instance_row::set_port(::google::protobuf::uint32 value) {
  set_has_port();
  port_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.server_name_instance_row.port)
}

// optional uint64 instance_list_version = 8;
inline bool server_name_instance_row::has_instance_list_version() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void server_name_instance_row::set_has_instance_list_version() {
  _has_bits_[0] |= 0x00000080u;
}
inline void server_name_instance_row::clear_has_instance_list_version() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void server_name_instance_row::clear_instance_list_version() {
  instance_list_version_ = GOOGLE_ULONGLONG(0);
  clear_has_instance_list_version();
}
inline ::google::protobuf::uint64 server_name_instance_row::instance_list_version() const {
  // @@protoc_insertion_point(field_get:db_name_server.server_name_instance_row.instance_list_version)
  return instance_list_version_;
}
inline void server_name_instance_row::set_instance_list_version(::google::protobuf::uint64 value) {
  set_has_instance_list_version();
  instance_list_version_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.server_name_instance_row.instance_list_version)
}

// optional uint64 end_instance_list_version = 9;
inline bool server_name_instance_row::has_end_instance_list_version() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void server_name_instance_row::set_has_end_instance_list_version() {
  _has_bits_[0] |= 0x00000100u;
}
inline void server_name_instance_row::clear_has_end_instance_list_version() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void server_name_instance_row::clear_end_instance_list_version() {
  end_instance_list_version_ = GOOGLE_ULONGLONG(0);
  clear_has_end_instance_list_version();
}
inline ::google::protobuf::uint64 server_name_instance_row::end_instance_list_version() const {
  // @@protoc_insertion_point(field_get:db_name_server.server_name_instance_row.end_instance_list_version)
  return end_instance_list_version_;
}
inline void server_name_instance_row::set_end_instance_list_version(::google::protobuf::uint64 value) {
  set_has_end_instance_list_version();
  end_instance_list_version_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.server_name_instance_row.end_instance_list_version)
}

// optional uint64 utime = 10;
inline bool server_name_instance_row::has_utime() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
inline void server_name_instance_row::set_has_utime() {
  _has_bits_[0] |= 0x00000200u;
}
inline void server_name_instance_row::clear_has_utime() {
  _has_bits_[0] &= ~0x00000200u;
}
inline void server_name_instance_row::clear_utime() {
  utime_ = GOOGLE_ULONGLONG(0);
  clear_has_utime();
}
inline ::google::protobuf::uint64 server_name_instance_row::utime() const {
  // @@protoc_insertion_point(field_get:db_name_server.server_name_instance_row.utime)
  return utime_;
}
inline void server_name_instance_row::set_utime(::google::protobuf::uint64 value) {
  set_has_utime();
  utime_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.server_name_instance_row.utime)
}

// optional bytes fingerprint = 11;
inline bool server_name_instance_row::has_fingerprint() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
inline void server_name_instance_row::set_has_fingerprint() {
  _has_bits_[0] |= 0x00000400u;
}
inline void server_name_instance_row::clear_has_fingerprint() {
  _has_bits_[0] &= ~0x00000400u;
}
inline void server_name_instance_row::clear_fingerprint() {
  if (fingerprint_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    fingerprint_->clear();
  }
  clear_has_fingerprint();
}
inline const ::std::string& server_name_instance_row::fingerprint() const {
  // @@protoc_insertion_point(field_get:db_name_server.server_name_instance_row.fingerprint)
  return *fingerprint_;
}
inline void server_name_instance_row::set_fingerprint(const ::std::string& value) {
  set_has_fingerprint();
  if (fingerprint_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    fingerprint_ = new ::std::string;
  }
  fingerprint_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.server_name_instance_row.fingerprint)
}
inline void server_name_instance_row::set_fingerprint(const char* value) {
  set_has_fingerprint();
  if (fingerprint_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    fingerprint_ = new ::std::string;
  }
  fingerprint_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.server_name_instance_row.fingerprint)
}
inline void server_name_instance_row::set_fingerprint(const void* value, size_t size) {
  set_has_fingerprint();
  if (fingerprint_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    fingerprint_ = new ::std::string;
  }
  fingerprint_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.server_name_instance_row.fingerprint)
}
inline ::std::string* server_name_instance_row::mutable_fingerprint() {
  set_has_fingerprint();
  if (fingerprint_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    fingerprint_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.server_name_instance_row.fingerprint)
  return fingerprint_;
}
inline ::std::string* server_name_instance_row::release_fingerprint() {
  clear_has_fingerprint();
  if (fingerprint_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = fingerprint_;
    fingerprint_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void server_name_instance_row::set_allocated_fingerprint(::std::string* fingerprint) {
  if (fingerprint_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete fingerprint_;
  }
  if (fingerprint) {
    set_has_fingerprint();
    fingerprint_ = fingerprint;
  } else {
    clear_has_fingerprint();
    fingerprint_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.server_name_instance_row.fingerprint)
}

// -------------------------------------------------------------------

// server_name_instance_rows

// repeated .db_name_server.server_name_instance_row rows = 1;
inline int server_name_instance_rows::rows_size() const {
  return rows_.size();
}
inline void server_name_instance_rows::clear_rows() {
  rows_.Clear();
}
inline const ::db_name_server::server_name_instance_row& server_name_instance_rows::rows(int index) const {
  // @@protoc_insertion_point(field_get:db_name_server.server_name_instance_rows.rows)
  return rows_.Get(index);
}
inline ::db_name_server::server_name_instance_row* server_name_instance_rows::mutable_rows(int index) {
  // @@protoc_insertion_point(field_mutable:db_name_server.server_name_instance_rows.rows)
  return rows_.Mutable(index);
}
inline ::db_name_server::server_name_instance_row* server_name_instance_rows::add_rows() {
  // @@protoc_insertion_point(field_add:db_name_server.server_name_instance_rows.rows)
  return rows_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::db_name_server::server_name_instance_row >&
server_name_instance_rows::rows() const {
  // @@protoc_insertion_point(field_list:db_name_server.server_name_instance_rows.rows)
  return rows_;
}
inline ::google::protobuf::RepeatedPtrField< ::db_name_server::server_name_instance_row >*
server_name_instance_rows::mutable_rows() {
  // @@protoc_insertion_point(field_mutable_list:db_name_server.server_name_instance_rows.rows)
  return &rows_;
}

// -------------------------------------------------------------------

// ip_field2

// optional uint64 id = 1;
inline bool ip_field2::has_id() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ip_field2::set_has_id() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ip_field2::clear_has_id() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ip_field2::clear_id() {
  id_ = GOOGLE_ULONGLONG(0);
  clear_has_id();
}
inline ::google::protobuf::uint64 ip_field2::id() const {
  // @@protoc_insertion_point(field_get:db_name_server.ip_field2.id)
  return id_;
}
inline void ip_field2::set_id(::google::protobuf::uint64 value) {
  set_has_id();
  id_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.ip_field2.id)
}

// optional bytes ip = 2;
inline bool ip_field2::has_ip() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ip_field2::set_has_ip() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ip_field2::clear_has_ip() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ip_field2::clear_ip() {
  if (ip_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_->clear();
  }
  clear_has_ip();
}
inline const ::std::string& ip_field2::ip() const {
  // @@protoc_insertion_point(field_get:db_name_server.ip_field2.ip)
  return *ip_;
}
inline void ip_field2::set_ip(const ::std::string& value) {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  ip_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.ip_field2.ip)
}
inline void ip_field2::set_ip(const char* value) {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  ip_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.ip_field2.ip)
}
inline void ip_field2::set_ip(const void* value, size_t size) {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  ip_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.ip_field2.ip)
}
inline ::std::string* ip_field2::mutable_ip() {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.ip_field2.ip)
  return ip_;
}
inline ::std::string* ip_field2::release_ip() {
  clear_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = ip_;
    ip_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void ip_field2::set_allocated_ip(::std::string* ip) {
  if (ip_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete ip_;
  }
  if (ip) {
    set_has_ip();
    ip_ = ip;
  } else {
    clear_has_ip();
    ip_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.ip_field2.ip)
}

// optional uint32 port = 3;
inline bool ip_field2::has_port() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ip_field2::set_has_port() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ip_field2::clear_has_port() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ip_field2::clear_port() {
  port_ = 0u;
  clear_has_port();
}
inline ::google::protobuf::uint32 ip_field2::port() const {
  // @@protoc_insertion_point(field_get:db_name_server.ip_field2.port)
  return port_;
}
inline void ip_field2::set_port(::google::protobuf::uint32 value) {
  set_has_port();
  port_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.ip_field2.port)
}

// optional bytes table_name = 4;
inline bool ip_field2::has_table_name() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void ip_field2::set_has_table_name() {
  _has_bits_[0] |= 0x00000008u;
}
inline void ip_field2::clear_has_table_name() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void ip_field2::clear_table_name() {
  if (table_name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    table_name_->clear();
  }
  clear_has_table_name();
}
inline const ::std::string& ip_field2::table_name() const {
  // @@protoc_insertion_point(field_get:db_name_server.ip_field2.table_name)
  return *table_name_;
}
inline void ip_field2::set_table_name(const ::std::string& value) {
  set_has_table_name();
  if (table_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    table_name_ = new ::std::string;
  }
  table_name_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.ip_field2.table_name)
}
inline void ip_field2::set_table_name(const char* value) {
  set_has_table_name();
  if (table_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    table_name_ = new ::std::string;
  }
  table_name_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.ip_field2.table_name)
}
inline void ip_field2::set_table_name(const void* value, size_t size) {
  set_has_table_name();
  if (table_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    table_name_ = new ::std::string;
  }
  table_name_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.ip_field2.table_name)
}
inline ::std::string* ip_field2::mutable_table_name() {
  set_has_table_name();
  if (table_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    table_name_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.ip_field2.table_name)
  return table_name_;
}
inline ::std::string* ip_field2::release_table_name() {
  clear_has_table_name();
  if (table_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = table_name_;
    table_name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void ip_field2::set_allocated_table_name(::std::string* table_name) {
  if (table_name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete table_name_;
  }
  if (table_name) {
    set_has_table_name();
    table_name_ = table_name;
  } else {
    clear_has_table_name();
    table_name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.ip_field2.table_name)
}

// optional bytes server_name = 5;
inline bool ip_field2::has_server_name() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void ip_field2::set_has_server_name() {
  _has_bits_[0] |= 0x00000010u;
}
inline void ip_field2::clear_has_server_name() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void ip_field2::clear_server_name() {
  if (server_name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_->clear();
  }
  clear_has_server_name();
}
inline const ::std::string& ip_field2::server_name() const {
  // @@protoc_insertion_point(field_get:db_name_server.ip_field2.server_name)
  return *server_name_;
}
inline void ip_field2::set_server_name(const ::std::string& value) {
  set_has_server_name();
  if (server_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_ = new ::std::string;
  }
  server_name_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.ip_field2.server_name)
}
inline void ip_field2::set_server_name(const char* value) {
  set_has_server_name();
  if (server_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_ = new ::std::string;
  }
  server_name_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.ip_field2.server_name)
}
inline void ip_field2::set_server_name(const void* value, size_t size) {
  set_has_server_name();
  if (server_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_ = new ::std::string;
  }
  server_name_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.ip_field2.server_name)
}
inline ::std::string* ip_field2::mutable_server_name() {
  set_has_server_name();
  if (server_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.ip_field2.server_name)
  return server_name_;
}
inline ::std::string* ip_field2::release_server_name() {
  clear_has_server_name();
  if (server_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = server_name_;
    server_name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void ip_field2::set_allocated_server_name(::std::string* server_name) {
  if (server_name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete server_name_;
  }
  if (server_name) {
    set_has_server_name();
    server_name_ = server_name;
  } else {
    clear_has_server_name();
    server_name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.ip_field2.server_name)
}

// optional bytes server_name_packet = 6;
inline bool ip_field2::has_server_name_packet() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void ip_field2::set_has_server_name_packet() {
  _has_bits_[0] |= 0x00000020u;
}
inline void ip_field2::clear_has_server_name_packet() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void ip_field2::clear_server_name_packet() {
  if (server_name_packet_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_packet_->clear();
  }
  clear_has_server_name_packet();
}
inline const ::std::string& ip_field2::server_name_packet() const {
  // @@protoc_insertion_point(field_get:db_name_server.ip_field2.server_name_packet)
  return *server_name_packet_;
}
inline void ip_field2::set_server_name_packet(const ::std::string& value) {
  set_has_server_name_packet();
  if (server_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_packet_ = new ::std::string;
  }
  server_name_packet_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.ip_field2.server_name_packet)
}
inline void ip_field2::set_server_name_packet(const char* value) {
  set_has_server_name_packet();
  if (server_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_packet_ = new ::std::string;
  }
  server_name_packet_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.ip_field2.server_name_packet)
}
inline void ip_field2::set_server_name_packet(const void* value, size_t size) {
  set_has_server_name_packet();
  if (server_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_packet_ = new ::std::string;
  }
  server_name_packet_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.ip_field2.server_name_packet)
}
inline ::std::string* ip_field2::mutable_server_name_packet() {
  set_has_server_name_packet();
  if (server_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_packet_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.ip_field2.server_name_packet)
  return server_name_packet_;
}
inline ::std::string* ip_field2::release_server_name_packet() {
  clear_has_server_name_packet();
  if (server_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = server_name_packet_;
    server_name_packet_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void ip_field2::set_allocated_server_name_packet(::std::string* server_name_packet) {
  if (server_name_packet_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete server_name_packet_;
  }
  if (server_name_packet) {
    set_has_server_name_packet();
    server_name_packet_ = server_name_packet;
  } else {
    clear_has_server_name_packet();
    server_name_packet_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.ip_field2.server_name_packet)
}

// optional bytes instance_name = 7;
inline bool ip_field2::has_instance_name() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void ip_field2::set_has_instance_name() {
  _has_bits_[0] |= 0x00000040u;
}
inline void ip_field2::clear_has_instance_name() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void ip_field2::clear_instance_name() {
  if (instance_name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    instance_name_->clear();
  }
  clear_has_instance_name();
}
inline const ::std::string& ip_field2::instance_name() const {
  // @@protoc_insertion_point(field_get:db_name_server.ip_field2.instance_name)
  return *instance_name_;
}
inline void ip_field2::set_instance_name(const ::std::string& value) {
  set_has_instance_name();
  if (instance_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    instance_name_ = new ::std::string;
  }
  instance_name_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.ip_field2.instance_name)
}
inline void ip_field2::set_instance_name(const char* value) {
  set_has_instance_name();
  if (instance_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    instance_name_ = new ::std::string;
  }
  instance_name_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.ip_field2.instance_name)
}
inline void ip_field2::set_instance_name(const void* value, size_t size) {
  set_has_instance_name();
  if (instance_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    instance_name_ = new ::std::string;
  }
  instance_name_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.ip_field2.instance_name)
}
inline ::std::string* ip_field2::mutable_instance_name() {
  set_has_instance_name();
  if (instance_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    instance_name_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.ip_field2.instance_name)
  return instance_name_;
}
inline ::std::string* ip_field2::release_instance_name() {
  clear_has_instance_name();
  if (instance_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = instance_name_;
    instance_name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void ip_field2::set_allocated_instance_name(::std::string* instance_name) {
  if (instance_name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete instance_name_;
  }
  if (instance_name) {
    set_has_instance_name();
    instance_name_ = instance_name;
  } else {
    clear_has_instance_name();
    instance_name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.ip_field2.instance_name)
}

// optional bytes instance_name_packet = 8;
inline bool ip_field2::has_instance_name_packet() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void ip_field2::set_has_instance_name_packet() {
  _has_bits_[0] |= 0x00000080u;
}
inline void ip_field2::clear_has_instance_name_packet() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void ip_field2::clear_instance_name_packet() {
  if (instance_name_packet_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    instance_name_packet_->clear();
  }
  clear_has_instance_name_packet();
}
inline const ::std::string& ip_field2::instance_name_packet() const {
  // @@protoc_insertion_point(field_get:db_name_server.ip_field2.instance_name_packet)
  return *instance_name_packet_;
}
inline void ip_field2::set_instance_name_packet(const ::std::string& value) {
  set_has_instance_name_packet();
  if (instance_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    instance_name_packet_ = new ::std::string;
  }
  instance_name_packet_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.ip_field2.instance_name_packet)
}
inline void ip_field2::set_instance_name_packet(const char* value) {
  set_has_instance_name_packet();
  if (instance_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    instance_name_packet_ = new ::std::string;
  }
  instance_name_packet_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.ip_field2.instance_name_packet)
}
inline void ip_field2::set_instance_name_packet(const void* value, size_t size) {
  set_has_instance_name_packet();
  if (instance_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    instance_name_packet_ = new ::std::string;
  }
  instance_name_packet_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.ip_field2.instance_name_packet)
}
inline ::std::string* ip_field2::mutable_instance_name_packet() {
  set_has_instance_name_packet();
  if (instance_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    instance_name_packet_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.ip_field2.instance_name_packet)
  return instance_name_packet_;
}
inline ::std::string* ip_field2::release_instance_name_packet() {
  clear_has_instance_name_packet();
  if (instance_name_packet_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = instance_name_packet_;
    instance_name_packet_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void ip_field2::set_allocated_instance_name_packet(::std::string* instance_name_packet) {
  if (instance_name_packet_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete instance_name_packet_;
  }
  if (instance_name_packet) {
    set_has_instance_name_packet();
    instance_name_packet_ = instance_name_packet;
  } else {
    clear_has_instance_name_packet();
    instance_name_packet_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.ip_field2.instance_name_packet)
}

// optional uint64 utime = 9;
inline bool ip_field2::has_utime() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void ip_field2::set_has_utime() {
  _has_bits_[0] |= 0x00000100u;
}
inline void ip_field2::clear_has_utime() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void ip_field2::clear_utime() {
  utime_ = GOOGLE_ULONGLONG(0);
  clear_has_utime();
}
inline ::google::protobuf::uint64 ip_field2::utime() const {
  // @@protoc_insertion_point(field_get:db_name_server.ip_field2.utime)
  return utime_;
}
inline void ip_field2::set_utime(::google::protobuf::uint64 value) {
  set_has_utime();
  utime_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.ip_field2.utime)
}

// optional bytes fingerprint = 10;
inline bool ip_field2::has_fingerprint() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
inline void ip_field2::set_has_fingerprint() {
  _has_bits_[0] |= 0x00000200u;
}
inline void ip_field2::clear_has_fingerprint() {
  _has_bits_[0] &= ~0x00000200u;
}
inline void ip_field2::clear_fingerprint() {
  if (fingerprint_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    fingerprint_->clear();
  }
  clear_has_fingerprint();
}
inline const ::std::string& ip_field2::fingerprint() const {
  // @@protoc_insertion_point(field_get:db_name_server.ip_field2.fingerprint)
  return *fingerprint_;
}
inline void ip_field2::set_fingerprint(const ::std::string& value) {
  set_has_fingerprint();
  if (fingerprint_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    fingerprint_ = new ::std::string;
  }
  fingerprint_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.ip_field2.fingerprint)
}
inline void ip_field2::set_fingerprint(const char* value) {
  set_has_fingerprint();
  if (fingerprint_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    fingerprint_ = new ::std::string;
  }
  fingerprint_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.ip_field2.fingerprint)
}
inline void ip_field2::set_fingerprint(const void* value, size_t size) {
  set_has_fingerprint();
  if (fingerprint_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    fingerprint_ = new ::std::string;
  }
  fingerprint_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.ip_field2.fingerprint)
}
inline ::std::string* ip_field2::mutable_fingerprint() {
  set_has_fingerprint();
  if (fingerprint_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    fingerprint_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.ip_field2.fingerprint)
  return fingerprint_;
}
inline ::std::string* ip_field2::release_fingerprint() {
  clear_has_fingerprint();
  if (fingerprint_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = fingerprint_;
    fingerprint_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void ip_field2::set_allocated_fingerprint(::std::string* fingerprint) {
  if (fingerprint_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete fingerprint_;
  }
  if (fingerprint) {
    set_has_fingerprint();
    fingerprint_ = fingerprint;
  } else {
    clear_has_fingerprint();
    fingerprint_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.ip_field2.fingerprint)
}

// -------------------------------------------------------------------

// ip_field2_rows

// repeated .db_name_server.ip_field2 rows = 1;
inline int ip_field2_rows::rows_size() const {
  return rows_.size();
}
inline void ip_field2_rows::clear_rows() {
  rows_.Clear();
}
inline const ::db_name_server::ip_field2& ip_field2_rows::rows(int index) const {
  // @@protoc_insertion_point(field_get:db_name_server.ip_field2_rows.rows)
  return rows_.Get(index);
}
inline ::db_name_server::ip_field2* ip_field2_rows::mutable_rows(int index) {
  // @@protoc_insertion_point(field_mutable:db_name_server.ip_field2_rows.rows)
  return rows_.Mutable(index);
}
inline ::db_name_server::ip_field2* ip_field2_rows::add_rows() {
  // @@protoc_insertion_point(field_add:db_name_server.ip_field2_rows.rows)
  return rows_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::db_name_server::ip_field2 >&
ip_field2_rows::rows() const {
  // @@protoc_insertion_point(field_list:db_name_server.ip_field2_rows.rows)
  return rows_;
}
inline ::google::protobuf::RepeatedPtrField< ::db_name_server::ip_field2 >*
ip_field2_rows::mutable_rows() {
  // @@protoc_insertion_point(field_mutable_list:db_name_server.ip_field2_rows.rows)
  return &rows_;
}

// -------------------------------------------------------------------

// agent_route_ip_field2

// optional uint64 id = 1;
inline bool agent_route_ip_field2::has_id() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void agent_route_ip_field2::set_has_id() {
  _has_bits_[0] |= 0x00000001u;
}
inline void agent_route_ip_field2::clear_has_id() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void agent_route_ip_field2::clear_id() {
  id_ = GOOGLE_ULONGLONG(0);
  clear_has_id();
}
inline ::google::protobuf::uint64 agent_route_ip_field2::id() const {
  // @@protoc_insertion_point(field_get:db_name_server.agent_route_ip_field2.id)
  return id_;
}
inline void agent_route_ip_field2::set_id(::google::protobuf::uint64 value) {
  set_has_id();
  id_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.agent_route_ip_field2.id)
}

// optional bytes ip = 2;
inline bool agent_route_ip_field2::has_ip() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void agent_route_ip_field2::set_has_ip() {
  _has_bits_[0] |= 0x00000002u;
}
inline void agent_route_ip_field2::clear_has_ip() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void agent_route_ip_field2::clear_ip() {
  if (ip_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_->clear();
  }
  clear_has_ip();
}
inline const ::std::string& agent_route_ip_field2::ip() const {
  // @@protoc_insertion_point(field_get:db_name_server.agent_route_ip_field2.ip)
  return *ip_;
}
inline void agent_route_ip_field2::set_ip(const ::std::string& value) {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  ip_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.agent_route_ip_field2.ip)
}
inline void agent_route_ip_field2::set_ip(const char* value) {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  ip_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.agent_route_ip_field2.ip)
}
inline void agent_route_ip_field2::set_ip(const void* value, size_t size) {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  ip_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.agent_route_ip_field2.ip)
}
inline ::std::string* agent_route_ip_field2::mutable_ip() {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.agent_route_ip_field2.ip)
  return ip_;
}
inline ::std::string* agent_route_ip_field2::release_ip() {
  clear_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = ip_;
    ip_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void agent_route_ip_field2::set_allocated_ip(::std::string* ip) {
  if (ip_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete ip_;
  }
  if (ip) {
    set_has_ip();
    ip_ = ip;
  } else {
    clear_has_ip();
    ip_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.agent_route_ip_field2.ip)
}

// optional uint32 port = 3;
inline bool agent_route_ip_field2::has_port() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void agent_route_ip_field2::set_has_port() {
  _has_bits_[0] |= 0x00000004u;
}
inline void agent_route_ip_field2::clear_has_port() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void agent_route_ip_field2::clear_port() {
  port_ = 0u;
  clear_has_port();
}
inline ::google::protobuf::uint32 agent_route_ip_field2::port() const {
  // @@protoc_insertion_point(field_get:db_name_server.agent_route_ip_field2.port)
  return port_;
}
inline void agent_route_ip_field2::set_port(::google::protobuf::uint32 value) {
  set_has_port();
  port_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.agent_route_ip_field2.port)
}

// optional bytes server_name = 4;
inline bool agent_route_ip_field2::has_server_name() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void agent_route_ip_field2::set_has_server_name() {
  _has_bits_[0] |= 0x00000008u;
}
inline void agent_route_ip_field2::clear_has_server_name() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void agent_route_ip_field2::clear_server_name() {
  if (server_name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_->clear();
  }
  clear_has_server_name();
}
inline const ::std::string& agent_route_ip_field2::server_name() const {
  // @@protoc_insertion_point(field_get:db_name_server.agent_route_ip_field2.server_name)
  return *server_name_;
}
inline void agent_route_ip_field2::set_server_name(const ::std::string& value) {
  set_has_server_name();
  if (server_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_ = new ::std::string;
  }
  server_name_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.agent_route_ip_field2.server_name)
}
inline void agent_route_ip_field2::set_server_name(const char* value) {
  set_has_server_name();
  if (server_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_ = new ::std::string;
  }
  server_name_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.agent_route_ip_field2.server_name)
}
inline void agent_route_ip_field2::set_server_name(const void* value, size_t size) {
  set_has_server_name();
  if (server_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_ = new ::std::string;
  }
  server_name_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.agent_route_ip_field2.server_name)
}
inline ::std::string* agent_route_ip_field2::mutable_server_name() {
  set_has_server_name();
  if (server_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    server_name_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.agent_route_ip_field2.server_name)
  return server_name_;
}
inline ::std::string* agent_route_ip_field2::release_server_name() {
  clear_has_server_name();
  if (server_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = server_name_;
    server_name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void agent_route_ip_field2::set_allocated_server_name(::std::string* server_name) {
  if (server_name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete server_name_;
  }
  if (server_name) {
    set_has_server_name();
    server_name_ = server_name;
  } else {
    clear_has_server_name();
    server_name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.agent_route_ip_field2.server_name)
}

// optional bytes instance_list = 5;
inline bool agent_route_ip_field2::has_instance_list() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void agent_route_ip_field2::set_has_instance_list() {
  _has_bits_[0] |= 0x00000010u;
}
inline void agent_route_ip_field2::clear_has_instance_list() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void agent_route_ip_field2::clear_instance_list() {
  if (instance_list_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    instance_list_->clear();
  }
  clear_has_instance_list();
}
inline const ::std::string& agent_route_ip_field2::instance_list() const {
  // @@protoc_insertion_point(field_get:db_name_server.agent_route_ip_field2.instance_list)
  return *instance_list_;
}
inline void agent_route_ip_field2::set_instance_list(const ::std::string& value) {
  set_has_instance_list();
  if (instance_list_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    instance_list_ = new ::std::string;
  }
  instance_list_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.agent_route_ip_field2.instance_list)
}
inline void agent_route_ip_field2::set_instance_list(const char* value) {
  set_has_instance_list();
  if (instance_list_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    instance_list_ = new ::std::string;
  }
  instance_list_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.agent_route_ip_field2.instance_list)
}
inline void agent_route_ip_field2::set_instance_list(const void* value, size_t size) {
  set_has_instance_list();
  if (instance_list_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    instance_list_ = new ::std::string;
  }
  instance_list_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.agent_route_ip_field2.instance_list)
}
inline ::std::string* agent_route_ip_field2::mutable_instance_list() {
  set_has_instance_list();
  if (instance_list_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    instance_list_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.agent_route_ip_field2.instance_list)
  return instance_list_;
}
inline ::std::string* agent_route_ip_field2::release_instance_list() {
  clear_has_instance_list();
  if (instance_list_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = instance_list_;
    instance_list_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void agent_route_ip_field2::set_allocated_instance_list(::std::string* instance_list) {
  if (instance_list_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete instance_list_;
  }
  if (instance_list) {
    set_has_instance_list();
    instance_list_ = instance_list;
  } else {
    clear_has_instance_list();
    instance_list_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.agent_route_ip_field2.instance_list)
}

// optional uint64 utime = 6;
inline bool agent_route_ip_field2::has_utime() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void agent_route_ip_field2::set_has_utime() {
  _has_bits_[0] |= 0x00000020u;
}
inline void agent_route_ip_field2::clear_has_utime() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void agent_route_ip_field2::clear_utime() {
  utime_ = GOOGLE_ULONGLONG(0);
  clear_has_utime();
}
inline ::google::protobuf::uint64 agent_route_ip_field2::utime() const {
  // @@protoc_insertion_point(field_get:db_name_server.agent_route_ip_field2.utime)
  return utime_;
}
inline void agent_route_ip_field2::set_utime(::google::protobuf::uint64 value) {
  set_has_utime();
  utime_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.agent_route_ip_field2.utime)
}

// -------------------------------------------------------------------

// agent_route_ip_field2_rows

// repeated .db_name_server.agent_route_ip_field2 rows = 1;
inline int agent_route_ip_field2_rows::rows_size() const {
  return rows_.size();
}
inline void agent_route_ip_field2_rows::clear_rows() {
  rows_.Clear();
}
inline const ::db_name_server::agent_route_ip_field2& agent_route_ip_field2_rows::rows(int index) const {
  // @@protoc_insertion_point(field_get:db_name_server.agent_route_ip_field2_rows.rows)
  return rows_.Get(index);
}
inline ::db_name_server::agent_route_ip_field2* agent_route_ip_field2_rows::mutable_rows(int index) {
  // @@protoc_insertion_point(field_mutable:db_name_server.agent_route_ip_field2_rows.rows)
  return rows_.Mutable(index);
}
inline ::db_name_server::agent_route_ip_field2* agent_route_ip_field2_rows::add_rows() {
  // @@protoc_insertion_point(field_add:db_name_server.agent_route_ip_field2_rows.rows)
  return rows_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::db_name_server::agent_route_ip_field2 >&
agent_route_ip_field2_rows::rows() const {
  // @@protoc_insertion_point(field_list:db_name_server.agent_route_ip_field2_rows.rows)
  return rows_;
}
inline ::google::protobuf::RepeatedPtrField< ::db_name_server::agent_route_ip_field2 >*
agent_route_ip_field2_rows::mutable_rows() {
  // @@protoc_insertion_point(field_mutable_list:db_name_server.agent_route_ip_field2_rows.rows)
  return &rows_;
}

// -------------------------------------------------------------------

// resource_status_row

// optional uint64 id = 1;
inline bool resource_status_row::has_id() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void resource_status_row::set_has_id() {
  _has_bits_[0] |= 0x00000001u;
}
inline void resource_status_row::clear_has_id() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void resource_status_row::clear_id() {
  id_ = GOOGLE_ULONGLONG(0);
  clear_has_id();
}
inline ::google::protobuf::uint64 resource_status_row::id() const {
  // @@protoc_insertion_point(field_get:db_name_server.resource_status_row.id)
  return id_;
}
inline void resource_status_row::set_id(::google::protobuf::uint64 value) {
  set_has_id();
  id_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.resource_status_row.id)
}

// optional bytes ip = 2;
inline bool resource_status_row::has_ip() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void resource_status_row::set_has_ip() {
  _has_bits_[0] |= 0x00000002u;
}
inline void resource_status_row::clear_has_ip() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void resource_status_row::clear_ip() {
  if (ip_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_->clear();
  }
  clear_has_ip();
}
inline const ::std::string& resource_status_row::ip() const {
  // @@protoc_insertion_point(field_get:db_name_server.resource_status_row.ip)
  return *ip_;
}
inline void resource_status_row::set_ip(const ::std::string& value) {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  ip_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.resource_status_row.ip)
}
inline void resource_status_row::set_ip(const char* value) {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  ip_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.resource_status_row.ip)
}
inline void resource_status_row::set_ip(const void* value, size_t size) {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  ip_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.resource_status_row.ip)
}
inline ::std::string* resource_status_row::mutable_ip() {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.resource_status_row.ip)
  return ip_;
}
inline ::std::string* resource_status_row::release_ip() {
  clear_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = ip_;
    ip_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void resource_status_row::set_allocated_ip(::std::string* ip) {
  if (ip_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete ip_;
  }
  if (ip) {
    set_has_ip();
    ip_ = ip;
  } else {
    clear_has_ip();
    ip_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.resource_status_row.ip)
}

// optional uint32 port = 3;
inline bool resource_status_row::has_port() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void resource_status_row::set_has_port() {
  _has_bits_[0] |= 0x00000004u;
}
inline void resource_status_row::clear_has_port() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void resource_status_row::clear_port() {
  port_ = 0u;
  clear_has_port();
}
inline ::google::protobuf::uint32 resource_status_row::port() const {
  // @@protoc_insertion_point(field_get:db_name_server.resource_status_row.port)
  return port_;
}
inline void resource_status_row::set_port(::google::protobuf::uint32 value) {
  set_has_port();
  port_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.resource_status_row.port)
}

// optional bytes resource = 4;
inline bool resource_status_row::has_resource() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void resource_status_row::set_has_resource() {
  _has_bits_[0] |= 0x00000008u;
}
inline void resource_status_row::clear_has_resource() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void resource_status_row::clear_resource() {
  if (resource_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    resource_->clear();
  }
  clear_has_resource();
}
inline const ::std::string& resource_status_row::resource() const {
  // @@protoc_insertion_point(field_get:db_name_server.resource_status_row.resource)
  return *resource_;
}
inline void resource_status_row::set_resource(const ::std::string& value) {
  set_has_resource();
  if (resource_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    resource_ = new ::std::string;
  }
  resource_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.resource_status_row.resource)
}
inline void resource_status_row::set_resource(const char* value) {
  set_has_resource();
  if (resource_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    resource_ = new ::std::string;
  }
  resource_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.resource_status_row.resource)
}
inline void resource_status_row::set_resource(const void* value, size_t size) {
  set_has_resource();
  if (resource_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    resource_ = new ::std::string;
  }
  resource_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.resource_status_row.resource)
}
inline ::std::string* resource_status_row::mutable_resource() {
  set_has_resource();
  if (resource_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    resource_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.resource_status_row.resource)
  return resource_;
}
inline ::std::string* resource_status_row::release_resource() {
  clear_has_resource();
  if (resource_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = resource_;
    resource_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void resource_status_row::set_allocated_resource(::std::string* resource) {
  if (resource_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete resource_;
  }
  if (resource) {
    set_has_resource();
    resource_ = resource;
  } else {
    clear_has_resource();
    resource_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.resource_status_row.resource)
}

// optional bytes name = 5;
inline bool resource_status_row::has_name() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void resource_status_row::set_has_name() {
  _has_bits_[0] |= 0x00000010u;
}
inline void resource_status_row::clear_has_name() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void resource_status_row::clear_name() {
  if (name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    name_->clear();
  }
  clear_has_name();
}
inline const ::std::string& resource_status_row::name() const {
  // @@protoc_insertion_point(field_get:db_name_server.resource_status_row.name)
  return *name_;
}
inline void resource_status_row::set_name(const ::std::string& value) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    name_ = new ::std::string;
  }
  name_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.resource_status_row.name)
}
inline void resource_status_row::set_name(const char* value) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    name_ = new ::std::string;
  }
  name_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.resource_status_row.name)
}
inline void resource_status_row::set_name(const void* value, size_t size) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    name_ = new ::std::string;
  }
  name_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.resource_status_row.name)
}
inline ::std::string* resource_status_row::mutable_name() {
  set_has_name();
  if (name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    name_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.resource_status_row.name)
  return name_;
}
inline ::std::string* resource_status_row::release_name() {
  clear_has_name();
  if (name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = name_;
    name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void resource_status_row::set_allocated_name(::std::string* name) {
  if (name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete name_;
  }
  if (name) {
    set_has_name();
    name_ = name;
  } else {
    clear_has_name();
    name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.resource_status_row.name)
}

// optional uint32 value_type = 6;
inline bool resource_status_row::has_value_type() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void resource_status_row::set_has_value_type() {
  _has_bits_[0] |= 0x00000020u;
}
inline void resource_status_row::clear_has_value_type() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void resource_status_row::clear_value_type() {
  value_type_ = 0u;
  clear_has_value_type();
}
inline ::google::protobuf::uint32 resource_status_row::value_type() const {
  // @@protoc_insertion_point(field_get:db_name_server.resource_status_row.value_type)
  return value_type_;
}
inline void resource_status_row::set_value_type(::google::protobuf::uint32 value) {
  set_has_value_type();
  value_type_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.resource_status_row.value_type)
}

// optional uint64 value = 7;
inline bool resource_status_row::has_value() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void resource_status_row::set_has_value() {
  _has_bits_[0] |= 0x00000040u;
}
inline void resource_status_row::clear_has_value() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void resource_status_row::clear_value() {
  value_ = GOOGLE_ULONGLONG(0);
  clear_has_value();
}
inline ::google::protobuf::uint64 resource_status_row::value() const {
  // @@protoc_insertion_point(field_get:db_name_server.resource_status_row.value)
  return value_;
}
inline void resource_status_row::set_value(::google::protobuf::uint64 value) {
  set_has_value();
  value_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.resource_status_row.value)
}

// optional bytes value_str = 8;
inline bool resource_status_row::has_value_str() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void resource_status_row::set_has_value_str() {
  _has_bits_[0] |= 0x00000080u;
}
inline void resource_status_row::clear_has_value_str() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void resource_status_row::clear_value_str() {
  if (value_str_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    value_str_->clear();
  }
  clear_has_value_str();
}
inline const ::std::string& resource_status_row::value_str() const {
  // @@protoc_insertion_point(field_get:db_name_server.resource_status_row.value_str)
  return *value_str_;
}
inline void resource_status_row::set_value_str(const ::std::string& value) {
  set_has_value_str();
  if (value_str_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    value_str_ = new ::std::string;
  }
  value_str_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.resource_status_row.value_str)
}
inline void resource_status_row::set_value_str(const char* value) {
  set_has_value_str();
  if (value_str_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    value_str_ = new ::std::string;
  }
  value_str_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.resource_status_row.value_str)
}
inline void resource_status_row::set_value_str(const void* value, size_t size) {
  set_has_value_str();
  if (value_str_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    value_str_ = new ::std::string;
  }
  value_str_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.resource_status_row.value_str)
}
inline ::std::string* resource_status_row::mutable_value_str() {
  set_has_value_str();
  if (value_str_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    value_str_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.resource_status_row.value_str)
  return value_str_;
}
inline ::std::string* resource_status_row::release_value_str() {
  clear_has_value_str();
  if (value_str_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = value_str_;
    value_str_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void resource_status_row::set_allocated_value_str(::std::string* value_str) {
  if (value_str_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete value_str_;
  }
  if (value_str) {
    set_has_value_str();
    value_str_ = value_str;
  } else {
    clear_has_value_str();
    value_str_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.resource_status_row.value_str)
}

// optional uint64 utime = 9;
inline bool resource_status_row::has_utime() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void resource_status_row::set_has_utime() {
  _has_bits_[0] |= 0x00000100u;
}
inline void resource_status_row::clear_has_utime() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void resource_status_row::clear_utime() {
  utime_ = GOOGLE_ULONGLONG(0);
  clear_has_utime();
}
inline ::google::protobuf::uint64 resource_status_row::utime() const {
  // @@protoc_insertion_point(field_get:db_name_server.resource_status_row.utime)
  return utime_;
}
inline void resource_status_row::set_utime(::google::protobuf::uint64 value) {
  set_has_utime();
  utime_ = value;
  // @@protoc_insertion_point(field_set:db_name_server.resource_status_row.utime)
}

// optional bytes descs = 10;
inline bool resource_status_row::has_descs() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
inline void resource_status_row::set_has_descs() {
  _has_bits_[0] |= 0x00000200u;
}
inline void resource_status_row::clear_has_descs() {
  _has_bits_[0] &= ~0x00000200u;
}
inline void resource_status_row::clear_descs() {
  if (descs_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    descs_->clear();
  }
  clear_has_descs();
}
inline const ::std::string& resource_status_row::descs() const {
  // @@protoc_insertion_point(field_get:db_name_server.resource_status_row.descs)
  return *descs_;
}
inline void resource_status_row::set_descs(const ::std::string& value) {
  set_has_descs();
  if (descs_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    descs_ = new ::std::string;
  }
  descs_->assign(value);
  // @@protoc_insertion_point(field_set:db_name_server.resource_status_row.descs)
}
inline void resource_status_row::set_descs(const char* value) {
  set_has_descs();
  if (descs_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    descs_ = new ::std::string;
  }
  descs_->assign(value);
  // @@protoc_insertion_point(field_set_char:db_name_server.resource_status_row.descs)
}
inline void resource_status_row::set_descs(const void* value, size_t size) {
  set_has_descs();
  if (descs_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    descs_ = new ::std::string;
  }
  descs_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:db_name_server.resource_status_row.descs)
}
inline ::std::string* resource_status_row::mutable_descs() {
  set_has_descs();
  if (descs_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    descs_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:db_name_server.resource_status_row.descs)
  return descs_;
}
inline ::std::string* resource_status_row::release_descs() {
  clear_has_descs();
  if (descs_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = descs_;
    descs_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void resource_status_row::set_allocated_descs(::std::string* descs) {
  if (descs_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete descs_;
  }
  if (descs) {
    set_has_descs();
    descs_ = descs;
  } else {
    clear_has_descs();
    descs_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:db_name_server.resource_status_row.descs)
}

// -------------------------------------------------------------------

// resource_status_rows

// repeated .db_name_server.resource_status_row rows = 1;
inline int resource_status_rows::rows_size() const {
  return rows_.size();
}
inline void resource_status_rows::clear_rows() {
  rows_.Clear();
}
inline const ::db_name_server::resource_status_row& resource_status_rows::rows(int index) const {
  // @@protoc_insertion_point(field_get:db_name_server.resource_status_rows.rows)
  return rows_.Get(index);
}
inline ::db_name_server::resource_status_row* resource_status_rows::mutable_rows(int index) {
  // @@protoc_insertion_point(field_mutable:db_name_server.resource_status_rows.rows)
  return rows_.Mutable(index);
}
inline ::db_name_server::resource_status_row* resource_status_rows::add_rows() {
  // @@protoc_insertion_point(field_add:db_name_server.resource_status_rows.rows)
  return rows_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::db_name_server::resource_status_row >&
resource_status_rows::rows() const {
  // @@protoc_insertion_point(field_list:db_name_server.resource_status_rows.rows)
  return rows_;
}
inline ::google::protobuf::RepeatedPtrField< ::db_name_server::resource_status_row >*
resource_status_rows::mutable_rows() {
  // @@protoc_insertion_point(field_mutable_list:db_name_server.resource_status_rows.rows)
  return &rows_;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace db_name_server

#ifndef SWIG
namespace google {
namespace protobuf {


}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_source_2fbaseagent_2fproto_2fname_5fserver_2eproto__INCLUDED
