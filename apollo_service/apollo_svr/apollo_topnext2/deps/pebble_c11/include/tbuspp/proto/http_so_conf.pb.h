// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tbuspp/proto/http_so_conf.proto

#ifndef PROTOBUF_source_2fbaseagent_2fproto_2fhttp_5fso_5fconf_2eproto__INCLUDED
#define PROTOBUF_source_2fbaseagent_2fproto_2fhttp_5fso_5fconf_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2006000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "tbuspp/proto/troute.pb.h"
// @@protoc_insertion_point(includes)

namespace http_conf {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fhttp_5fso_5fconf_2eproto();
void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fhttp_5fso_5fconf_2eproto();
void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fhttp_5fso_5fconf_2eproto();

class http_uri_route_conf;
class http_so_conf;

// ===================================================================

class http_uri_route_conf : public ::google::protobuf::Message {
 public:
  http_uri_route_conf();
  virtual ~http_uri_route_conf();

  http_uri_route_conf(const http_uri_route_conf& from);

  inline http_uri_route_conf& operator=(const http_uri_route_conf& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const http_uri_route_conf& default_instance();

  void Swap(http_uri_route_conf* other);

  // implements Message ----------------------------------------------

  http_uri_route_conf* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const http_uri_route_conf& from);
  void MergeFrom(const http_uri_route_conf& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string uri = 1;
  inline bool has_uri() const;
  inline void clear_uri();
  static const int kUriFieldNumber = 1;
  inline const ::std::string& uri() const;
  inline void set_uri(const ::std::string& value);
  inline void set_uri(const char* value);
  inline void set_uri(const char* value, size_t size);
  inline ::std::string* mutable_uri();
  inline ::std::string* release_uri();
  inline void set_allocated_uri(::std::string* uri);

  // optional int32 version = 2;
  inline bool has_version() const;
  inline void clear_version();
  static const int kVersionFieldNumber = 2;
  inline ::google::protobuf::int32 version() const;
  inline void set_version(::google::protobuf::int32 value);

  // optional int32 type = 3;
  inline bool has_type() const;
  inline void clear_type();
  static const int kTypeFieldNumber = 3;
  inline ::google::protobuf::int32 type() const;
  inline void set_type(::google::protobuf::int32 value);

  // optional .troute.server_name_and_address src = 4;
  inline bool has_src() const;
  inline void clear_src();
  static const int kSrcFieldNumber = 4;
  inline const ::troute::server_name_and_address& src() const;
  inline ::troute::server_name_and_address* mutable_src();
  inline ::troute::server_name_and_address* release_src();
  inline void set_allocated_src(::troute::server_name_and_address* src);

  // repeated .troute.server_name_and_address dsts = 5;
  inline int dsts_size() const;
  inline void clear_dsts();
  static const int kDstsFieldNumber = 5;
  inline const ::troute::server_name_and_address& dsts(int index) const;
  inline ::troute::server_name_and_address* mutable_dsts(int index);
  inline ::troute::server_name_and_address* add_dsts();
  inline const ::google::protobuf::RepeatedPtrField< ::troute::server_name_and_address >&
      dsts() const;
  inline ::google::protobuf::RepeatedPtrField< ::troute::server_name_and_address >*
      mutable_dsts();

  // @@protoc_insertion_point(class_scope:http_conf.http_uri_route_conf)
 private:
  inline void set_has_uri();
  inline void clear_has_uri();
  inline void set_has_version();
  inline void clear_has_version();
  inline void set_has_type();
  inline void clear_has_type();
  inline void set_has_src();
  inline void clear_has_src();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::std::string* uri_;
  ::google::protobuf::int32 version_;
  ::google::protobuf::int32 type_;
  ::troute::server_name_and_address* src_;
  ::google::protobuf::RepeatedPtrField< ::troute::server_name_and_address > dsts_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fhttp_5fso_5fconf_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fhttp_5fso_5fconf_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fhttp_5fso_5fconf_2eproto();

  void InitAsDefaultInstance();
  static http_uri_route_conf* default_instance_;
};
// -------------------------------------------------------------------

class http_so_conf : public ::google::protobuf::Message {
 public:
  http_so_conf();
  virtual ~http_so_conf();

  http_so_conf(const http_so_conf& from);

  inline http_so_conf& operator=(const http_so_conf& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const http_so_conf& default_instance();

  void Swap(http_so_conf* other);

  // implements Message ----------------------------------------------

  http_so_conf* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const http_so_conf& from);
  void MergeFrom(const http_so_conf& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 version = 1;
  inline bool has_version() const;
  inline void clear_version();
  static const int kVersionFieldNumber = 1;
  inline ::google::protobuf::int32 version() const;
  inline void set_version(::google::protobuf::int32 value);

  // repeated .http_conf.http_uri_route_conf uri_conf = 2;
  inline int uri_conf_size() const;
  inline void clear_uri_conf();
  static const int kUriConfFieldNumber = 2;
  inline const ::http_conf::http_uri_route_conf& uri_conf(int index) const;
  inline ::http_conf::http_uri_route_conf* mutable_uri_conf(int index);
  inline ::http_conf::http_uri_route_conf* add_uri_conf();
  inline const ::google::protobuf::RepeatedPtrField< ::http_conf::http_uri_route_conf >&
      uri_conf() const;
  inline ::google::protobuf::RepeatedPtrField< ::http_conf::http_uri_route_conf >*
      mutable_uri_conf();

  // optional .troute.server_name_and_address default_src = 3;
  inline bool has_default_src() const;
  inline void clear_default_src();
  static const int kDefaultSrcFieldNumber = 3;
  inline const ::troute::server_name_and_address& default_src() const;
  inline ::troute::server_name_and_address* mutable_default_src();
  inline ::troute::server_name_and_address* release_default_src();
  inline void set_allocated_default_src(::troute::server_name_and_address* default_src);

  // @@protoc_insertion_point(class_scope:http_conf.http_so_conf)
 private:
  inline void set_has_version();
  inline void clear_has_version();
  inline void set_has_default_src();
  inline void clear_has_default_src();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::http_conf::http_uri_route_conf > uri_conf_;
  ::troute::server_name_and_address* default_src_;
  ::google::protobuf::int32 version_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fhttp_5fso_5fconf_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fhttp_5fso_5fconf_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fhttp_5fso_5fconf_2eproto();

  void InitAsDefaultInstance();
  static http_so_conf* default_instance_;
};
// ===================================================================


// ===================================================================

// http_uri_route_conf

// optional string uri = 1;
inline bool http_uri_route_conf::has_uri() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void http_uri_route_conf::set_has_uri() {
  _has_bits_[0] |= 0x00000001u;
}
inline void http_uri_route_conf::clear_has_uri() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void http_uri_route_conf::clear_uri() {
  if (uri_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    uri_->clear();
  }
  clear_has_uri();
}
inline const ::std::string& http_uri_route_conf::uri() const {
  // @@protoc_insertion_point(field_get:http_conf.http_uri_route_conf.uri)
  return *uri_;
}
inline void http_uri_route_conf::set_uri(const ::std::string& value) {
  set_has_uri();
  if (uri_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    uri_ = new ::std::string;
  }
  uri_->assign(value);
  // @@protoc_insertion_point(field_set:http_conf.http_uri_route_conf.uri)
}
inline void http_uri_route_conf::set_uri(const char* value) {
  set_has_uri();
  if (uri_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    uri_ = new ::std::string;
  }
  uri_->assign(value);
  // @@protoc_insertion_point(field_set_char:http_conf.http_uri_route_conf.uri)
}
inline void http_uri_route_conf::set_uri(const char* value, size_t size) {
  set_has_uri();
  if (uri_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    uri_ = new ::std::string;
  }
  uri_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:http_conf.http_uri_route_conf.uri)
}
inline ::std::string* http_uri_route_conf::mutable_uri() {
  set_has_uri();
  if (uri_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    uri_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:http_conf.http_uri_route_conf.uri)
  return uri_;
}
inline ::std::string* http_uri_route_conf::release_uri() {
  clear_has_uri();
  if (uri_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = uri_;
    uri_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void http_uri_route_conf::set_allocated_uri(::std::string* uri) {
  if (uri_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete uri_;
  }
  if (uri) {
    set_has_uri();
    uri_ = uri;
  } else {
    clear_has_uri();
    uri_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:http_conf.http_uri_route_conf.uri)
}

// optional int32 version = 2;
inline bool http_uri_route_conf::has_version() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void http_uri_route_conf::set_has_version() {
  _has_bits_[0] |= 0x00000002u;
}
inline void http_uri_route_conf::clear_has_version() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void http_uri_route_conf::clear_version() {
  version_ = 0;
  clear_has_version();
}
inline ::google::protobuf::int32 http_uri_route_conf::version() const {
  // @@protoc_insertion_point(field_get:http_conf.http_uri_route_conf.version)
  return version_;
}
inline void http_uri_route_conf::set_version(::google::protobuf::int32 value) {
  set_has_version();
  version_ = value;
  // @@protoc_insertion_point(field_set:http_conf.http_uri_route_conf.version)
}

// optional int32 type = 3;
inline bool http_uri_route_conf::has_type() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void http_uri_route_conf::set_has_type() {
  _has_bits_[0] |= 0x00000004u;
}
inline void http_uri_route_conf::clear_has_type() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void http_uri_route_conf::clear_type() {
  type_ = 0;
  clear_has_type();
}
inline ::google::protobuf::int32 http_uri_route_conf::type() const {
  // @@protoc_insertion_point(field_get:http_conf.http_uri_route_conf.type)
  return type_;
}
inline void http_uri_route_conf::set_type(::google::protobuf::int32 value) {
  set_has_type();
  type_ = value;
  // @@protoc_insertion_point(field_set:http_conf.http_uri_route_conf.type)
}

// optional .troute.server_name_and_address src = 4;
inline bool http_uri_route_conf::has_src() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void http_uri_route_conf::set_has_src() {
  _has_bits_[0] |= 0x00000008u;
}
inline void http_uri_route_conf::clear_has_src() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void http_uri_route_conf::clear_src() {
  if (src_ != NULL) src_->::troute::server_name_and_address::Clear();
  clear_has_src();
}
inline const ::troute::server_name_and_address& http_uri_route_conf::src() const {
  // @@protoc_insertion_point(field_get:http_conf.http_uri_route_conf.src)
  return src_ != NULL ? *src_ : *default_instance_->src_;
}
inline ::troute::server_name_and_address* http_uri_route_conf::mutable_src() {
  set_has_src();
  if (src_ == NULL) src_ = new ::troute::server_name_and_address;
  // @@protoc_insertion_point(field_mutable:http_conf.http_uri_route_conf.src)
  return src_;
}
inline ::troute::server_name_and_address* http_uri_route_conf::release_src() {
  clear_has_src();
  ::troute::server_name_and_address* temp = src_;
  src_ = NULL;
  return temp;
}
inline void http_uri_route_conf::set_allocated_src(::troute::server_name_and_address* src) {
  delete src_;
  src_ = src;
  if (src) {
    set_has_src();
  } else {
    clear_has_src();
  }
  // @@protoc_insertion_point(field_set_allocated:http_conf.http_uri_route_conf.src)
}

// repeated .troute.server_name_and_address dsts = 5;
inline int http_uri_route_conf::dsts_size() const {
  return dsts_.size();
}
inline void http_uri_route_conf::clear_dsts() {
  dsts_.Clear();
}
inline const ::troute::server_name_and_address& http_uri_route_conf::dsts(int index) const {
  // @@protoc_insertion_point(field_get:http_conf.http_uri_route_conf.dsts)
  return dsts_.Get(index);
}
inline ::troute::server_name_and_address* http_uri_route_conf::mutable_dsts(int index) {
  // @@protoc_insertion_point(field_mutable:http_conf.http_uri_route_conf.dsts)
  return dsts_.Mutable(index);
}
inline ::troute::server_name_and_address* http_uri_route_conf::add_dsts() {
  // @@protoc_insertion_point(field_add:http_conf.http_uri_route_conf.dsts)
  return dsts_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::troute::server_name_and_address >&
http_uri_route_conf::dsts() const {
  // @@protoc_insertion_point(field_list:http_conf.http_uri_route_conf.dsts)
  return dsts_;
}
inline ::google::protobuf::RepeatedPtrField< ::troute::server_name_and_address >*
http_uri_route_conf::mutable_dsts() {
  // @@protoc_insertion_point(field_mutable_list:http_conf.http_uri_route_conf.dsts)
  return &dsts_;
}

// -------------------------------------------------------------------

// http_so_conf

// optional int32 version = 1;
inline bool http_so_conf::has_version() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void http_so_conf::set_has_version() {
  _has_bits_[0] |= 0x00000001u;
}
inline void http_so_conf::clear_has_version() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void http_so_conf::clear_version() {
  version_ = 0;
  clear_has_version();
}
inline ::google::protobuf::int32 http_so_conf::version() const {
  // @@protoc_insertion_point(field_get:http_conf.http_so_conf.version)
  return version_;
}
inline void http_so_conf::set_version(::google::protobuf::int32 value) {
  set_has_version();
  version_ = value;
  // @@protoc_insertion_point(field_set:http_conf.http_so_conf.version)
}

// repeated .http_conf.http_uri_route_conf uri_conf = 2;
inline int http_so_conf::uri_conf_size() const {
  return uri_conf_.size();
}
inline void http_so_conf::clear_uri_conf() {
  uri_conf_.Clear();
}
inline const ::http_conf::http_uri_route_conf& http_so_conf::uri_conf(int index) const {
  // @@protoc_insertion_point(field_get:http_conf.http_so_conf.uri_conf)
  return uri_conf_.Get(index);
}
inline ::http_conf::http_uri_route_conf* http_so_conf::mutable_uri_conf(int index) {
  // @@protoc_insertion_point(field_mutable:http_conf.http_so_conf.uri_conf)
  return uri_conf_.Mutable(index);
}
inline ::http_conf::http_uri_route_conf* http_so_conf::add_uri_conf() {
  // @@protoc_insertion_point(field_add:http_conf.http_so_conf.uri_conf)
  return uri_conf_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::http_conf::http_uri_route_conf >&
http_so_conf::uri_conf() const {
  // @@protoc_insertion_point(field_list:http_conf.http_so_conf.uri_conf)
  return uri_conf_;
}
inline ::google::protobuf::RepeatedPtrField< ::http_conf::http_uri_route_conf >*
http_so_conf::mutable_uri_conf() {
  // @@protoc_insertion_point(field_mutable_list:http_conf.http_so_conf.uri_conf)
  return &uri_conf_;
}

// optional .troute.server_name_and_address default_src = 3;
inline bool http_so_conf::has_default_src() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void http_so_conf::set_has_default_src() {
  _has_bits_[0] |= 0x00000004u;
}
inline void http_so_conf::clear_has_default_src() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void http_so_conf::clear_default_src() {
  if (default_src_ != NULL) default_src_->::troute::server_name_and_address::Clear();
  clear_has_default_src();
}
inline const ::troute::server_name_and_address& http_so_conf::default_src() const {
  // @@protoc_insertion_point(field_get:http_conf.http_so_conf.default_src)
  return default_src_ != NULL ? *default_src_ : *default_instance_->default_src_;
}
inline ::troute::server_name_and_address* http_so_conf::mutable_default_src() {
  set_has_default_src();
  if (default_src_ == NULL) default_src_ = new ::troute::server_name_and_address;
  // @@protoc_insertion_point(field_mutable:http_conf.http_so_conf.default_src)
  return default_src_;
}
inline ::troute::server_name_and_address* http_so_conf::release_default_src() {
  clear_has_default_src();
  ::troute::server_name_and_address* temp = default_src_;
  default_src_ = NULL;
  return temp;
}
inline void http_so_conf::set_allocated_default_src(::troute::server_name_and_address* default_src) {
  delete default_src_;
  default_src_ = default_src;
  if (default_src) {
    set_has_default_src();
  } else {
    clear_has_default_src();
  }
  // @@protoc_insertion_point(field_set_allocated:http_conf.http_so_conf.default_src)
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace http_conf

#ifndef SWIG
namespace google {
namespace protobuf {


}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_source_2fbaseagent_2fproto_2fhttp_5fso_5fconf_2eproto__INCLUDED
