// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tbuspp/proto/oms.proto

#ifndef PROTOBUF_source_2fbaseagent_2fproto_2foms_2eproto__INCLUDED
#define PROTOBUF_source_2fbaseagent_2fproto_2foms_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2006000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace tbusplus_oms {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_source_2fbaseagent_2fproto_2foms_2eproto();
void protobuf_AssignDesc_source_2fbaseagent_2fproto_2foms_2eproto();
void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2foms_2eproto();

class pkg_head;
class route_hash_table_id;
class queue_rangle;

// ===================================================================

class pkg_head : public ::google::protobuf::Message {
 public:
  pkg_head();
  virtual ~pkg_head();

  pkg_head(const pkg_head& from);

  inline pkg_head& operator=(const pkg_head& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const pkg_head& default_instance();

  void Swap(pkg_head* other);

  // implements Message ----------------------------------------------

  pkg_head* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const pkg_head& from);
  void MergeFrom(const pkg_head& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 magic = 1 [default = 305419896];
  inline bool has_magic() const;
  inline void clear_magic();
  static const int kMagicFieldNumber = 1;
  inline ::google::protobuf::int32 magic() const;
  inline void set_magic(::google::protobuf::int32 value);

  // optional int32 msg_len = 2;
  inline bool has_msg_len() const;
  inline void clear_msg_len();
  static const int kMsgLenFieldNumber = 2;
  inline ::google::protobuf::int32 msg_len() const;
  inline void set_msg_len(::google::protobuf::int32 value);

  // optional int32 cmd = 3;
  inline bool has_cmd() const;
  inline void clear_cmd();
  static const int kCmdFieldNumber = 3;
  inline ::google::protobuf::int32 cmd() const;
  inline void set_cmd(::google::protobuf::int32 value);

  // optional int32 status = 4;
  inline bool has_status() const;
  inline void clear_status();
  static const int kStatusFieldNumber = 4;
  inline ::google::protobuf::int32 status() const;
  inline void set_status(::google::protobuf::int32 value);

  // optional int32 sseq = 5;
  inline bool has_sseq() const;
  inline void clear_sseq();
  static const int kSseqFieldNumber = 5;
  inline ::google::protobuf::int32 sseq() const;
  inline void set_sseq(::google::protobuf::int32 value);

  // optional int32 rseq = 6;
  inline bool has_rseq() const;
  inline void clear_rseq();
  static const int kRseqFieldNumber = 6;
  inline ::google::protobuf::int32 rseq() const;
  inline void set_rseq(::google::protobuf::int32 value);

  // optional int32 body_len = 7;
  inline bool has_body_len() const;
  inline void clear_body_len();
  static const int kBodyLenFieldNumber = 7;
  inline ::google::protobuf::int32 body_len() const;
  inline void set_body_len(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tbusplus_oms.pkg_head)
 private:
  inline void set_has_magic();
  inline void clear_has_magic();
  inline void set_has_msg_len();
  inline void clear_has_msg_len();
  inline void set_has_cmd();
  inline void clear_has_cmd();
  inline void set_has_status();
  inline void clear_has_status();
  inline void set_has_sseq();
  inline void clear_has_sseq();
  inline void set_has_rseq();
  inline void clear_has_rseq();
  inline void set_has_body_len();
  inline void clear_has_body_len();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::int32 magic_;
  ::google::protobuf::int32 msg_len_;
  ::google::protobuf::int32 cmd_;
  ::google::protobuf::int32 status_;
  ::google::protobuf::int32 sseq_;
  ::google::protobuf::int32 rseq_;
  ::google::protobuf::int32 body_len_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2foms_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2foms_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2foms_2eproto();

  void InitAsDefaultInstance();
  static pkg_head* default_instance_;
};
// -------------------------------------------------------------------

class route_hash_table_id : public ::google::protobuf::Message {
 public:
  route_hash_table_id();
  virtual ~route_hash_table_id();

  route_hash_table_id(const route_hash_table_id& from);

  inline route_hash_table_id& operator=(const route_hash_table_id& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const route_hash_table_id& default_instance();

  void Swap(route_hash_table_id* other);

  // implements Message ----------------------------------------------

  route_hash_table_id* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const route_hash_table_id& from);
  void MergeFrom(const route_hash_table_id& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 tableid = 1;
  inline bool has_tableid() const;
  inline void clear_tableid();
  static const int kTableidFieldNumber = 1;
  inline ::google::protobuf::int32 tableid() const;
  inline void set_tableid(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tbusplus_oms.route_hash_table_id)
 private:
  inline void set_has_tableid();
  inline void clear_has_tableid();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::int32 tableid_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2foms_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2foms_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2foms_2eproto();

  void InitAsDefaultInstance();
  static route_hash_table_id* default_instance_;
};
// -------------------------------------------------------------------

class queue_rangle : public ::google::protobuf::Message {
 public:
  queue_rangle();
  virtual ~queue_rangle();

  queue_rangle(const queue_rangle& from);

  inline queue_rangle& operator=(const queue_rangle& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const queue_rangle& default_instance();

  void Swap(queue_rangle* other);

  // implements Message ----------------------------------------------

  queue_rangle* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const queue_rangle& from);
  void MergeFrom(const queue_rangle& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 minid = 1;
  inline bool has_minid() const;
  inline void clear_minid();
  static const int kMinidFieldNumber = 1;
  inline ::google::protobuf::int32 minid() const;
  inline void set_minid(::google::protobuf::int32 value);

  // optional int32 maxid = 2;
  inline bool has_maxid() const;
  inline void clear_maxid();
  static const int kMaxidFieldNumber = 2;
  inline ::google::protobuf::int32 maxid() const;
  inline void set_maxid(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tbusplus_oms.queue_rangle)
 private:
  inline void set_has_minid();
  inline void clear_has_minid();
  inline void set_has_maxid();
  inline void clear_has_maxid();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::int32 minid_;
  ::google::protobuf::int32 maxid_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2foms_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2foms_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2foms_2eproto();

  void InitAsDefaultInstance();
  static queue_rangle* default_instance_;
};
// ===================================================================


// ===================================================================

// pkg_head

// optional int32 magic = 1 [default = 305419896];
inline bool pkg_head::has_magic() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void pkg_head::set_has_magic() {
  _has_bits_[0] |= 0x00000001u;
}
inline void pkg_head::clear_has_magic() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void pkg_head::clear_magic() {
  magic_ = 305419896;
  clear_has_magic();
}
inline ::google::protobuf::int32 pkg_head::magic() const {
  // @@protoc_insertion_point(field_get:tbusplus_oms.pkg_head.magic)
  return magic_;
}
inline void pkg_head::set_magic(::google::protobuf::int32 value) {
  set_has_magic();
  magic_ = value;
  // @@protoc_insertion_point(field_set:tbusplus_oms.pkg_head.magic)
}

// optional int32 msg_len = 2;
inline bool pkg_head::has_msg_len() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void pkg_head::set_has_msg_len() {
  _has_bits_[0] |= 0x00000002u;
}
inline void pkg_head::clear_has_msg_len() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void pkg_head::clear_msg_len() {
  msg_len_ = 0;
  clear_has_msg_len();
}
inline ::google::protobuf::int32 pkg_head::msg_len() const {
  // @@protoc_insertion_point(field_get:tbusplus_oms.pkg_head.msg_len)
  return msg_len_;
}
inline void pkg_head::set_msg_len(::google::protobuf::int32 value) {
  set_has_msg_len();
  msg_len_ = value;
  // @@protoc_insertion_point(field_set:tbusplus_oms.pkg_head.msg_len)
}

// optional int32 cmd = 3;
inline bool pkg_head::has_cmd() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void pkg_head::set_has_cmd() {
  _has_bits_[0] |= 0x00000004u;
}
inline void pkg_head::clear_has_cmd() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void pkg_head::clear_cmd() {
  cmd_ = 0;
  clear_has_cmd();
}
inline ::google::protobuf::int32 pkg_head::cmd() const {
  // @@protoc_insertion_point(field_get:tbusplus_oms.pkg_head.cmd)
  return cmd_;
}
inline void pkg_head::set_cmd(::google::protobuf::int32 value) {
  set_has_cmd();
  cmd_ = value;
  // @@protoc_insertion_point(field_set:tbusplus_oms.pkg_head.cmd)
}

// optional int32 status = 4;
inline bool pkg_head::has_status() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void pkg_head::set_has_status() {
  _has_bits_[0] |= 0x00000008u;
}
inline void pkg_head::clear_has_status() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void pkg_head::clear_status() {
  status_ = 0;
  clear_has_status();
}
inline ::google::protobuf::int32 pkg_head::status() const {
  // @@protoc_insertion_point(field_get:tbusplus_oms.pkg_head.status)
  return status_;
}
inline void pkg_head::set_status(::google::protobuf::int32 value) {
  set_has_status();
  status_ = value;
  // @@protoc_insertion_point(field_set:tbusplus_oms.pkg_head.status)
}

// optional int32 sseq = 5;
inline bool pkg_head::has_sseq() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void pkg_head::set_has_sseq() {
  _has_bits_[0] |= 0x00000010u;
}
inline void pkg_head::clear_has_sseq() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void pkg_head::clear_sseq() {
  sseq_ = 0;
  clear_has_sseq();
}
inline ::google::protobuf::int32 pkg_head::sseq() const {
  // @@protoc_insertion_point(field_get:tbusplus_oms.pkg_head.sseq)
  return sseq_;
}
inline void pkg_head::set_sseq(::google::protobuf::int32 value) {
  set_has_sseq();
  sseq_ = value;
  // @@protoc_insertion_point(field_set:tbusplus_oms.pkg_head.sseq)
}

// optional int32 rseq = 6;
inline bool pkg_head::has_rseq() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void pkg_head::set_has_rseq() {
  _has_bits_[0] |= 0x00000020u;
}
inline void pkg_head::clear_has_rseq() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void pkg_head::clear_rseq() {
  rseq_ = 0;
  clear_has_rseq();
}
inline ::google::protobuf::int32 pkg_head::rseq() const {
  // @@protoc_insertion_point(field_get:tbusplus_oms.pkg_head.rseq)
  return rseq_;
}
inline void pkg_head::set_rseq(::google::protobuf::int32 value) {
  set_has_rseq();
  rseq_ = value;
  // @@protoc_insertion_point(field_set:tbusplus_oms.pkg_head.rseq)
}

// optional int32 body_len = 7;
inline bool pkg_head::has_body_len() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void pkg_head::set_has_body_len() {
  _has_bits_[0] |= 0x00000040u;
}
inline void pkg_head::clear_has_body_len() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void pkg_head::clear_body_len() {
  body_len_ = 0;
  clear_has_body_len();
}
inline ::google::protobuf::int32 pkg_head::body_len() const {
  // @@protoc_insertion_point(field_get:tbusplus_oms.pkg_head.body_len)
  return body_len_;
}
inline void pkg_head::set_body_len(::google::protobuf::int32 value) {
  set_has_body_len();
  body_len_ = value;
  // @@protoc_insertion_point(field_set:tbusplus_oms.pkg_head.body_len)
}

// -------------------------------------------------------------------

// route_hash_table_id

// optional int32 tableid = 1;
inline bool route_hash_table_id::has_tableid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void route_hash_table_id::set_has_tableid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void route_hash_table_id::clear_has_tableid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void route_hash_table_id::clear_tableid() {
  tableid_ = 0;
  clear_has_tableid();
}
inline ::google::protobuf::int32 route_hash_table_id::tableid() const {
  // @@protoc_insertion_point(field_get:tbusplus_oms.route_hash_table_id.tableid)
  return tableid_;
}
inline void route_hash_table_id::set_tableid(::google::protobuf::int32 value) {
  set_has_tableid();
  tableid_ = value;
  // @@protoc_insertion_point(field_set:tbusplus_oms.route_hash_table_id.tableid)
}

// -------------------------------------------------------------------

// queue_rangle

// optional int32 minid = 1;
inline bool queue_rangle::has_minid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void queue_rangle::set_has_minid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void queue_rangle::clear_has_minid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void queue_rangle::clear_minid() {
  minid_ = 0;
  clear_has_minid();
}
inline ::google::protobuf::int32 queue_rangle::minid() const {
  // @@protoc_insertion_point(field_get:tbusplus_oms.queue_rangle.minid)
  return minid_;
}
inline void queue_rangle::set_minid(::google::protobuf::int32 value) {
  set_has_minid();
  minid_ = value;
  // @@protoc_insertion_point(field_set:tbusplus_oms.queue_rangle.minid)
}

// optional int32 maxid = 2;
inline bool queue_rangle::has_maxid() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void queue_rangle::set_has_maxid() {
  _has_bits_[0] |= 0x00000002u;
}
inline void queue_rangle::clear_has_maxid() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void queue_rangle::clear_maxid() {
  maxid_ = 0;
  clear_has_maxid();
}
inline ::google::protobuf::int32 queue_rangle::maxid() const {
  // @@protoc_insertion_point(field_get:tbusplus_oms.queue_rangle.maxid)
  return maxid_;
}
inline void queue_rangle::set_maxid(::google::protobuf::int32 value) {
  set_has_maxid();
  maxid_ = value;
  // @@protoc_insertion_point(field_set:tbusplus_oms.queue_rangle.maxid)
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace tbusplus_oms

#ifndef SWIG
namespace google {
namespace protobuf {


}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_source_2fbaseagent_2fproto_2foms_2eproto__INCLUDED
