// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tbuspp/proto/notify_msg.proto

#ifndef PROTOBUF_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto__INCLUDED
#define PROTOBUF_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2006000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tbuspp/proto/troute.pb.h"
// @@protoc_insertion_point(includes)

namespace notify {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();

class RegisterRouteAddrReq;
class UnRegisterRouteAddrReq;
class DoRouteCheckReq;
class DoRouteRepairReq;
class DoRoutePickupReq;
class ForceRouteNotifyReq;
class QueryRouteStateReq;
class WatchFilterItem;
class WatchFilters;
class WatchRouteNotifyReq;
class UnWatchRouteNotifyReq;
class QueryRouteStateRsp;
class InnerNotifyReq;
class InnerCheckReq;
class InnerRepairReq;
class InnerCheckRsp;
class NotifyRequest;
class NotifyResponse;
class NotifyMessage;
class WatchEventMessage;

enum NotifyMessageType {
  ONEWAY = 0,
  TWOWAY = 1
};
bool NotifyMessageType_IsValid(int value);
const NotifyMessageType NotifyMessageType_MIN = ONEWAY;
const NotifyMessageType NotifyMessageType_MAX = TWOWAY;
const int NotifyMessageType_ARRAYSIZE = NotifyMessageType_MAX + 1;

const ::google::protobuf::EnumDescriptor* NotifyMessageType_descriptor();
inline const ::std::string& NotifyMessageType_Name(NotifyMessageType value) {
  return ::google::protobuf::internal::NameOfEnum(
    NotifyMessageType_descriptor(), value);
}
inline bool NotifyMessageType_Parse(
    const ::std::string& name, NotifyMessageType* value) {
  return ::google::protobuf::internal::ParseNamedEnum<NotifyMessageType>(
    NotifyMessageType_descriptor(), name, value);
}
enum NotifyEvent {
  ACTIVE_EVENT = 1,
  DEACTIVE_EVENT = 2,
  ENABLE_EVENT = 4,
  DISABLE_EVENT = 8,
  BECALLED_NOTIFY = 16,
  CALL_NOTIFY = 32,
  BROTHER_NOTIFY = 64
};
bool NotifyEvent_IsValid(int value);
const NotifyEvent NotifyEvent_MIN = ACTIVE_EVENT;
const NotifyEvent NotifyEvent_MAX = BROTHER_NOTIFY;
const int NotifyEvent_ARRAYSIZE = NotifyEvent_MAX + 1;

const ::google::protobuf::EnumDescriptor* NotifyEvent_descriptor();
inline const ::std::string& NotifyEvent_Name(NotifyEvent value) {
  return ::google::protobuf::internal::NameOfEnum(
    NotifyEvent_descriptor(), value);
}
inline bool NotifyEvent_Parse(
    const ::std::string& name, NotifyEvent* value) {
  return ::google::protobuf::internal::ParseNamedEnum<NotifyEvent>(
    NotifyEvent_descriptor(), name, value);
}
// ===================================================================

class RegisterRouteAddrReq : public ::google::protobuf::Message {
 public:
  RegisterRouteAddrReq();
  virtual ~RegisterRouteAddrReq();

  RegisterRouteAddrReq(const RegisterRouteAddrReq& from);

  inline RegisterRouteAddrReq& operator=(const RegisterRouteAddrReq& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RegisterRouteAddrReq& default_instance();

  void Swap(RegisterRouteAddrReq* other);

  // implements Message ----------------------------------------------

  RegisterRouteAddrReq* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RegisterRouteAddrReq& from);
  void MergeFrom(const RegisterRouteAddrReq& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .troute.server_name_and_address target = 1;
  inline bool has_target() const;
  inline void clear_target();
  static const int kTargetFieldNumber = 1;
  inline const ::troute::server_name_and_address& target() const;
  inline ::troute::server_name_and_address* mutable_target();
  inline ::troute::server_name_and_address* release_target();
  inline void set_allocated_target(::troute::server_name_and_address* target);

  // optional int32 check_interval = 2 [default = 3];
  inline bool has_check_interval() const;
  inline void clear_check_interval();
  static const int kCheckIntervalFieldNumber = 2;
  inline ::google::protobuf::int32 check_interval() const;
  inline void set_check_interval(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:notify.RegisterRouteAddrReq)
 private:
  inline void set_has_target();
  inline void clear_has_target();
  inline void set_has_check_interval();
  inline void clear_has_check_interval();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::troute::server_name_and_address* target_;
  ::google::protobuf::int32 check_interval_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();

  void InitAsDefaultInstance();
  static RegisterRouteAddrReq* default_instance_;
};
// -------------------------------------------------------------------

class UnRegisterRouteAddrReq : public ::google::protobuf::Message {
 public:
  UnRegisterRouteAddrReq();
  virtual ~UnRegisterRouteAddrReq();

  UnRegisterRouteAddrReq(const UnRegisterRouteAddrReq& from);

  inline UnRegisterRouteAddrReq& operator=(const UnRegisterRouteAddrReq& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const UnRegisterRouteAddrReq& default_instance();

  void Swap(UnRegisterRouteAddrReq* other);

  // implements Message ----------------------------------------------

  UnRegisterRouteAddrReq* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const UnRegisterRouteAddrReq& from);
  void MergeFrom(const UnRegisterRouteAddrReq& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .troute.server_name_and_address target = 1;
  inline bool has_target() const;
  inline void clear_target();
  static const int kTargetFieldNumber = 1;
  inline const ::troute::server_name_and_address& target() const;
  inline ::troute::server_name_and_address* mutable_target();
  inline ::troute::server_name_and_address* release_target();
  inline void set_allocated_target(::troute::server_name_and_address* target);

  // optional int32 force_un_reg = 2 [default = 0];
  inline bool has_force_un_reg() const;
  inline void clear_force_un_reg();
  static const int kForceUnRegFieldNumber = 2;
  inline ::google::protobuf::int32 force_un_reg() const;
  inline void set_force_un_reg(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:notify.UnRegisterRouteAddrReq)
 private:
  inline void set_has_target();
  inline void clear_has_target();
  inline void set_has_force_un_reg();
  inline void clear_has_force_un_reg();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::troute::server_name_and_address* target_;
  ::google::protobuf::int32 force_un_reg_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();

  void InitAsDefaultInstance();
  static UnRegisterRouteAddrReq* default_instance_;
};
// -------------------------------------------------------------------

class DoRouteCheckReq : public ::google::protobuf::Message {
 public:
  DoRouteCheckReq();
  virtual ~DoRouteCheckReq();

  DoRouteCheckReq(const DoRouteCheckReq& from);

  inline DoRouteCheckReq& operator=(const DoRouteCheckReq& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DoRouteCheckReq& default_instance();

  void Swap(DoRouteCheckReq* other);

  // implements Message ----------------------------------------------

  DoRouteCheckReq* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DoRouteCheckReq& from);
  void MergeFrom(const DoRouteCheckReq& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .troute.server_name_and_address target = 1;
  inline bool has_target() const;
  inline void clear_target();
  static const int kTargetFieldNumber = 1;
  inline const ::troute::server_name_and_address& target() const;
  inline ::troute::server_name_and_address* mutable_target();
  inline ::troute::server_name_and_address* release_target();
  inline void set_allocated_target(::troute::server_name_and_address* target);

  // @@protoc_insertion_point(class_scope:notify.DoRouteCheckReq)
 private:
  inline void set_has_target();
  inline void clear_has_target();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::troute::server_name_and_address* target_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();

  void InitAsDefaultInstance();
  static DoRouteCheckReq* default_instance_;
};
// -------------------------------------------------------------------

class DoRouteRepairReq : public ::google::protobuf::Message {
 public:
  DoRouteRepairReq();
  virtual ~DoRouteRepairReq();

  DoRouteRepairReq(const DoRouteRepairReq& from);

  inline DoRouteRepairReq& operator=(const DoRouteRepairReq& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DoRouteRepairReq& default_instance();

  void Swap(DoRouteRepairReq* other);

  // implements Message ----------------------------------------------

  DoRouteRepairReq* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DoRouteRepairReq& from);
  void MergeFrom(const DoRouteRepairReq& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .troute.server_name_and_address target = 1;
  inline bool has_target() const;
  inline void clear_target();
  static const int kTargetFieldNumber = 1;
  inline const ::troute::server_name_and_address& target() const;
  inline ::troute::server_name_and_address* mutable_target();
  inline ::troute::server_name_and_address* release_target();
  inline void set_allocated_target(::troute::server_name_and_address* target);

  // optional .troute.server_name_and_address brother = 3;
  inline bool has_brother() const;
  inline void clear_brother();
  static const int kBrotherFieldNumber = 3;
  inline const ::troute::server_name_and_address& brother() const;
  inline ::troute::server_name_and_address* mutable_brother();
  inline ::troute::server_name_and_address* release_brother();
  inline void set_allocated_brother(::troute::server_name_and_address* brother);

  // @@protoc_insertion_point(class_scope:notify.DoRouteRepairReq)
 private:
  inline void set_has_target();
  inline void clear_has_target();
  inline void set_has_brother();
  inline void clear_has_brother();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::troute::server_name_and_address* target_;
  ::troute::server_name_and_address* brother_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();

  void InitAsDefaultInstance();
  static DoRouteRepairReq* default_instance_;
};
// -------------------------------------------------------------------

class DoRoutePickupReq : public ::google::protobuf::Message {
 public:
  DoRoutePickupReq();
  virtual ~DoRoutePickupReq();

  DoRoutePickupReq(const DoRoutePickupReq& from);

  inline DoRoutePickupReq& operator=(const DoRoutePickupReq& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DoRoutePickupReq& default_instance();

  void Swap(DoRoutePickupReq* other);

  // implements Message ----------------------------------------------

  DoRoutePickupReq* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DoRoutePickupReq& from);
  void MergeFrom(const DoRoutePickupReq& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .troute.server_name_and_address target = 1;
  inline bool has_target() const;
  inline void clear_target();
  static const int kTargetFieldNumber = 1;
  inline const ::troute::server_name_and_address& target() const;
  inline ::troute::server_name_and_address* mutable_target();
  inline ::troute::server_name_and_address* release_target();
  inline void set_allocated_target(::troute::server_name_and_address* target);

  // required int32 pickup_level = 2;
  inline bool has_pickup_level() const;
  inline void clear_pickup_level();
  static const int kPickupLevelFieldNumber = 2;
  inline ::google::protobuf::int32 pickup_level() const;
  inline void set_pickup_level(::google::protobuf::int32 value);

  // required int64 timestamp = 3;
  inline bool has_timestamp() const;
  inline void clear_timestamp();
  static const int kTimestampFieldNumber = 3;
  inline ::google::protobuf::int64 timestamp() const;
  inline void set_timestamp(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:notify.DoRoutePickupReq)
 private:
  inline void set_has_target();
  inline void clear_has_target();
  inline void set_has_pickup_level();
  inline void clear_has_pickup_level();
  inline void set_has_timestamp();
  inline void clear_has_timestamp();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::troute::server_name_and_address* target_;
  ::google::protobuf::int64 timestamp_;
  ::google::protobuf::int32 pickup_level_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();

  void InitAsDefaultInstance();
  static DoRoutePickupReq* default_instance_;
};
// -------------------------------------------------------------------

class ForceRouteNotifyReq : public ::google::protobuf::Message {
 public:
  ForceRouteNotifyReq();
  virtual ~ForceRouteNotifyReq();

  ForceRouteNotifyReq(const ForceRouteNotifyReq& from);

  inline ForceRouteNotifyReq& operator=(const ForceRouteNotifyReq& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ForceRouteNotifyReq& default_instance();

  void Swap(ForceRouteNotifyReq* other);

  // implements Message ----------------------------------------------

  ForceRouteNotifyReq* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ForceRouteNotifyReq& from);
  void MergeFrom(const ForceRouteNotifyReq& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .troute.server_name_and_address target = 1;
  inline bool has_target() const;
  inline void clear_target();
  static const int kTargetFieldNumber = 1;
  inline const ::troute::server_name_and_address& target() const;
  inline ::troute::server_name_and_address* mutable_target();
  inline ::troute::server_name_and_address* release_target();
  inline void set_allocated_target(::troute::server_name_and_address* target);

  // required int32 notify_eve = 2;
  inline bool has_notify_eve() const;
  inline void clear_notify_eve();
  static const int kNotifyEveFieldNumber = 2;
  inline ::google::protobuf::int32 notify_eve() const;
  inline void set_notify_eve(::google::protobuf::int32 value);

  // optional .troute.server_name_and_address_list called = 3;
  inline bool has_called() const;
  inline void clear_called();
  static const int kCalledFieldNumber = 3;
  inline const ::troute::server_name_and_address_list& called() const;
  inline ::troute::server_name_and_address_list* mutable_called();
  inline ::troute::server_name_and_address_list* release_called();
  inline void set_allocated_called(::troute::server_name_and_address_list* called);

  // optional .troute.server_name_and_address_list brother = 4;
  inline bool has_brother() const;
  inline void clear_brother();
  static const int kBrotherFieldNumber = 4;
  inline const ::troute::server_name_and_address_list& brother() const;
  inline ::troute::server_name_and_address_list* mutable_brother();
  inline ::troute::server_name_and_address_list* release_brother();
  inline void set_allocated_brother(::troute::server_name_and_address_list* brother);

  // optional .troute.server_name_and_address_list be_called = 5;
  inline bool has_be_called() const;
  inline void clear_be_called();
  static const int kBeCalledFieldNumber = 5;
  inline const ::troute::server_name_and_address_list& be_called() const;
  inline ::troute::server_name_and_address_list* mutable_be_called();
  inline ::troute::server_name_and_address_list* release_be_called();
  inline void set_allocated_be_called(::troute::server_name_and_address_list* be_called);

  // @@protoc_insertion_point(class_scope:notify.ForceRouteNotifyReq)
 private:
  inline void set_has_target();
  inline void clear_has_target();
  inline void set_has_notify_eve();
  inline void clear_has_notify_eve();
  inline void set_has_called();
  inline void clear_has_called();
  inline void set_has_brother();
  inline void clear_has_brother();
  inline void set_has_be_called();
  inline void clear_has_be_called();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::troute::server_name_and_address* target_;
  ::troute::server_name_and_address_list* called_;
  ::troute::server_name_and_address_list* brother_;
  ::troute::server_name_and_address_list* be_called_;
  ::google::protobuf::int32 notify_eve_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();

  void InitAsDefaultInstance();
  static ForceRouteNotifyReq* default_instance_;
};
// -------------------------------------------------------------------

class QueryRouteStateReq : public ::google::protobuf::Message {
 public:
  QueryRouteStateReq();
  virtual ~QueryRouteStateReq();

  QueryRouteStateReq(const QueryRouteStateReq& from);

  inline QueryRouteStateReq& operator=(const QueryRouteStateReq& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const QueryRouteStateReq& default_instance();

  void Swap(QueryRouteStateReq* other);

  // implements Message ----------------------------------------------

  QueryRouteStateReq* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const QueryRouteStateReq& from);
  void MergeFrom(const QueryRouteStateReq& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .troute.server_name_and_address target = 1;
  inline bool has_target() const;
  inline void clear_target();
  static const int kTargetFieldNumber = 1;
  inline const ::troute::server_name_and_address& target() const;
  inline ::troute::server_name_and_address* mutable_target();
  inline ::troute::server_name_and_address* release_target();
  inline void set_allocated_target(::troute::server_name_and_address* target);

  // @@protoc_insertion_point(class_scope:notify.QueryRouteStateReq)
 private:
  inline void set_has_target();
  inline void clear_has_target();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::troute::server_name_and_address* target_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();

  void InitAsDefaultInstance();
  static QueryRouteStateReq* default_instance_;
};
// -------------------------------------------------------------------

class WatchFilterItem : public ::google::protobuf::Message {
 public:
  WatchFilterItem();
  virtual ~WatchFilterItem();

  WatchFilterItem(const WatchFilterItem& from);

  inline WatchFilterItem& operator=(const WatchFilterItem& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const WatchFilterItem& default_instance();

  void Swap(WatchFilterItem* other);

  // implements Message ----------------------------------------------

  WatchFilterItem* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const WatchFilterItem& from);
  void MergeFrom(const WatchFilterItem& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 type = 1;
  inline bool has_type() const;
  inline void clear_type();
  static const int kTypeFieldNumber = 1;
  inline ::google::protobuf::int32 type() const;
  inline void set_type(::google::protobuf::int32 value);

  // required .troute.server_name_and_address addr = 2;
  inline bool has_addr() const;
  inline void clear_addr();
  static const int kAddrFieldNumber = 2;
  inline const ::troute::server_name_and_address& addr() const;
  inline ::troute::server_name_and_address* mutable_addr();
  inline ::troute::server_name_and_address* release_addr();
  inline void set_allocated_addr(::troute::server_name_and_address* addr);

  // required int32 notify_eve = 3;
  inline bool has_notify_eve() const;
  inline void clear_notify_eve();
  static const int kNotifyEveFieldNumber = 3;
  inline ::google::protobuf::int32 notify_eve() const;
  inline void set_notify_eve(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:notify.WatchFilterItem)
 private:
  inline void set_has_type();
  inline void clear_has_type();
  inline void set_has_addr();
  inline void clear_has_addr();
  inline void set_has_notify_eve();
  inline void clear_has_notify_eve();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::troute::server_name_and_address* addr_;
  ::google::protobuf::int32 type_;
  ::google::protobuf::int32 notify_eve_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();

  void InitAsDefaultInstance();
  static WatchFilterItem* default_instance_;
};
// -------------------------------------------------------------------

class WatchFilters : public ::google::protobuf::Message {
 public:
  WatchFilters();
  virtual ~WatchFilters();

  WatchFilters(const WatchFilters& from);

  inline WatchFilters& operator=(const WatchFilters& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const WatchFilters& default_instance();

  void Swap(WatchFilters* other);

  // implements Message ----------------------------------------------

  WatchFilters* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const WatchFilters& from);
  void MergeFrom(const WatchFilters& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .notify.WatchFilterItem items = 1;
  inline int items_size() const;
  inline void clear_items();
  static const int kItemsFieldNumber = 1;
  inline const ::notify::WatchFilterItem& items(int index) const;
  inline ::notify::WatchFilterItem* mutable_items(int index);
  inline ::notify::WatchFilterItem* add_items();
  inline const ::google::protobuf::RepeatedPtrField< ::notify::WatchFilterItem >&
      items() const;
  inline ::google::protobuf::RepeatedPtrField< ::notify::WatchFilterItem >*
      mutable_items();

  // @@protoc_insertion_point(class_scope:notify.WatchFilters)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::notify::WatchFilterItem > items_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();

  void InitAsDefaultInstance();
  static WatchFilters* default_instance_;
};
// -------------------------------------------------------------------

class WatchRouteNotifyReq : public ::google::protobuf::Message {
 public:
  WatchRouteNotifyReq();
  virtual ~WatchRouteNotifyReq();

  WatchRouteNotifyReq(const WatchRouteNotifyReq& from);

  inline WatchRouteNotifyReq& operator=(const WatchRouteNotifyReq& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const WatchRouteNotifyReq& default_instance();

  void Swap(WatchRouteNotifyReq* other);

  // implements Message ----------------------------------------------

  WatchRouteNotifyReq* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const WatchRouteNotifyReq& from);
  void MergeFrom(const WatchRouteNotifyReq& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .troute.server_name_and_address watcher_addr = 1;
  inline bool has_watcher_addr() const;
  inline void clear_watcher_addr();
  static const int kWatcherAddrFieldNumber = 1;
  inline const ::troute::server_name_and_address& watcher_addr() const;
  inline ::troute::server_name_and_address* mutable_watcher_addr();
  inline ::troute::server_name_and_address* release_watcher_addr();
  inline void set_allocated_watcher_addr(::troute::server_name_and_address* watcher_addr);

  // required .notify.WatchFilters filters = 2;
  inline bool has_filters() const;
  inline void clear_filters();
  static const int kFiltersFieldNumber = 2;
  inline const ::notify::WatchFilters& filters() const;
  inline ::notify::WatchFilters* mutable_filters();
  inline ::notify::WatchFilters* release_filters();
  inline void set_allocated_filters(::notify::WatchFilters* filters);

  // @@protoc_insertion_point(class_scope:notify.WatchRouteNotifyReq)
 private:
  inline void set_has_watcher_addr();
  inline void clear_has_watcher_addr();
  inline void set_has_filters();
  inline void clear_has_filters();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::troute::server_name_and_address* watcher_addr_;
  ::notify::WatchFilters* filters_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();

  void InitAsDefaultInstance();
  static WatchRouteNotifyReq* default_instance_;
};
// -------------------------------------------------------------------

class UnWatchRouteNotifyReq : public ::google::protobuf::Message {
 public:
  UnWatchRouteNotifyReq();
  virtual ~UnWatchRouteNotifyReq();

  UnWatchRouteNotifyReq(const UnWatchRouteNotifyReq& from);

  inline UnWatchRouteNotifyReq& operator=(const UnWatchRouteNotifyReq& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const UnWatchRouteNotifyReq& default_instance();

  void Swap(UnWatchRouteNotifyReq* other);

  // implements Message ----------------------------------------------

  UnWatchRouteNotifyReq* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const UnWatchRouteNotifyReq& from);
  void MergeFrom(const UnWatchRouteNotifyReq& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .troute.server_name_and_address watcher_addr = 1;
  inline bool has_watcher_addr() const;
  inline void clear_watcher_addr();
  static const int kWatcherAddrFieldNumber = 1;
  inline const ::troute::server_name_and_address& watcher_addr() const;
  inline ::troute::server_name_and_address* mutable_watcher_addr();
  inline ::troute::server_name_and_address* release_watcher_addr();
  inline void set_allocated_watcher_addr(::troute::server_name_and_address* watcher_addr);

  // @@protoc_insertion_point(class_scope:notify.UnWatchRouteNotifyReq)
 private:
  inline void set_has_watcher_addr();
  inline void clear_has_watcher_addr();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::troute::server_name_and_address* watcher_addr_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();

  void InitAsDefaultInstance();
  static UnWatchRouteNotifyReq* default_instance_;
};
// -------------------------------------------------------------------

class QueryRouteStateRsp : public ::google::protobuf::Message {
 public:
  QueryRouteStateRsp();
  virtual ~QueryRouteStateRsp();

  QueryRouteStateRsp(const QueryRouteStateRsp& from);

  inline QueryRouteStateRsp& operator=(const QueryRouteStateRsp& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const QueryRouteStateRsp& default_instance();

  void Swap(QueryRouteStateRsp* other);

  // implements Message ----------------------------------------------

  QueryRouteStateRsp* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const QueryRouteStateRsp& from);
  void MergeFrom(const QueryRouteStateRsp& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 route_state = 1;
  inline bool has_route_state() const;
  inline void clear_route_state();
  static const int kRouteStateFieldNumber = 1;
  inline ::google::protobuf::int32 route_state() const;
  inline void set_route_state(::google::protobuf::int32 value);

  // required int64 c_timestamp = 2;
  inline bool has_c_timestamp() const;
  inline void clear_c_timestamp();
  static const int kCTimestampFieldNumber = 2;
  inline ::google::protobuf::int64 c_timestamp() const;
  inline void set_c_timestamp(::google::protobuf::int64 value);

  // required int64 m_timestamp = 3;
  inline bool has_m_timestamp() const;
  inline void clear_m_timestamp();
  static const int kMTimestampFieldNumber = 3;
  inline ::google::protobuf::int64 m_timestamp() const;
  inline void set_m_timestamp(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:notify.QueryRouteStateRsp)
 private:
  inline void set_has_route_state();
  inline void clear_has_route_state();
  inline void set_has_c_timestamp();
  inline void clear_has_c_timestamp();
  inline void set_has_m_timestamp();
  inline void clear_has_m_timestamp();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::int64 c_timestamp_;
  ::google::protobuf::int64 m_timestamp_;
  ::google::protobuf::int32 route_state_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();

  void InitAsDefaultInstance();
  static QueryRouteStateRsp* default_instance_;
};
// -------------------------------------------------------------------

class InnerNotifyReq : public ::google::protobuf::Message {
 public:
  InnerNotifyReq();
  virtual ~InnerNotifyReq();

  InnerNotifyReq(const InnerNotifyReq& from);

  inline InnerNotifyReq& operator=(const InnerNotifyReq& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const InnerNotifyReq& default_instance();

  void Swap(InnerNotifyReq* other);

  // implements Message ----------------------------------------------

  InnerNotifyReq* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const InnerNotifyReq& from);
  void MergeFrom(const InnerNotifyReq& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 cmd = 1;
  inline bool has_cmd() const;
  inline void clear_cmd();
  static const int kCmdFieldNumber = 1;
  inline ::google::protobuf::int32 cmd() const;
  inline void set_cmd(::google::protobuf::int32 value);

  // required .troute.server_name_and_address src = 2;
  inline bool has_src() const;
  inline void clear_src();
  static const int kSrcFieldNumber = 2;
  inline const ::troute::server_name_and_address& src() const;
  inline ::troute::server_name_and_address* mutable_src();
  inline ::troute::server_name_and_address* release_src();
  inline void set_allocated_src(::troute::server_name_and_address* src);

  // required .troute.server_name_and_address target = 3;
  inline bool has_target() const;
  inline void clear_target();
  static const int kTargetFieldNumber = 3;
  inline const ::troute::server_name_and_address& target() const;
  inline ::troute::server_name_and_address* mutable_target();
  inline ::troute::server_name_and_address* release_target();
  inline void set_allocated_target(::troute::server_name_and_address* target);

  // @@protoc_insertion_point(class_scope:notify.InnerNotifyReq)
 private:
  inline void set_has_cmd();
  inline void clear_has_cmd();
  inline void set_has_src();
  inline void clear_has_src();
  inline void set_has_target();
  inline void clear_has_target();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::troute::server_name_and_address* src_;
  ::troute::server_name_and_address* target_;
  ::google::protobuf::int32 cmd_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();

  void InitAsDefaultInstance();
  static InnerNotifyReq* default_instance_;
};
// -------------------------------------------------------------------

class InnerCheckReq : public ::google::protobuf::Message {
 public:
  InnerCheckReq();
  virtual ~InnerCheckReq();

  InnerCheckReq(const InnerCheckReq& from);

  inline InnerCheckReq& operator=(const InnerCheckReq& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const InnerCheckReq& default_instance();

  void Swap(InnerCheckReq* other);

  // implements Message ----------------------------------------------

  InnerCheckReq* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const InnerCheckReq& from);
  void MergeFrom(const InnerCheckReq& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .troute.server_name_and_address target = 1;
  inline bool has_target() const;
  inline void clear_target();
  static const int kTargetFieldNumber = 1;
  inline const ::troute::server_name_and_address& target() const;
  inline ::troute::server_name_and_address* mutable_target();
  inline ::troute::server_name_and_address* release_target();
  inline void set_allocated_target(::troute::server_name_and_address* target);

  // required int32 addr_num = 2;
  inline bool has_addr_num() const;
  inline void clear_addr_num();
  static const int kAddrNumFieldNumber = 2;
  inline ::google::protobuf::int32 addr_num() const;
  inline void set_addr_num(::google::protobuf::int32 value);

  // required string trait_md5 = 3;
  inline bool has_trait_md5() const;
  inline void clear_trait_md5();
  static const int kTraitMd5FieldNumber = 3;
  inline const ::std::string& trait_md5() const;
  inline void set_trait_md5(const ::std::string& value);
  inline void set_trait_md5(const char* value);
  inline void set_trait_md5(const char* value, size_t size);
  inline ::std::string* mutable_trait_md5();
  inline ::std::string* release_trait_md5();
  inline void set_allocated_trait_md5(::std::string* trait_md5);

  // @@protoc_insertion_point(class_scope:notify.InnerCheckReq)
 private:
  inline void set_has_target();
  inline void clear_has_target();
  inline void set_has_addr_num();
  inline void clear_has_addr_num();
  inline void set_has_trait_md5();
  inline void clear_has_trait_md5();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::troute::server_name_and_address* target_;
  ::std::string* trait_md5_;
  ::google::protobuf::int32 addr_num_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();

  void InitAsDefaultInstance();
  static InnerCheckReq* default_instance_;
};
// -------------------------------------------------------------------

class InnerRepairReq : public ::google::protobuf::Message {
 public:
  InnerRepairReq();
  virtual ~InnerRepairReq();

  InnerRepairReq(const InnerRepairReq& from);

  inline InnerRepairReq& operator=(const InnerRepairReq& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const InnerRepairReq& default_instance();

  void Swap(InnerRepairReq* other);

  // implements Message ----------------------------------------------

  InnerRepairReq* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const InnerRepairReq& from);
  void MergeFrom(const InnerRepairReq& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .troute.server_name_and_address target = 1;
  inline bool has_target() const;
  inline void clear_target();
  static const int kTargetFieldNumber = 1;
  inline const ::troute::server_name_and_address& target() const;
  inline ::troute::server_name_and_address* mutable_target();
  inline ::troute::server_name_and_address* release_target();
  inline void set_allocated_target(::troute::server_name_and_address* target);

  // optional .troute.server_name_and_address brother = 2;
  inline bool has_brother() const;
  inline void clear_brother();
  static const int kBrotherFieldNumber = 2;
  inline const ::troute::server_name_and_address& brother() const;
  inline ::troute::server_name_and_address* mutable_brother();
  inline ::troute::server_name_and_address* release_brother();
  inline void set_allocated_brother(::troute::server_name_and_address* brother);

  // @@protoc_insertion_point(class_scope:notify.InnerRepairReq)
 private:
  inline void set_has_target();
  inline void clear_has_target();
  inline void set_has_brother();
  inline void clear_has_brother();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::troute::server_name_and_address* target_;
  ::troute::server_name_and_address* brother_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();

  void InitAsDefaultInstance();
  static InnerRepairReq* default_instance_;
};
// -------------------------------------------------------------------

class InnerCheckRsp : public ::google::protobuf::Message {
 public:
  InnerCheckRsp();
  virtual ~InnerCheckRsp();

  InnerCheckRsp(const InnerCheckRsp& from);

  inline InnerCheckRsp& operator=(const InnerCheckRsp& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const InnerCheckRsp& default_instance();

  void Swap(InnerCheckRsp* other);

  // implements Message ----------------------------------------------

  InnerCheckRsp* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const InnerCheckRsp& from);
  void MergeFrom(const InnerCheckRsp& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .troute.server_name_and_address_list trait_detail = 1;
  inline bool has_trait_detail() const;
  inline void clear_trait_detail();
  static const int kTraitDetailFieldNumber = 1;
  inline const ::troute::server_name_and_address_list& trait_detail() const;
  inline ::troute::server_name_and_address_list* mutable_trait_detail();
  inline ::troute::server_name_and_address_list* release_trait_detail();
  inline void set_allocated_trait_detail(::troute::server_name_and_address_list* trait_detail);

  // @@protoc_insertion_point(class_scope:notify.InnerCheckRsp)
 private:
  inline void set_has_trait_detail();
  inline void clear_has_trait_detail();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::troute::server_name_and_address_list* trait_detail_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();

  void InitAsDefaultInstance();
  static InnerCheckRsp* default_instance_;
};
// -------------------------------------------------------------------

class NotifyRequest : public ::google::protobuf::Message {
 public:
  NotifyRequest();
  virtual ~NotifyRequest();

  NotifyRequest(const NotifyRequest& from);

  inline NotifyRequest& operator=(const NotifyRequest& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const NotifyRequest& default_instance();

  void Swap(NotifyRequest* other);

  // implements Message ----------------------------------------------

  NotifyRequest* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const NotifyRequest& from);
  void MergeFrom(const NotifyRequest& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 cmd = 1;
  inline bool has_cmd() const;
  inline void clear_cmd();
  static const int kCmdFieldNumber = 1;
  inline ::google::protobuf::int32 cmd() const;
  inline void set_cmd(::google::protobuf::int32 value);

  // optional .notify.RegisterRouteAddrReq register_addr = 101;
  inline bool has_register_addr() const;
  inline void clear_register_addr();
  static const int kRegisterAddrFieldNumber = 101;
  inline const ::notify::RegisterRouteAddrReq& register_addr() const;
  inline ::notify::RegisterRouteAddrReq* mutable_register_addr();
  inline ::notify::RegisterRouteAddrReq* release_register_addr();
  inline void set_allocated_register_addr(::notify::RegisterRouteAddrReq* register_addr);

  // optional .notify.UnRegisterRouteAddrReq un_register_addr = 102;
  inline bool has_un_register_addr() const;
  inline void clear_un_register_addr();
  static const int kUnRegisterAddrFieldNumber = 102;
  inline const ::notify::UnRegisterRouteAddrReq& un_register_addr() const;
  inline ::notify::UnRegisterRouteAddrReq* mutable_un_register_addr();
  inline ::notify::UnRegisterRouteAddrReq* release_un_register_addr();
  inline void set_allocated_un_register_addr(::notify::UnRegisterRouteAddrReq* un_register_addr);

  // optional .notify.DoRouteCheckReq do_check = 111;
  inline bool has_do_check() const;
  inline void clear_do_check();
  static const int kDoCheckFieldNumber = 111;
  inline const ::notify::DoRouteCheckReq& do_check() const;
  inline ::notify::DoRouteCheckReq* mutable_do_check();
  inline ::notify::DoRouteCheckReq* release_do_check();
  inline void set_allocated_do_check(::notify::DoRouteCheckReq* do_check);

  // optional .notify.DoRouteRepairReq do_repair = 112;
  inline bool has_do_repair() const;
  inline void clear_do_repair();
  static const int kDoRepairFieldNumber = 112;
  inline const ::notify::DoRouteRepairReq& do_repair() const;
  inline ::notify::DoRouteRepairReq* mutable_do_repair();
  inline ::notify::DoRouteRepairReq* release_do_repair();
  inline void set_allocated_do_repair(::notify::DoRouteRepairReq* do_repair);

  // optional .notify.DoRoutePickupReq do_pickup = 113;
  inline bool has_do_pickup() const;
  inline void clear_do_pickup();
  static const int kDoPickupFieldNumber = 113;
  inline const ::notify::DoRoutePickupReq& do_pickup() const;
  inline ::notify::DoRoutePickupReq* mutable_do_pickup();
  inline ::notify::DoRoutePickupReq* release_do_pickup();
  inline void set_allocated_do_pickup(::notify::DoRoutePickupReq* do_pickup);

  // optional .notify.ForceRouteNotifyReq force_notify = 121;
  inline bool has_force_notify() const;
  inline void clear_force_notify();
  static const int kForceNotifyFieldNumber = 121;
  inline const ::notify::ForceRouteNotifyReq& force_notify() const;
  inline ::notify::ForceRouteNotifyReq* mutable_force_notify();
  inline ::notify::ForceRouteNotifyReq* release_force_notify();
  inline void set_allocated_force_notify(::notify::ForceRouteNotifyReq* force_notify);

  // optional .notify.QueryRouteStateReq query_route = 122;
  inline bool has_query_route() const;
  inline void clear_query_route();
  static const int kQueryRouteFieldNumber = 122;
  inline const ::notify::QueryRouteStateReq& query_route() const;
  inline ::notify::QueryRouteStateReq* mutable_query_route();
  inline ::notify::QueryRouteStateReq* release_query_route();
  inline void set_allocated_query_route(::notify::QueryRouteStateReq* query_route);

  // optional .notify.WatchRouteNotifyReq watch_notify = 131;
  inline bool has_watch_notify() const;
  inline void clear_watch_notify();
  static const int kWatchNotifyFieldNumber = 131;
  inline const ::notify::WatchRouteNotifyReq& watch_notify() const;
  inline ::notify::WatchRouteNotifyReq* mutable_watch_notify();
  inline ::notify::WatchRouteNotifyReq* release_watch_notify();
  inline void set_allocated_watch_notify(::notify::WatchRouteNotifyReq* watch_notify);

  // optional .notify.UnWatchRouteNotifyReq un_watch_notify = 132;
  inline bool has_un_watch_notify() const;
  inline void clear_un_watch_notify();
  static const int kUnWatchNotifyFieldNumber = 132;
  inline const ::notify::UnWatchRouteNotifyReq& un_watch_notify() const;
  inline ::notify::UnWatchRouteNotifyReq* mutable_un_watch_notify();
  inline ::notify::UnWatchRouteNotifyReq* release_un_watch_notify();
  inline void set_allocated_un_watch_notify(::notify::UnWatchRouteNotifyReq* un_watch_notify);

  // optional .notify.InnerNotifyReq inner_notify = 201;
  inline bool has_inner_notify() const;
  inline void clear_inner_notify();
  static const int kInnerNotifyFieldNumber = 201;
  inline const ::notify::InnerNotifyReq& inner_notify() const;
  inline ::notify::InnerNotifyReq* mutable_inner_notify();
  inline ::notify::InnerNotifyReq* release_inner_notify();
  inline void set_allocated_inner_notify(::notify::InnerNotifyReq* inner_notify);

  // optional .notify.InnerCheckReq inner_check = 202;
  inline bool has_inner_check() const;
  inline void clear_inner_check();
  static const int kInnerCheckFieldNumber = 202;
  inline const ::notify::InnerCheckReq& inner_check() const;
  inline ::notify::InnerCheckReq* mutable_inner_check();
  inline ::notify::InnerCheckReq* release_inner_check();
  inline void set_allocated_inner_check(::notify::InnerCheckReq* inner_check);

  // optional .notify.InnerRepairReq inner_repair = 203;
  inline bool has_inner_repair() const;
  inline void clear_inner_repair();
  static const int kInnerRepairFieldNumber = 203;
  inline const ::notify::InnerRepairReq& inner_repair() const;
  inline ::notify::InnerRepairReq* mutable_inner_repair();
  inline ::notify::InnerRepairReq* release_inner_repair();
  inline void set_allocated_inner_repair(::notify::InnerRepairReq* inner_repair);

  // @@protoc_insertion_point(class_scope:notify.NotifyRequest)
 private:
  inline void set_has_cmd();
  inline void clear_has_cmd();
  inline void set_has_register_addr();
  inline void clear_has_register_addr();
  inline void set_has_un_register_addr();
  inline void clear_has_un_register_addr();
  inline void set_has_do_check();
  inline void clear_has_do_check();
  inline void set_has_do_repair();
  inline void clear_has_do_repair();
  inline void set_has_do_pickup();
  inline void clear_has_do_pickup();
  inline void set_has_force_notify();
  inline void clear_has_force_notify();
  inline void set_has_query_route();
  inline void clear_has_query_route();
  inline void set_has_watch_notify();
  inline void clear_has_watch_notify();
  inline void set_has_un_watch_notify();
  inline void clear_has_un_watch_notify();
  inline void set_has_inner_notify();
  inline void clear_has_inner_notify();
  inline void set_has_inner_check();
  inline void clear_has_inner_check();
  inline void set_has_inner_repair();
  inline void clear_has_inner_repair();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::notify::RegisterRouteAddrReq* register_addr_;
  ::notify::UnRegisterRouteAddrReq* un_register_addr_;
  ::notify::DoRouteCheckReq* do_check_;
  ::notify::DoRouteRepairReq* do_repair_;
  ::notify::DoRoutePickupReq* do_pickup_;
  ::notify::ForceRouteNotifyReq* force_notify_;
  ::notify::QueryRouteStateReq* query_route_;
  ::notify::WatchRouteNotifyReq* watch_notify_;
  ::notify::UnWatchRouteNotifyReq* un_watch_notify_;
  ::notify::InnerNotifyReq* inner_notify_;
  ::notify::InnerCheckReq* inner_check_;
  ::notify::InnerRepairReq* inner_repair_;
  ::google::protobuf::int32 cmd_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();

  void InitAsDefaultInstance();
  static NotifyRequest* default_instance_;
};
// -------------------------------------------------------------------

class NotifyResponse : public ::google::protobuf::Message {
 public:
  NotifyResponse();
  virtual ~NotifyResponse();

  NotifyResponse(const NotifyResponse& from);

  inline NotifyResponse& operator=(const NotifyResponse& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const NotifyResponse& default_instance();

  void Swap(NotifyResponse* other);

  // implements Message ----------------------------------------------

  NotifyResponse* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const NotifyResponse& from);
  void MergeFrom(const NotifyResponse& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 result = 1;
  inline bool has_result() const;
  inline void clear_result();
  static const int kResultFieldNumber = 1;
  inline ::google::protobuf::int32 result() const;
  inline void set_result(::google::protobuf::int32 value);

  // optional string reason = 2;
  inline bool has_reason() const;
  inline void clear_reason();
  static const int kReasonFieldNumber = 2;
  inline const ::std::string& reason() const;
  inline void set_reason(const ::std::string& value);
  inline void set_reason(const char* value);
  inline void set_reason(const char* value, size_t size);
  inline ::std::string* mutable_reason();
  inline ::std::string* release_reason();
  inline void set_allocated_reason(::std::string* reason);

  // optional .notify.QueryRouteStateRsp query_route = 122;
  inline bool has_query_route() const;
  inline void clear_query_route();
  static const int kQueryRouteFieldNumber = 122;
  inline const ::notify::QueryRouteStateRsp& query_route() const;
  inline ::notify::QueryRouteStateRsp* mutable_query_route();
  inline ::notify::QueryRouteStateRsp* release_query_route();
  inline void set_allocated_query_route(::notify::QueryRouteStateRsp* query_route);

  // optional .notify.InnerCheckRsp inner_check = 201;
  inline bool has_inner_check() const;
  inline void clear_inner_check();
  static const int kInnerCheckFieldNumber = 201;
  inline const ::notify::InnerCheckRsp& inner_check() const;
  inline ::notify::InnerCheckRsp* mutable_inner_check();
  inline ::notify::InnerCheckRsp* release_inner_check();
  inline void set_allocated_inner_check(::notify::InnerCheckRsp* inner_check);

  // @@protoc_insertion_point(class_scope:notify.NotifyResponse)
 private:
  inline void set_has_result();
  inline void clear_has_result();
  inline void set_has_reason();
  inline void clear_has_reason();
  inline void set_has_query_route();
  inline void clear_has_query_route();
  inline void set_has_inner_check();
  inline void clear_has_inner_check();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::std::string* reason_;
  ::notify::QueryRouteStateRsp* query_route_;
  ::notify::InnerCheckRsp* inner_check_;
  ::google::protobuf::int32 result_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();

  void InitAsDefaultInstance();
  static NotifyResponse* default_instance_;
};
// -------------------------------------------------------------------

class NotifyMessage : public ::google::protobuf::Message {
 public:
  NotifyMessage();
  virtual ~NotifyMessage();

  NotifyMessage(const NotifyMessage& from);

  inline NotifyMessage& operator=(const NotifyMessage& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const NotifyMessage& default_instance();

  void Swap(NotifyMessage* other);

  // implements Message ----------------------------------------------

  NotifyMessage* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const NotifyMessage& from);
  void MergeFrom(const NotifyMessage& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 type = 1;
  inline bool has_type() const;
  inline void clear_type();
  static const int kTypeFieldNumber = 1;
  inline ::google::protobuf::int32 type() const;
  inline void set_type(::google::protobuf::int32 value);

  // required string name = 2;
  inline bool has_name() const;
  inline void clear_name();
  static const int kNameFieldNumber = 2;
  inline const ::std::string& name() const;
  inline void set_name(const ::std::string& value);
  inline void set_name(const char* value);
  inline void set_name(const char* value, size_t size);
  inline ::std::string* mutable_name();
  inline ::std::string* release_name();
  inline void set_allocated_name(::std::string* name);

  // required int64 seq = 3;
  inline bool has_seq() const;
  inline void clear_seq();
  static const int kSeqFieldNumber = 3;
  inline ::google::protobuf::int64 seq() const;
  inline void set_seq(::google::protobuf::int64 value);

  // optional .notify.NotifyRequest request = 11;
  inline bool has_request() const;
  inline void clear_request();
  static const int kRequestFieldNumber = 11;
  inline const ::notify::NotifyRequest& request() const;
  inline ::notify::NotifyRequest* mutable_request();
  inline ::notify::NotifyRequest* release_request();
  inline void set_allocated_request(::notify::NotifyRequest* request);

  // optional .notify.NotifyResponse response = 12;
  inline bool has_response() const;
  inline void clear_response();
  static const int kResponseFieldNumber = 12;
  inline const ::notify::NotifyResponse& response() const;
  inline ::notify::NotifyResponse* mutable_response();
  inline ::notify::NotifyResponse* release_response();
  inline void set_allocated_response(::notify::NotifyResponse* response);

  // @@protoc_insertion_point(class_scope:notify.NotifyMessage)
 private:
  inline void set_has_type();
  inline void clear_has_type();
  inline void set_has_name();
  inline void clear_has_name();
  inline void set_has_seq();
  inline void clear_has_seq();
  inline void set_has_request();
  inline void clear_has_request();
  inline void set_has_response();
  inline void clear_has_response();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::std::string* name_;
  ::google::protobuf::int64 seq_;
  ::notify::NotifyRequest* request_;
  ::notify::NotifyResponse* response_;
  ::google::protobuf::int32 type_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();

  void InitAsDefaultInstance();
  static NotifyMessage* default_instance_;
};
// -------------------------------------------------------------------

class WatchEventMessage : public ::google::protobuf::Message {
 public:
  WatchEventMessage();
  virtual ~WatchEventMessage();

  WatchEventMessage(const WatchEventMessage& from);

  inline WatchEventMessage& operator=(const WatchEventMessage& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const WatchEventMessage& default_instance();

  void Swap(WatchEventMessage* other);

  // implements Message ----------------------------------------------

  WatchEventMessage* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const WatchEventMessage& from);
  void MergeFrom(const WatchEventMessage& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 event = 1;
  inline bool has_event() const;
  inline void clear_event();
  static const int kEventFieldNumber = 1;
  inline ::google::protobuf::int32 event() const;
  inline void set_event(::google::protobuf::int32 value);

  // required int64 timestamp = 2;
  inline bool has_timestamp() const;
  inline void clear_timestamp();
  static const int kTimestampFieldNumber = 2;
  inline ::google::protobuf::int64 timestamp() const;
  inline void set_timestamp(::google::protobuf::int64 value);

  // required .troute.server_name_and_address target = 3;
  inline bool has_target() const;
  inline void clear_target();
  static const int kTargetFieldNumber = 3;
  inline const ::troute::server_name_and_address& target() const;
  inline ::troute::server_name_and_address* mutable_target();
  inline ::troute::server_name_and_address* release_target();
  inline void set_allocated_target(::troute::server_name_and_address* target);

  // optional .troute.server_name_and_address trigger_addr = 11;
  inline bool has_trigger_addr() const;
  inline void clear_trigger_addr();
  static const int kTriggerAddrFieldNumber = 11;
  inline const ::troute::server_name_and_address& trigger_addr() const;
  inline ::troute::server_name_and_address* mutable_trigger_addr();
  inline ::troute::server_name_and_address* release_trigger_addr();
  inline void set_allocated_trigger_addr(::troute::server_name_and_address* trigger_addr);

  // @@protoc_insertion_point(class_scope:notify.WatchEventMessage)
 private:
  inline void set_has_event();
  inline void clear_has_event();
  inline void set_has_timestamp();
  inline void clear_has_timestamp();
  inline void set_has_target();
  inline void clear_has_target();
  inline void set_has_trigger_addr();
  inline void clear_has_trigger_addr();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::int64 timestamp_;
  ::troute::server_name_and_address* target_;
  ::troute::server_name_and_address* trigger_addr_;
  ::google::protobuf::int32 event_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto();

  void InitAsDefaultInstance();
  static WatchEventMessage* default_instance_;
};
// ===================================================================


// ===================================================================

// RegisterRouteAddrReq

// required .troute.server_name_and_address target = 1;
inline bool RegisterRouteAddrReq::has_target() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RegisterRouteAddrReq::set_has_target() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RegisterRouteAddrReq::clear_has_target() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RegisterRouteAddrReq::clear_target() {
  if (target_ != NULL) target_->::troute::server_name_and_address::Clear();
  clear_has_target();
}
inline const ::troute::server_name_and_address& RegisterRouteAddrReq::target() const {
  // @@protoc_insertion_point(field_get:notify.RegisterRouteAddrReq.target)
  return target_ != NULL ? *target_ : *default_instance_->target_;
}
inline ::troute::server_name_and_address* RegisterRouteAddrReq::mutable_target() {
  set_has_target();
  if (target_ == NULL) target_ = new ::troute::server_name_and_address;
  // @@protoc_insertion_point(field_mutable:notify.RegisterRouteAddrReq.target)
  return target_;
}
inline ::troute::server_name_and_address* RegisterRouteAddrReq::release_target() {
  clear_has_target();
  ::troute::server_name_and_address* temp = target_;
  target_ = NULL;
  return temp;
}
inline void RegisterRouteAddrReq::set_allocated_target(::troute::server_name_and_address* target) {
  delete target_;
  target_ = target;
  if (target) {
    set_has_target();
  } else {
    clear_has_target();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.RegisterRouteAddrReq.target)
}

// optional int32 check_interval = 2 [default = 3];
inline bool RegisterRouteAddrReq::has_check_interval() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void RegisterRouteAddrReq::set_has_check_interval() {
  _has_bits_[0] |= 0x00000002u;
}
inline void RegisterRouteAddrReq::clear_has_check_interval() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void RegisterRouteAddrReq::clear_check_interval() {
  check_interval_ = 3;
  clear_has_check_interval();
}
inline ::google::protobuf::int32 RegisterRouteAddrReq::check_interval() const {
  // @@protoc_insertion_point(field_get:notify.RegisterRouteAddrReq.check_interval)
  return check_interval_;
}
inline void RegisterRouteAddrReq::set_check_interval(::google::protobuf::int32 value) {
  set_has_check_interval();
  check_interval_ = value;
  // @@protoc_insertion_point(field_set:notify.RegisterRouteAddrReq.check_interval)
}

// -------------------------------------------------------------------

// UnRegisterRouteAddrReq

// required .troute.server_name_and_address target = 1;
inline bool UnRegisterRouteAddrReq::has_target() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void UnRegisterRouteAddrReq::set_has_target() {
  _has_bits_[0] |= 0x00000001u;
}
inline void UnRegisterRouteAddrReq::clear_has_target() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void UnRegisterRouteAddrReq::clear_target() {
  if (target_ != NULL) target_->::troute::server_name_and_address::Clear();
  clear_has_target();
}
inline const ::troute::server_name_and_address& UnRegisterRouteAddrReq::target() const {
  // @@protoc_insertion_point(field_get:notify.UnRegisterRouteAddrReq.target)
  return target_ != NULL ? *target_ : *default_instance_->target_;
}
inline ::troute::server_name_and_address* UnRegisterRouteAddrReq::mutable_target() {
  set_has_target();
  if (target_ == NULL) target_ = new ::troute::server_name_and_address;
  // @@protoc_insertion_point(field_mutable:notify.UnRegisterRouteAddrReq.target)
  return target_;
}
inline ::troute::server_name_and_address* UnRegisterRouteAddrReq::release_target() {
  clear_has_target();
  ::troute::server_name_and_address* temp = target_;
  target_ = NULL;
  return temp;
}
inline void UnRegisterRouteAddrReq::set_allocated_target(::troute::server_name_and_address* target) {
  delete target_;
  target_ = target;
  if (target) {
    set_has_target();
  } else {
    clear_has_target();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.UnRegisterRouteAddrReq.target)
}

// optional int32 force_un_reg = 2 [default = 0];
inline bool UnRegisterRouteAddrReq::has_force_un_reg() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void UnRegisterRouteAddrReq::set_has_force_un_reg() {
  _has_bits_[0] |= 0x00000002u;
}
inline void UnRegisterRouteAddrReq::clear_has_force_un_reg() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void UnRegisterRouteAddrReq::clear_force_un_reg() {
  force_un_reg_ = 0;
  clear_has_force_un_reg();
}
inline ::google::protobuf::int32 UnRegisterRouteAddrReq::force_un_reg() const {
  // @@protoc_insertion_point(field_get:notify.UnRegisterRouteAddrReq.force_un_reg)
  return force_un_reg_;
}
inline void UnRegisterRouteAddrReq::set_force_un_reg(::google::protobuf::int32 value) {
  set_has_force_un_reg();
  force_un_reg_ = value;
  // @@protoc_insertion_point(field_set:notify.UnRegisterRouteAddrReq.force_un_reg)
}

// -------------------------------------------------------------------

// DoRouteCheckReq

// required .troute.server_name_and_address target = 1;
inline bool DoRouteCheckReq::has_target() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void DoRouteCheckReq::set_has_target() {
  _has_bits_[0] |= 0x00000001u;
}
inline void DoRouteCheckReq::clear_has_target() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void DoRouteCheckReq::clear_target() {
  if (target_ != NULL) target_->::troute::server_name_and_address::Clear();
  clear_has_target();
}
inline const ::troute::server_name_and_address& DoRouteCheckReq::target() const {
  // @@protoc_insertion_point(field_get:notify.DoRouteCheckReq.target)
  return target_ != NULL ? *target_ : *default_instance_->target_;
}
inline ::troute::server_name_and_address* DoRouteCheckReq::mutable_target() {
  set_has_target();
  if (target_ == NULL) target_ = new ::troute::server_name_and_address;
  // @@protoc_insertion_point(field_mutable:notify.DoRouteCheckReq.target)
  return target_;
}
inline ::troute::server_name_and_address* DoRouteCheckReq::release_target() {
  clear_has_target();
  ::troute::server_name_and_address* temp = target_;
  target_ = NULL;
  return temp;
}
inline void DoRouteCheckReq::set_allocated_target(::troute::server_name_and_address* target) {
  delete target_;
  target_ = target;
  if (target) {
    set_has_target();
  } else {
    clear_has_target();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.DoRouteCheckReq.target)
}

// -------------------------------------------------------------------

// DoRouteRepairReq

// required .troute.server_name_and_address target = 1;
inline bool DoRouteRepairReq::has_target() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void DoRouteRepairReq::set_has_target() {
  _has_bits_[0] |= 0x00000001u;
}
inline void DoRouteRepairReq::clear_has_target() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void DoRouteRepairReq::clear_target() {
  if (target_ != NULL) target_->::troute::server_name_and_address::Clear();
  clear_has_target();
}
inline const ::troute::server_name_and_address& DoRouteRepairReq::target() const {
  // @@protoc_insertion_point(field_get:notify.DoRouteRepairReq.target)
  return target_ != NULL ? *target_ : *default_instance_->target_;
}
inline ::troute::server_name_and_address* DoRouteRepairReq::mutable_target() {
  set_has_target();
  if (target_ == NULL) target_ = new ::troute::server_name_and_address;
  // @@protoc_insertion_point(field_mutable:notify.DoRouteRepairReq.target)
  return target_;
}
inline ::troute::server_name_and_address* DoRouteRepairReq::release_target() {
  clear_has_target();
  ::troute::server_name_and_address* temp = target_;
  target_ = NULL;
  return temp;
}
inline void DoRouteRepairReq::set_allocated_target(::troute::server_name_and_address* target) {
  delete target_;
  target_ = target;
  if (target) {
    set_has_target();
  } else {
    clear_has_target();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.DoRouteRepairReq.target)
}

// optional .troute.server_name_and_address brother = 3;
inline bool DoRouteRepairReq::has_brother() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void DoRouteRepairReq::set_has_brother() {
  _has_bits_[0] |= 0x00000002u;
}
inline void DoRouteRepairReq::clear_has_brother() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void DoRouteRepairReq::clear_brother() {
  if (brother_ != NULL) brother_->::troute::server_name_and_address::Clear();
  clear_has_brother();
}
inline const ::troute::server_name_and_address& DoRouteRepairReq::brother() const {
  // @@protoc_insertion_point(field_get:notify.DoRouteRepairReq.brother)
  return brother_ != NULL ? *brother_ : *default_instance_->brother_;
}
inline ::troute::server_name_and_address* DoRouteRepairReq::mutable_brother() {
  set_has_brother();
  if (brother_ == NULL) brother_ = new ::troute::server_name_and_address;
  // @@protoc_insertion_point(field_mutable:notify.DoRouteRepairReq.brother)
  return brother_;
}
inline ::troute::server_name_and_address* DoRouteRepairReq::release_brother() {
  clear_has_brother();
  ::troute::server_name_and_address* temp = brother_;
  brother_ = NULL;
  return temp;
}
inline void DoRouteRepairReq::set_allocated_brother(::troute::server_name_and_address* brother) {
  delete brother_;
  brother_ = brother;
  if (brother) {
    set_has_brother();
  } else {
    clear_has_brother();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.DoRouteRepairReq.brother)
}

// -------------------------------------------------------------------

// DoRoutePickupReq

// required .troute.server_name_and_address target = 1;
inline bool DoRoutePickupReq::has_target() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void DoRoutePickupReq::set_has_target() {
  _has_bits_[0] |= 0x00000001u;
}
inline void DoRoutePickupReq::clear_has_target() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void DoRoutePickupReq::clear_target() {
  if (target_ != NULL) target_->::troute::server_name_and_address::Clear();
  clear_has_target();
}
inline const ::troute::server_name_and_address& DoRoutePickupReq::target() const {
  // @@protoc_insertion_point(field_get:notify.DoRoutePickupReq.target)
  return target_ != NULL ? *target_ : *default_instance_->target_;
}
inline ::troute::server_name_and_address* DoRoutePickupReq::mutable_target() {
  set_has_target();
  if (target_ == NULL) target_ = new ::troute::server_name_and_address;
  // @@protoc_insertion_point(field_mutable:notify.DoRoutePickupReq.target)
  return target_;
}
inline ::troute::server_name_and_address* DoRoutePickupReq::release_target() {
  clear_has_target();
  ::troute::server_name_and_address* temp = target_;
  target_ = NULL;
  return temp;
}
inline void DoRoutePickupReq::set_allocated_target(::troute::server_name_and_address* target) {
  delete target_;
  target_ = target;
  if (target) {
    set_has_target();
  } else {
    clear_has_target();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.DoRoutePickupReq.target)
}

// required int32 pickup_level = 2;
inline bool DoRoutePickupReq::has_pickup_level() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void DoRoutePickupReq::set_has_pickup_level() {
  _has_bits_[0] |= 0x00000002u;
}
inline void DoRoutePickupReq::clear_has_pickup_level() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void DoRoutePickupReq::clear_pickup_level() {
  pickup_level_ = 0;
  clear_has_pickup_level();
}
inline ::google::protobuf::int32 DoRoutePickupReq::pickup_level() const {
  // @@protoc_insertion_point(field_get:notify.DoRoutePickupReq.pickup_level)
  return pickup_level_;
}
inline void DoRoutePickupReq::set_pickup_level(::google::protobuf::int32 value) {
  set_has_pickup_level();
  pickup_level_ = value;
  // @@protoc_insertion_point(field_set:notify.DoRoutePickupReq.pickup_level)
}

// required int64 timestamp = 3;
inline bool DoRoutePickupReq::has_timestamp() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void DoRoutePickupReq::set_has_timestamp() {
  _has_bits_[0] |= 0x00000004u;
}
inline void DoRoutePickupReq::clear_has_timestamp() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void DoRoutePickupReq::clear_timestamp() {
  timestamp_ = GOOGLE_LONGLONG(0);
  clear_has_timestamp();
}
inline ::google::protobuf::int64 DoRoutePickupReq::timestamp() const {
  // @@protoc_insertion_point(field_get:notify.DoRoutePickupReq.timestamp)
  return timestamp_;
}
inline void DoRoutePickupReq::set_timestamp(::google::protobuf::int64 value) {
  set_has_timestamp();
  timestamp_ = value;
  // @@protoc_insertion_point(field_set:notify.DoRoutePickupReq.timestamp)
}

// -------------------------------------------------------------------

// ForceRouteNotifyReq

// required .troute.server_name_and_address target = 1;
inline bool ForceRouteNotifyReq::has_target() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ForceRouteNotifyReq::set_has_target() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ForceRouteNotifyReq::clear_has_target() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ForceRouteNotifyReq::clear_target() {
  if (target_ != NULL) target_->::troute::server_name_and_address::Clear();
  clear_has_target();
}
inline const ::troute::server_name_and_address& ForceRouteNotifyReq::target() const {
  // @@protoc_insertion_point(field_get:notify.ForceRouteNotifyReq.target)
  return target_ != NULL ? *target_ : *default_instance_->target_;
}
inline ::troute::server_name_and_address* ForceRouteNotifyReq::mutable_target() {
  set_has_target();
  if (target_ == NULL) target_ = new ::troute::server_name_and_address;
  // @@protoc_insertion_point(field_mutable:notify.ForceRouteNotifyReq.target)
  return target_;
}
inline ::troute::server_name_and_address* ForceRouteNotifyReq::release_target() {
  clear_has_target();
  ::troute::server_name_and_address* temp = target_;
  target_ = NULL;
  return temp;
}
inline void ForceRouteNotifyReq::set_allocated_target(::troute::server_name_and_address* target) {
  delete target_;
  target_ = target;
  if (target) {
    set_has_target();
  } else {
    clear_has_target();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.ForceRouteNotifyReq.target)
}

// required int32 notify_eve = 2;
inline bool ForceRouteNotifyReq::has_notify_eve() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ForceRouteNotifyReq::set_has_notify_eve() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ForceRouteNotifyReq::clear_has_notify_eve() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ForceRouteNotifyReq::clear_notify_eve() {
  notify_eve_ = 0;
  clear_has_notify_eve();
}
inline ::google::protobuf::int32 ForceRouteNotifyReq::notify_eve() const {
  // @@protoc_insertion_point(field_get:notify.ForceRouteNotifyReq.notify_eve)
  return notify_eve_;
}
inline void ForceRouteNotifyReq::set_notify_eve(::google::protobuf::int32 value) {
  set_has_notify_eve();
  notify_eve_ = value;
  // @@protoc_insertion_point(field_set:notify.ForceRouteNotifyReq.notify_eve)
}

// optional .troute.server_name_and_address_list called = 3;
inline bool ForceRouteNotifyReq::has_called() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ForceRouteNotifyReq::set_has_called() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ForceRouteNotifyReq::clear_has_called() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ForceRouteNotifyReq::clear_called() {
  if (called_ != NULL) called_->::troute::server_name_and_address_list::Clear();
  clear_has_called();
}
inline const ::troute::server_name_and_address_list& ForceRouteNotifyReq::called() const {
  // @@protoc_insertion_point(field_get:notify.ForceRouteNotifyReq.called)
  return called_ != NULL ? *called_ : *default_instance_->called_;
}
inline ::troute::server_name_and_address_list* ForceRouteNotifyReq::mutable_called() {
  set_has_called();
  if (called_ == NULL) called_ = new ::troute::server_name_and_address_list;
  // @@protoc_insertion_point(field_mutable:notify.ForceRouteNotifyReq.called)
  return called_;
}
inline ::troute::server_name_and_address_list* ForceRouteNotifyReq::release_called() {
  clear_has_called();
  ::troute::server_name_and_address_list* temp = called_;
  called_ = NULL;
  return temp;
}
inline void ForceRouteNotifyReq::set_allocated_called(::troute::server_name_and_address_list* called) {
  delete called_;
  called_ = called;
  if (called) {
    set_has_called();
  } else {
    clear_has_called();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.ForceRouteNotifyReq.called)
}

// optional .troute.server_name_and_address_list brother = 4;
inline bool ForceRouteNotifyReq::has_brother() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void ForceRouteNotifyReq::set_has_brother() {
  _has_bits_[0] |= 0x00000008u;
}
inline void ForceRouteNotifyReq::clear_has_brother() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void ForceRouteNotifyReq::clear_brother() {
  if (brother_ != NULL) brother_->::troute::server_name_and_address_list::Clear();
  clear_has_brother();
}
inline const ::troute::server_name_and_address_list& ForceRouteNotifyReq::brother() const {
  // @@protoc_insertion_point(field_get:notify.ForceRouteNotifyReq.brother)
  return brother_ != NULL ? *brother_ : *default_instance_->brother_;
}
inline ::troute::server_name_and_address_list* ForceRouteNotifyReq::mutable_brother() {
  set_has_brother();
  if (brother_ == NULL) brother_ = new ::troute::server_name_and_address_list;
  // @@protoc_insertion_point(field_mutable:notify.ForceRouteNotifyReq.brother)
  return brother_;
}
inline ::troute::server_name_and_address_list* ForceRouteNotifyReq::release_brother() {
  clear_has_brother();
  ::troute::server_name_and_address_list* temp = brother_;
  brother_ = NULL;
  return temp;
}
inline void ForceRouteNotifyReq::set_allocated_brother(::troute::server_name_and_address_list* brother) {
  delete brother_;
  brother_ = brother;
  if (brother) {
    set_has_brother();
  } else {
    clear_has_brother();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.ForceRouteNotifyReq.brother)
}

// optional .troute.server_name_and_address_list be_called = 5;
inline bool ForceRouteNotifyReq::has_be_called() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void ForceRouteNotifyReq::set_has_be_called() {
  _has_bits_[0] |= 0x00000010u;
}
inline void ForceRouteNotifyReq::clear_has_be_called() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void ForceRouteNotifyReq::clear_be_called() {
  if (be_called_ != NULL) be_called_->::troute::server_name_and_address_list::Clear();
  clear_has_be_called();
}
inline const ::troute::server_name_and_address_list& ForceRouteNotifyReq::be_called() const {
  // @@protoc_insertion_point(field_get:notify.ForceRouteNotifyReq.be_called)
  return be_called_ != NULL ? *be_called_ : *default_instance_->be_called_;
}
inline ::troute::server_name_and_address_list* ForceRouteNotifyReq::mutable_be_called() {
  set_has_be_called();
  if (be_called_ == NULL) be_called_ = new ::troute::server_name_and_address_list;
  // @@protoc_insertion_point(field_mutable:notify.ForceRouteNotifyReq.be_called)
  return be_called_;
}
inline ::troute::server_name_and_address_list* ForceRouteNotifyReq::release_be_called() {
  clear_has_be_called();
  ::troute::server_name_and_address_list* temp = be_called_;
  be_called_ = NULL;
  return temp;
}
inline void ForceRouteNotifyReq::set_allocated_be_called(::troute::server_name_and_address_list* be_called) {
  delete be_called_;
  be_called_ = be_called;
  if (be_called) {
    set_has_be_called();
  } else {
    clear_has_be_called();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.ForceRouteNotifyReq.be_called)
}

// -------------------------------------------------------------------

// QueryRouteStateReq

// required .troute.server_name_and_address target = 1;
inline bool QueryRouteStateReq::has_target() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void QueryRouteStateReq::set_has_target() {
  _has_bits_[0] |= 0x00000001u;
}
inline void QueryRouteStateReq::clear_has_target() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void QueryRouteStateReq::clear_target() {
  if (target_ != NULL) target_->::troute::server_name_and_address::Clear();
  clear_has_target();
}
inline const ::troute::server_name_and_address& QueryRouteStateReq::target() const {
  // @@protoc_insertion_point(field_get:notify.QueryRouteStateReq.target)
  return target_ != NULL ? *target_ : *default_instance_->target_;
}
inline ::troute::server_name_and_address* QueryRouteStateReq::mutable_target() {
  set_has_target();
  if (target_ == NULL) target_ = new ::troute::server_name_and_address;
  // @@protoc_insertion_point(field_mutable:notify.QueryRouteStateReq.target)
  return target_;
}
inline ::troute::server_name_and_address* QueryRouteStateReq::release_target() {
  clear_has_target();
  ::troute::server_name_and_address* temp = target_;
  target_ = NULL;
  return temp;
}
inline void QueryRouteStateReq::set_allocated_target(::troute::server_name_and_address* target) {
  delete target_;
  target_ = target;
  if (target) {
    set_has_target();
  } else {
    clear_has_target();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.QueryRouteStateReq.target)
}

// -------------------------------------------------------------------

// WatchFilterItem

// required int32 type = 1;
inline bool WatchFilterItem::has_type() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void WatchFilterItem::set_has_type() {
  _has_bits_[0] |= 0x00000001u;
}
inline void WatchFilterItem::clear_has_type() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void WatchFilterItem::clear_type() {
  type_ = 0;
  clear_has_type();
}
inline ::google::protobuf::int32 WatchFilterItem::type() const {
  // @@protoc_insertion_point(field_get:notify.WatchFilterItem.type)
  return type_;
}
inline void WatchFilterItem::set_type(::google::protobuf::int32 value) {
  set_has_type();
  type_ = value;
  // @@protoc_insertion_point(field_set:notify.WatchFilterItem.type)
}

// required .troute.server_name_and_address addr = 2;
inline bool WatchFilterItem::has_addr() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void WatchFilterItem::set_has_addr() {
  _has_bits_[0] |= 0x00000002u;
}
inline void WatchFilterItem::clear_has_addr() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void WatchFilterItem::clear_addr() {
  if (addr_ != NULL) addr_->::troute::server_name_and_address::Clear();
  clear_has_addr();
}
inline const ::troute::server_name_and_address& WatchFilterItem::addr() const {
  // @@protoc_insertion_point(field_get:notify.WatchFilterItem.addr)
  return addr_ != NULL ? *addr_ : *default_instance_->addr_;
}
inline ::troute::server_name_and_address* WatchFilterItem::mutable_addr() {
  set_has_addr();
  if (addr_ == NULL) addr_ = new ::troute::server_name_and_address;
  // @@protoc_insertion_point(field_mutable:notify.WatchFilterItem.addr)
  return addr_;
}
inline ::troute::server_name_and_address* WatchFilterItem::release_addr() {
  clear_has_addr();
  ::troute::server_name_and_address* temp = addr_;
  addr_ = NULL;
  return temp;
}
inline void WatchFilterItem::set_allocated_addr(::troute::server_name_and_address* addr) {
  delete addr_;
  addr_ = addr;
  if (addr) {
    set_has_addr();
  } else {
    clear_has_addr();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.WatchFilterItem.addr)
}

// required int32 notify_eve = 3;
inline bool WatchFilterItem::has_notify_eve() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void WatchFilterItem::set_has_notify_eve() {
  _has_bits_[0] |= 0x00000004u;
}
inline void WatchFilterItem::clear_has_notify_eve() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void WatchFilterItem::clear_notify_eve() {
  notify_eve_ = 0;
  clear_has_notify_eve();
}
inline ::google::protobuf::int32 WatchFilterItem::notify_eve() const {
  // @@protoc_insertion_point(field_get:notify.WatchFilterItem.notify_eve)
  return notify_eve_;
}
inline void WatchFilterItem::set_notify_eve(::google::protobuf::int32 value) {
  set_has_notify_eve();
  notify_eve_ = value;
  // @@protoc_insertion_point(field_set:notify.WatchFilterItem.notify_eve)
}

// -------------------------------------------------------------------

// WatchFilters

// repeated .notify.WatchFilterItem items = 1;
inline int WatchFilters::items_size() const {
  return items_.size();
}
inline void WatchFilters::clear_items() {
  items_.Clear();
}
inline const ::notify::WatchFilterItem& WatchFilters::items(int index) const {
  // @@protoc_insertion_point(field_get:notify.WatchFilters.items)
  return items_.Get(index);
}
inline ::notify::WatchFilterItem* WatchFilters::mutable_items(int index) {
  // @@protoc_insertion_point(field_mutable:notify.WatchFilters.items)
  return items_.Mutable(index);
}
inline ::notify::WatchFilterItem* WatchFilters::add_items() {
  // @@protoc_insertion_point(field_add:notify.WatchFilters.items)
  return items_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::notify::WatchFilterItem >&
WatchFilters::items() const {
  // @@protoc_insertion_point(field_list:notify.WatchFilters.items)
  return items_;
}
inline ::google::protobuf::RepeatedPtrField< ::notify::WatchFilterItem >*
WatchFilters::mutable_items() {
  // @@protoc_insertion_point(field_mutable_list:notify.WatchFilters.items)
  return &items_;
}

// -------------------------------------------------------------------

// WatchRouteNotifyReq

// required .troute.server_name_and_address watcher_addr = 1;
inline bool WatchRouteNotifyReq::has_watcher_addr() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void WatchRouteNotifyReq::set_has_watcher_addr() {
  _has_bits_[0] |= 0x00000001u;
}
inline void WatchRouteNotifyReq::clear_has_watcher_addr() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void WatchRouteNotifyReq::clear_watcher_addr() {
  if (watcher_addr_ != NULL) watcher_addr_->::troute::server_name_and_address::Clear();
  clear_has_watcher_addr();
}
inline const ::troute::server_name_and_address& WatchRouteNotifyReq::watcher_addr() const {
  // @@protoc_insertion_point(field_get:notify.WatchRouteNotifyReq.watcher_addr)
  return watcher_addr_ != NULL ? *watcher_addr_ : *default_instance_->watcher_addr_;
}
inline ::troute::server_name_and_address* WatchRouteNotifyReq::mutable_watcher_addr() {
  set_has_watcher_addr();
  if (watcher_addr_ == NULL) watcher_addr_ = new ::troute::server_name_and_address;
  // @@protoc_insertion_point(field_mutable:notify.WatchRouteNotifyReq.watcher_addr)
  return watcher_addr_;
}
inline ::troute::server_name_and_address* WatchRouteNotifyReq::release_watcher_addr() {
  clear_has_watcher_addr();
  ::troute::server_name_and_address* temp = watcher_addr_;
  watcher_addr_ = NULL;
  return temp;
}
inline void WatchRouteNotifyReq::set_allocated_watcher_addr(::troute::server_name_and_address* watcher_addr) {
  delete watcher_addr_;
  watcher_addr_ = watcher_addr;
  if (watcher_addr) {
    set_has_watcher_addr();
  } else {
    clear_has_watcher_addr();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.WatchRouteNotifyReq.watcher_addr)
}

// required .notify.WatchFilters filters = 2;
inline bool WatchRouteNotifyReq::has_filters() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void WatchRouteNotifyReq::set_has_filters() {
  _has_bits_[0] |= 0x00000002u;
}
inline void WatchRouteNotifyReq::clear_has_filters() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void WatchRouteNotifyReq::clear_filters() {
  if (filters_ != NULL) filters_->::notify::WatchFilters::Clear();
  clear_has_filters();
}
inline const ::notify::WatchFilters& WatchRouteNotifyReq::filters() const {
  // @@protoc_insertion_point(field_get:notify.WatchRouteNotifyReq.filters)
  return filters_ != NULL ? *filters_ : *default_instance_->filters_;
}
inline ::notify::WatchFilters* WatchRouteNotifyReq::mutable_filters() {
  set_has_filters();
  if (filters_ == NULL) filters_ = new ::notify::WatchFilters;
  // @@protoc_insertion_point(field_mutable:notify.WatchRouteNotifyReq.filters)
  return filters_;
}
inline ::notify::WatchFilters* WatchRouteNotifyReq::release_filters() {
  clear_has_filters();
  ::notify::WatchFilters* temp = filters_;
  filters_ = NULL;
  return temp;
}
inline void WatchRouteNotifyReq::set_allocated_filters(::notify::WatchFilters* filters) {
  delete filters_;
  filters_ = filters;
  if (filters) {
    set_has_filters();
  } else {
    clear_has_filters();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.WatchRouteNotifyReq.filters)
}

// -------------------------------------------------------------------

// UnWatchRouteNotifyReq

// required .troute.server_name_and_address watcher_addr = 1;
inline bool UnWatchRouteNotifyReq::has_watcher_addr() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void UnWatchRouteNotifyReq::set_has_watcher_addr() {
  _has_bits_[0] |= 0x00000001u;
}
inline void UnWatchRouteNotifyReq::clear_has_watcher_addr() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void UnWatchRouteNotifyReq::clear_watcher_addr() {
  if (watcher_addr_ != NULL) watcher_addr_->::troute::server_name_and_address::Clear();
  clear_has_watcher_addr();
}
inline const ::troute::server_name_and_address& UnWatchRouteNotifyReq::watcher_addr() const {
  // @@protoc_insertion_point(field_get:notify.UnWatchRouteNotifyReq.watcher_addr)
  return watcher_addr_ != NULL ? *watcher_addr_ : *default_instance_->watcher_addr_;
}
inline ::troute::server_name_and_address* UnWatchRouteNotifyReq::mutable_watcher_addr() {
  set_has_watcher_addr();
  if (watcher_addr_ == NULL) watcher_addr_ = new ::troute::server_name_and_address;
  // @@protoc_insertion_point(field_mutable:notify.UnWatchRouteNotifyReq.watcher_addr)
  return watcher_addr_;
}
inline ::troute::server_name_and_address* UnWatchRouteNotifyReq::release_watcher_addr() {
  clear_has_watcher_addr();
  ::troute::server_name_and_address* temp = watcher_addr_;
  watcher_addr_ = NULL;
  return temp;
}
inline void UnWatchRouteNotifyReq::set_allocated_watcher_addr(::troute::server_name_and_address* watcher_addr) {
  delete watcher_addr_;
  watcher_addr_ = watcher_addr;
  if (watcher_addr) {
    set_has_watcher_addr();
  } else {
    clear_has_watcher_addr();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.UnWatchRouteNotifyReq.watcher_addr)
}

// -------------------------------------------------------------------

// QueryRouteStateRsp

// required int32 route_state = 1;
inline bool QueryRouteStateRsp::has_route_state() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void QueryRouteStateRsp::set_has_route_state() {
  _has_bits_[0] |= 0x00000001u;
}
inline void QueryRouteStateRsp::clear_has_route_state() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void QueryRouteStateRsp::clear_route_state() {
  route_state_ = 0;
  clear_has_route_state();
}
inline ::google::protobuf::int32 QueryRouteStateRsp::route_state() const {
  // @@protoc_insertion_point(field_get:notify.QueryRouteStateRsp.route_state)
  return route_state_;
}
inline void QueryRouteStateRsp::set_route_state(::google::protobuf::int32 value) {
  set_has_route_state();
  route_state_ = value;
  // @@protoc_insertion_point(field_set:notify.QueryRouteStateRsp.route_state)
}

// required int64 c_timestamp = 2;
inline bool QueryRouteStateRsp::has_c_timestamp() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void QueryRouteStateRsp::set_has_c_timestamp() {
  _has_bits_[0] |= 0x00000002u;
}
inline void QueryRouteStateRsp::clear_has_c_timestamp() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void QueryRouteStateRsp::clear_c_timestamp() {
  c_timestamp_ = GOOGLE_LONGLONG(0);
  clear_has_c_timestamp();
}
inline ::google::protobuf::int64 QueryRouteStateRsp::c_timestamp() const {
  // @@protoc_insertion_point(field_get:notify.QueryRouteStateRsp.c_timestamp)
  return c_timestamp_;
}
inline void QueryRouteStateRsp::set_c_timestamp(::google::protobuf::int64 value) {
  set_has_c_timestamp();
  c_timestamp_ = value;
  // @@protoc_insertion_point(field_set:notify.QueryRouteStateRsp.c_timestamp)
}

// required int64 m_timestamp = 3;
inline bool QueryRouteStateRsp::has_m_timestamp() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void QueryRouteStateRsp::set_has_m_timestamp() {
  _has_bits_[0] |= 0x00000004u;
}
inline void QueryRouteStateRsp::clear_has_m_timestamp() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void QueryRouteStateRsp::clear_m_timestamp() {
  m_timestamp_ = GOOGLE_LONGLONG(0);
  clear_has_m_timestamp();
}
inline ::google::protobuf::int64 QueryRouteStateRsp::m_timestamp() const {
  // @@protoc_insertion_point(field_get:notify.QueryRouteStateRsp.m_timestamp)
  return m_timestamp_;
}
inline void QueryRouteStateRsp::set_m_timestamp(::google::protobuf::int64 value) {
  set_has_m_timestamp();
  m_timestamp_ = value;
  // @@protoc_insertion_point(field_set:notify.QueryRouteStateRsp.m_timestamp)
}

// -------------------------------------------------------------------

// InnerNotifyReq

// required int32 cmd = 1;
inline bool InnerNotifyReq::has_cmd() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void InnerNotifyReq::set_has_cmd() {
  _has_bits_[0] |= 0x00000001u;
}
inline void InnerNotifyReq::clear_has_cmd() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void InnerNotifyReq::clear_cmd() {
  cmd_ = 0;
  clear_has_cmd();
}
inline ::google::protobuf::int32 InnerNotifyReq::cmd() const {
  // @@protoc_insertion_point(field_get:notify.InnerNotifyReq.cmd)
  return cmd_;
}
inline void InnerNotifyReq::set_cmd(::google::protobuf::int32 value) {
  set_has_cmd();
  cmd_ = value;
  // @@protoc_insertion_point(field_set:notify.InnerNotifyReq.cmd)
}

// required .troute.server_name_and_address src = 2;
inline bool InnerNotifyReq::has_src() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void InnerNotifyReq::set_has_src() {
  _has_bits_[0] |= 0x00000002u;
}
inline void InnerNotifyReq::clear_has_src() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void InnerNotifyReq::clear_src() {
  if (src_ != NULL) src_->::troute::server_name_and_address::Clear();
  clear_has_src();
}
inline const ::troute::server_name_and_address& InnerNotifyReq::src() const {
  // @@protoc_insertion_point(field_get:notify.InnerNotifyReq.src)
  return src_ != NULL ? *src_ : *default_instance_->src_;
}
inline ::troute::server_name_and_address* InnerNotifyReq::mutable_src() {
  set_has_src();
  if (src_ == NULL) src_ = new ::troute::server_name_and_address;
  // @@protoc_insertion_point(field_mutable:notify.InnerNotifyReq.src)
  return src_;
}
inline ::troute::server_name_and_address* InnerNotifyReq::release_src() {
  clear_has_src();
  ::troute::server_name_and_address* temp = src_;
  src_ = NULL;
  return temp;
}
inline void InnerNotifyReq::set_allocated_src(::troute::server_name_and_address* src) {
  delete src_;
  src_ = src;
  if (src) {
    set_has_src();
  } else {
    clear_has_src();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.InnerNotifyReq.src)
}

// required .troute.server_name_and_address target = 3;
inline bool InnerNotifyReq::has_target() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void InnerNotifyReq::set_has_target() {
  _has_bits_[0] |= 0x00000004u;
}
inline void InnerNotifyReq::clear_has_target() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void InnerNotifyReq::clear_target() {
  if (target_ != NULL) target_->::troute::server_name_and_address::Clear();
  clear_has_target();
}
inline const ::troute::server_name_and_address& InnerNotifyReq::target() const {
  // @@protoc_insertion_point(field_get:notify.InnerNotifyReq.target)
  return target_ != NULL ? *target_ : *default_instance_->target_;
}
inline ::troute::server_name_and_address* InnerNotifyReq::mutable_target() {
  set_has_target();
  if (target_ == NULL) target_ = new ::troute::server_name_and_address;
  // @@protoc_insertion_point(field_mutable:notify.InnerNotifyReq.target)
  return target_;
}
inline ::troute::server_name_and_address* InnerNotifyReq::release_target() {
  clear_has_target();
  ::troute::server_name_and_address* temp = target_;
  target_ = NULL;
  return temp;
}
inline void InnerNotifyReq::set_allocated_target(::troute::server_name_and_address* target) {
  delete target_;
  target_ = target;
  if (target) {
    set_has_target();
  } else {
    clear_has_target();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.InnerNotifyReq.target)
}

// -------------------------------------------------------------------

// InnerCheckReq

// required .troute.server_name_and_address target = 1;
inline bool InnerCheckReq::has_target() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void InnerCheckReq::set_has_target() {
  _has_bits_[0] |= 0x00000001u;
}
inline void InnerCheckReq::clear_has_target() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void InnerCheckReq::clear_target() {
  if (target_ != NULL) target_->::troute::server_name_and_address::Clear();
  clear_has_target();
}
inline const ::troute::server_name_and_address& InnerCheckReq::target() const {
  // @@protoc_insertion_point(field_get:notify.InnerCheckReq.target)
  return target_ != NULL ? *target_ : *default_instance_->target_;
}
inline ::troute::server_name_and_address* InnerCheckReq::mutable_target() {
  set_has_target();
  if (target_ == NULL) target_ = new ::troute::server_name_and_address;
  // @@protoc_insertion_point(field_mutable:notify.InnerCheckReq.target)
  return target_;
}
inline ::troute::server_name_and_address* InnerCheckReq::release_target() {
  clear_has_target();
  ::troute::server_name_and_address* temp = target_;
  target_ = NULL;
  return temp;
}
inline void InnerCheckReq::set_allocated_target(::troute::server_name_and_address* target) {
  delete target_;
  target_ = target;
  if (target) {
    set_has_target();
  } else {
    clear_has_target();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.InnerCheckReq.target)
}

// required int32 addr_num = 2;
inline bool InnerCheckReq::has_addr_num() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void InnerCheckReq::set_has_addr_num() {
  _has_bits_[0] |= 0x00000002u;
}
inline void InnerCheckReq::clear_has_addr_num() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void InnerCheckReq::clear_addr_num() {
  addr_num_ = 0;
  clear_has_addr_num();
}
inline ::google::protobuf::int32 InnerCheckReq::addr_num() const {
  // @@protoc_insertion_point(field_get:notify.InnerCheckReq.addr_num)
  return addr_num_;
}
inline void InnerCheckReq::set_addr_num(::google::protobuf::int32 value) {
  set_has_addr_num();
  addr_num_ = value;
  // @@protoc_insertion_point(field_set:notify.InnerCheckReq.addr_num)
}

// required string trait_md5 = 3;
inline bool InnerCheckReq::has_trait_md5() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void InnerCheckReq::set_has_trait_md5() {
  _has_bits_[0] |= 0x00000004u;
}
inline void InnerCheckReq::clear_has_trait_md5() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void InnerCheckReq::clear_trait_md5() {
  if (trait_md5_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    trait_md5_->clear();
  }
  clear_has_trait_md5();
}
inline const ::std::string& InnerCheckReq::trait_md5() const {
  // @@protoc_insertion_point(field_get:notify.InnerCheckReq.trait_md5)
  return *trait_md5_;
}
inline void InnerCheckReq::set_trait_md5(const ::std::string& value) {
  set_has_trait_md5();
  if (trait_md5_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    trait_md5_ = new ::std::string;
  }
  trait_md5_->assign(value);
  // @@protoc_insertion_point(field_set:notify.InnerCheckReq.trait_md5)
}
inline void InnerCheckReq::set_trait_md5(const char* value) {
  set_has_trait_md5();
  if (trait_md5_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    trait_md5_ = new ::std::string;
  }
  trait_md5_->assign(value);
  // @@protoc_insertion_point(field_set_char:notify.InnerCheckReq.trait_md5)
}
inline void InnerCheckReq::set_trait_md5(const char* value, size_t size) {
  set_has_trait_md5();
  if (trait_md5_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    trait_md5_ = new ::std::string;
  }
  trait_md5_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:notify.InnerCheckReq.trait_md5)
}
inline ::std::string* InnerCheckReq::mutable_trait_md5() {
  set_has_trait_md5();
  if (trait_md5_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    trait_md5_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:notify.InnerCheckReq.trait_md5)
  return trait_md5_;
}
inline ::std::string* InnerCheckReq::release_trait_md5() {
  clear_has_trait_md5();
  if (trait_md5_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = trait_md5_;
    trait_md5_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void InnerCheckReq::set_allocated_trait_md5(::std::string* trait_md5) {
  if (trait_md5_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete trait_md5_;
  }
  if (trait_md5) {
    set_has_trait_md5();
    trait_md5_ = trait_md5;
  } else {
    clear_has_trait_md5();
    trait_md5_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:notify.InnerCheckReq.trait_md5)
}

// -------------------------------------------------------------------

// InnerRepairReq

// required .troute.server_name_and_address target = 1;
inline bool InnerRepairReq::has_target() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void InnerRepairReq::set_has_target() {
  _has_bits_[0] |= 0x00000001u;
}
inline void InnerRepairReq::clear_has_target() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void InnerRepairReq::clear_target() {
  if (target_ != NULL) target_->::troute::server_name_and_address::Clear();
  clear_has_target();
}
inline const ::troute::server_name_and_address& InnerRepairReq::target() const {
  // @@protoc_insertion_point(field_get:notify.InnerRepairReq.target)
  return target_ != NULL ? *target_ : *default_instance_->target_;
}
inline ::troute::server_name_and_address* InnerRepairReq::mutable_target() {
  set_has_target();
  if (target_ == NULL) target_ = new ::troute::server_name_and_address;
  // @@protoc_insertion_point(field_mutable:notify.InnerRepairReq.target)
  return target_;
}
inline ::troute::server_name_and_address* InnerRepairReq::release_target() {
  clear_has_target();
  ::troute::server_name_and_address* temp = target_;
  target_ = NULL;
  return temp;
}
inline void InnerRepairReq::set_allocated_target(::troute::server_name_and_address* target) {
  delete target_;
  target_ = target;
  if (target) {
    set_has_target();
  } else {
    clear_has_target();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.InnerRepairReq.target)
}

// optional .troute.server_name_and_address brother = 2;
inline bool InnerRepairReq::has_brother() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void InnerRepairReq::set_has_brother() {
  _has_bits_[0] |= 0x00000002u;
}
inline void InnerRepairReq::clear_has_brother() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void InnerRepairReq::clear_brother() {
  if (brother_ != NULL) brother_->::troute::server_name_and_address::Clear();
  clear_has_brother();
}
inline const ::troute::server_name_and_address& InnerRepairReq::brother() const {
  // @@protoc_insertion_point(field_get:notify.InnerRepairReq.brother)
  return brother_ != NULL ? *brother_ : *default_instance_->brother_;
}
inline ::troute::server_name_and_address* InnerRepairReq::mutable_brother() {
  set_has_brother();
  if (brother_ == NULL) brother_ = new ::troute::server_name_and_address;
  // @@protoc_insertion_point(field_mutable:notify.InnerRepairReq.brother)
  return brother_;
}
inline ::troute::server_name_and_address* InnerRepairReq::release_brother() {
  clear_has_brother();
  ::troute::server_name_and_address* temp = brother_;
  brother_ = NULL;
  return temp;
}
inline void InnerRepairReq::set_allocated_brother(::troute::server_name_and_address* brother) {
  delete brother_;
  brother_ = brother;
  if (brother) {
    set_has_brother();
  } else {
    clear_has_brother();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.InnerRepairReq.brother)
}

// -------------------------------------------------------------------

// InnerCheckRsp

// optional .troute.server_name_and_address_list trait_detail = 1;
inline bool InnerCheckRsp::has_trait_detail() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void InnerCheckRsp::set_has_trait_detail() {
  _has_bits_[0] |= 0x00000001u;
}
inline void InnerCheckRsp::clear_has_trait_detail() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void InnerCheckRsp::clear_trait_detail() {
  if (trait_detail_ != NULL) trait_detail_->::troute::server_name_and_address_list::Clear();
  clear_has_trait_detail();
}
inline const ::troute::server_name_and_address_list& InnerCheckRsp::trait_detail() const {
  // @@protoc_insertion_point(field_get:notify.InnerCheckRsp.trait_detail)
  return trait_detail_ != NULL ? *trait_detail_ : *default_instance_->trait_detail_;
}
inline ::troute::server_name_and_address_list* InnerCheckRsp::mutable_trait_detail() {
  set_has_trait_detail();
  if (trait_detail_ == NULL) trait_detail_ = new ::troute::server_name_and_address_list;
  // @@protoc_insertion_point(field_mutable:notify.InnerCheckRsp.trait_detail)
  return trait_detail_;
}
inline ::troute::server_name_and_address_list* InnerCheckRsp::release_trait_detail() {
  clear_has_trait_detail();
  ::troute::server_name_and_address_list* temp = trait_detail_;
  trait_detail_ = NULL;
  return temp;
}
inline void InnerCheckRsp::set_allocated_trait_detail(::troute::server_name_and_address_list* trait_detail) {
  delete trait_detail_;
  trait_detail_ = trait_detail;
  if (trait_detail) {
    set_has_trait_detail();
  } else {
    clear_has_trait_detail();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.InnerCheckRsp.trait_detail)
}

// -------------------------------------------------------------------

// NotifyRequest

// required int32 cmd = 1;
inline bool NotifyRequest::has_cmd() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void NotifyRequest::set_has_cmd() {
  _has_bits_[0] |= 0x00000001u;
}
inline void NotifyRequest::clear_has_cmd() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void NotifyRequest::clear_cmd() {
  cmd_ = 0;
  clear_has_cmd();
}
inline ::google::protobuf::int32 NotifyRequest::cmd() const {
  // @@protoc_insertion_point(field_get:notify.NotifyRequest.cmd)
  return cmd_;
}
inline void NotifyRequest::set_cmd(::google::protobuf::int32 value) {
  set_has_cmd();
  cmd_ = value;
  // @@protoc_insertion_point(field_set:notify.NotifyRequest.cmd)
}

// optional .notify.RegisterRouteAddrReq register_addr = 101;
inline bool NotifyRequest::has_register_addr() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void NotifyRequest::set_has_register_addr() {
  _has_bits_[0] |= 0x00000002u;
}
inline void NotifyRequest::clear_has_register_addr() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void NotifyRequest::clear_register_addr() {
  if (register_addr_ != NULL) register_addr_->::notify::RegisterRouteAddrReq::Clear();
  clear_has_register_addr();
}
inline const ::notify::RegisterRouteAddrReq& NotifyRequest::register_addr() const {
  // @@protoc_insertion_point(field_get:notify.NotifyRequest.register_addr)
  return register_addr_ != NULL ? *register_addr_ : *default_instance_->register_addr_;
}
inline ::notify::RegisterRouteAddrReq* NotifyRequest::mutable_register_addr() {
  set_has_register_addr();
  if (register_addr_ == NULL) register_addr_ = new ::notify::RegisterRouteAddrReq;
  // @@protoc_insertion_point(field_mutable:notify.NotifyRequest.register_addr)
  return register_addr_;
}
inline ::notify::RegisterRouteAddrReq* NotifyRequest::release_register_addr() {
  clear_has_register_addr();
  ::notify::RegisterRouteAddrReq* temp = register_addr_;
  register_addr_ = NULL;
  return temp;
}
inline void NotifyRequest::set_allocated_register_addr(::notify::RegisterRouteAddrReq* register_addr) {
  delete register_addr_;
  register_addr_ = register_addr;
  if (register_addr) {
    set_has_register_addr();
  } else {
    clear_has_register_addr();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.NotifyRequest.register_addr)
}

// optional .notify.UnRegisterRouteAddrReq un_register_addr = 102;
inline bool NotifyRequest::has_un_register_addr() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void NotifyRequest::set_has_un_register_addr() {
  _has_bits_[0] |= 0x00000004u;
}
inline void NotifyRequest::clear_has_un_register_addr() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void NotifyRequest::clear_un_register_addr() {
  if (un_register_addr_ != NULL) un_register_addr_->::notify::UnRegisterRouteAddrReq::Clear();
  clear_has_un_register_addr();
}
inline const ::notify::UnRegisterRouteAddrReq& NotifyRequest::un_register_addr() const {
  // @@protoc_insertion_point(field_get:notify.NotifyRequest.un_register_addr)
  return un_register_addr_ != NULL ? *un_register_addr_ : *default_instance_->un_register_addr_;
}
inline ::notify::UnRegisterRouteAddrReq* NotifyRequest::mutable_un_register_addr() {
  set_has_un_register_addr();
  if (un_register_addr_ == NULL) un_register_addr_ = new ::notify::UnRegisterRouteAddrReq;
  // @@protoc_insertion_point(field_mutable:notify.NotifyRequest.un_register_addr)
  return un_register_addr_;
}
inline ::notify::UnRegisterRouteAddrReq* NotifyRequest::release_un_register_addr() {
  clear_has_un_register_addr();
  ::notify::UnRegisterRouteAddrReq* temp = un_register_addr_;
  un_register_addr_ = NULL;
  return temp;
}
inline void NotifyRequest::set_allocated_un_register_addr(::notify::UnRegisterRouteAddrReq* un_register_addr) {
  delete un_register_addr_;
  un_register_addr_ = un_register_addr;
  if (un_register_addr) {
    set_has_un_register_addr();
  } else {
    clear_has_un_register_addr();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.NotifyRequest.un_register_addr)
}

// optional .notify.DoRouteCheckReq do_check = 111;
inline bool NotifyRequest::has_do_check() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void NotifyRequest::set_has_do_check() {
  _has_bits_[0] |= 0x00000008u;
}
inline void NotifyRequest::clear_has_do_check() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void NotifyRequest::clear_do_check() {
  if (do_check_ != NULL) do_check_->::notify::DoRouteCheckReq::Clear();
  clear_has_do_check();
}
inline const ::notify::DoRouteCheckReq& NotifyRequest::do_check() const {
  // @@protoc_insertion_point(field_get:notify.NotifyRequest.do_check)
  return do_check_ != NULL ? *do_check_ : *default_instance_->do_check_;
}
inline ::notify::DoRouteCheckReq* NotifyRequest::mutable_do_check() {
  set_has_do_check();
  if (do_check_ == NULL) do_check_ = new ::notify::DoRouteCheckReq;
  // @@protoc_insertion_point(field_mutable:notify.NotifyRequest.do_check)
  return do_check_;
}
inline ::notify::DoRouteCheckReq* NotifyRequest::release_do_check() {
  clear_has_do_check();
  ::notify::DoRouteCheckReq* temp = do_check_;
  do_check_ = NULL;
  return temp;
}
inline void NotifyRequest::set_allocated_do_check(::notify::DoRouteCheckReq* do_check) {
  delete do_check_;
  do_check_ = do_check;
  if (do_check) {
    set_has_do_check();
  } else {
    clear_has_do_check();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.NotifyRequest.do_check)
}

// optional .notify.DoRouteRepairReq do_repair = 112;
inline bool NotifyRequest::has_do_repair() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void NotifyRequest::set_has_do_repair() {
  _has_bits_[0] |= 0x00000010u;
}
inline void NotifyRequest::clear_has_do_repair() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void NotifyRequest::clear_do_repair() {
  if (do_repair_ != NULL) do_repair_->::notify::DoRouteRepairReq::Clear();
  clear_has_do_repair();
}
inline const ::notify::DoRouteRepairReq& NotifyRequest::do_repair() const {
  // @@protoc_insertion_point(field_get:notify.NotifyRequest.do_repair)
  return do_repair_ != NULL ? *do_repair_ : *default_instance_->do_repair_;
}
inline ::notify::DoRouteRepairReq* NotifyRequest::mutable_do_repair() {
  set_has_do_repair();
  if (do_repair_ == NULL) do_repair_ = new ::notify::DoRouteRepairReq;
  // @@protoc_insertion_point(field_mutable:notify.NotifyRequest.do_repair)
  return do_repair_;
}
inline ::notify::DoRouteRepairReq* NotifyRequest::release_do_repair() {
  clear_has_do_repair();
  ::notify::DoRouteRepairReq* temp = do_repair_;
  do_repair_ = NULL;
  return temp;
}
inline void NotifyRequest::set_allocated_do_repair(::notify::DoRouteRepairReq* do_repair) {
  delete do_repair_;
  do_repair_ = do_repair;
  if (do_repair) {
    set_has_do_repair();
  } else {
    clear_has_do_repair();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.NotifyRequest.do_repair)
}

// optional .notify.DoRoutePickupReq do_pickup = 113;
inline bool NotifyRequest::has_do_pickup() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void NotifyRequest::set_has_do_pickup() {
  _has_bits_[0] |= 0x00000020u;
}
inline void NotifyRequest::clear_has_do_pickup() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void NotifyRequest::clear_do_pickup() {
  if (do_pickup_ != NULL) do_pickup_->::notify::DoRoutePickupReq::Clear();
  clear_has_do_pickup();
}
inline const ::notify::DoRoutePickupReq& NotifyRequest::do_pickup() const {
  // @@protoc_insertion_point(field_get:notify.NotifyRequest.do_pickup)
  return do_pickup_ != NULL ? *do_pickup_ : *default_instance_->do_pickup_;
}
inline ::notify::DoRoutePickupReq* NotifyRequest::mutable_do_pickup() {
  set_has_do_pickup();
  if (do_pickup_ == NULL) do_pickup_ = new ::notify::DoRoutePickupReq;
  // @@protoc_insertion_point(field_mutable:notify.NotifyRequest.do_pickup)
  return do_pickup_;
}
inline ::notify::DoRoutePickupReq* NotifyRequest::release_do_pickup() {
  clear_has_do_pickup();
  ::notify::DoRoutePickupReq* temp = do_pickup_;
  do_pickup_ = NULL;
  return temp;
}
inline void NotifyRequest::set_allocated_do_pickup(::notify::DoRoutePickupReq* do_pickup) {
  delete do_pickup_;
  do_pickup_ = do_pickup;
  if (do_pickup) {
    set_has_do_pickup();
  } else {
    clear_has_do_pickup();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.NotifyRequest.do_pickup)
}

// optional .notify.ForceRouteNotifyReq force_notify = 121;
inline bool NotifyRequest::has_force_notify() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void NotifyRequest::set_has_force_notify() {
  _has_bits_[0] |= 0x00000040u;
}
inline void NotifyRequest::clear_has_force_notify() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void NotifyRequest::clear_force_notify() {
  if (force_notify_ != NULL) force_notify_->::notify::ForceRouteNotifyReq::Clear();
  clear_has_force_notify();
}
inline const ::notify::ForceRouteNotifyReq& NotifyRequest::force_notify() const {
  // @@protoc_insertion_point(field_get:notify.NotifyRequest.force_notify)
  return force_notify_ != NULL ? *force_notify_ : *default_instance_->force_notify_;
}
inline ::notify::ForceRouteNotifyReq* NotifyRequest::mutable_force_notify() {
  set_has_force_notify();
  if (force_notify_ == NULL) force_notify_ = new ::notify::ForceRouteNotifyReq;
  // @@protoc_insertion_point(field_mutable:notify.NotifyRequest.force_notify)
  return force_notify_;
}
inline ::notify::ForceRouteNotifyReq* NotifyRequest::release_force_notify() {
  clear_has_force_notify();
  ::notify::ForceRouteNotifyReq* temp = force_notify_;
  force_notify_ = NULL;
  return temp;
}
inline void NotifyRequest::set_allocated_force_notify(::notify::ForceRouteNotifyReq* force_notify) {
  delete force_notify_;
  force_notify_ = force_notify;
  if (force_notify) {
    set_has_force_notify();
  } else {
    clear_has_force_notify();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.NotifyRequest.force_notify)
}

// optional .notify.QueryRouteStateReq query_route = 122;
inline bool NotifyRequest::has_query_route() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void NotifyRequest::set_has_query_route() {
  _has_bits_[0] |= 0x00000080u;
}
inline void NotifyRequest::clear_has_query_route() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void NotifyRequest::clear_query_route() {
  if (query_route_ != NULL) query_route_->::notify::QueryRouteStateReq::Clear();
  clear_has_query_route();
}
inline const ::notify::QueryRouteStateReq& NotifyRequest::query_route() const {
  // @@protoc_insertion_point(field_get:notify.NotifyRequest.query_route)
  return query_route_ != NULL ? *query_route_ : *default_instance_->query_route_;
}
inline ::notify::QueryRouteStateReq* NotifyRequest::mutable_query_route() {
  set_has_query_route();
  if (query_route_ == NULL) query_route_ = new ::notify::QueryRouteStateReq;
  // @@protoc_insertion_point(field_mutable:notify.NotifyRequest.query_route)
  return query_route_;
}
inline ::notify::QueryRouteStateReq* NotifyRequest::release_query_route() {
  clear_has_query_route();
  ::notify::QueryRouteStateReq* temp = query_route_;
  query_route_ = NULL;
  return temp;
}
inline void NotifyRequest::set_allocated_query_route(::notify::QueryRouteStateReq* query_route) {
  delete query_route_;
  query_route_ = query_route;
  if (query_route) {
    set_has_query_route();
  } else {
    clear_has_query_route();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.NotifyRequest.query_route)
}

// optional .notify.WatchRouteNotifyReq watch_notify = 131;
inline bool NotifyRequest::has_watch_notify() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void NotifyRequest::set_has_watch_notify() {
  _has_bits_[0] |= 0x00000100u;
}
inline void NotifyRequest::clear_has_watch_notify() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void NotifyRequest::clear_watch_notify() {
  if (watch_notify_ != NULL) watch_notify_->::notify::WatchRouteNotifyReq::Clear();
  clear_has_watch_notify();
}
inline const ::notify::WatchRouteNotifyReq& NotifyRequest::watch_notify() const {
  // @@protoc_insertion_point(field_get:notify.NotifyRequest.watch_notify)
  return watch_notify_ != NULL ? *watch_notify_ : *default_instance_->watch_notify_;
}
inline ::notify::WatchRouteNotifyReq* NotifyRequest::mutable_watch_notify() {
  set_has_watch_notify();
  if (watch_notify_ == NULL) watch_notify_ = new ::notify::WatchRouteNotifyReq;
  // @@protoc_insertion_point(field_mutable:notify.NotifyRequest.watch_notify)
  return watch_notify_;
}
inline ::notify::WatchRouteNotifyReq* NotifyRequest::release_watch_notify() {
  clear_has_watch_notify();
  ::notify::WatchRouteNotifyReq* temp = watch_notify_;
  watch_notify_ = NULL;
  return temp;
}
inline void NotifyRequest::set_allocated_watch_notify(::notify::WatchRouteNotifyReq* watch_notify) {
  delete watch_notify_;
  watch_notify_ = watch_notify;
  if (watch_notify) {
    set_has_watch_notify();
  } else {
    clear_has_watch_notify();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.NotifyRequest.watch_notify)
}

// optional .notify.UnWatchRouteNotifyReq un_watch_notify = 132;
inline bool NotifyRequest::has_un_watch_notify() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
inline void NotifyRequest::set_has_un_watch_notify() {
  _has_bits_[0] |= 0x00000200u;
}
inline void NotifyRequest::clear_has_un_watch_notify() {
  _has_bits_[0] &= ~0x00000200u;
}
inline void NotifyRequest::clear_un_watch_notify() {
  if (un_watch_notify_ != NULL) un_watch_notify_->::notify::UnWatchRouteNotifyReq::Clear();
  clear_has_un_watch_notify();
}
inline const ::notify::UnWatchRouteNotifyReq& NotifyRequest::un_watch_notify() const {
  // @@protoc_insertion_point(field_get:notify.NotifyRequest.un_watch_notify)
  return un_watch_notify_ != NULL ? *un_watch_notify_ : *default_instance_->un_watch_notify_;
}
inline ::notify::UnWatchRouteNotifyReq* NotifyRequest::mutable_un_watch_notify() {
  set_has_un_watch_notify();
  if (un_watch_notify_ == NULL) un_watch_notify_ = new ::notify::UnWatchRouteNotifyReq;
  // @@protoc_insertion_point(field_mutable:notify.NotifyRequest.un_watch_notify)
  return un_watch_notify_;
}
inline ::notify::UnWatchRouteNotifyReq* NotifyRequest::release_un_watch_notify() {
  clear_has_un_watch_notify();
  ::notify::UnWatchRouteNotifyReq* temp = un_watch_notify_;
  un_watch_notify_ = NULL;
  return temp;
}
inline void NotifyRequest::set_allocated_un_watch_notify(::notify::UnWatchRouteNotifyReq* un_watch_notify) {
  delete un_watch_notify_;
  un_watch_notify_ = un_watch_notify;
  if (un_watch_notify) {
    set_has_un_watch_notify();
  } else {
    clear_has_un_watch_notify();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.NotifyRequest.un_watch_notify)
}

// optional .notify.InnerNotifyReq inner_notify = 201;
inline bool NotifyRequest::has_inner_notify() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
inline void NotifyRequest::set_has_inner_notify() {
  _has_bits_[0] |= 0x00000400u;
}
inline void NotifyRequest::clear_has_inner_notify() {
  _has_bits_[0] &= ~0x00000400u;
}
inline void NotifyRequest::clear_inner_notify() {
  if (inner_notify_ != NULL) inner_notify_->::notify::InnerNotifyReq::Clear();
  clear_has_inner_notify();
}
inline const ::notify::InnerNotifyReq& NotifyRequest::inner_notify() const {
  // @@protoc_insertion_point(field_get:notify.NotifyRequest.inner_notify)
  return inner_notify_ != NULL ? *inner_notify_ : *default_instance_->inner_notify_;
}
inline ::notify::InnerNotifyReq* NotifyRequest::mutable_inner_notify() {
  set_has_inner_notify();
  if (inner_notify_ == NULL) inner_notify_ = new ::notify::InnerNotifyReq;
  // @@protoc_insertion_point(field_mutable:notify.NotifyRequest.inner_notify)
  return inner_notify_;
}
inline ::notify::InnerNotifyReq* NotifyRequest::release_inner_notify() {
  clear_has_inner_notify();
  ::notify::InnerNotifyReq* temp = inner_notify_;
  inner_notify_ = NULL;
  return temp;
}
inline void NotifyRequest::set_allocated_inner_notify(::notify::InnerNotifyReq* inner_notify) {
  delete inner_notify_;
  inner_notify_ = inner_notify;
  if (inner_notify) {
    set_has_inner_notify();
  } else {
    clear_has_inner_notify();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.NotifyRequest.inner_notify)
}

// optional .notify.InnerCheckReq inner_check = 202;
inline bool NotifyRequest::has_inner_check() const {
  return (_has_bits_[0] & 0x00000800u) != 0;
}
inline void NotifyRequest::set_has_inner_check() {
  _has_bits_[0] |= 0x00000800u;
}
inline void NotifyRequest::clear_has_inner_check() {
  _has_bits_[0] &= ~0x00000800u;
}
inline void NotifyRequest::clear_inner_check() {
  if (inner_check_ != NULL) inner_check_->::notify::InnerCheckReq::Clear();
  clear_has_inner_check();
}
inline const ::notify::InnerCheckReq& NotifyRequest::inner_check() const {
  // @@protoc_insertion_point(field_get:notify.NotifyRequest.inner_check)
  return inner_check_ != NULL ? *inner_check_ : *default_instance_->inner_check_;
}
inline ::notify::InnerCheckReq* NotifyRequest::mutable_inner_check() {
  set_has_inner_check();
  if (inner_check_ == NULL) inner_check_ = new ::notify::InnerCheckReq;
  // @@protoc_insertion_point(field_mutable:notify.NotifyRequest.inner_check)
  return inner_check_;
}
inline ::notify::InnerCheckReq* NotifyRequest::release_inner_check() {
  clear_has_inner_check();
  ::notify::InnerCheckReq* temp = inner_check_;
  inner_check_ = NULL;
  return temp;
}
inline void NotifyRequest::set_allocated_inner_check(::notify::InnerCheckReq* inner_check) {
  delete inner_check_;
  inner_check_ = inner_check;
  if (inner_check) {
    set_has_inner_check();
  } else {
    clear_has_inner_check();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.NotifyRequest.inner_check)
}

// optional .notify.InnerRepairReq inner_repair = 203;
inline bool NotifyRequest::has_inner_repair() const {
  return (_has_bits_[0] & 0x00001000u) != 0;
}
inline void NotifyRequest::set_has_inner_repair() {
  _has_bits_[0] |= 0x00001000u;
}
inline void NotifyRequest::clear_has_inner_repair() {
  _has_bits_[0] &= ~0x00001000u;
}
inline void NotifyRequest::clear_inner_repair() {
  if (inner_repair_ != NULL) inner_repair_->::notify::InnerRepairReq::Clear();
  clear_has_inner_repair();
}
inline const ::notify::InnerRepairReq& NotifyRequest::inner_repair() const {
  // @@protoc_insertion_point(field_get:notify.NotifyRequest.inner_repair)
  return inner_repair_ != NULL ? *inner_repair_ : *default_instance_->inner_repair_;
}
inline ::notify::InnerRepairReq* NotifyRequest::mutable_inner_repair() {
  set_has_inner_repair();
  if (inner_repair_ == NULL) inner_repair_ = new ::notify::InnerRepairReq;
  // @@protoc_insertion_point(field_mutable:notify.NotifyRequest.inner_repair)
  return inner_repair_;
}
inline ::notify::InnerRepairReq* NotifyRequest::release_inner_repair() {
  clear_has_inner_repair();
  ::notify::InnerRepairReq* temp = inner_repair_;
  inner_repair_ = NULL;
  return temp;
}
inline void NotifyRequest::set_allocated_inner_repair(::notify::InnerRepairReq* inner_repair) {
  delete inner_repair_;
  inner_repair_ = inner_repair;
  if (inner_repair) {
    set_has_inner_repair();
  } else {
    clear_has_inner_repair();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.NotifyRequest.inner_repair)
}

// -------------------------------------------------------------------

// NotifyResponse

// required int32 result = 1;
inline bool NotifyResponse::has_result() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void NotifyResponse::set_has_result() {
  _has_bits_[0] |= 0x00000001u;
}
inline void NotifyResponse::clear_has_result() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void NotifyResponse::clear_result() {
  result_ = 0;
  clear_has_result();
}
inline ::google::protobuf::int32 NotifyResponse::result() const {
  // @@protoc_insertion_point(field_get:notify.NotifyResponse.result)
  return result_;
}
inline void NotifyResponse::set_result(::google::protobuf::int32 value) {
  set_has_result();
  result_ = value;
  // @@protoc_insertion_point(field_set:notify.NotifyResponse.result)
}

// optional string reason = 2;
inline bool NotifyResponse::has_reason() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void NotifyResponse::set_has_reason() {
  _has_bits_[0] |= 0x00000002u;
}
inline void NotifyResponse::clear_has_reason() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void NotifyResponse::clear_reason() {
  if (reason_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    reason_->clear();
  }
  clear_has_reason();
}
inline const ::std::string& NotifyResponse::reason() const {
  // @@protoc_insertion_point(field_get:notify.NotifyResponse.reason)
  return *reason_;
}
inline void NotifyResponse::set_reason(const ::std::string& value) {
  set_has_reason();
  if (reason_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    reason_ = new ::std::string;
  }
  reason_->assign(value);
  // @@protoc_insertion_point(field_set:notify.NotifyResponse.reason)
}
inline void NotifyResponse::set_reason(const char* value) {
  set_has_reason();
  if (reason_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    reason_ = new ::std::string;
  }
  reason_->assign(value);
  // @@protoc_insertion_point(field_set_char:notify.NotifyResponse.reason)
}
inline void NotifyResponse::set_reason(const char* value, size_t size) {
  set_has_reason();
  if (reason_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    reason_ = new ::std::string;
  }
  reason_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:notify.NotifyResponse.reason)
}
inline ::std::string* NotifyResponse::mutable_reason() {
  set_has_reason();
  if (reason_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    reason_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:notify.NotifyResponse.reason)
  return reason_;
}
inline ::std::string* NotifyResponse::release_reason() {
  clear_has_reason();
  if (reason_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = reason_;
    reason_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void NotifyResponse::set_allocated_reason(::std::string* reason) {
  if (reason_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete reason_;
  }
  if (reason) {
    set_has_reason();
    reason_ = reason;
  } else {
    clear_has_reason();
    reason_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:notify.NotifyResponse.reason)
}

// optional .notify.QueryRouteStateRsp query_route = 122;
inline bool NotifyResponse::has_query_route() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void NotifyResponse::set_has_query_route() {
  _has_bits_[0] |= 0x00000004u;
}
inline void NotifyResponse::clear_has_query_route() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void NotifyResponse::clear_query_route() {
  if (query_route_ != NULL) query_route_->::notify::QueryRouteStateRsp::Clear();
  clear_has_query_route();
}
inline const ::notify::QueryRouteStateRsp& NotifyResponse::query_route() const {
  // @@protoc_insertion_point(field_get:notify.NotifyResponse.query_route)
  return query_route_ != NULL ? *query_route_ : *default_instance_->query_route_;
}
inline ::notify::QueryRouteStateRsp* NotifyResponse::mutable_query_route() {
  set_has_query_route();
  if (query_route_ == NULL) query_route_ = new ::notify::QueryRouteStateRsp;
  // @@protoc_insertion_point(field_mutable:notify.NotifyResponse.query_route)
  return query_route_;
}
inline ::notify::QueryRouteStateRsp* NotifyResponse::release_query_route() {
  clear_has_query_route();
  ::notify::QueryRouteStateRsp* temp = query_route_;
  query_route_ = NULL;
  return temp;
}
inline void NotifyResponse::set_allocated_query_route(::notify::QueryRouteStateRsp* query_route) {
  delete query_route_;
  query_route_ = query_route;
  if (query_route) {
    set_has_query_route();
  } else {
    clear_has_query_route();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.NotifyResponse.query_route)
}

// optional .notify.InnerCheckRsp inner_check = 201;
inline bool NotifyResponse::has_inner_check() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void NotifyResponse::set_has_inner_check() {
  _has_bits_[0] |= 0x00000008u;
}
inline void NotifyResponse::clear_has_inner_check() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void NotifyResponse::clear_inner_check() {
  if (inner_check_ != NULL) inner_check_->::notify::InnerCheckRsp::Clear();
  clear_has_inner_check();
}
inline const ::notify::InnerCheckRsp& NotifyResponse::inner_check() const {
  // @@protoc_insertion_point(field_get:notify.NotifyResponse.inner_check)
  return inner_check_ != NULL ? *inner_check_ : *default_instance_->inner_check_;
}
inline ::notify::InnerCheckRsp* NotifyResponse::mutable_inner_check() {
  set_has_inner_check();
  if (inner_check_ == NULL) inner_check_ = new ::notify::InnerCheckRsp;
  // @@protoc_insertion_point(field_mutable:notify.NotifyResponse.inner_check)
  return inner_check_;
}
inline ::notify::InnerCheckRsp* NotifyResponse::release_inner_check() {
  clear_has_inner_check();
  ::notify::InnerCheckRsp* temp = inner_check_;
  inner_check_ = NULL;
  return temp;
}
inline void NotifyResponse::set_allocated_inner_check(::notify::InnerCheckRsp* inner_check) {
  delete inner_check_;
  inner_check_ = inner_check;
  if (inner_check) {
    set_has_inner_check();
  } else {
    clear_has_inner_check();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.NotifyResponse.inner_check)
}

// -------------------------------------------------------------------

// NotifyMessage

// required int32 type = 1;
inline bool NotifyMessage::has_type() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void NotifyMessage::set_has_type() {
  _has_bits_[0] |= 0x00000001u;
}
inline void NotifyMessage::clear_has_type() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void NotifyMessage::clear_type() {
  type_ = 0;
  clear_has_type();
}
inline ::google::protobuf::int32 NotifyMessage::type() const {
  // @@protoc_insertion_point(field_get:notify.NotifyMessage.type)
  return type_;
}
inline void NotifyMessage::set_type(::google::protobuf::int32 value) {
  set_has_type();
  type_ = value;
  // @@protoc_insertion_point(field_set:notify.NotifyMessage.type)
}

// required string name = 2;
inline bool NotifyMessage::has_name() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void NotifyMessage::set_has_name() {
  _has_bits_[0] |= 0x00000002u;
}
inline void NotifyMessage::clear_has_name() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void NotifyMessage::clear_name() {
  if (name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    name_->clear();
  }
  clear_has_name();
}
inline const ::std::string& NotifyMessage::name() const {
  // @@protoc_insertion_point(field_get:notify.NotifyMessage.name)
  return *name_;
}
inline void NotifyMessage::set_name(const ::std::string& value) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    name_ = new ::std::string;
  }
  name_->assign(value);
  // @@protoc_insertion_point(field_set:notify.NotifyMessage.name)
}
inline void NotifyMessage::set_name(const char* value) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    name_ = new ::std::string;
  }
  name_->assign(value);
  // @@protoc_insertion_point(field_set_char:notify.NotifyMessage.name)
}
inline void NotifyMessage::set_name(const char* value, size_t size) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    name_ = new ::std::string;
  }
  name_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:notify.NotifyMessage.name)
}
inline ::std::string* NotifyMessage::mutable_name() {
  set_has_name();
  if (name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    name_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:notify.NotifyMessage.name)
  return name_;
}
inline ::std::string* NotifyMessage::release_name() {
  clear_has_name();
  if (name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = name_;
    name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void NotifyMessage::set_allocated_name(::std::string* name) {
  if (name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete name_;
  }
  if (name) {
    set_has_name();
    name_ = name;
  } else {
    clear_has_name();
    name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:notify.NotifyMessage.name)
}

// required int64 seq = 3;
inline bool NotifyMessage::has_seq() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void NotifyMessage::set_has_seq() {
  _has_bits_[0] |= 0x00000004u;
}
inline void NotifyMessage::clear_has_seq() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void NotifyMessage::clear_seq() {
  seq_ = GOOGLE_LONGLONG(0);
  clear_has_seq();
}
inline ::google::protobuf::int64 NotifyMessage::seq() const {
  // @@protoc_insertion_point(field_get:notify.NotifyMessage.seq)
  return seq_;
}
inline void NotifyMessage::set_seq(::google::protobuf::int64 value) {
  set_has_seq();
  seq_ = value;
  // @@protoc_insertion_point(field_set:notify.NotifyMessage.seq)
}

// optional .notify.NotifyRequest request = 11;
inline bool NotifyMessage::has_request() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void NotifyMessage::set_has_request() {
  _has_bits_[0] |= 0x00000008u;
}
inline void NotifyMessage::clear_has_request() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void NotifyMessage::clear_request() {
  if (request_ != NULL) request_->::notify::NotifyRequest::Clear();
  clear_has_request();
}
inline const ::notify::NotifyRequest& NotifyMessage::request() const {
  // @@protoc_insertion_point(field_get:notify.NotifyMessage.request)
  return request_ != NULL ? *request_ : *default_instance_->request_;
}
inline ::notify::NotifyRequest* NotifyMessage::mutable_request() {
  set_has_request();
  if (request_ == NULL) request_ = new ::notify::NotifyRequest;
  // @@protoc_insertion_point(field_mutable:notify.NotifyMessage.request)
  return request_;
}
inline ::notify::NotifyRequest* NotifyMessage::release_request() {
  clear_has_request();
  ::notify::NotifyRequest* temp = request_;
  request_ = NULL;
  return temp;
}
inline void NotifyMessage::set_allocated_request(::notify::NotifyRequest* request) {
  delete request_;
  request_ = request;
  if (request) {
    set_has_request();
  } else {
    clear_has_request();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.NotifyMessage.request)
}

// optional .notify.NotifyResponse response = 12;
inline bool NotifyMessage::has_response() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void NotifyMessage::set_has_response() {
  _has_bits_[0] |= 0x00000010u;
}
inline void NotifyMessage::clear_has_response() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void NotifyMessage::clear_response() {
  if (response_ != NULL) response_->::notify::NotifyResponse::Clear();
  clear_has_response();
}
inline const ::notify::NotifyResponse& NotifyMessage::response() const {
  // @@protoc_insertion_point(field_get:notify.NotifyMessage.response)
  return response_ != NULL ? *response_ : *default_instance_->response_;
}
inline ::notify::NotifyResponse* NotifyMessage::mutable_response() {
  set_has_response();
  if (response_ == NULL) response_ = new ::notify::NotifyResponse;
  // @@protoc_insertion_point(field_mutable:notify.NotifyMessage.response)
  return response_;
}
inline ::notify::NotifyResponse* NotifyMessage::release_response() {
  clear_has_response();
  ::notify::NotifyResponse* temp = response_;
  response_ = NULL;
  return temp;
}
inline void NotifyMessage::set_allocated_response(::notify::NotifyResponse* response) {
  delete response_;
  response_ = response;
  if (response) {
    set_has_response();
  } else {
    clear_has_response();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.NotifyMessage.response)
}

// -------------------------------------------------------------------

// WatchEventMessage

// required int32 event = 1;
inline bool WatchEventMessage::has_event() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void WatchEventMessage::set_has_event() {
  _has_bits_[0] |= 0x00000001u;
}
inline void WatchEventMessage::clear_has_event() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void WatchEventMessage::clear_event() {
  event_ = 0;
  clear_has_event();
}
inline ::google::protobuf::int32 WatchEventMessage::event() const {
  // @@protoc_insertion_point(field_get:notify.WatchEventMessage.event)
  return event_;
}
inline void WatchEventMessage::set_event(::google::protobuf::int32 value) {
  set_has_event();
  event_ = value;
  // @@protoc_insertion_point(field_set:notify.WatchEventMessage.event)
}

// required int64 timestamp = 2;
inline bool WatchEventMessage::has_timestamp() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void WatchEventMessage::set_has_timestamp() {
  _has_bits_[0] |= 0x00000002u;
}
inline void WatchEventMessage::clear_has_timestamp() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void WatchEventMessage::clear_timestamp() {
  timestamp_ = GOOGLE_LONGLONG(0);
  clear_has_timestamp();
}
inline ::google::protobuf::int64 WatchEventMessage::timestamp() const {
  // @@protoc_insertion_point(field_get:notify.WatchEventMessage.timestamp)
  return timestamp_;
}
inline void WatchEventMessage::set_timestamp(::google::protobuf::int64 value) {
  set_has_timestamp();
  timestamp_ = value;
  // @@protoc_insertion_point(field_set:notify.WatchEventMessage.timestamp)
}

// required .troute.server_name_and_address target = 3;
inline bool WatchEventMessage::has_target() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void WatchEventMessage::set_has_target() {
  _has_bits_[0] |= 0x00000004u;
}
inline void WatchEventMessage::clear_has_target() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void WatchEventMessage::clear_target() {
  if (target_ != NULL) target_->::troute::server_name_and_address::Clear();
  clear_has_target();
}
inline const ::troute::server_name_and_address& WatchEventMessage::target() const {
  // @@protoc_insertion_point(field_get:notify.WatchEventMessage.target)
  return target_ != NULL ? *target_ : *default_instance_->target_;
}
inline ::troute::server_name_and_address* WatchEventMessage::mutable_target() {
  set_has_target();
  if (target_ == NULL) target_ = new ::troute::server_name_and_address;
  // @@protoc_insertion_point(field_mutable:notify.WatchEventMessage.target)
  return target_;
}
inline ::troute::server_name_and_address* WatchEventMessage::release_target() {
  clear_has_target();
  ::troute::server_name_and_address* temp = target_;
  target_ = NULL;
  return temp;
}
inline void WatchEventMessage::set_allocated_target(::troute::server_name_and_address* target) {
  delete target_;
  target_ = target;
  if (target) {
    set_has_target();
  } else {
    clear_has_target();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.WatchEventMessage.target)
}

// optional .troute.server_name_and_address trigger_addr = 11;
inline bool WatchEventMessage::has_trigger_addr() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void WatchEventMessage::set_has_trigger_addr() {
  _has_bits_[0] |= 0x00000008u;
}
inline void WatchEventMessage::clear_has_trigger_addr() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void WatchEventMessage::clear_trigger_addr() {
  if (trigger_addr_ != NULL) trigger_addr_->::troute::server_name_and_address::Clear();
  clear_has_trigger_addr();
}
inline const ::troute::server_name_and_address& WatchEventMessage::trigger_addr() const {
  // @@protoc_insertion_point(field_get:notify.WatchEventMessage.trigger_addr)
  return trigger_addr_ != NULL ? *trigger_addr_ : *default_instance_->trigger_addr_;
}
inline ::troute::server_name_and_address* WatchEventMessage::mutable_trigger_addr() {
  set_has_trigger_addr();
  if (trigger_addr_ == NULL) trigger_addr_ = new ::troute::server_name_and_address;
  // @@protoc_insertion_point(field_mutable:notify.WatchEventMessage.trigger_addr)
  return trigger_addr_;
}
inline ::troute::server_name_and_address* WatchEventMessage::release_trigger_addr() {
  clear_has_trigger_addr();
  ::troute::server_name_and_address* temp = trigger_addr_;
  trigger_addr_ = NULL;
  return temp;
}
inline void WatchEventMessage::set_allocated_trigger_addr(::troute::server_name_and_address* trigger_addr) {
  delete trigger_addr_;
  trigger_addr_ = trigger_addr;
  if (trigger_addr) {
    set_has_trigger_addr();
  } else {
    clear_has_trigger_addr();
  }
  // @@protoc_insertion_point(field_set_allocated:notify.WatchEventMessage.trigger_addr)
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace notify

#ifndef SWIG
namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::notify::NotifyMessageType> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::notify::NotifyMessageType>() {
  return ::notify::NotifyMessageType_descriptor();
}
template <> struct is_proto_enum< ::notify::NotifyEvent> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::notify::NotifyEvent>() {
  return ::notify::NotifyEvent_descriptor();
}

}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_source_2fbaseagent_2fproto_2fnotify_5fmsg_2eproto__INCLUDED
