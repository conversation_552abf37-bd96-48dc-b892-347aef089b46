// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tbuspp/proto/base_agent.proto

#ifndef PROTOBUF_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto__INCLUDED
#define PROTOBUF_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2006000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "tbuspp/proto/dbconf.pb.h"
#include "tbuspp/proto/troute.pb.h"
// @@protoc_insertion_point(includes)

namespace baseagent {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();
void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();
void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();

class tmsg_conf;
class troute_conf;
class net_conf;
class busid_map_conf;
class extern_proto_conf;
class stat_conf;
class base_agent_conf;
class sync_name_server_info;
class name_list;
class trans_info;
class event_handle;
class event_handle_list;

// ===================================================================

class tmsg_conf : public ::google::protobuf::Message {
 public:
  tmsg_conf();
  virtual ~tmsg_conf();

  tmsg_conf(const tmsg_conf& from);

  inline tmsg_conf& operator=(const tmsg_conf& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const tmsg_conf& default_instance();

  void Swap(tmsg_conf* other);

  // implements Message ----------------------------------------------

  tmsg_conf* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const tmsg_conf& from);
  void MergeFrom(const tmsg_conf& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint32 tmsg_mgr_index = 1 [default = 0];
  inline bool has_tmsg_mgr_index() const;
  inline void clear_tmsg_mgr_index();
  static const int kTmsgMgrIndexFieldNumber = 1;
  inline ::google::protobuf::uint32 tmsg_mgr_index() const;
  inline void set_tmsg_mgr_index(::google::protobuf::uint32 value);

  // optional uint32 tmsg_data_index = 2 [default = 1];
  inline bool has_tmsg_data_index() const;
  inline void clear_tmsg_data_index();
  static const int kTmsgDataIndexFieldNumber = 2;
  inline ::google::protobuf::uint32 tmsg_data_index() const;
  inline void set_tmsg_data_index(::google::protobuf::uint32 value);

  // optional uint32 max_queue_count = 3 [default = 500];
  inline bool has_max_queue_count() const;
  inline void clear_max_queue_count();
  static const int kMaxQueueCountFieldNumber = 3;
  inline ::google::protobuf::uint32 max_queue_count() const;
  inline void set_max_queue_count(::google::protobuf::uint32 value);

  // optional uint32 max_sub_queue_count = 4 [default = 10];
  inline bool has_max_sub_queue_count() const;
  inline void clear_max_sub_queue_count();
  static const int kMaxSubQueueCountFieldNumber = 4;
  inline ::google::protobuf::uint32 max_sub_queue_count() const;
  inline void set_max_sub_queue_count(::google::protobuf::uint32 value);

  // optional uint32 max_observer_count = 5 [default = 500];
  inline bool has_max_observer_count() const;
  inline void clear_max_observer_count();
  static const int kMaxObserverCountFieldNumber = 5;
  inline ::google::protobuf::uint32 max_observer_count() const;
  inline void set_max_observer_count(::google::protobuf::uint32 value);

  // optional uint32 observer_max_event_count = 6 [default = 10];
  inline bool has_observer_max_event_count() const;
  inline void clear_observer_max_event_count();
  static const int kObserverMaxEventCountFieldNumber = 6;
  inline ::google::protobuf::uint32 observer_max_event_count() const;
  inline void set_observer_max_event_count(::google::protobuf::uint32 value);

  // optional uint32 max_event_mgr_count = 7 [default = 100];
  inline bool has_max_event_mgr_count() const;
  inline void clear_max_event_mgr_count();
  static const int kMaxEventMgrCountFieldNumber = 7;
  inline ::google::protobuf::uint32 max_event_mgr_count() const;
  inline void set_max_event_mgr_count(::google::protobuf::uint32 value);

  // optional uint32 max_data_block_count = 8 [default = 500];
  inline bool has_max_data_block_count() const;
  inline void clear_max_data_block_count();
  static const int kMaxDataBlockCountFieldNumber = 8;
  inline ::google::protobuf::uint32 max_data_block_count() const;
  inline void set_max_data_block_count(::google::protobuf::uint32 value);

  // optional uint32 data_block_size = 9 [default = 4096];
  inline bool has_data_block_size() const;
  inline void clear_data_block_size();
  static const int kDataBlockSizeFieldNumber = 9;
  inline ::google::protobuf::uint32 data_block_size() const;
  inline void set_data_block_size(::google::protobuf::uint32 value);

  // optional uint32 mem_type = 10 [default = 3];
  inline bool has_mem_type() const;
  inline void clear_mem_type();
  static const int kMemTypeFieldNumber = 10;
  inline ::google::protobuf::uint32 mem_type() const;
  inline void set_mem_type(::google::protobuf::uint32 value);

  // optional uint32 mgr_shmkey = 11 [default = 210001];
  inline bool has_mgr_shmkey() const;
  inline void clear_mgr_shmkey();
  static const int kMgrShmkeyFieldNumber = 11;
  inline ::google::protobuf::uint32 mgr_shmkey() const;
  inline void set_mgr_shmkey(::google::protobuf::uint32 value);

  // optional uint32 data_shmkey = 12 [default = 210002];
  inline bool has_data_shmkey() const;
  inline void clear_data_shmkey();
  static const int kDataShmkeyFieldNumber = 12;
  inline ::google::protobuf::uint32 data_shmkey() const;
  inline void set_data_shmkey(::google::protobuf::uint32 value);

  // optional string mgr_map_file = 13 [default = "/dev/shm/baseagent/tmsg/mgr.data"];
  inline bool has_mgr_map_file() const;
  inline void clear_mgr_map_file();
  static const int kMgrMapFileFieldNumber = 13;
  inline const ::std::string& mgr_map_file() const;
  inline void set_mgr_map_file(const ::std::string& value);
  inline void set_mgr_map_file(const char* value);
  inline void set_mgr_map_file(const char* value, size_t size);
  inline ::std::string* mutable_mgr_map_file();
  inline ::std::string* release_mgr_map_file();
  inline void set_allocated_mgr_map_file(::std::string* mgr_map_file);

  // optional string data_map_file = 14 [default = "/dev/shm/baseagent/tmsg/msg.data"];
  inline bool has_data_map_file() const;
  inline void clear_data_map_file();
  static const int kDataMapFileFieldNumber = 14;
  inline const ::std::string& data_map_file() const;
  inline void set_data_map_file(const ::std::string& value);
  inline void set_data_map_file(const char* value);
  inline void set_data_map_file(const char* value, size_t size);
  inline ::std::string* mutable_data_map_file();
  inline ::std::string* release_data_map_file();
  inline void set_allocated_data_map_file(::std::string* data_map_file);

  // @@protoc_insertion_point(class_scope:baseagent.tmsg_conf)
 private:
  inline void set_has_tmsg_mgr_index();
  inline void clear_has_tmsg_mgr_index();
  inline void set_has_tmsg_data_index();
  inline void clear_has_tmsg_data_index();
  inline void set_has_max_queue_count();
  inline void clear_has_max_queue_count();
  inline void set_has_max_sub_queue_count();
  inline void clear_has_max_sub_queue_count();
  inline void set_has_max_observer_count();
  inline void clear_has_max_observer_count();
  inline void set_has_observer_max_event_count();
  inline void clear_has_observer_max_event_count();
  inline void set_has_max_event_mgr_count();
  inline void clear_has_max_event_mgr_count();
  inline void set_has_max_data_block_count();
  inline void clear_has_max_data_block_count();
  inline void set_has_data_block_size();
  inline void clear_has_data_block_size();
  inline void set_has_mem_type();
  inline void clear_has_mem_type();
  inline void set_has_mgr_shmkey();
  inline void clear_has_mgr_shmkey();
  inline void set_has_data_shmkey();
  inline void clear_has_data_shmkey();
  inline void set_has_mgr_map_file();
  inline void clear_has_mgr_map_file();
  inline void set_has_data_map_file();
  inline void clear_has_data_map_file();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::uint32 tmsg_mgr_index_;
  ::google::protobuf::uint32 tmsg_data_index_;
  ::google::protobuf::uint32 max_queue_count_;
  ::google::protobuf::uint32 max_sub_queue_count_;
  ::google::protobuf::uint32 max_observer_count_;
  ::google::protobuf::uint32 observer_max_event_count_;
  ::google::protobuf::uint32 max_event_mgr_count_;
  ::google::protobuf::uint32 max_data_block_count_;
  ::google::protobuf::uint32 data_block_size_;
  ::google::protobuf::uint32 mem_type_;
  ::google::protobuf::uint32 mgr_shmkey_;
  ::google::protobuf::uint32 data_shmkey_;
  static ::std::string* _default_mgr_map_file_;
  ::std::string* mgr_map_file_;
  static ::std::string* _default_data_map_file_;
  ::std::string* data_map_file_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();

  void InitAsDefaultInstance();
  static tmsg_conf* default_instance_;
};
// -------------------------------------------------------------------

class troute_conf : public ::google::protobuf::Message {
 public:
  troute_conf();
  virtual ~troute_conf();

  troute_conf(const troute_conf& from);

  inline troute_conf& operator=(const troute_conf& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const troute_conf& default_instance();

  void Swap(troute_conf* other);

  // implements Message ----------------------------------------------

  troute_conf* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const troute_conf& from);
  void MergeFrom(const troute_conf& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint32 global_mem_index = 1 [default = 2];
  inline bool has_global_mem_index() const;
  inline void clear_global_mem_index();
  static const int kGlobalMemIndexFieldNumber = 1;
  inline ::google::protobuf::uint32 global_mem_index() const;
  inline void set_global_mem_index(::google::protobuf::uint32 value);

  // optional uint32 bucket_count = 2 [default = 50];
  inline bool has_bucket_count() const;
  inline void clear_bucket_count();
  static const int kBucketCountFieldNumber = 2;
  inline ::google::protobuf::uint32 bucket_count() const;
  inline void set_bucket_count(::google::protobuf::uint32 value);

  // optional uint32 name_count = 3 [default = 50];
  inline bool has_name_count() const;
  inline void clear_name_count();
  static const int kNameCountFieldNumber = 3;
  inline ::google::protobuf::uint32 name_count() const;
  inline void set_name_count(::google::protobuf::uint32 value);

  // optional uint32 address_count = 4 [default = 2000];
  inline bool has_address_count() const;
  inline void clear_address_count();
  static const int kAddressCountFieldNumber = 4;
  inline ::google::protobuf::uint32 address_count() const;
  inline void set_address_count(::google::protobuf::uint32 value);

  // optional uint32 mem_type = 5 [default = 3];
  inline bool has_mem_type() const;
  inline void clear_mem_type();
  static const int kMemTypeFieldNumber = 5;
  inline ::google::protobuf::uint32 mem_type() const;
  inline void set_mem_type(::google::protobuf::uint32 value);

  // optional uint32 shmkey = 6 [default = 210003];
  inline bool has_shmkey() const;
  inline void clear_shmkey();
  static const int kShmkeyFieldNumber = 6;
  inline ::google::protobuf::uint32 shmkey() const;
  inline void set_shmkey(::google::protobuf::uint32 value);

  // optional string mgr_map_file = 7 [default = "/dev/shm/baseagent/troute/troue.data"];
  inline bool has_mgr_map_file() const;
  inline void clear_mgr_map_file();
  static const int kMgrMapFileFieldNumber = 7;
  inline const ::std::string& mgr_map_file() const;
  inline void set_mgr_map_file(const ::std::string& value);
  inline void set_mgr_map_file(const char* value);
  inline void set_mgr_map_file(const char* value, size_t size);
  inline ::std::string* mutable_mgr_map_file();
  inline ::std::string* release_mgr_map_file();
  inline void set_allocated_mgr_map_file(::std::string* mgr_map_file);

  // optional double err_rate_limit = 8 [default = 0.2];
  inline bool has_err_rate_limit() const;
  inline void clear_err_rate_limit();
  static const int kErrRateLimitFieldNumber = 8;
  inline double err_rate_limit() const;
  inline void set_err_rate_limit(double value);

  // optional double server_normal_expand_rate = 9 [default = 0.2];
  inline bool has_server_normal_expand_rate() const;
  inline void clear_server_normal_expand_rate();
  static const int kServerNormalExpandRateFieldNumber = 9;
  inline double server_normal_expand_rate() const;
  inline void set_server_normal_expand_rate(double value);

  // optional double min_req_down_rate = 10 [default = 0.1];
  inline bool has_min_req_down_rate() const;
  inline void clear_min_req_down_rate();
  static const int kMinReqDownRateFieldNumber = 10;
  inline double min_req_down_rate() const;
  inline void set_min_req_down_rate(double value);

  // optional double min_err_rate_down_rate = 11 [default = 0.05];
  inline bool has_min_err_rate_down_rate() const;
  inline void clear_min_err_rate_down_rate();
  static const int kMinErrRateDownRateFieldNumber = 11;
  inline double min_err_rate_down_rate() const;
  inline void set_min_err_rate_down_rate(double value);

  // optional uint32 min_non_pressure_fault_periodic = 12 [default = 2];
  inline bool has_min_non_pressure_fault_periodic() const;
  inline void clear_min_non_pressure_fault_periodic();
  static const int kMinNonPressureFaultPeriodicFieldNumber = 12;
  inline ::google::protobuf::uint32 min_non_pressure_fault_periodic() const;
  inline void set_min_non_pressure_fault_periodic(::google::protobuf::uint32 value);

  // optional double min_non_pressure_fault_bias_ratio = 13 [default = 0.2];
  inline bool has_min_non_pressure_fault_bias_ratio() const;
  inline void clear_min_non_pressure_fault_bias_ratio();
  static const int kMinNonPressureFaultBiasRatioFieldNumber = 13;
  inline double min_non_pressure_fault_bias_ratio() const;
  inline void set_min_non_pressure_fault_bias_ratio(double value);

  // optional uint32 route_pri_calculating_period = 14 [default = 60];
  inline bool has_route_pri_calculating_period() const;
  inline void clear_route_pri_calculating_period();
  static const int kRoutePriCalculatingPeriodFieldNumber = 14;
  inline ::google::protobuf::uint32 route_pri_calculating_period() const;
  inline void set_route_pri_calculating_period(::google::protobuf::uint32 value);

  // optional uint32 server_io_up_limit = 15 [default = 5000];
  inline bool has_server_io_up_limit() const;
  inline void clear_server_io_up_limit();
  static const int kServerIoUpLimitFieldNumber = 15;
  inline ::google::protobuf::uint32 server_io_up_limit() const;
  inline void set_server_io_up_limit(::google::protobuf::uint32 value);

  // optional uint32 server_io_down_limit = 16 [default = 10];
  inline bool has_server_io_down_limit() const;
  inline void clear_server_io_down_limit();
  static const int kServerIoDownLimitFieldNumber = 16;
  inline ::google::protobuf::uint32 server_io_down_limit() const;
  inline void set_server_io_down_limit(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:baseagent.troute_conf)
 private:
  inline void set_has_global_mem_index();
  inline void clear_has_global_mem_index();
  inline void set_has_bucket_count();
  inline void clear_has_bucket_count();
  inline void set_has_name_count();
  inline void clear_has_name_count();
  inline void set_has_address_count();
  inline void clear_has_address_count();
  inline void set_has_mem_type();
  inline void clear_has_mem_type();
  inline void set_has_shmkey();
  inline void clear_has_shmkey();
  inline void set_has_mgr_map_file();
  inline void clear_has_mgr_map_file();
  inline void set_has_err_rate_limit();
  inline void clear_has_err_rate_limit();
  inline void set_has_server_normal_expand_rate();
  inline void clear_has_server_normal_expand_rate();
  inline void set_has_min_req_down_rate();
  inline void clear_has_min_req_down_rate();
  inline void set_has_min_err_rate_down_rate();
  inline void clear_has_min_err_rate_down_rate();
  inline void set_has_min_non_pressure_fault_periodic();
  inline void clear_has_min_non_pressure_fault_periodic();
  inline void set_has_min_non_pressure_fault_bias_ratio();
  inline void clear_has_min_non_pressure_fault_bias_ratio();
  inline void set_has_route_pri_calculating_period();
  inline void clear_has_route_pri_calculating_period();
  inline void set_has_server_io_up_limit();
  inline void clear_has_server_io_up_limit();
  inline void set_has_server_io_down_limit();
  inline void clear_has_server_io_down_limit();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::uint32 global_mem_index_;
  ::google::protobuf::uint32 bucket_count_;
  ::google::protobuf::uint32 name_count_;
  ::google::protobuf::uint32 address_count_;
  ::google::protobuf::uint32 mem_type_;
  ::google::protobuf::uint32 shmkey_;
  static ::std::string* _default_mgr_map_file_;
  ::std::string* mgr_map_file_;
  double err_rate_limit_;
  double server_normal_expand_rate_;
  double min_req_down_rate_;
  double min_err_rate_down_rate_;
  double min_non_pressure_fault_bias_ratio_;
  ::google::protobuf::uint32 min_non_pressure_fault_periodic_;
  ::google::protobuf::uint32 route_pri_calculating_period_;
  ::google::protobuf::uint32 server_io_up_limit_;
  ::google::protobuf::uint32 server_io_down_limit_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();

  void InitAsDefaultInstance();
  static troute_conf* default_instance_;
};
// -------------------------------------------------------------------

class net_conf : public ::google::protobuf::Message {
 public:
  net_conf();
  virtual ~net_conf();

  net_conf(const net_conf& from);

  inline net_conf& operator=(const net_conf& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const net_conf& default_instance();

  void Swap(net_conf* other);

  // implements Message ----------------------------------------------

  net_conf* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const net_conf& from);
  void MergeFrom(const net_conf& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional bool use_nagle = 1 [default = true];
  inline bool has_use_nagle() const;
  inline void clear_use_nagle();
  static const int kUseNagleFieldNumber = 1;
  inline bool use_nagle() const;
  inline void set_use_nagle(bool value);

  // optional bool use_linger = 2 [default = false];
  inline bool has_use_linger() const;
  inline void clear_use_linger();
  static const int kUseLingerFieldNumber = 2;
  inline bool use_linger() const;
  inline void set_use_linger(bool value);

  // optional int32 linger_time = 3 [default = 0];
  inline bool has_linger_time() const;
  inline void clear_linger_time();
  static const int kLingerTimeFieldNumber = 3;
  inline ::google::protobuf::int32 linger_time() const;
  inline void set_linger_time(::google::protobuf::int32 value);

  // optional int32 listen_backlog = 4 [default = 10240];
  inline bool has_listen_backlog() const;
  inline void clear_listen_backlog();
  static const int kListenBacklogFieldNumber = 4;
  inline ::google::protobuf::int32 listen_backlog() const;
  inline void set_listen_backlog(::google::protobuf::int32 value);

  // optional uint32 max_connect_num = 5 [default = 20480];
  inline bool has_max_connect_num() const;
  inline void clear_max_connect_num();
  static const int kMaxConnectNumFieldNumber = 5;
  inline ::google::protobuf::uint32 max_connect_num() const;
  inline void set_max_connect_num(::google::protobuf::uint32 value);

  // optional uint32 recv_cache_block_num = 6 [default = 10240];
  inline bool has_recv_cache_block_num() const;
  inline void clear_recv_cache_block_num();
  static const int kRecvCacheBlockNumFieldNumber = 6;
  inline ::google::protobuf::uint32 recv_cache_block_num() const;
  inline void set_recv_cache_block_num(::google::protobuf::uint32 value);

  // optional uint32 recv_cache_block_size = 7 [default = 512];
  inline bool has_recv_cache_block_size() const;
  inline void clear_recv_cache_block_size();
  static const int kRecvCacheBlockSizeFieldNumber = 7;
  inline ::google::protobuf::uint32 recv_cache_block_size() const;
  inline void set_recv_cache_block_size(::google::protobuf::uint32 value);

  // optional uint32 send_cache_block_num = 8 [default = 10240];
  inline bool has_send_cache_block_num() const;
  inline void clear_send_cache_block_num();
  static const int kSendCacheBlockNumFieldNumber = 8;
  inline ::google::protobuf::uint32 send_cache_block_num() const;
  inline void set_send_cache_block_num(::google::protobuf::uint32 value);

  // optional uint32 send_cache_block_size = 9 [default = 512];
  inline bool has_send_cache_block_size() const;
  inline void clear_send_cache_block_size();
  static const int kSendCacheBlockSizeFieldNumber = 9;
  inline ::google::protobuf::uint32 send_cache_block_size() const;
  inline void set_send_cache_block_size(::google::protobuf::uint32 value);

  // optional uint32 max_msg_length = 11 [default = 1048576];
  inline bool has_max_msg_length() const;
  inline void clear_max_msg_length();
  static const int kMaxMsgLengthFieldNumber = 11;
  inline ::google::protobuf::uint32 max_msg_length() const;
  inline void set_max_msg_length(::google::protobuf::uint32 value);

  // optional uint32 max_queue_num = 21 [default = 5120];
  inline bool has_max_queue_num() const;
  inline void clear_max_queue_num();
  static const int kMaxQueueNumFieldNumber = 21;
  inline ::google::protobuf::uint32 max_queue_num() const;
  inline void set_max_queue_num(::google::protobuf::uint32 value);

  // optional uint32 max_wait_ack_msg_num_per_queue = 22 [default = 128];
  inline bool has_max_wait_ack_msg_num_per_queue() const;
  inline void clear_max_wait_ack_msg_num_per_queue();
  static const int kMaxWaitAckMsgNumPerQueueFieldNumber = 22;
  inline ::google::protobuf::uint32 max_wait_ack_msg_num_per_queue() const;
  inline void set_max_wait_ack_msg_num_per_queue(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:baseagent.net_conf)
 private:
  inline void set_has_use_nagle();
  inline void clear_has_use_nagle();
  inline void set_has_use_linger();
  inline void clear_has_use_linger();
  inline void set_has_linger_time();
  inline void clear_has_linger_time();
  inline void set_has_listen_backlog();
  inline void clear_has_listen_backlog();
  inline void set_has_max_connect_num();
  inline void clear_has_max_connect_num();
  inline void set_has_recv_cache_block_num();
  inline void clear_has_recv_cache_block_num();
  inline void set_has_recv_cache_block_size();
  inline void clear_has_recv_cache_block_size();
  inline void set_has_send_cache_block_num();
  inline void clear_has_send_cache_block_num();
  inline void set_has_send_cache_block_size();
  inline void clear_has_send_cache_block_size();
  inline void set_has_max_msg_length();
  inline void clear_has_max_msg_length();
  inline void set_has_max_queue_num();
  inline void clear_has_max_queue_num();
  inline void set_has_max_wait_ack_msg_num_per_queue();
  inline void clear_has_max_wait_ack_msg_num_per_queue();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  bool use_nagle_;
  bool use_linger_;
  ::google::protobuf::int32 linger_time_;
  ::google::protobuf::int32 listen_backlog_;
  ::google::protobuf::uint32 max_connect_num_;
  ::google::protobuf::uint32 recv_cache_block_num_;
  ::google::protobuf::uint32 recv_cache_block_size_;
  ::google::protobuf::uint32 send_cache_block_num_;
  ::google::protobuf::uint32 send_cache_block_size_;
  ::google::protobuf::uint32 max_msg_length_;
  ::google::protobuf::uint32 max_queue_num_;
  ::google::protobuf::uint32 max_wait_ack_msg_num_per_queue_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();

  void InitAsDefaultInstance();
  static net_conf* default_instance_;
};
// -------------------------------------------------------------------

class busid_map_conf : public ::google::protobuf::Message {
 public:
  busid_map_conf();
  virtual ~busid_map_conf();

  busid_map_conf(const busid_map_conf& from);

  inline busid_map_conf& operator=(const busid_map_conf& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const busid_map_conf& default_instance();

  void Swap(busid_map_conf* other);

  // implements Message ----------------------------------------------

  busid_map_conf* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const busid_map_conf& from);
  void MergeFrom(const busid_map_conf& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint32 global_mem_index = 1 [default = 3];
  inline bool has_global_mem_index() const;
  inline void clear_global_mem_index();
  static const int kGlobalMemIndexFieldNumber = 1;
  inline ::google::protobuf::uint32 global_mem_index() const;
  inline void set_global_mem_index(::google::protobuf::uint32 value);

  // optional uint32 address_count = 2 [default = 200];
  inline bool has_address_count() const;
  inline void clear_address_count();
  static const int kAddressCountFieldNumber = 2;
  inline ::google::protobuf::uint32 address_count() const;
  inline void set_address_count(::google::protobuf::uint32 value);

  // optional uint32 mem_type = 3 [default = 3];
  inline bool has_mem_type() const;
  inline void clear_mem_type();
  static const int kMemTypeFieldNumber = 3;
  inline ::google::protobuf::uint32 mem_type() const;
  inline void set_mem_type(::google::protobuf::uint32 value);

  // optional uint32 shmkey = 4 [default = 210004];
  inline bool has_shmkey() const;
  inline void clear_shmkey();
  static const int kShmkeyFieldNumber = 4;
  inline ::google::protobuf::uint32 shmkey() const;
  inline void set_shmkey(::google::protobuf::uint32 value);

  // optional string mgr_map_file = 5 [default = "/dev/shm/baseagent/busidmap/busidmap.data"];
  inline bool has_mgr_map_file() const;
  inline void clear_mgr_map_file();
  static const int kMgrMapFileFieldNumber = 5;
  inline const ::std::string& mgr_map_file() const;
  inline void set_mgr_map_file(const ::std::string& value);
  inline void set_mgr_map_file(const char* value);
  inline void set_mgr_map_file(const char* value, size_t size);
  inline ::std::string* mutable_mgr_map_file();
  inline ::std::string* release_mgr_map_file();
  inline void set_allocated_mgr_map_file(::std::string* mgr_map_file);

  // @@protoc_insertion_point(class_scope:baseagent.busid_map_conf)
 private:
  inline void set_has_global_mem_index();
  inline void clear_has_global_mem_index();
  inline void set_has_address_count();
  inline void clear_has_address_count();
  inline void set_has_mem_type();
  inline void clear_has_mem_type();
  inline void set_has_shmkey();
  inline void clear_has_shmkey();
  inline void set_has_mgr_map_file();
  inline void clear_has_mgr_map_file();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::uint32 global_mem_index_;
  ::google::protobuf::uint32 address_count_;
  ::google::protobuf::uint32 mem_type_;
  ::google::protobuf::uint32 shmkey_;
  static ::std::string* _default_mgr_map_file_;
  ::std::string* mgr_map_file_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();

  void InitAsDefaultInstance();
  static busid_map_conf* default_instance_;
};
// -------------------------------------------------------------------

class extern_proto_conf : public ::google::protobuf::Message {
 public:
  extern_proto_conf();
  virtual ~extern_proto_conf();

  extern_proto_conf(const extern_proto_conf& from);

  inline extern_proto_conf& operator=(const extern_proto_conf& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const extern_proto_conf& default_instance();

  void Swap(extern_proto_conf* other);

  // implements Message ----------------------------------------------

  extern_proto_conf* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const extern_proto_conf& from);
  void MergeFrom(const extern_proto_conf& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 proto_type = 1;
  inline bool has_proto_type() const;
  inline void clear_proto_type();
  static const int kProtoTypeFieldNumber = 1;
  inline ::google::protobuf::int32 proto_type() const;
  inline void set_proto_type(::google::protobuf::int32 value);

  // required string proto_so_path = 2;
  inline bool has_proto_so_path() const;
  inline void clear_proto_so_path();
  static const int kProtoSoPathFieldNumber = 2;
  inline const ::std::string& proto_so_path() const;
  inline void set_proto_so_path(const ::std::string& value);
  inline void set_proto_so_path(const char* value);
  inline void set_proto_so_path(const char* value, size_t size);
  inline ::std::string* mutable_proto_so_path();
  inline ::std::string* release_proto_so_path();
  inline void set_allocated_proto_so_path(::std::string* proto_so_path);

  // required string proto_conf_path = 3;
  inline bool has_proto_conf_path() const;
  inline void clear_proto_conf_path();
  static const int kProtoConfPathFieldNumber = 3;
  inline const ::std::string& proto_conf_path() const;
  inline void set_proto_conf_path(const ::std::string& value);
  inline void set_proto_conf_path(const char* value);
  inline void set_proto_conf_path(const char* value, size_t size);
  inline ::std::string* mutable_proto_conf_path();
  inline ::std::string* release_proto_conf_path();
  inline void set_allocated_proto_conf_path(::std::string* proto_conf_path);

  // optional string ifx = 4 [default = "eth1"];
  inline bool has_ifx() const;
  inline void clear_ifx();
  static const int kIfxFieldNumber = 4;
  inline const ::std::string& ifx() const;
  inline void set_ifx(const ::std::string& value);
  inline void set_ifx(const char* value);
  inline void set_ifx(const char* value, size_t size);
  inline ::std::string* mutable_ifx();
  inline ::std::string* release_ifx();
  inline void set_allocated_ifx(::std::string* ifx);

  // optional uint32 port = 5 [default = 9971];
  inline bool has_port() const;
  inline void clear_port();
  static const int kPortFieldNumber = 5;
  inline ::google::protobuf::uint32 port() const;
  inline void set_port(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:baseagent.extern_proto_conf)
 private:
  inline void set_has_proto_type();
  inline void clear_has_proto_type();
  inline void set_has_proto_so_path();
  inline void clear_has_proto_so_path();
  inline void set_has_proto_conf_path();
  inline void clear_has_proto_conf_path();
  inline void set_has_ifx();
  inline void clear_has_ifx();
  inline void set_has_port();
  inline void clear_has_port();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::std::string* proto_so_path_;
  ::std::string* proto_conf_path_;
  ::google::protobuf::int32 proto_type_;
  ::google::protobuf::uint32 port_;
  static ::std::string* _default_ifx_;
  ::std::string* ifx_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();

  void InitAsDefaultInstance();
  static extern_proto_conf* default_instance_;
};
// -------------------------------------------------------------------

class stat_conf : public ::google::protobuf::Message {
 public:
  stat_conf();
  virtual ~stat_conf();

  stat_conf(const stat_conf& from);

  inline stat_conf& operator=(const stat_conf& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const stat_conf& default_instance();

  void Swap(stat_conf* other);

  // implements Message ----------------------------------------------

  stat_conf* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const stat_conf& from);
  void MergeFrom(const stat_conf& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string stat_so_path = 1;
  inline bool has_stat_so_path() const;
  inline void clear_stat_so_path();
  static const int kStatSoPathFieldNumber = 1;
  inline const ::std::string& stat_so_path() const;
  inline void set_stat_so_path(const ::std::string& value);
  inline void set_stat_so_path(const char* value);
  inline void set_stat_so_path(const char* value, size_t size);
  inline ::std::string* mutable_stat_so_path();
  inline ::std::string* release_stat_so_path();
  inline void set_allocated_stat_so_path(::std::string* stat_so_path);

  // optional string stat_conf_path = 2;
  inline bool has_stat_conf_path() const;
  inline void clear_stat_conf_path();
  static const int kStatConfPathFieldNumber = 2;
  inline const ::std::string& stat_conf_path() const;
  inline void set_stat_conf_path(const ::std::string& value);
  inline void set_stat_conf_path(const char* value);
  inline void set_stat_conf_path(const char* value, size_t size);
  inline ::std::string* mutable_stat_conf_path();
  inline ::std::string* release_stat_conf_path();
  inline void set_allocated_stat_conf_path(::std::string* stat_conf_path);

  // @@protoc_insertion_point(class_scope:baseagent.stat_conf)
 private:
  inline void set_has_stat_so_path();
  inline void clear_has_stat_so_path();
  inline void set_has_stat_conf_path();
  inline void clear_has_stat_conf_path();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::std::string* stat_so_path_;
  ::std::string* stat_conf_path_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();

  void InitAsDefaultInstance();
  static stat_conf* default_instance_;
};
// -------------------------------------------------------------------

class base_agent_conf : public ::google::protobuf::Message {
 public:
  base_agent_conf();
  virtual ~base_agent_conf();

  base_agent_conf(const base_agent_conf& from);

  inline base_agent_conf& operator=(const base_agent_conf& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const base_agent_conf& default_instance();

  void Swap(base_agent_conf* other);

  // implements Message ----------------------------------------------

  base_agent_conf* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const base_agent_conf& from);
  void MergeFrom(const base_agent_conf& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint32 global_mem_index = 1 [default = 3];
  inline bool has_global_mem_index() const;
  inline void clear_global_mem_index();
  static const int kGlobalMemIndexFieldNumber = 1;
  inline ::google::protobuf::uint32 global_mem_index() const;
  inline void set_global_mem_index(::google::protobuf::uint32 value);

  // optional string private_data_path = 2 [default = "/dev/shm/baseagent/private/"];
  inline bool has_private_data_path() const;
  inline void clear_private_data_path();
  static const int kPrivateDataPathFieldNumber = 2;
  inline const ::std::string& private_data_path() const;
  inline void set_private_data_path(const ::std::string& value);
  inline void set_private_data_path(const char* value);
  inline void set_private_data_path(const char* value, size_t size);
  inline ::std::string* mutable_private_data_path();
  inline ::std::string* release_private_data_path();
  inline void set_allocated_private_data_path(::std::string* private_data_path);

  // optional string name_data_path = 3 [default = "/data/baseagent/name_data/"];
  inline bool has_name_data_path() const;
  inline void clear_name_data_path();
  static const int kNameDataPathFieldNumber = 3;
  inline const ::std::string& name_data_path() const;
  inline void set_name_data_path(const ::std::string& value);
  inline void set_name_data_path(const char* value);
  inline void set_name_data_path(const char* value, size_t size);
  inline ::std::string* mutable_name_data_path();
  inline ::std::string* release_name_data_path();
  inline void set_allocated_name_data_path(::std::string* name_data_path);

  // optional string tmsg_data_path = 4 [default = "/dev/shm/baseagent/tmsg/"];
  inline bool has_tmsg_data_path() const;
  inline void clear_tmsg_data_path();
  static const int kTmsgDataPathFieldNumber = 4;
  inline const ::std::string& tmsg_data_path() const;
  inline void set_tmsg_data_path(const ::std::string& value);
  inline void set_tmsg_data_path(const char* value);
  inline void set_tmsg_data_path(const char* value, size_t size);
  inline ::std::string* mutable_tmsg_data_path();
  inline ::std::string* release_tmsg_data_path();
  inline void set_allocated_tmsg_data_path(::std::string* tmsg_data_path);

  // optional string troute_data_path = 5 [default = "/dev/shm/baseagent/troute/"];
  inline bool has_troute_data_path() const;
  inline void clear_troute_data_path();
  static const int kTrouteDataPathFieldNumber = 5;
  inline const ::std::string& troute_data_path() const;
  inline void set_troute_data_path(const ::std::string& value);
  inline void set_troute_data_path(const char* value);
  inline void set_troute_data_path(const char* value, size_t size);
  inline ::std::string* mutable_troute_data_path();
  inline ::std::string* release_troute_data_path();
  inline void set_allocated_troute_data_path(::std::string* troute_data_path);

  // optional .baseagent.tmsg_conf conf_tmsg = 6;
  inline bool has_conf_tmsg() const;
  inline void clear_conf_tmsg();
  static const int kConfTmsgFieldNumber = 6;
  inline const ::baseagent::tmsg_conf& conf_tmsg() const;
  inline ::baseagent::tmsg_conf* mutable_conf_tmsg();
  inline ::baseagent::tmsg_conf* release_conf_tmsg();
  inline void set_allocated_conf_tmsg(::baseagent::tmsg_conf* conf_tmsg);

  // optional .baseagent.troute_conf conf_troute = 7;
  inline bool has_conf_troute() const;
  inline void clear_conf_troute();
  static const int kConfTrouteFieldNumber = 7;
  inline const ::baseagent::troute_conf& conf_troute() const;
  inline ::baseagent::troute_conf* mutable_conf_troute();
  inline ::baseagent::troute_conf* release_conf_troute();
  inline void set_allocated_conf_troute(::baseagent::troute_conf* conf_troute);

  // optional .baseagent_comm.dbconf conf_nameserver = 8;
  inline bool has_conf_nameserver() const;
  inline void clear_conf_nameserver();
  static const int kConfNameserverFieldNumber = 8;
  inline const ::baseagent_comm::dbconf& conf_nameserver() const;
  inline ::baseagent_comm::dbconf* mutable_conf_nameserver();
  inline ::baseagent_comm::dbconf* release_conf_nameserver();
  inline void set_allocated_conf_nameserver(::baseagent_comm::dbconf* conf_nameserver);

  // optional .baseagent.net_conf conf_net = 9;
  inline bool has_conf_net() const;
  inline void clear_conf_net();
  static const int kConfNetFieldNumber = 9;
  inline const ::baseagent::net_conf& conf_net() const;
  inline ::baseagent::net_conf* mutable_conf_net();
  inline ::baseagent::net_conf* release_conf_net();
  inline void set_allocated_conf_net(::baseagent::net_conf* conf_net);

  // optional string ifx = 10 [default = "eth1"];
  inline bool has_ifx() const;
  inline void clear_ifx();
  static const int kIfxFieldNumber = 10;
  inline const ::std::string& ifx() const;
  inline void set_ifx(const ::std::string& value);
  inline void set_ifx(const char* value);
  inline void set_ifx(const char* value, size_t size);
  inline ::std::string* mutable_ifx();
  inline ::std::string* release_ifx();
  inline void set_allocated_ifx(::std::string* ifx);

  // optional uint32 port = 11 [default = 2001];
  inline bool has_port() const;
  inline void clear_port();
  static const int kPortFieldNumber = 11;
  inline ::google::protobuf::uint32 port() const;
  inline void set_port(::google::protobuf::uint32 value);

  // optional string private_data_name = 12 [default = "baseagent.data"];
  inline bool has_private_data_name() const;
  inline void clear_private_data_name();
  static const int kPrivateDataNameFieldNumber = 12;
  inline const ::std::string& private_data_name() const;
  inline void set_private_data_name(const ::std::string& value);
  inline void set_private_data_name(const char* value);
  inline void set_private_data_name(const char* value, size_t size);
  inline ::std::string* mutable_private_data_name();
  inline ::std::string* release_private_data_name();
  inline void set_allocated_private_data_name(::std::string* private_data_name);

  // optional .baseagent.busid_map_conf conf_busid_map = 13;
  inline bool has_conf_busid_map() const;
  inline void clear_conf_busid_map();
  static const int kConfBusidMapFieldNumber = 13;
  inline const ::baseagent::busid_map_conf& conf_busid_map() const;
  inline ::baseagent::busid_map_conf* mutable_conf_busid_map();
  inline ::baseagent::busid_map_conf* release_conf_busid_map();
  inline void set_allocated_conf_busid_map(::baseagent::busid_map_conf* conf_busid_map);

  // optional string busid_map_path = 15 [default = "/dev/shm/baseagent/busidmap/"];
  inline bool has_busid_map_path() const;
  inline void clear_busid_map_path();
  static const int kBusidMapPathFieldNumber = 15;
  inline const ::std::string& busid_map_path() const;
  inline void set_busid_map_path(const ::std::string& value);
  inline void set_busid_map_path(const char* value);
  inline void set_busid_map_path(const char* value, size_t size);
  inline ::std::string* mutable_busid_map_path();
  inline ::std::string* release_busid_map_path();
  inline void set_allocated_busid_map_path(::std::string* busid_map_path);

  // repeated .baseagent.extern_proto_conf proto_conf = 21;
  inline int proto_conf_size() const;
  inline void clear_proto_conf();
  static const int kProtoConfFieldNumber = 21;
  inline const ::baseagent::extern_proto_conf& proto_conf(int index) const;
  inline ::baseagent::extern_proto_conf* mutable_proto_conf(int index);
  inline ::baseagent::extern_proto_conf* add_proto_conf();
  inline const ::google::protobuf::RepeatedPtrField< ::baseagent::extern_proto_conf >&
      proto_conf() const;
  inline ::google::protobuf::RepeatedPtrField< ::baseagent::extern_proto_conf >*
      mutable_proto_conf();

  // repeated .baseagent.stat_conf conf_stat = 22;
  inline int conf_stat_size() const;
  inline void clear_conf_stat();
  static const int kConfStatFieldNumber = 22;
  inline const ::baseagent::stat_conf& conf_stat(int index) const;
  inline ::baseagent::stat_conf* mutable_conf_stat(int index);
  inline ::baseagent::stat_conf* add_conf_stat();
  inline const ::google::protobuf::RepeatedPtrField< ::baseagent::stat_conf >&
      conf_stat() const;
  inline ::google::protobuf::RepeatedPtrField< ::baseagent::stat_conf >*
      mutable_conf_stat();

  // @@protoc_insertion_point(class_scope:baseagent.base_agent_conf)
 private:
  inline void set_has_global_mem_index();
  inline void clear_has_global_mem_index();
  inline void set_has_private_data_path();
  inline void clear_has_private_data_path();
  inline void set_has_name_data_path();
  inline void clear_has_name_data_path();
  inline void set_has_tmsg_data_path();
  inline void clear_has_tmsg_data_path();
  inline void set_has_troute_data_path();
  inline void clear_has_troute_data_path();
  inline void set_has_conf_tmsg();
  inline void clear_has_conf_tmsg();
  inline void set_has_conf_troute();
  inline void clear_has_conf_troute();
  inline void set_has_conf_nameserver();
  inline void clear_has_conf_nameserver();
  inline void set_has_conf_net();
  inline void clear_has_conf_net();
  inline void set_has_ifx();
  inline void clear_has_ifx();
  inline void set_has_port();
  inline void clear_has_port();
  inline void set_has_private_data_name();
  inline void clear_has_private_data_name();
  inline void set_has_conf_busid_map();
  inline void clear_has_conf_busid_map();
  inline void set_has_busid_map_path();
  inline void clear_has_busid_map_path();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  static ::std::string* _default_private_data_path_;
  ::std::string* private_data_path_;
  static ::std::string* _default_name_data_path_;
  ::std::string* name_data_path_;
  static ::std::string* _default_tmsg_data_path_;
  ::std::string* tmsg_data_path_;
  static ::std::string* _default_troute_data_path_;
  ::std::string* troute_data_path_;
  ::baseagent::tmsg_conf* conf_tmsg_;
  ::google::protobuf::uint32 global_mem_index_;
  ::google::protobuf::uint32 port_;
  ::baseagent::troute_conf* conf_troute_;
  ::baseagent_comm::dbconf* conf_nameserver_;
  ::baseagent::net_conf* conf_net_;
  static ::std::string* _default_ifx_;
  ::std::string* ifx_;
  static ::std::string* _default_private_data_name_;
  ::std::string* private_data_name_;
  ::baseagent::busid_map_conf* conf_busid_map_;
  static ::std::string* _default_busid_map_path_;
  ::std::string* busid_map_path_;
  ::google::protobuf::RepeatedPtrField< ::baseagent::extern_proto_conf > proto_conf_;
  ::google::protobuf::RepeatedPtrField< ::baseagent::stat_conf > conf_stat_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();

  void InitAsDefaultInstance();
  static base_agent_conf* default_instance_;
};
// -------------------------------------------------------------------

class sync_name_server_info : public ::google::protobuf::Message {
 public:
  sync_name_server_info();
  virtual ~sync_name_server_info();

  sync_name_server_info(const sync_name_server_info& from);

  inline sync_name_server_info& operator=(const sync_name_server_info& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const sync_name_server_info& default_instance();

  void Swap(sync_name_server_info* other);

  // implements Message ----------------------------------------------

  sync_name_server_info* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const sync_name_server_info& from);
  void MergeFrom(const sync_name_server_info& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string sync_depend_list_fail = 1;
  inline bool has_sync_depend_list_fail() const;
  inline void clear_sync_depend_list_fail();
  static const int kSyncDependListFailFieldNumber = 1;
  inline const ::std::string& sync_depend_list_fail() const;
  inline void set_sync_depend_list_fail(const ::std::string& value);
  inline void set_sync_depend_list_fail(const char* value);
  inline void set_sync_depend_list_fail(const char* value, size_t size);
  inline ::std::string* mutable_sync_depend_list_fail();
  inline ::std::string* release_sync_depend_list_fail();
  inline void set_allocated_sync_depend_list_fail(::std::string* sync_depend_list_fail);

  // optional string register_instance_fail = 2;
  inline bool has_register_instance_fail() const;
  inline void clear_register_instance_fail();
  static const int kRegisterInstanceFailFieldNumber = 2;
  inline const ::std::string& register_instance_fail() const;
  inline void set_register_instance_fail(const ::std::string& value);
  inline void set_register_instance_fail(const char* value);
  inline void set_register_instance_fail(const char* value, size_t size);
  inline ::std::string* mutable_register_instance_fail();
  inline ::std::string* release_register_instance_fail();
  inline void set_allocated_register_instance_fail(::std::string* register_instance_fail);

  // optional string cancel_register_instance_fail = 3;
  inline bool has_cancel_register_instance_fail() const;
  inline void clear_cancel_register_instance_fail();
  static const int kCancelRegisterInstanceFailFieldNumber = 3;
  inline const ::std::string& cancel_register_instance_fail() const;
  inline void set_cancel_register_instance_fail(const ::std::string& value);
  inline void set_cancel_register_instance_fail(const char* value);
  inline void set_cancel_register_instance_fail(const char* value, size_t size);
  inline ::std::string* mutable_cancel_register_instance_fail();
  inline ::std::string* release_cancel_register_instance_fail();
  inline void set_allocated_cancel_register_instance_fail(::std::string* cancel_register_instance_fail);

  // @@protoc_insertion_point(class_scope:baseagent.sync_name_server_info)
 private:
  inline void set_has_sync_depend_list_fail();
  inline void clear_has_sync_depend_list_fail();
  inline void set_has_register_instance_fail();
  inline void clear_has_register_instance_fail();
  inline void set_has_cancel_register_instance_fail();
  inline void clear_has_cancel_register_instance_fail();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::std::string* sync_depend_list_fail_;
  ::std::string* register_instance_fail_;
  ::std::string* cancel_register_instance_fail_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();

  void InitAsDefaultInstance();
  static sync_name_server_info* default_instance_;
};
// -------------------------------------------------------------------

class name_list : public ::google::protobuf::Message {
 public:
  name_list();
  virtual ~name_list();

  name_list(const name_list& from);

  inline name_list& operator=(const name_list& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const name_list& default_instance();

  void Swap(name_list* other);

  // implements Message ----------------------------------------------

  name_list* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const name_list& from);
  void MergeFrom(const name_list& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string list = 1;
  inline bool has_list() const;
  inline void clear_list();
  static const int kListFieldNumber = 1;
  inline const ::std::string& list() const;
  inline void set_list(const ::std::string& value);
  inline void set_list(const char* value);
  inline void set_list(const char* value, size_t size);
  inline ::std::string* mutable_list();
  inline ::std::string* release_list();
  inline void set_allocated_list(::std::string* list);

  // @@protoc_insertion_point(class_scope:baseagent.name_list)
 private:
  inline void set_has_list();
  inline void clear_has_list();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::std::string* list_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();

  void InitAsDefaultInstance();
  static name_list* default_instance_;
};
// -------------------------------------------------------------------

class trans_info : public ::google::protobuf::Message {
 public:
  trans_info();
  virtual ~trans_info();

  trans_info(const trans_info& from);

  inline trans_info& operator=(const trans_info& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const trans_info& default_instance();

  void Swap(trans_info* other);

  // implements Message ----------------------------------------------

  trans_info* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const trans_info& from);
  void MergeFrom(const trans_info& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .troute.server_name_and_address src = 1;
  inline bool has_src() const;
  inline void clear_src();
  static const int kSrcFieldNumber = 1;
  inline const ::troute::server_name_and_address& src() const;
  inline ::troute::server_name_and_address* mutable_src();
  inline ::troute::server_name_and_address* release_src();
  inline void set_allocated_src(::troute::server_name_and_address* src);

  // optional .troute.server_name_and_address dst = 2;
  inline bool has_dst() const;
  inline void clear_dst();
  static const int kDstFieldNumber = 2;
  inline const ::troute::server_name_and_address& dst() const;
  inline ::troute::server_name_and_address* mutable_dst();
  inline ::troute::server_name_and_address* release_dst();
  inline void set_allocated_dst(::troute::server_name_and_address* dst);

  // @@protoc_insertion_point(class_scope:baseagent.trans_info)
 private:
  inline void set_has_src();
  inline void clear_has_src();
  inline void set_has_dst();
  inline void clear_has_dst();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::troute::server_name_and_address* src_;
  ::troute::server_name_and_address* dst_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();

  void InitAsDefaultInstance();
  static trans_info* default_instance_;
};
// -------------------------------------------------------------------

class event_handle : public ::google::protobuf::Message {
 public:
  event_handle();
  virtual ~event_handle();

  event_handle(const event_handle& from);

  inline event_handle& operator=(const event_handle& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const event_handle& default_instance();

  void Swap(event_handle* other);

  // implements Message ----------------------------------------------

  event_handle* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const event_handle& from);
  void MergeFrom(const event_handle& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint32 event_notify_id = 1;
  inline bool has_event_notify_id() const;
  inline void clear_event_notify_id();
  static const int kEventNotifyIdFieldNumber = 1;
  inline ::google::protobuf::uint32 event_notify_id() const;
  inline void set_event_notify_id(::google::protobuf::uint32 value);

  // optional uint32 version = 2;
  inline bool has_version() const;
  inline void clear_version();
  static const int kVersionFieldNumber = 2;
  inline ::google::protobuf::uint32 version() const;
  inline void set_version(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:baseagent.event_handle)
 private:
  inline void set_has_event_notify_id();
  inline void clear_has_event_notify_id();
  inline void set_has_version();
  inline void clear_has_version();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::uint32 event_notify_id_;
  ::google::protobuf::uint32 version_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();

  void InitAsDefaultInstance();
  static event_handle* default_instance_;
};
// -------------------------------------------------------------------

class event_handle_list : public ::google::protobuf::Message {
 public:
  event_handle_list();
  virtual ~event_handle_list();

  event_handle_list(const event_handle_list& from);

  inline event_handle_list& operator=(const event_handle_list& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const event_handle_list& default_instance();

  void Swap(event_handle_list* other);

  // implements Message ----------------------------------------------

  event_handle_list* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const event_handle_list& from);
  void MergeFrom(const event_handle_list& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .baseagent.event_handle list = 1;
  inline int list_size() const;
  inline void clear_list();
  static const int kListFieldNumber = 1;
  inline const ::baseagent::event_handle& list(int index) const;
  inline ::baseagent::event_handle* mutable_list(int index);
  inline ::baseagent::event_handle* add_list();
  inline const ::google::protobuf::RepeatedPtrField< ::baseagent::event_handle >&
      list() const;
  inline ::google::protobuf::RepeatedPtrField< ::baseagent::event_handle >*
      mutable_list();

  // @@protoc_insertion_point(class_scope:baseagent.event_handle_list)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::baseagent::event_handle > list_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto();

  void InitAsDefaultInstance();
  static event_handle_list* default_instance_;
};
// ===================================================================


// ===================================================================

// tmsg_conf

// optional uint32 tmsg_mgr_index = 1 [default = 0];
inline bool tmsg_conf::has_tmsg_mgr_index() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void tmsg_conf::set_has_tmsg_mgr_index() {
  _has_bits_[0] |= 0x00000001u;
}
inline void tmsg_conf::clear_has_tmsg_mgr_index() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void tmsg_conf::clear_tmsg_mgr_index() {
  tmsg_mgr_index_ = 0u;
  clear_has_tmsg_mgr_index();
}
inline ::google::protobuf::uint32 tmsg_conf::tmsg_mgr_index() const {
  // @@protoc_insertion_point(field_get:baseagent.tmsg_conf.tmsg_mgr_index)
  return tmsg_mgr_index_;
}
inline void tmsg_conf::set_tmsg_mgr_index(::google::protobuf::uint32 value) {
  set_has_tmsg_mgr_index();
  tmsg_mgr_index_ = value;
  // @@protoc_insertion_point(field_set:baseagent.tmsg_conf.tmsg_mgr_index)
}

// optional uint32 tmsg_data_index = 2 [default = 1];
inline bool tmsg_conf::has_tmsg_data_index() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void tmsg_conf::set_has_tmsg_data_index() {
  _has_bits_[0] |= 0x00000002u;
}
inline void tmsg_conf::clear_has_tmsg_data_index() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void tmsg_conf::clear_tmsg_data_index() {
  tmsg_data_index_ = 1u;
  clear_has_tmsg_data_index();
}
inline ::google::protobuf::uint32 tmsg_conf::tmsg_data_index() const {
  // @@protoc_insertion_point(field_get:baseagent.tmsg_conf.tmsg_data_index)
  return tmsg_data_index_;
}
inline void tmsg_conf::set_tmsg_data_index(::google::protobuf::uint32 value) {
  set_has_tmsg_data_index();
  tmsg_data_index_ = value;
  // @@protoc_insertion_point(field_set:baseagent.tmsg_conf.tmsg_data_index)
}

// optional uint32 max_queue_count = 3 [default = 500];
inline bool tmsg_conf::has_max_queue_count() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void tmsg_conf::set_has_max_queue_count() {
  _has_bits_[0] |= 0x00000004u;
}
inline void tmsg_conf::clear_has_max_queue_count() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void tmsg_conf::clear_max_queue_count() {
  max_queue_count_ = 500u;
  clear_has_max_queue_count();
}
inline ::google::protobuf::uint32 tmsg_conf::max_queue_count() const {
  // @@protoc_insertion_point(field_get:baseagent.tmsg_conf.max_queue_count)
  return max_queue_count_;
}
inline void tmsg_conf::set_max_queue_count(::google::protobuf::uint32 value) {
  set_has_max_queue_count();
  max_queue_count_ = value;
  // @@protoc_insertion_point(field_set:baseagent.tmsg_conf.max_queue_count)
}

// optional uint32 max_sub_queue_count = 4 [default = 10];
inline bool tmsg_conf::has_max_sub_queue_count() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void tmsg_conf::set_has_max_sub_queue_count() {
  _has_bits_[0] |= 0x00000008u;
}
inline void tmsg_conf::clear_has_max_sub_queue_count() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void tmsg_conf::clear_max_sub_queue_count() {
  max_sub_queue_count_ = 10u;
  clear_has_max_sub_queue_count();
}
inline ::google::protobuf::uint32 tmsg_conf::max_sub_queue_count() const {
  // @@protoc_insertion_point(field_get:baseagent.tmsg_conf.max_sub_queue_count)
  return max_sub_queue_count_;
}
inline void tmsg_conf::set_max_sub_queue_count(::google::protobuf::uint32 value) {
  set_has_max_sub_queue_count();
  max_sub_queue_count_ = value;
  // @@protoc_insertion_point(field_set:baseagent.tmsg_conf.max_sub_queue_count)
}

// optional uint32 max_observer_count = 5 [default = 500];
inline bool tmsg_conf::has_max_observer_count() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void tmsg_conf::set_has_max_observer_count() {
  _has_bits_[0] |= 0x00000010u;
}
inline void tmsg_conf::clear_has_max_observer_count() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void tmsg_conf::clear_max_observer_count() {
  max_observer_count_ = 500u;
  clear_has_max_observer_count();
}
inline ::google::protobuf::uint32 tmsg_conf::max_observer_count() const {
  // @@protoc_insertion_point(field_get:baseagent.tmsg_conf.max_observer_count)
  return max_observer_count_;
}
inline void tmsg_conf::set_max_observer_count(::google::protobuf::uint32 value) {
  set_has_max_observer_count();
  max_observer_count_ = value;
  // @@protoc_insertion_point(field_set:baseagent.tmsg_conf.max_observer_count)
}

// optional uint32 observer_max_event_count = 6 [default = 10];
inline bool tmsg_conf::has_observer_max_event_count() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void tmsg_conf::set_has_observer_max_event_count() {
  _has_bits_[0] |= 0x00000020u;
}
inline void tmsg_conf::clear_has_observer_max_event_count() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void tmsg_conf::clear_observer_max_event_count() {
  observer_max_event_count_ = 10u;
  clear_has_observer_max_event_count();
}
inline ::google::protobuf::uint32 tmsg_conf::observer_max_event_count() const {
  // @@protoc_insertion_point(field_get:baseagent.tmsg_conf.observer_max_event_count)
  return observer_max_event_count_;
}
inline void tmsg_conf::set_observer_max_event_count(::google::protobuf::uint32 value) {
  set_has_observer_max_event_count();
  observer_max_event_count_ = value;
  // @@protoc_insertion_point(field_set:baseagent.tmsg_conf.observer_max_event_count)
}

// optional uint32 max_event_mgr_count = 7 [default = 100];
inline bool tmsg_conf::has_max_event_mgr_count() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void tmsg_conf::set_has_max_event_mgr_count() {
  _has_bits_[0] |= 0x00000040u;
}
inline void tmsg_conf::clear_has_max_event_mgr_count() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void tmsg_conf::clear_max_event_mgr_count() {
  max_event_mgr_count_ = 100u;
  clear_has_max_event_mgr_count();
}
inline ::google::protobuf::uint32 tmsg_conf::max_event_mgr_count() const {
  // @@protoc_insertion_point(field_get:baseagent.tmsg_conf.max_event_mgr_count)
  return max_event_mgr_count_;
}
inline void tmsg_conf::set_max_event_mgr_count(::google::protobuf::uint32 value) {
  set_has_max_event_mgr_count();
  max_event_mgr_count_ = value;
  // @@protoc_insertion_point(field_set:baseagent.tmsg_conf.max_event_mgr_count)
}

// optional uint32 max_data_block_count = 8 [default = 500];
inline bool tmsg_conf::has_max_data_block_count() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void tmsg_conf::set_has_max_data_block_count() {
  _has_bits_[0] |= 0x00000080u;
}
inline void tmsg_conf::clear_has_max_data_block_count() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void tmsg_conf::clear_max_data_block_count() {
  max_data_block_count_ = 500u;
  clear_has_max_data_block_count();
}
inline ::google::protobuf::uint32 tmsg_conf::max_data_block_count() const {
  // @@protoc_insertion_point(field_get:baseagent.tmsg_conf.max_data_block_count)
  return max_data_block_count_;
}
inline void tmsg_conf::set_max_data_block_count(::google::protobuf::uint32 value) {
  set_has_max_data_block_count();
  max_data_block_count_ = value;
  // @@protoc_insertion_point(field_set:baseagent.tmsg_conf.max_data_block_count)
}

// optional uint32 data_block_size = 9 [default = 4096];
inline bool tmsg_conf::has_data_block_size() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void tmsg_conf::set_has_data_block_size() {
  _has_bits_[0] |= 0x00000100u;
}
inline void tmsg_conf::clear_has_data_block_size() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void tmsg_conf::clear_data_block_size() {
  data_block_size_ = 4096u;
  clear_has_data_block_size();
}
inline ::google::protobuf::uint32 tmsg_conf::data_block_size() const {
  // @@protoc_insertion_point(field_get:baseagent.tmsg_conf.data_block_size)
  return data_block_size_;
}
inline void tmsg_conf::set_data_block_size(::google::protobuf::uint32 value) {
  set_has_data_block_size();
  data_block_size_ = value;
  // @@protoc_insertion_point(field_set:baseagent.tmsg_conf.data_block_size)
}

// optional uint32 mem_type = 10 [default = 3];
inline bool tmsg_conf::has_mem_type() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
inline void tmsg_conf::set_has_mem_type() {
  _has_bits_[0] |= 0x00000200u;
}
inline void tmsg_conf::clear_has_mem_type() {
  _has_bits_[0] &= ~0x00000200u;
}
inline void tmsg_conf::clear_mem_type() {
  mem_type_ = 3u;
  clear_has_mem_type();
}
inline ::google::protobuf::uint32 tmsg_conf::mem_type() const {
  // @@protoc_insertion_point(field_get:baseagent.tmsg_conf.mem_type)
  return mem_type_;
}
inline void tmsg_conf::set_mem_type(::google::protobuf::uint32 value) {
  set_has_mem_type();
  mem_type_ = value;
  // @@protoc_insertion_point(field_set:baseagent.tmsg_conf.mem_type)
}

// optional uint32 mgr_shmkey = 11 [default = 210001];
inline bool tmsg_conf::has_mgr_shmkey() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
inline void tmsg_conf::set_has_mgr_shmkey() {
  _has_bits_[0] |= 0x00000400u;
}
inline void tmsg_conf::clear_has_mgr_shmkey() {
  _has_bits_[0] &= ~0x00000400u;
}
inline void tmsg_conf::clear_mgr_shmkey() {
  mgr_shmkey_ = 210001u;
  clear_has_mgr_shmkey();
}
inline ::google::protobuf::uint32 tmsg_conf::mgr_shmkey() const {
  // @@protoc_insertion_point(field_get:baseagent.tmsg_conf.mgr_shmkey)
  return mgr_shmkey_;
}
inline void tmsg_conf::set_mgr_shmkey(::google::protobuf::uint32 value) {
  set_has_mgr_shmkey();
  mgr_shmkey_ = value;
  // @@protoc_insertion_point(field_set:baseagent.tmsg_conf.mgr_shmkey)
}

// optional uint32 data_shmkey = 12 [default = 210002];
inline bool tmsg_conf::has_data_shmkey() const {
  return (_has_bits_[0] & 0x00000800u) != 0;
}
inline void tmsg_conf::set_has_data_shmkey() {
  _has_bits_[0] |= 0x00000800u;
}
inline void tmsg_conf::clear_has_data_shmkey() {
  _has_bits_[0] &= ~0x00000800u;
}
inline void tmsg_conf::clear_data_shmkey() {
  data_shmkey_ = 210002u;
  clear_has_data_shmkey();
}
inline ::google::protobuf::uint32 tmsg_conf::data_shmkey() const {
  // @@protoc_insertion_point(field_get:baseagent.tmsg_conf.data_shmkey)
  return data_shmkey_;
}
inline void tmsg_conf::set_data_shmkey(::google::protobuf::uint32 value) {
  set_has_data_shmkey();
  data_shmkey_ = value;
  // @@protoc_insertion_point(field_set:baseagent.tmsg_conf.data_shmkey)
}

// optional string mgr_map_file = 13 [default = "/dev/shm/baseagent/tmsg/mgr.data"];
inline bool tmsg_conf::has_mgr_map_file() const {
  return (_has_bits_[0] & 0x00001000u) != 0;
}
inline void tmsg_conf::set_has_mgr_map_file() {
  _has_bits_[0] |= 0x00001000u;
}
inline void tmsg_conf::clear_has_mgr_map_file() {
  _has_bits_[0] &= ~0x00001000u;
}
inline void tmsg_conf::clear_mgr_map_file() {
  if (mgr_map_file_ != _default_mgr_map_file_) {
    mgr_map_file_->assign(*_default_mgr_map_file_);
  }
  clear_has_mgr_map_file();
}
inline const ::std::string& tmsg_conf::mgr_map_file() const {
  // @@protoc_insertion_point(field_get:baseagent.tmsg_conf.mgr_map_file)
  return *mgr_map_file_;
}
inline void tmsg_conf::set_mgr_map_file(const ::std::string& value) {
  set_has_mgr_map_file();
  if (mgr_map_file_ == _default_mgr_map_file_) {
    mgr_map_file_ = new ::std::string;
  }
  mgr_map_file_->assign(value);
  // @@protoc_insertion_point(field_set:baseagent.tmsg_conf.mgr_map_file)
}
inline void tmsg_conf::set_mgr_map_file(const char* value) {
  set_has_mgr_map_file();
  if (mgr_map_file_ == _default_mgr_map_file_) {
    mgr_map_file_ = new ::std::string;
  }
  mgr_map_file_->assign(value);
  // @@protoc_insertion_point(field_set_char:baseagent.tmsg_conf.mgr_map_file)
}
inline void tmsg_conf::set_mgr_map_file(const char* value, size_t size) {
  set_has_mgr_map_file();
  if (mgr_map_file_ == _default_mgr_map_file_) {
    mgr_map_file_ = new ::std::string;
  }
  mgr_map_file_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:baseagent.tmsg_conf.mgr_map_file)
}
inline ::std::string* tmsg_conf::mutable_mgr_map_file() {
  set_has_mgr_map_file();
  if (mgr_map_file_ == _default_mgr_map_file_) {
    mgr_map_file_ = new ::std::string(*_default_mgr_map_file_);
  }
  // @@protoc_insertion_point(field_mutable:baseagent.tmsg_conf.mgr_map_file)
  return mgr_map_file_;
}
inline ::std::string* tmsg_conf::release_mgr_map_file() {
  clear_has_mgr_map_file();
  if (mgr_map_file_ == _default_mgr_map_file_) {
    return NULL;
  } else {
    ::std::string* temp = mgr_map_file_;
    mgr_map_file_ = const_cast< ::std::string*>(_default_mgr_map_file_);
    return temp;
  }
}
inline void tmsg_conf::set_allocated_mgr_map_file(::std::string* mgr_map_file) {
  if (mgr_map_file_ != _default_mgr_map_file_) {
    delete mgr_map_file_;
  }
  if (mgr_map_file) {
    set_has_mgr_map_file();
    mgr_map_file_ = mgr_map_file;
  } else {
    clear_has_mgr_map_file();
    mgr_map_file_ = const_cast< ::std::string*>(_default_mgr_map_file_);
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.tmsg_conf.mgr_map_file)
}

// optional string data_map_file = 14 [default = "/dev/shm/baseagent/tmsg/msg.data"];
inline bool tmsg_conf::has_data_map_file() const {
  return (_has_bits_[0] & 0x00002000u) != 0;
}
inline void tmsg_conf::set_has_data_map_file() {
  _has_bits_[0] |= 0x00002000u;
}
inline void tmsg_conf::clear_has_data_map_file() {
  _has_bits_[0] &= ~0x00002000u;
}
inline void tmsg_conf::clear_data_map_file() {
  if (data_map_file_ != _default_data_map_file_) {
    data_map_file_->assign(*_default_data_map_file_);
  }
  clear_has_data_map_file();
}
inline const ::std::string& tmsg_conf::data_map_file() const {
  // @@protoc_insertion_point(field_get:baseagent.tmsg_conf.data_map_file)
  return *data_map_file_;
}
inline void tmsg_conf::set_data_map_file(const ::std::string& value) {
  set_has_data_map_file();
  if (data_map_file_ == _default_data_map_file_) {
    data_map_file_ = new ::std::string;
  }
  data_map_file_->assign(value);
  // @@protoc_insertion_point(field_set:baseagent.tmsg_conf.data_map_file)
}
inline void tmsg_conf::set_data_map_file(const char* value) {
  set_has_data_map_file();
  if (data_map_file_ == _default_data_map_file_) {
    data_map_file_ = new ::std::string;
  }
  data_map_file_->assign(value);
  // @@protoc_insertion_point(field_set_char:baseagent.tmsg_conf.data_map_file)
}
inline void tmsg_conf::set_data_map_file(const char* value, size_t size) {
  set_has_data_map_file();
  if (data_map_file_ == _default_data_map_file_) {
    data_map_file_ = new ::std::string;
  }
  data_map_file_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:baseagent.tmsg_conf.data_map_file)
}
inline ::std::string* tmsg_conf::mutable_data_map_file() {
  set_has_data_map_file();
  if (data_map_file_ == _default_data_map_file_) {
    data_map_file_ = new ::std::string(*_default_data_map_file_);
  }
  // @@protoc_insertion_point(field_mutable:baseagent.tmsg_conf.data_map_file)
  return data_map_file_;
}
inline ::std::string* tmsg_conf::release_data_map_file() {
  clear_has_data_map_file();
  if (data_map_file_ == _default_data_map_file_) {
    return NULL;
  } else {
    ::std::string* temp = data_map_file_;
    data_map_file_ = const_cast< ::std::string*>(_default_data_map_file_);
    return temp;
  }
}
inline void tmsg_conf::set_allocated_data_map_file(::std::string* data_map_file) {
  if (data_map_file_ != _default_data_map_file_) {
    delete data_map_file_;
  }
  if (data_map_file) {
    set_has_data_map_file();
    data_map_file_ = data_map_file;
  } else {
    clear_has_data_map_file();
    data_map_file_ = const_cast< ::std::string*>(_default_data_map_file_);
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.tmsg_conf.data_map_file)
}

// -------------------------------------------------------------------

// troute_conf

// optional uint32 global_mem_index = 1 [default = 2];
inline bool troute_conf::has_global_mem_index() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void troute_conf::set_has_global_mem_index() {
  _has_bits_[0] |= 0x00000001u;
}
inline void troute_conf::clear_has_global_mem_index() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void troute_conf::clear_global_mem_index() {
  global_mem_index_ = 2u;
  clear_has_global_mem_index();
}
inline ::google::protobuf::uint32 troute_conf::global_mem_index() const {
  // @@protoc_insertion_point(field_get:baseagent.troute_conf.global_mem_index)
  return global_mem_index_;
}
inline void troute_conf::set_global_mem_index(::google::protobuf::uint32 value) {
  set_has_global_mem_index();
  global_mem_index_ = value;
  // @@protoc_insertion_point(field_set:baseagent.troute_conf.global_mem_index)
}

// optional uint32 bucket_count = 2 [default = 50];
inline bool troute_conf::has_bucket_count() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void troute_conf::set_has_bucket_count() {
  _has_bits_[0] |= 0x00000002u;
}
inline void troute_conf::clear_has_bucket_count() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void troute_conf::clear_bucket_count() {
  bucket_count_ = 50u;
  clear_has_bucket_count();
}
inline ::google::protobuf::uint32 troute_conf::bucket_count() const {
  // @@protoc_insertion_point(field_get:baseagent.troute_conf.bucket_count)
  return bucket_count_;
}
inline void troute_conf::set_bucket_count(::google::protobuf::uint32 value) {
  set_has_bucket_count();
  bucket_count_ = value;
  // @@protoc_insertion_point(field_set:baseagent.troute_conf.bucket_count)
}

// optional uint32 name_count = 3 [default = 50];
inline bool troute_conf::has_name_count() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void troute_conf::set_has_name_count() {
  _has_bits_[0] |= 0x00000004u;
}
inline void troute_conf::clear_has_name_count() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void troute_conf::clear_name_count() {
  name_count_ = 50u;
  clear_has_name_count();
}
inline ::google::protobuf::uint32 troute_conf::name_count() const {
  // @@protoc_insertion_point(field_get:baseagent.troute_conf.name_count)
  return name_count_;
}
inline void troute_conf::set_name_count(::google::protobuf::uint32 value) {
  set_has_name_count();
  name_count_ = value;
  // @@protoc_insertion_point(field_set:baseagent.troute_conf.name_count)
}

// optional uint32 address_count = 4 [default = 2000];
inline bool troute_conf::has_address_count() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void troute_conf::set_has_address_count() {
  _has_bits_[0] |= 0x00000008u;
}
inline void troute_conf::clear_has_address_count() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void troute_conf::clear_address_count() {
  address_count_ = 2000u;
  clear_has_address_count();
}
inline ::google::protobuf::uint32 troute_conf::address_count() const {
  // @@protoc_insertion_point(field_get:baseagent.troute_conf.address_count)
  return address_count_;
}
inline void troute_conf::set_address_count(::google::protobuf::uint32 value) {
  set_has_address_count();
  address_count_ = value;
  // @@protoc_insertion_point(field_set:baseagent.troute_conf.address_count)
}

// optional uint32 mem_type = 5 [default = 3];
inline bool troute_conf::has_mem_type() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void troute_conf::set_has_mem_type() {
  _has_bits_[0] |= 0x00000010u;
}
inline void troute_conf::clear_has_mem_type() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void troute_conf::clear_mem_type() {
  mem_type_ = 3u;
  clear_has_mem_type();
}
inline ::google::protobuf::uint32 troute_conf::mem_type() const {
  // @@protoc_insertion_point(field_get:baseagent.troute_conf.mem_type)
  return mem_type_;
}
inline void troute_conf::set_mem_type(::google::protobuf::uint32 value) {
  set_has_mem_type();
  mem_type_ = value;
  // @@protoc_insertion_point(field_set:baseagent.troute_conf.mem_type)
}

// optional uint32 shmkey = 6 [default = 210003];
inline bool troute_conf::has_shmkey() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void troute_conf::set_has_shmkey() {
  _has_bits_[0] |= 0x00000020u;
}
inline void troute_conf::clear_has_shmkey() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void troute_conf::clear_shmkey() {
  shmkey_ = 210003u;
  clear_has_shmkey();
}
inline ::google::protobuf::uint32 troute_conf::shmkey() const {
  // @@protoc_insertion_point(field_get:baseagent.troute_conf.shmkey)
  return shmkey_;
}
inline void troute_conf::set_shmkey(::google::protobuf::uint32 value) {
  set_has_shmkey();
  shmkey_ = value;
  // @@protoc_insertion_point(field_set:baseagent.troute_conf.shmkey)
}

// optional string mgr_map_file = 7 [default = "/dev/shm/baseagent/troute/troue.data"];
inline bool troute_conf::has_mgr_map_file() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void troute_conf::set_has_mgr_map_file() {
  _has_bits_[0] |= 0x00000040u;
}
inline void troute_conf::clear_has_mgr_map_file() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void troute_conf::clear_mgr_map_file() {
  if (mgr_map_file_ != _default_mgr_map_file_) {
    mgr_map_file_->assign(*_default_mgr_map_file_);
  }
  clear_has_mgr_map_file();
}
inline const ::std::string& troute_conf::mgr_map_file() const {
  // @@protoc_insertion_point(field_get:baseagent.troute_conf.mgr_map_file)
  return *mgr_map_file_;
}
inline void troute_conf::set_mgr_map_file(const ::std::string& value) {
  set_has_mgr_map_file();
  if (mgr_map_file_ == _default_mgr_map_file_) {
    mgr_map_file_ = new ::std::string;
  }
  mgr_map_file_->assign(value);
  // @@protoc_insertion_point(field_set:baseagent.troute_conf.mgr_map_file)
}
inline void troute_conf::set_mgr_map_file(const char* value) {
  set_has_mgr_map_file();
  if (mgr_map_file_ == _default_mgr_map_file_) {
    mgr_map_file_ = new ::std::string;
  }
  mgr_map_file_->assign(value);
  // @@protoc_insertion_point(field_set_char:baseagent.troute_conf.mgr_map_file)
}
inline void troute_conf::set_mgr_map_file(const char* value, size_t size) {
  set_has_mgr_map_file();
  if (mgr_map_file_ == _default_mgr_map_file_) {
    mgr_map_file_ = new ::std::string;
  }
  mgr_map_file_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:baseagent.troute_conf.mgr_map_file)
}
inline ::std::string* troute_conf::mutable_mgr_map_file() {
  set_has_mgr_map_file();
  if (mgr_map_file_ == _default_mgr_map_file_) {
    mgr_map_file_ = new ::std::string(*_default_mgr_map_file_);
  }
  // @@protoc_insertion_point(field_mutable:baseagent.troute_conf.mgr_map_file)
  return mgr_map_file_;
}
inline ::std::string* troute_conf::release_mgr_map_file() {
  clear_has_mgr_map_file();
  if (mgr_map_file_ == _default_mgr_map_file_) {
    return NULL;
  } else {
    ::std::string* temp = mgr_map_file_;
    mgr_map_file_ = const_cast< ::std::string*>(_default_mgr_map_file_);
    return temp;
  }
}
inline void troute_conf::set_allocated_mgr_map_file(::std::string* mgr_map_file) {
  if (mgr_map_file_ != _default_mgr_map_file_) {
    delete mgr_map_file_;
  }
  if (mgr_map_file) {
    set_has_mgr_map_file();
    mgr_map_file_ = mgr_map_file;
  } else {
    clear_has_mgr_map_file();
    mgr_map_file_ = const_cast< ::std::string*>(_default_mgr_map_file_);
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.troute_conf.mgr_map_file)
}

// optional double err_rate_limit = 8 [default = 0.2];
inline bool troute_conf::has_err_rate_limit() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void troute_conf::set_has_err_rate_limit() {
  _has_bits_[0] |= 0x00000080u;
}
inline void troute_conf::clear_has_err_rate_limit() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void troute_conf::clear_err_rate_limit() {
  err_rate_limit_ = 0.2;
  clear_has_err_rate_limit();
}
inline double troute_conf::err_rate_limit() const {
  // @@protoc_insertion_point(field_get:baseagent.troute_conf.err_rate_limit)
  return err_rate_limit_;
}
inline void troute_conf::set_err_rate_limit(double value) {
  set_has_err_rate_limit();
  err_rate_limit_ = value;
  // @@protoc_insertion_point(field_set:baseagent.troute_conf.err_rate_limit)
}

// optional double server_normal_expand_rate = 9 [default = 0.2];
inline bool troute_conf::has_server_normal_expand_rate() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void troute_conf::set_has_server_normal_expand_rate() {
  _has_bits_[0] |= 0x00000100u;
}
inline void troute_conf::clear_has_server_normal_expand_rate() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void troute_conf::clear_server_normal_expand_rate() {
  server_normal_expand_rate_ = 0.2;
  clear_has_server_normal_expand_rate();
}
inline double troute_conf::server_normal_expand_rate() const {
  // @@protoc_insertion_point(field_get:baseagent.troute_conf.server_normal_expand_rate)
  return server_normal_expand_rate_;
}
inline void troute_conf::set_server_normal_expand_rate(double value) {
  set_has_server_normal_expand_rate();
  server_normal_expand_rate_ = value;
  // @@protoc_insertion_point(field_set:baseagent.troute_conf.server_normal_expand_rate)
}

// optional double min_req_down_rate = 10 [default = 0.1];
inline bool troute_conf::has_min_req_down_rate() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
inline void troute_conf::set_has_min_req_down_rate() {
  _has_bits_[0] |= 0x00000200u;
}
inline void troute_conf::clear_has_min_req_down_rate() {
  _has_bits_[0] &= ~0x00000200u;
}
inline void troute_conf::clear_min_req_down_rate() {
  min_req_down_rate_ = 0.1;
  clear_has_min_req_down_rate();
}
inline double troute_conf::min_req_down_rate() const {
  // @@protoc_insertion_point(field_get:baseagent.troute_conf.min_req_down_rate)
  return min_req_down_rate_;
}
inline void troute_conf::set_min_req_down_rate(double value) {
  set_has_min_req_down_rate();
  min_req_down_rate_ = value;
  // @@protoc_insertion_point(field_set:baseagent.troute_conf.min_req_down_rate)
}

// optional double min_err_rate_down_rate = 11 [default = 0.05];
inline bool troute_conf::has_min_err_rate_down_rate() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
inline void troute_conf::set_has_min_err_rate_down_rate() {
  _has_bits_[0] |= 0x00000400u;
}
inline void troute_conf::clear_has_min_err_rate_down_rate() {
  _has_bits_[0] &= ~0x00000400u;
}
inline void troute_conf::clear_min_err_rate_down_rate() {
  min_err_rate_down_rate_ = 0.05;
  clear_has_min_err_rate_down_rate();
}
inline double troute_conf::min_err_rate_down_rate() const {
  // @@protoc_insertion_point(field_get:baseagent.troute_conf.min_err_rate_down_rate)
  return min_err_rate_down_rate_;
}
inline void troute_conf::set_min_err_rate_down_rate(double value) {
  set_has_min_err_rate_down_rate();
  min_err_rate_down_rate_ = value;
  // @@protoc_insertion_point(field_set:baseagent.troute_conf.min_err_rate_down_rate)
}

// optional uint32 min_non_pressure_fault_periodic = 12 [default = 2];
inline bool troute_conf::has_min_non_pressure_fault_periodic() const {
  return (_has_bits_[0] & 0x00000800u) != 0;
}
inline void troute_conf::set_has_min_non_pressure_fault_periodic() {
  _has_bits_[0] |= 0x00000800u;
}
inline void troute_conf::clear_has_min_non_pressure_fault_periodic() {
  _has_bits_[0] &= ~0x00000800u;
}
inline void troute_conf::clear_min_non_pressure_fault_periodic() {
  min_non_pressure_fault_periodic_ = 2u;
  clear_has_min_non_pressure_fault_periodic();
}
inline ::google::protobuf::uint32 troute_conf::min_non_pressure_fault_periodic() const {
  // @@protoc_insertion_point(field_get:baseagent.troute_conf.min_non_pressure_fault_periodic)
  return min_non_pressure_fault_periodic_;
}
inline void troute_conf::set_min_non_pressure_fault_periodic(::google::protobuf::uint32 value) {
  set_has_min_non_pressure_fault_periodic();
  min_non_pressure_fault_periodic_ = value;
  // @@protoc_insertion_point(field_set:baseagent.troute_conf.min_non_pressure_fault_periodic)
}

// optional double min_non_pressure_fault_bias_ratio = 13 [default = 0.2];
inline bool troute_conf::has_min_non_pressure_fault_bias_ratio() const {
  return (_has_bits_[0] & 0x00001000u) != 0;
}
inline void troute_conf::set_has_min_non_pressure_fault_bias_ratio() {
  _has_bits_[0] |= 0x00001000u;
}
inline void troute_conf::clear_has_min_non_pressure_fault_bias_ratio() {
  _has_bits_[0] &= ~0x00001000u;
}
inline void troute_conf::clear_min_non_pressure_fault_bias_ratio() {
  min_non_pressure_fault_bias_ratio_ = 0.2;
  clear_has_min_non_pressure_fault_bias_ratio();
}
inline double troute_conf::min_non_pressure_fault_bias_ratio() const {
  // @@protoc_insertion_point(field_get:baseagent.troute_conf.min_non_pressure_fault_bias_ratio)
  return min_non_pressure_fault_bias_ratio_;
}
inline void troute_conf::set_min_non_pressure_fault_bias_ratio(double value) {
  set_has_min_non_pressure_fault_bias_ratio();
  min_non_pressure_fault_bias_ratio_ = value;
  // @@protoc_insertion_point(field_set:baseagent.troute_conf.min_non_pressure_fault_bias_ratio)
}

// optional uint32 route_pri_calculating_period = 14 [default = 60];
inline bool troute_conf::has_route_pri_calculating_period() const {
  return (_has_bits_[0] & 0x00002000u) != 0;
}
inline void troute_conf::set_has_route_pri_calculating_period() {
  _has_bits_[0] |= 0x00002000u;
}
inline void troute_conf::clear_has_route_pri_calculating_period() {
  _has_bits_[0] &= ~0x00002000u;
}
inline void troute_conf::clear_route_pri_calculating_period() {
  route_pri_calculating_period_ = 60u;
  clear_has_route_pri_calculating_period();
}
inline ::google::protobuf::uint32 troute_conf::route_pri_calculating_period() const {
  // @@protoc_insertion_point(field_get:baseagent.troute_conf.route_pri_calculating_period)
  return route_pri_calculating_period_;
}
inline void troute_conf::set_route_pri_calculating_period(::google::protobuf::uint32 value) {
  set_has_route_pri_calculating_period();
  route_pri_calculating_period_ = value;
  // @@protoc_insertion_point(field_set:baseagent.troute_conf.route_pri_calculating_period)
}

// optional uint32 server_io_up_limit = 15 [default = 5000];
inline bool troute_conf::has_server_io_up_limit() const {
  return (_has_bits_[0] & 0x00004000u) != 0;
}
inline void troute_conf::set_has_server_io_up_limit() {
  _has_bits_[0] |= 0x00004000u;
}
inline void troute_conf::clear_has_server_io_up_limit() {
  _has_bits_[0] &= ~0x00004000u;
}
inline void troute_conf::clear_server_io_up_limit() {
  server_io_up_limit_ = 5000u;
  clear_has_server_io_up_limit();
}
inline ::google::protobuf::uint32 troute_conf::server_io_up_limit() const {
  // @@protoc_insertion_point(field_get:baseagent.troute_conf.server_io_up_limit)
  return server_io_up_limit_;
}
inline void troute_conf::set_server_io_up_limit(::google::protobuf::uint32 value) {
  set_has_server_io_up_limit();
  server_io_up_limit_ = value;
  // @@protoc_insertion_point(field_set:baseagent.troute_conf.server_io_up_limit)
}

// optional uint32 server_io_down_limit = 16 [default = 10];
inline bool troute_conf::has_server_io_down_limit() const {
  return (_has_bits_[0] & 0x00008000u) != 0;
}
inline void troute_conf::set_has_server_io_down_limit() {
  _has_bits_[0] |= 0x00008000u;
}
inline void troute_conf::clear_has_server_io_down_limit() {
  _has_bits_[0] &= ~0x00008000u;
}
inline void troute_conf::clear_server_io_down_limit() {
  server_io_down_limit_ = 10u;
  clear_has_server_io_down_limit();
}
inline ::google::protobuf::uint32 troute_conf::server_io_down_limit() const {
  // @@protoc_insertion_point(field_get:baseagent.troute_conf.server_io_down_limit)
  return server_io_down_limit_;
}
inline void troute_conf::set_server_io_down_limit(::google::protobuf::uint32 value) {
  set_has_server_io_down_limit();
  server_io_down_limit_ = value;
  // @@protoc_insertion_point(field_set:baseagent.troute_conf.server_io_down_limit)
}

// -------------------------------------------------------------------

// net_conf

// optional bool use_nagle = 1 [default = true];
inline bool net_conf::has_use_nagle() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void net_conf::set_has_use_nagle() {
  _has_bits_[0] |= 0x00000001u;
}
inline void net_conf::clear_has_use_nagle() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void net_conf::clear_use_nagle() {
  use_nagle_ = true;
  clear_has_use_nagle();
}
inline bool net_conf::use_nagle() const {
  // @@protoc_insertion_point(field_get:baseagent.net_conf.use_nagle)
  return use_nagle_;
}
inline void net_conf::set_use_nagle(bool value) {
  set_has_use_nagle();
  use_nagle_ = value;
  // @@protoc_insertion_point(field_set:baseagent.net_conf.use_nagle)
}

// optional bool use_linger = 2 [default = false];
inline bool net_conf::has_use_linger() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void net_conf::set_has_use_linger() {
  _has_bits_[0] |= 0x00000002u;
}
inline void net_conf::clear_has_use_linger() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void net_conf::clear_use_linger() {
  use_linger_ = false;
  clear_has_use_linger();
}
inline bool net_conf::use_linger() const {
  // @@protoc_insertion_point(field_get:baseagent.net_conf.use_linger)
  return use_linger_;
}
inline void net_conf::set_use_linger(bool value) {
  set_has_use_linger();
  use_linger_ = value;
  // @@protoc_insertion_point(field_set:baseagent.net_conf.use_linger)
}

// optional int32 linger_time = 3 [default = 0];
inline bool net_conf::has_linger_time() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void net_conf::set_has_linger_time() {
  _has_bits_[0] |= 0x00000004u;
}
inline void net_conf::clear_has_linger_time() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void net_conf::clear_linger_time() {
  linger_time_ = 0;
  clear_has_linger_time();
}
inline ::google::protobuf::int32 net_conf::linger_time() const {
  // @@protoc_insertion_point(field_get:baseagent.net_conf.linger_time)
  return linger_time_;
}
inline void net_conf::set_linger_time(::google::protobuf::int32 value) {
  set_has_linger_time();
  linger_time_ = value;
  // @@protoc_insertion_point(field_set:baseagent.net_conf.linger_time)
}

// optional int32 listen_backlog = 4 [default = 10240];
inline bool net_conf::has_listen_backlog() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void net_conf::set_has_listen_backlog() {
  _has_bits_[0] |= 0x00000008u;
}
inline void net_conf::clear_has_listen_backlog() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void net_conf::clear_listen_backlog() {
  listen_backlog_ = 10240;
  clear_has_listen_backlog();
}
inline ::google::protobuf::int32 net_conf::listen_backlog() const {
  // @@protoc_insertion_point(field_get:baseagent.net_conf.listen_backlog)
  return listen_backlog_;
}
inline void net_conf::set_listen_backlog(::google::protobuf::int32 value) {
  set_has_listen_backlog();
  listen_backlog_ = value;
  // @@protoc_insertion_point(field_set:baseagent.net_conf.listen_backlog)
}

// optional uint32 max_connect_num = 5 [default = 20480];
inline bool net_conf::has_max_connect_num() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void net_conf::set_has_max_connect_num() {
  _has_bits_[0] |= 0x00000010u;
}
inline void net_conf::clear_has_max_connect_num() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void net_conf::clear_max_connect_num() {
  max_connect_num_ = 20480u;
  clear_has_max_connect_num();
}
inline ::google::protobuf::uint32 net_conf::max_connect_num() const {
  // @@protoc_insertion_point(field_get:baseagent.net_conf.max_connect_num)
  return max_connect_num_;
}
inline void net_conf::set_max_connect_num(::google::protobuf::uint32 value) {
  set_has_max_connect_num();
  max_connect_num_ = value;
  // @@protoc_insertion_point(field_set:baseagent.net_conf.max_connect_num)
}

// optional uint32 recv_cache_block_num = 6 [default = 10240];
inline bool net_conf::has_recv_cache_block_num() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void net_conf::set_has_recv_cache_block_num() {
  _has_bits_[0] |= 0x00000020u;
}
inline void net_conf::clear_has_recv_cache_block_num() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void net_conf::clear_recv_cache_block_num() {
  recv_cache_block_num_ = 10240u;
  clear_has_recv_cache_block_num();
}
inline ::google::protobuf::uint32 net_conf::recv_cache_block_num() const {
  // @@protoc_insertion_point(field_get:baseagent.net_conf.recv_cache_block_num)
  return recv_cache_block_num_;
}
inline void net_conf::set_recv_cache_block_num(::google::protobuf::uint32 value) {
  set_has_recv_cache_block_num();
  recv_cache_block_num_ = value;
  // @@protoc_insertion_point(field_set:baseagent.net_conf.recv_cache_block_num)
}

// optional uint32 recv_cache_block_size = 7 [default = 512];
inline bool net_conf::has_recv_cache_block_size() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void net_conf::set_has_recv_cache_block_size() {
  _has_bits_[0] |= 0x00000040u;
}
inline void net_conf::clear_has_recv_cache_block_size() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void net_conf::clear_recv_cache_block_size() {
  recv_cache_block_size_ = 512u;
  clear_has_recv_cache_block_size();
}
inline ::google::protobuf::uint32 net_conf::recv_cache_block_size() const {
  // @@protoc_insertion_point(field_get:baseagent.net_conf.recv_cache_block_size)
  return recv_cache_block_size_;
}
inline void net_conf::set_recv_cache_block_size(::google::protobuf::uint32 value) {
  set_has_recv_cache_block_size();
  recv_cache_block_size_ = value;
  // @@protoc_insertion_point(field_set:baseagent.net_conf.recv_cache_block_size)
}

// optional uint32 send_cache_block_num = 8 [default = 10240];
inline bool net_conf::has_send_cache_block_num() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void net_conf::set_has_send_cache_block_num() {
  _has_bits_[0] |= 0x00000080u;
}
inline void net_conf::clear_has_send_cache_block_num() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void net_conf::clear_send_cache_block_num() {
  send_cache_block_num_ = 10240u;
  clear_has_send_cache_block_num();
}
inline ::google::protobuf::uint32 net_conf::send_cache_block_num() const {
  // @@protoc_insertion_point(field_get:baseagent.net_conf.send_cache_block_num)
  return send_cache_block_num_;
}
inline void net_conf::set_send_cache_block_num(::google::protobuf::uint32 value) {
  set_has_send_cache_block_num();
  send_cache_block_num_ = value;
  // @@protoc_insertion_point(field_set:baseagent.net_conf.send_cache_block_num)
}

// optional uint32 send_cache_block_size = 9 [default = 512];
inline bool net_conf::has_send_cache_block_size() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void net_conf::set_has_send_cache_block_size() {
  _has_bits_[0] |= 0x00000100u;
}
inline void net_conf::clear_has_send_cache_block_size() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void net_conf::clear_send_cache_block_size() {
  send_cache_block_size_ = 512u;
  clear_has_send_cache_block_size();
}
inline ::google::protobuf::uint32 net_conf::send_cache_block_size() const {
  // @@protoc_insertion_point(field_get:baseagent.net_conf.send_cache_block_size)
  return send_cache_block_size_;
}
inline void net_conf::set_send_cache_block_size(::google::protobuf::uint32 value) {
  set_has_send_cache_block_size();
  send_cache_block_size_ = value;
  // @@protoc_insertion_point(field_set:baseagent.net_conf.send_cache_block_size)
}

// optional uint32 max_msg_length = 11 [default = 1048576];
inline bool net_conf::has_max_msg_length() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
inline void net_conf::set_has_max_msg_length() {
  _has_bits_[0] |= 0x00000200u;
}
inline void net_conf::clear_has_max_msg_length() {
  _has_bits_[0] &= ~0x00000200u;
}
inline void net_conf::clear_max_msg_length() {
  max_msg_length_ = 1048576u;
  clear_has_max_msg_length();
}
inline ::google::protobuf::uint32 net_conf::max_msg_length() const {
  // @@protoc_insertion_point(field_get:baseagent.net_conf.max_msg_length)
  return max_msg_length_;
}
inline void net_conf::set_max_msg_length(::google::protobuf::uint32 value) {
  set_has_max_msg_length();
  max_msg_length_ = value;
  // @@protoc_insertion_point(field_set:baseagent.net_conf.max_msg_length)
}

// optional uint32 max_queue_num = 21 [default = 5120];
inline bool net_conf::has_max_queue_num() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
inline void net_conf::set_has_max_queue_num() {
  _has_bits_[0] |= 0x00000400u;
}
inline void net_conf::clear_has_max_queue_num() {
  _has_bits_[0] &= ~0x00000400u;
}
inline void net_conf::clear_max_queue_num() {
  max_queue_num_ = 5120u;
  clear_has_max_queue_num();
}
inline ::google::protobuf::uint32 net_conf::max_queue_num() const {
  // @@protoc_insertion_point(field_get:baseagent.net_conf.max_queue_num)
  return max_queue_num_;
}
inline void net_conf::set_max_queue_num(::google::protobuf::uint32 value) {
  set_has_max_queue_num();
  max_queue_num_ = value;
  // @@protoc_insertion_point(field_set:baseagent.net_conf.max_queue_num)
}

// optional uint32 max_wait_ack_msg_num_per_queue = 22 [default = 128];
inline bool net_conf::has_max_wait_ack_msg_num_per_queue() const {
  return (_has_bits_[0] & 0x00000800u) != 0;
}
inline void net_conf::set_has_max_wait_ack_msg_num_per_queue() {
  _has_bits_[0] |= 0x00000800u;
}
inline void net_conf::clear_has_max_wait_ack_msg_num_per_queue() {
  _has_bits_[0] &= ~0x00000800u;
}
inline void net_conf::clear_max_wait_ack_msg_num_per_queue() {
  max_wait_ack_msg_num_per_queue_ = 128u;
  clear_has_max_wait_ack_msg_num_per_queue();
}
inline ::google::protobuf::uint32 net_conf::max_wait_ack_msg_num_per_queue() const {
  // @@protoc_insertion_point(field_get:baseagent.net_conf.max_wait_ack_msg_num_per_queue)
  return max_wait_ack_msg_num_per_queue_;
}
inline void net_conf::set_max_wait_ack_msg_num_per_queue(::google::protobuf::uint32 value) {
  set_has_max_wait_ack_msg_num_per_queue();
  max_wait_ack_msg_num_per_queue_ = value;
  // @@protoc_insertion_point(field_set:baseagent.net_conf.max_wait_ack_msg_num_per_queue)
}

// -------------------------------------------------------------------

// busid_map_conf

// optional uint32 global_mem_index = 1 [default = 3];
inline bool busid_map_conf::has_global_mem_index() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void busid_map_conf::set_has_global_mem_index() {
  _has_bits_[0] |= 0x00000001u;
}
inline void busid_map_conf::clear_has_global_mem_index() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void busid_map_conf::clear_global_mem_index() {
  global_mem_index_ = 3u;
  clear_has_global_mem_index();
}
inline ::google::protobuf::uint32 busid_map_conf::global_mem_index() const {
  // @@protoc_insertion_point(field_get:baseagent.busid_map_conf.global_mem_index)
  return global_mem_index_;
}
inline void busid_map_conf::set_global_mem_index(::google::protobuf::uint32 value) {
  set_has_global_mem_index();
  global_mem_index_ = value;
  // @@protoc_insertion_point(field_set:baseagent.busid_map_conf.global_mem_index)
}

// optional uint32 address_count = 2 [default = 200];
inline bool busid_map_conf::has_address_count() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void busid_map_conf::set_has_address_count() {
  _has_bits_[0] |= 0x00000002u;
}
inline void busid_map_conf::clear_has_address_count() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void busid_map_conf::clear_address_count() {
  address_count_ = 200u;
  clear_has_address_count();
}
inline ::google::protobuf::uint32 busid_map_conf::address_count() const {
  // @@protoc_insertion_point(field_get:baseagent.busid_map_conf.address_count)
  return address_count_;
}
inline void busid_map_conf::set_address_count(::google::protobuf::uint32 value) {
  set_has_address_count();
  address_count_ = value;
  // @@protoc_insertion_point(field_set:baseagent.busid_map_conf.address_count)
}

// optional uint32 mem_type = 3 [default = 3];
inline bool busid_map_conf::has_mem_type() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void busid_map_conf::set_has_mem_type() {
  _has_bits_[0] |= 0x00000004u;
}
inline void busid_map_conf::clear_has_mem_type() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void busid_map_conf::clear_mem_type() {
  mem_type_ = 3u;
  clear_has_mem_type();
}
inline ::google::protobuf::uint32 busid_map_conf::mem_type() const {
  // @@protoc_insertion_point(field_get:baseagent.busid_map_conf.mem_type)
  return mem_type_;
}
inline void busid_map_conf::set_mem_type(::google::protobuf::uint32 value) {
  set_has_mem_type();
  mem_type_ = value;
  // @@protoc_insertion_point(field_set:baseagent.busid_map_conf.mem_type)
}

// optional uint32 shmkey = 4 [default = 210004];
inline bool busid_map_conf::has_shmkey() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void busid_map_conf::set_has_shmkey() {
  _has_bits_[0] |= 0x00000008u;
}
inline void busid_map_conf::clear_has_shmkey() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void busid_map_conf::clear_shmkey() {
  shmkey_ = 210004u;
  clear_has_shmkey();
}
inline ::google::protobuf::uint32 busid_map_conf::shmkey() const {
  // @@protoc_insertion_point(field_get:baseagent.busid_map_conf.shmkey)
  return shmkey_;
}
inline void busid_map_conf::set_shmkey(::google::protobuf::uint32 value) {
  set_has_shmkey();
  shmkey_ = value;
  // @@protoc_insertion_point(field_set:baseagent.busid_map_conf.shmkey)
}

// optional string mgr_map_file = 5 [default = "/dev/shm/baseagent/busidmap/busidmap.data"];
inline bool busid_map_conf::has_mgr_map_file() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void busid_map_conf::set_has_mgr_map_file() {
  _has_bits_[0] |= 0x00000010u;
}
inline void busid_map_conf::clear_has_mgr_map_file() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void busid_map_conf::clear_mgr_map_file() {
  if (mgr_map_file_ != _default_mgr_map_file_) {
    mgr_map_file_->assign(*_default_mgr_map_file_);
  }
  clear_has_mgr_map_file();
}
inline const ::std::string& busid_map_conf::mgr_map_file() const {
  // @@protoc_insertion_point(field_get:baseagent.busid_map_conf.mgr_map_file)
  return *mgr_map_file_;
}
inline void busid_map_conf::set_mgr_map_file(const ::std::string& value) {
  set_has_mgr_map_file();
  if (mgr_map_file_ == _default_mgr_map_file_) {
    mgr_map_file_ = new ::std::string;
  }
  mgr_map_file_->assign(value);
  // @@protoc_insertion_point(field_set:baseagent.busid_map_conf.mgr_map_file)
}
inline void busid_map_conf::set_mgr_map_file(const char* value) {
  set_has_mgr_map_file();
  if (mgr_map_file_ == _default_mgr_map_file_) {
    mgr_map_file_ = new ::std::string;
  }
  mgr_map_file_->assign(value);
  // @@protoc_insertion_point(field_set_char:baseagent.busid_map_conf.mgr_map_file)
}
inline void busid_map_conf::set_mgr_map_file(const char* value, size_t size) {
  set_has_mgr_map_file();
  if (mgr_map_file_ == _default_mgr_map_file_) {
    mgr_map_file_ = new ::std::string;
  }
  mgr_map_file_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:baseagent.busid_map_conf.mgr_map_file)
}
inline ::std::string* busid_map_conf::mutable_mgr_map_file() {
  set_has_mgr_map_file();
  if (mgr_map_file_ == _default_mgr_map_file_) {
    mgr_map_file_ = new ::std::string(*_default_mgr_map_file_);
  }
  // @@protoc_insertion_point(field_mutable:baseagent.busid_map_conf.mgr_map_file)
  return mgr_map_file_;
}
inline ::std::string* busid_map_conf::release_mgr_map_file() {
  clear_has_mgr_map_file();
  if (mgr_map_file_ == _default_mgr_map_file_) {
    return NULL;
  } else {
    ::std::string* temp = mgr_map_file_;
    mgr_map_file_ = const_cast< ::std::string*>(_default_mgr_map_file_);
    return temp;
  }
}
inline void busid_map_conf::set_allocated_mgr_map_file(::std::string* mgr_map_file) {
  if (mgr_map_file_ != _default_mgr_map_file_) {
    delete mgr_map_file_;
  }
  if (mgr_map_file) {
    set_has_mgr_map_file();
    mgr_map_file_ = mgr_map_file;
  } else {
    clear_has_mgr_map_file();
    mgr_map_file_ = const_cast< ::std::string*>(_default_mgr_map_file_);
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.busid_map_conf.mgr_map_file)
}

// -------------------------------------------------------------------

// extern_proto_conf

// required int32 proto_type = 1;
inline bool extern_proto_conf::has_proto_type() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void extern_proto_conf::set_has_proto_type() {
  _has_bits_[0] |= 0x00000001u;
}
inline void extern_proto_conf::clear_has_proto_type() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void extern_proto_conf::clear_proto_type() {
  proto_type_ = 0;
  clear_has_proto_type();
}
inline ::google::protobuf::int32 extern_proto_conf::proto_type() const {
  // @@protoc_insertion_point(field_get:baseagent.extern_proto_conf.proto_type)
  return proto_type_;
}
inline void extern_proto_conf::set_proto_type(::google::protobuf::int32 value) {
  set_has_proto_type();
  proto_type_ = value;
  // @@protoc_insertion_point(field_set:baseagent.extern_proto_conf.proto_type)
}

// required string proto_so_path = 2;
inline bool extern_proto_conf::has_proto_so_path() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void extern_proto_conf::set_has_proto_so_path() {
  _has_bits_[0] |= 0x00000002u;
}
inline void extern_proto_conf::clear_has_proto_so_path() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void extern_proto_conf::clear_proto_so_path() {
  if (proto_so_path_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    proto_so_path_->clear();
  }
  clear_has_proto_so_path();
}
inline const ::std::string& extern_proto_conf::proto_so_path() const {
  // @@protoc_insertion_point(field_get:baseagent.extern_proto_conf.proto_so_path)
  return *proto_so_path_;
}
inline void extern_proto_conf::set_proto_so_path(const ::std::string& value) {
  set_has_proto_so_path();
  if (proto_so_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    proto_so_path_ = new ::std::string;
  }
  proto_so_path_->assign(value);
  // @@protoc_insertion_point(field_set:baseagent.extern_proto_conf.proto_so_path)
}
inline void extern_proto_conf::set_proto_so_path(const char* value) {
  set_has_proto_so_path();
  if (proto_so_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    proto_so_path_ = new ::std::string;
  }
  proto_so_path_->assign(value);
  // @@protoc_insertion_point(field_set_char:baseagent.extern_proto_conf.proto_so_path)
}
inline void extern_proto_conf::set_proto_so_path(const char* value, size_t size) {
  set_has_proto_so_path();
  if (proto_so_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    proto_so_path_ = new ::std::string;
  }
  proto_so_path_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:baseagent.extern_proto_conf.proto_so_path)
}
inline ::std::string* extern_proto_conf::mutable_proto_so_path() {
  set_has_proto_so_path();
  if (proto_so_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    proto_so_path_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:baseagent.extern_proto_conf.proto_so_path)
  return proto_so_path_;
}
inline ::std::string* extern_proto_conf::release_proto_so_path() {
  clear_has_proto_so_path();
  if (proto_so_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = proto_so_path_;
    proto_so_path_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void extern_proto_conf::set_allocated_proto_so_path(::std::string* proto_so_path) {
  if (proto_so_path_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete proto_so_path_;
  }
  if (proto_so_path) {
    set_has_proto_so_path();
    proto_so_path_ = proto_so_path;
  } else {
    clear_has_proto_so_path();
    proto_so_path_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.extern_proto_conf.proto_so_path)
}

// required string proto_conf_path = 3;
inline bool extern_proto_conf::has_proto_conf_path() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void extern_proto_conf::set_has_proto_conf_path() {
  _has_bits_[0] |= 0x00000004u;
}
inline void extern_proto_conf::clear_has_proto_conf_path() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void extern_proto_conf::clear_proto_conf_path() {
  if (proto_conf_path_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    proto_conf_path_->clear();
  }
  clear_has_proto_conf_path();
}
inline const ::std::string& extern_proto_conf::proto_conf_path() const {
  // @@protoc_insertion_point(field_get:baseagent.extern_proto_conf.proto_conf_path)
  return *proto_conf_path_;
}
inline void extern_proto_conf::set_proto_conf_path(const ::std::string& value) {
  set_has_proto_conf_path();
  if (proto_conf_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    proto_conf_path_ = new ::std::string;
  }
  proto_conf_path_->assign(value);
  // @@protoc_insertion_point(field_set:baseagent.extern_proto_conf.proto_conf_path)
}
inline void extern_proto_conf::set_proto_conf_path(const char* value) {
  set_has_proto_conf_path();
  if (proto_conf_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    proto_conf_path_ = new ::std::string;
  }
  proto_conf_path_->assign(value);
  // @@protoc_insertion_point(field_set_char:baseagent.extern_proto_conf.proto_conf_path)
}
inline void extern_proto_conf::set_proto_conf_path(const char* value, size_t size) {
  set_has_proto_conf_path();
  if (proto_conf_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    proto_conf_path_ = new ::std::string;
  }
  proto_conf_path_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:baseagent.extern_proto_conf.proto_conf_path)
}
inline ::std::string* extern_proto_conf::mutable_proto_conf_path() {
  set_has_proto_conf_path();
  if (proto_conf_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    proto_conf_path_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:baseagent.extern_proto_conf.proto_conf_path)
  return proto_conf_path_;
}
inline ::std::string* extern_proto_conf::release_proto_conf_path() {
  clear_has_proto_conf_path();
  if (proto_conf_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = proto_conf_path_;
    proto_conf_path_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void extern_proto_conf::set_allocated_proto_conf_path(::std::string* proto_conf_path) {
  if (proto_conf_path_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete proto_conf_path_;
  }
  if (proto_conf_path) {
    set_has_proto_conf_path();
    proto_conf_path_ = proto_conf_path;
  } else {
    clear_has_proto_conf_path();
    proto_conf_path_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.extern_proto_conf.proto_conf_path)
}

// optional string ifx = 4 [default = "eth1"];
inline bool extern_proto_conf::has_ifx() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void extern_proto_conf::set_has_ifx() {
  _has_bits_[0] |= 0x00000008u;
}
inline void extern_proto_conf::clear_has_ifx() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void extern_proto_conf::clear_ifx() {
  if (ifx_ != _default_ifx_) {
    ifx_->assign(*_default_ifx_);
  }
  clear_has_ifx();
}
inline const ::std::string& extern_proto_conf::ifx() const {
  // @@protoc_insertion_point(field_get:baseagent.extern_proto_conf.ifx)
  return *ifx_;
}
inline void extern_proto_conf::set_ifx(const ::std::string& value) {
  set_has_ifx();
  if (ifx_ == _default_ifx_) {
    ifx_ = new ::std::string;
  }
  ifx_->assign(value);
  // @@protoc_insertion_point(field_set:baseagent.extern_proto_conf.ifx)
}
inline void extern_proto_conf::set_ifx(const char* value) {
  set_has_ifx();
  if (ifx_ == _default_ifx_) {
    ifx_ = new ::std::string;
  }
  ifx_->assign(value);
  // @@protoc_insertion_point(field_set_char:baseagent.extern_proto_conf.ifx)
}
inline void extern_proto_conf::set_ifx(const char* value, size_t size) {
  set_has_ifx();
  if (ifx_ == _default_ifx_) {
    ifx_ = new ::std::string;
  }
  ifx_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:baseagent.extern_proto_conf.ifx)
}
inline ::std::string* extern_proto_conf::mutable_ifx() {
  set_has_ifx();
  if (ifx_ == _default_ifx_) {
    ifx_ = new ::std::string(*_default_ifx_);
  }
  // @@protoc_insertion_point(field_mutable:baseagent.extern_proto_conf.ifx)
  return ifx_;
}
inline ::std::string* extern_proto_conf::release_ifx() {
  clear_has_ifx();
  if (ifx_ == _default_ifx_) {
    return NULL;
  } else {
    ::std::string* temp = ifx_;
    ifx_ = const_cast< ::std::string*>(_default_ifx_);
    return temp;
  }
}
inline void extern_proto_conf::set_allocated_ifx(::std::string* ifx) {
  if (ifx_ != _default_ifx_) {
    delete ifx_;
  }
  if (ifx) {
    set_has_ifx();
    ifx_ = ifx;
  } else {
    clear_has_ifx();
    ifx_ = const_cast< ::std::string*>(_default_ifx_);
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.extern_proto_conf.ifx)
}

// optional uint32 port = 5 [default = 9971];
inline bool extern_proto_conf::has_port() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void extern_proto_conf::set_has_port() {
  _has_bits_[0] |= 0x00000010u;
}
inline void extern_proto_conf::clear_has_port() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void extern_proto_conf::clear_port() {
  port_ = 9971u;
  clear_has_port();
}
inline ::google::protobuf::uint32 extern_proto_conf::port() const {
  // @@protoc_insertion_point(field_get:baseagent.extern_proto_conf.port)
  return port_;
}
inline void extern_proto_conf::set_port(::google::protobuf::uint32 value) {
  set_has_port();
  port_ = value;
  // @@protoc_insertion_point(field_set:baseagent.extern_proto_conf.port)
}

// -------------------------------------------------------------------

// stat_conf

// optional string stat_so_path = 1;
inline bool stat_conf::has_stat_so_path() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void stat_conf::set_has_stat_so_path() {
  _has_bits_[0] |= 0x00000001u;
}
inline void stat_conf::clear_has_stat_so_path() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void stat_conf::clear_stat_so_path() {
  if (stat_so_path_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    stat_so_path_->clear();
  }
  clear_has_stat_so_path();
}
inline const ::std::string& stat_conf::stat_so_path() const {
  // @@protoc_insertion_point(field_get:baseagent.stat_conf.stat_so_path)
  return *stat_so_path_;
}
inline void stat_conf::set_stat_so_path(const ::std::string& value) {
  set_has_stat_so_path();
  if (stat_so_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    stat_so_path_ = new ::std::string;
  }
  stat_so_path_->assign(value);
  // @@protoc_insertion_point(field_set:baseagent.stat_conf.stat_so_path)
}
inline void stat_conf::set_stat_so_path(const char* value) {
  set_has_stat_so_path();
  if (stat_so_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    stat_so_path_ = new ::std::string;
  }
  stat_so_path_->assign(value);
  // @@protoc_insertion_point(field_set_char:baseagent.stat_conf.stat_so_path)
}
inline void stat_conf::set_stat_so_path(const char* value, size_t size) {
  set_has_stat_so_path();
  if (stat_so_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    stat_so_path_ = new ::std::string;
  }
  stat_so_path_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:baseagent.stat_conf.stat_so_path)
}
inline ::std::string* stat_conf::mutable_stat_so_path() {
  set_has_stat_so_path();
  if (stat_so_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    stat_so_path_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:baseagent.stat_conf.stat_so_path)
  return stat_so_path_;
}
inline ::std::string* stat_conf::release_stat_so_path() {
  clear_has_stat_so_path();
  if (stat_so_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = stat_so_path_;
    stat_so_path_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void stat_conf::set_allocated_stat_so_path(::std::string* stat_so_path) {
  if (stat_so_path_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete stat_so_path_;
  }
  if (stat_so_path) {
    set_has_stat_so_path();
    stat_so_path_ = stat_so_path;
  } else {
    clear_has_stat_so_path();
    stat_so_path_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.stat_conf.stat_so_path)
}

// optional string stat_conf_path = 2;
inline bool stat_conf::has_stat_conf_path() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void stat_conf::set_has_stat_conf_path() {
  _has_bits_[0] |= 0x00000002u;
}
inline void stat_conf::clear_has_stat_conf_path() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void stat_conf::clear_stat_conf_path() {
  if (stat_conf_path_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    stat_conf_path_->clear();
  }
  clear_has_stat_conf_path();
}
inline const ::std::string& stat_conf::stat_conf_path() const {
  // @@protoc_insertion_point(field_get:baseagent.stat_conf.stat_conf_path)
  return *stat_conf_path_;
}
inline void stat_conf::set_stat_conf_path(const ::std::string& value) {
  set_has_stat_conf_path();
  if (stat_conf_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    stat_conf_path_ = new ::std::string;
  }
  stat_conf_path_->assign(value);
  // @@protoc_insertion_point(field_set:baseagent.stat_conf.stat_conf_path)
}
inline void stat_conf::set_stat_conf_path(const char* value) {
  set_has_stat_conf_path();
  if (stat_conf_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    stat_conf_path_ = new ::std::string;
  }
  stat_conf_path_->assign(value);
  // @@protoc_insertion_point(field_set_char:baseagent.stat_conf.stat_conf_path)
}
inline void stat_conf::set_stat_conf_path(const char* value, size_t size) {
  set_has_stat_conf_path();
  if (stat_conf_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    stat_conf_path_ = new ::std::string;
  }
  stat_conf_path_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:baseagent.stat_conf.stat_conf_path)
}
inline ::std::string* stat_conf::mutable_stat_conf_path() {
  set_has_stat_conf_path();
  if (stat_conf_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    stat_conf_path_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:baseagent.stat_conf.stat_conf_path)
  return stat_conf_path_;
}
inline ::std::string* stat_conf::release_stat_conf_path() {
  clear_has_stat_conf_path();
  if (stat_conf_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = stat_conf_path_;
    stat_conf_path_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void stat_conf::set_allocated_stat_conf_path(::std::string* stat_conf_path) {
  if (stat_conf_path_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete stat_conf_path_;
  }
  if (stat_conf_path) {
    set_has_stat_conf_path();
    stat_conf_path_ = stat_conf_path;
  } else {
    clear_has_stat_conf_path();
    stat_conf_path_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.stat_conf.stat_conf_path)
}

// -------------------------------------------------------------------

// base_agent_conf

// optional uint32 global_mem_index = 1 [default = 3];
inline bool base_agent_conf::has_global_mem_index() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void base_agent_conf::set_has_global_mem_index() {
  _has_bits_[0] |= 0x00000001u;
}
inline void base_agent_conf::clear_has_global_mem_index() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void base_agent_conf::clear_global_mem_index() {
  global_mem_index_ = 3u;
  clear_has_global_mem_index();
}
inline ::google::protobuf::uint32 base_agent_conf::global_mem_index() const {
  // @@protoc_insertion_point(field_get:baseagent.base_agent_conf.global_mem_index)
  return global_mem_index_;
}
inline void base_agent_conf::set_global_mem_index(::google::protobuf::uint32 value) {
  set_has_global_mem_index();
  global_mem_index_ = value;
  // @@protoc_insertion_point(field_set:baseagent.base_agent_conf.global_mem_index)
}

// optional string private_data_path = 2 [default = "/dev/shm/baseagent/private/"];
inline bool base_agent_conf::has_private_data_path() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void base_agent_conf::set_has_private_data_path() {
  _has_bits_[0] |= 0x00000002u;
}
inline void base_agent_conf::clear_has_private_data_path() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void base_agent_conf::clear_private_data_path() {
  if (private_data_path_ != _default_private_data_path_) {
    private_data_path_->assign(*_default_private_data_path_);
  }
  clear_has_private_data_path();
}
inline const ::std::string& base_agent_conf::private_data_path() const {
  // @@protoc_insertion_point(field_get:baseagent.base_agent_conf.private_data_path)
  return *private_data_path_;
}
inline void base_agent_conf::set_private_data_path(const ::std::string& value) {
  set_has_private_data_path();
  if (private_data_path_ == _default_private_data_path_) {
    private_data_path_ = new ::std::string;
  }
  private_data_path_->assign(value);
  // @@protoc_insertion_point(field_set:baseagent.base_agent_conf.private_data_path)
}
inline void base_agent_conf::set_private_data_path(const char* value) {
  set_has_private_data_path();
  if (private_data_path_ == _default_private_data_path_) {
    private_data_path_ = new ::std::string;
  }
  private_data_path_->assign(value);
  // @@protoc_insertion_point(field_set_char:baseagent.base_agent_conf.private_data_path)
}
inline void base_agent_conf::set_private_data_path(const char* value, size_t size) {
  set_has_private_data_path();
  if (private_data_path_ == _default_private_data_path_) {
    private_data_path_ = new ::std::string;
  }
  private_data_path_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:baseagent.base_agent_conf.private_data_path)
}
inline ::std::string* base_agent_conf::mutable_private_data_path() {
  set_has_private_data_path();
  if (private_data_path_ == _default_private_data_path_) {
    private_data_path_ = new ::std::string(*_default_private_data_path_);
  }
  // @@protoc_insertion_point(field_mutable:baseagent.base_agent_conf.private_data_path)
  return private_data_path_;
}
inline ::std::string* base_agent_conf::release_private_data_path() {
  clear_has_private_data_path();
  if (private_data_path_ == _default_private_data_path_) {
    return NULL;
  } else {
    ::std::string* temp = private_data_path_;
    private_data_path_ = const_cast< ::std::string*>(_default_private_data_path_);
    return temp;
  }
}
inline void base_agent_conf::set_allocated_private_data_path(::std::string* private_data_path) {
  if (private_data_path_ != _default_private_data_path_) {
    delete private_data_path_;
  }
  if (private_data_path) {
    set_has_private_data_path();
    private_data_path_ = private_data_path;
  } else {
    clear_has_private_data_path();
    private_data_path_ = const_cast< ::std::string*>(_default_private_data_path_);
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.base_agent_conf.private_data_path)
}

// optional string name_data_path = 3 [default = "/data/baseagent/name_data/"];
inline bool base_agent_conf::has_name_data_path() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void base_agent_conf::set_has_name_data_path() {
  _has_bits_[0] |= 0x00000004u;
}
inline void base_agent_conf::clear_has_name_data_path() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void base_agent_conf::clear_name_data_path() {
  if (name_data_path_ != _default_name_data_path_) {
    name_data_path_->assign(*_default_name_data_path_);
  }
  clear_has_name_data_path();
}
inline const ::std::string& base_agent_conf::name_data_path() const {
  // @@protoc_insertion_point(field_get:baseagent.base_agent_conf.name_data_path)
  return *name_data_path_;
}
inline void base_agent_conf::set_name_data_path(const ::std::string& value) {
  set_has_name_data_path();
  if (name_data_path_ == _default_name_data_path_) {
    name_data_path_ = new ::std::string;
  }
  name_data_path_->assign(value);
  // @@protoc_insertion_point(field_set:baseagent.base_agent_conf.name_data_path)
}
inline void base_agent_conf::set_name_data_path(const char* value) {
  set_has_name_data_path();
  if (name_data_path_ == _default_name_data_path_) {
    name_data_path_ = new ::std::string;
  }
  name_data_path_->assign(value);
  // @@protoc_insertion_point(field_set_char:baseagent.base_agent_conf.name_data_path)
}
inline void base_agent_conf::set_name_data_path(const char* value, size_t size) {
  set_has_name_data_path();
  if (name_data_path_ == _default_name_data_path_) {
    name_data_path_ = new ::std::string;
  }
  name_data_path_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:baseagent.base_agent_conf.name_data_path)
}
inline ::std::string* base_agent_conf::mutable_name_data_path() {
  set_has_name_data_path();
  if (name_data_path_ == _default_name_data_path_) {
    name_data_path_ = new ::std::string(*_default_name_data_path_);
  }
  // @@protoc_insertion_point(field_mutable:baseagent.base_agent_conf.name_data_path)
  return name_data_path_;
}
inline ::std::string* base_agent_conf::release_name_data_path() {
  clear_has_name_data_path();
  if (name_data_path_ == _default_name_data_path_) {
    return NULL;
  } else {
    ::std::string* temp = name_data_path_;
    name_data_path_ = const_cast< ::std::string*>(_default_name_data_path_);
    return temp;
  }
}
inline void base_agent_conf::set_allocated_name_data_path(::std::string* name_data_path) {
  if (name_data_path_ != _default_name_data_path_) {
    delete name_data_path_;
  }
  if (name_data_path) {
    set_has_name_data_path();
    name_data_path_ = name_data_path;
  } else {
    clear_has_name_data_path();
    name_data_path_ = const_cast< ::std::string*>(_default_name_data_path_);
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.base_agent_conf.name_data_path)
}

// optional string tmsg_data_path = 4 [default = "/dev/shm/baseagent/tmsg/"];
inline bool base_agent_conf::has_tmsg_data_path() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void base_agent_conf::set_has_tmsg_data_path() {
  _has_bits_[0] |= 0x00000008u;
}
inline void base_agent_conf::clear_has_tmsg_data_path() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void base_agent_conf::clear_tmsg_data_path() {
  if (tmsg_data_path_ != _default_tmsg_data_path_) {
    tmsg_data_path_->assign(*_default_tmsg_data_path_);
  }
  clear_has_tmsg_data_path();
}
inline const ::std::string& base_agent_conf::tmsg_data_path() const {
  // @@protoc_insertion_point(field_get:baseagent.base_agent_conf.tmsg_data_path)
  return *tmsg_data_path_;
}
inline void base_agent_conf::set_tmsg_data_path(const ::std::string& value) {
  set_has_tmsg_data_path();
  if (tmsg_data_path_ == _default_tmsg_data_path_) {
    tmsg_data_path_ = new ::std::string;
  }
  tmsg_data_path_->assign(value);
  // @@protoc_insertion_point(field_set:baseagent.base_agent_conf.tmsg_data_path)
}
inline void base_agent_conf::set_tmsg_data_path(const char* value) {
  set_has_tmsg_data_path();
  if (tmsg_data_path_ == _default_tmsg_data_path_) {
    tmsg_data_path_ = new ::std::string;
  }
  tmsg_data_path_->assign(value);
  // @@protoc_insertion_point(field_set_char:baseagent.base_agent_conf.tmsg_data_path)
}
inline void base_agent_conf::set_tmsg_data_path(const char* value, size_t size) {
  set_has_tmsg_data_path();
  if (tmsg_data_path_ == _default_tmsg_data_path_) {
    tmsg_data_path_ = new ::std::string;
  }
  tmsg_data_path_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:baseagent.base_agent_conf.tmsg_data_path)
}
inline ::std::string* base_agent_conf::mutable_tmsg_data_path() {
  set_has_tmsg_data_path();
  if (tmsg_data_path_ == _default_tmsg_data_path_) {
    tmsg_data_path_ = new ::std::string(*_default_tmsg_data_path_);
  }
  // @@protoc_insertion_point(field_mutable:baseagent.base_agent_conf.tmsg_data_path)
  return tmsg_data_path_;
}
inline ::std::string* base_agent_conf::release_tmsg_data_path() {
  clear_has_tmsg_data_path();
  if (tmsg_data_path_ == _default_tmsg_data_path_) {
    return NULL;
  } else {
    ::std::string* temp = tmsg_data_path_;
    tmsg_data_path_ = const_cast< ::std::string*>(_default_tmsg_data_path_);
    return temp;
  }
}
inline void base_agent_conf::set_allocated_tmsg_data_path(::std::string* tmsg_data_path) {
  if (tmsg_data_path_ != _default_tmsg_data_path_) {
    delete tmsg_data_path_;
  }
  if (tmsg_data_path) {
    set_has_tmsg_data_path();
    tmsg_data_path_ = tmsg_data_path;
  } else {
    clear_has_tmsg_data_path();
    tmsg_data_path_ = const_cast< ::std::string*>(_default_tmsg_data_path_);
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.base_agent_conf.tmsg_data_path)
}

// optional string troute_data_path = 5 [default = "/dev/shm/baseagent/troute/"];
inline bool base_agent_conf::has_troute_data_path() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void base_agent_conf::set_has_troute_data_path() {
  _has_bits_[0] |= 0x00000010u;
}
inline void base_agent_conf::clear_has_troute_data_path() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void base_agent_conf::clear_troute_data_path() {
  if (troute_data_path_ != _default_troute_data_path_) {
    troute_data_path_->assign(*_default_troute_data_path_);
  }
  clear_has_troute_data_path();
}
inline const ::std::string& base_agent_conf::troute_data_path() const {
  // @@protoc_insertion_point(field_get:baseagent.base_agent_conf.troute_data_path)
  return *troute_data_path_;
}
inline void base_agent_conf::set_troute_data_path(const ::std::string& value) {
  set_has_troute_data_path();
  if (troute_data_path_ == _default_troute_data_path_) {
    troute_data_path_ = new ::std::string;
  }
  troute_data_path_->assign(value);
  // @@protoc_insertion_point(field_set:baseagent.base_agent_conf.troute_data_path)
}
inline void base_agent_conf::set_troute_data_path(const char* value) {
  set_has_troute_data_path();
  if (troute_data_path_ == _default_troute_data_path_) {
    troute_data_path_ = new ::std::string;
  }
  troute_data_path_->assign(value);
  // @@protoc_insertion_point(field_set_char:baseagent.base_agent_conf.troute_data_path)
}
inline void base_agent_conf::set_troute_data_path(const char* value, size_t size) {
  set_has_troute_data_path();
  if (troute_data_path_ == _default_troute_data_path_) {
    troute_data_path_ = new ::std::string;
  }
  troute_data_path_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:baseagent.base_agent_conf.troute_data_path)
}
inline ::std::string* base_agent_conf::mutable_troute_data_path() {
  set_has_troute_data_path();
  if (troute_data_path_ == _default_troute_data_path_) {
    troute_data_path_ = new ::std::string(*_default_troute_data_path_);
  }
  // @@protoc_insertion_point(field_mutable:baseagent.base_agent_conf.troute_data_path)
  return troute_data_path_;
}
inline ::std::string* base_agent_conf::release_troute_data_path() {
  clear_has_troute_data_path();
  if (troute_data_path_ == _default_troute_data_path_) {
    return NULL;
  } else {
    ::std::string* temp = troute_data_path_;
    troute_data_path_ = const_cast< ::std::string*>(_default_troute_data_path_);
    return temp;
  }
}
inline void base_agent_conf::set_allocated_troute_data_path(::std::string* troute_data_path) {
  if (troute_data_path_ != _default_troute_data_path_) {
    delete troute_data_path_;
  }
  if (troute_data_path) {
    set_has_troute_data_path();
    troute_data_path_ = troute_data_path;
  } else {
    clear_has_troute_data_path();
    troute_data_path_ = const_cast< ::std::string*>(_default_troute_data_path_);
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.base_agent_conf.troute_data_path)
}

// optional .baseagent.tmsg_conf conf_tmsg = 6;
inline bool base_agent_conf::has_conf_tmsg() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void base_agent_conf::set_has_conf_tmsg() {
  _has_bits_[0] |= 0x00000020u;
}
inline void base_agent_conf::clear_has_conf_tmsg() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void base_agent_conf::clear_conf_tmsg() {
  if (conf_tmsg_ != NULL) conf_tmsg_->::baseagent::tmsg_conf::Clear();
  clear_has_conf_tmsg();
}
inline const ::baseagent::tmsg_conf& base_agent_conf::conf_tmsg() const {
  // @@protoc_insertion_point(field_get:baseagent.base_agent_conf.conf_tmsg)
  return conf_tmsg_ != NULL ? *conf_tmsg_ : *default_instance_->conf_tmsg_;
}
inline ::baseagent::tmsg_conf* base_agent_conf::mutable_conf_tmsg() {
  set_has_conf_tmsg();
  if (conf_tmsg_ == NULL) conf_tmsg_ = new ::baseagent::tmsg_conf;
  // @@protoc_insertion_point(field_mutable:baseagent.base_agent_conf.conf_tmsg)
  return conf_tmsg_;
}
inline ::baseagent::tmsg_conf* base_agent_conf::release_conf_tmsg() {
  clear_has_conf_tmsg();
  ::baseagent::tmsg_conf* temp = conf_tmsg_;
  conf_tmsg_ = NULL;
  return temp;
}
inline void base_agent_conf::set_allocated_conf_tmsg(::baseagent::tmsg_conf* conf_tmsg) {
  delete conf_tmsg_;
  conf_tmsg_ = conf_tmsg;
  if (conf_tmsg) {
    set_has_conf_tmsg();
  } else {
    clear_has_conf_tmsg();
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.base_agent_conf.conf_tmsg)
}

// optional .baseagent.troute_conf conf_troute = 7;
inline bool base_agent_conf::has_conf_troute() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void base_agent_conf::set_has_conf_troute() {
  _has_bits_[0] |= 0x00000040u;
}
inline void base_agent_conf::clear_has_conf_troute() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void base_agent_conf::clear_conf_troute() {
  if (conf_troute_ != NULL) conf_troute_->::baseagent::troute_conf::Clear();
  clear_has_conf_troute();
}
inline const ::baseagent::troute_conf& base_agent_conf::conf_troute() const {
  // @@protoc_insertion_point(field_get:baseagent.base_agent_conf.conf_troute)
  return conf_troute_ != NULL ? *conf_troute_ : *default_instance_->conf_troute_;
}
inline ::baseagent::troute_conf* base_agent_conf::mutable_conf_troute() {
  set_has_conf_troute();
  if (conf_troute_ == NULL) conf_troute_ = new ::baseagent::troute_conf;
  // @@protoc_insertion_point(field_mutable:baseagent.base_agent_conf.conf_troute)
  return conf_troute_;
}
inline ::baseagent::troute_conf* base_agent_conf::release_conf_troute() {
  clear_has_conf_troute();
  ::baseagent::troute_conf* temp = conf_troute_;
  conf_troute_ = NULL;
  return temp;
}
inline void base_agent_conf::set_allocated_conf_troute(::baseagent::troute_conf* conf_troute) {
  delete conf_troute_;
  conf_troute_ = conf_troute;
  if (conf_troute) {
    set_has_conf_troute();
  } else {
    clear_has_conf_troute();
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.base_agent_conf.conf_troute)
}

// optional .baseagent_comm.dbconf conf_nameserver = 8;
inline bool base_agent_conf::has_conf_nameserver() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void base_agent_conf::set_has_conf_nameserver() {
  _has_bits_[0] |= 0x00000080u;
}
inline void base_agent_conf::clear_has_conf_nameserver() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void base_agent_conf::clear_conf_nameserver() {
  if (conf_nameserver_ != NULL) conf_nameserver_->::baseagent_comm::dbconf::Clear();
  clear_has_conf_nameserver();
}
inline const ::baseagent_comm::dbconf& base_agent_conf::conf_nameserver() const {
  // @@protoc_insertion_point(field_get:baseagent.base_agent_conf.conf_nameserver)
  return conf_nameserver_ != NULL ? *conf_nameserver_ : *default_instance_->conf_nameserver_;
}
inline ::baseagent_comm::dbconf* base_agent_conf::mutable_conf_nameserver() {
  set_has_conf_nameserver();
  if (conf_nameserver_ == NULL) conf_nameserver_ = new ::baseagent_comm::dbconf;
  // @@protoc_insertion_point(field_mutable:baseagent.base_agent_conf.conf_nameserver)
  return conf_nameserver_;
}
inline ::baseagent_comm::dbconf* base_agent_conf::release_conf_nameserver() {
  clear_has_conf_nameserver();
  ::baseagent_comm::dbconf* temp = conf_nameserver_;
  conf_nameserver_ = NULL;
  return temp;
}
inline void base_agent_conf::set_allocated_conf_nameserver(::baseagent_comm::dbconf* conf_nameserver) {
  delete conf_nameserver_;
  conf_nameserver_ = conf_nameserver;
  if (conf_nameserver) {
    set_has_conf_nameserver();
  } else {
    clear_has_conf_nameserver();
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.base_agent_conf.conf_nameserver)
}

// optional .baseagent.net_conf conf_net = 9;
inline bool base_agent_conf::has_conf_net() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void base_agent_conf::set_has_conf_net() {
  _has_bits_[0] |= 0x00000100u;
}
inline void base_agent_conf::clear_has_conf_net() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void base_agent_conf::clear_conf_net() {
  if (conf_net_ != NULL) conf_net_->::baseagent::net_conf::Clear();
  clear_has_conf_net();
}
inline const ::baseagent::net_conf& base_agent_conf::conf_net() const {
  // @@protoc_insertion_point(field_get:baseagent.base_agent_conf.conf_net)
  return conf_net_ != NULL ? *conf_net_ : *default_instance_->conf_net_;
}
inline ::baseagent::net_conf* base_agent_conf::mutable_conf_net() {
  set_has_conf_net();
  if (conf_net_ == NULL) conf_net_ = new ::baseagent::net_conf;
  // @@protoc_insertion_point(field_mutable:baseagent.base_agent_conf.conf_net)
  return conf_net_;
}
inline ::baseagent::net_conf* base_agent_conf::release_conf_net() {
  clear_has_conf_net();
  ::baseagent::net_conf* temp = conf_net_;
  conf_net_ = NULL;
  return temp;
}
inline void base_agent_conf::set_allocated_conf_net(::baseagent::net_conf* conf_net) {
  delete conf_net_;
  conf_net_ = conf_net;
  if (conf_net) {
    set_has_conf_net();
  } else {
    clear_has_conf_net();
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.base_agent_conf.conf_net)
}

// optional string ifx = 10 [default = "eth1"];
inline bool base_agent_conf::has_ifx() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
inline void base_agent_conf::set_has_ifx() {
  _has_bits_[0] |= 0x00000200u;
}
inline void base_agent_conf::clear_has_ifx() {
  _has_bits_[0] &= ~0x00000200u;
}
inline void base_agent_conf::clear_ifx() {
  if (ifx_ != _default_ifx_) {
    ifx_->assign(*_default_ifx_);
  }
  clear_has_ifx();
}
inline const ::std::string& base_agent_conf::ifx() const {
  // @@protoc_insertion_point(field_get:baseagent.base_agent_conf.ifx)
  return *ifx_;
}
inline void base_agent_conf::set_ifx(const ::std::string& value) {
  set_has_ifx();
  if (ifx_ == _default_ifx_) {
    ifx_ = new ::std::string;
  }
  ifx_->assign(value);
  // @@protoc_insertion_point(field_set:baseagent.base_agent_conf.ifx)
}
inline void base_agent_conf::set_ifx(const char* value) {
  set_has_ifx();
  if (ifx_ == _default_ifx_) {
    ifx_ = new ::std::string;
  }
  ifx_->assign(value);
  // @@protoc_insertion_point(field_set_char:baseagent.base_agent_conf.ifx)
}
inline void base_agent_conf::set_ifx(const char* value, size_t size) {
  set_has_ifx();
  if (ifx_ == _default_ifx_) {
    ifx_ = new ::std::string;
  }
  ifx_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:baseagent.base_agent_conf.ifx)
}
inline ::std::string* base_agent_conf::mutable_ifx() {
  set_has_ifx();
  if (ifx_ == _default_ifx_) {
    ifx_ = new ::std::string(*_default_ifx_);
  }
  // @@protoc_insertion_point(field_mutable:baseagent.base_agent_conf.ifx)
  return ifx_;
}
inline ::std::string* base_agent_conf::release_ifx() {
  clear_has_ifx();
  if (ifx_ == _default_ifx_) {
    return NULL;
  } else {
    ::std::string* temp = ifx_;
    ifx_ = const_cast< ::std::string*>(_default_ifx_);
    return temp;
  }
}
inline void base_agent_conf::set_allocated_ifx(::std::string* ifx) {
  if (ifx_ != _default_ifx_) {
    delete ifx_;
  }
  if (ifx) {
    set_has_ifx();
    ifx_ = ifx;
  } else {
    clear_has_ifx();
    ifx_ = const_cast< ::std::string*>(_default_ifx_);
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.base_agent_conf.ifx)
}

// optional uint32 port = 11 [default = 2001];
inline bool base_agent_conf::has_port() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
inline void base_agent_conf::set_has_port() {
  _has_bits_[0] |= 0x00000400u;
}
inline void base_agent_conf::clear_has_port() {
  _has_bits_[0] &= ~0x00000400u;
}
inline void base_agent_conf::clear_port() {
  port_ = 2001u;
  clear_has_port();
}
inline ::google::protobuf::uint32 base_agent_conf::port() const {
  // @@protoc_insertion_point(field_get:baseagent.base_agent_conf.port)
  return port_;
}
inline void base_agent_conf::set_port(::google::protobuf::uint32 value) {
  set_has_port();
  port_ = value;
  // @@protoc_insertion_point(field_set:baseagent.base_agent_conf.port)
}

// optional string private_data_name = 12 [default = "baseagent.data"];
inline bool base_agent_conf::has_private_data_name() const {
  return (_has_bits_[0] & 0x00000800u) != 0;
}
inline void base_agent_conf::set_has_private_data_name() {
  _has_bits_[0] |= 0x00000800u;
}
inline void base_agent_conf::clear_has_private_data_name() {
  _has_bits_[0] &= ~0x00000800u;
}
inline void base_agent_conf::clear_private_data_name() {
  if (private_data_name_ != _default_private_data_name_) {
    private_data_name_->assign(*_default_private_data_name_);
  }
  clear_has_private_data_name();
}
inline const ::std::string& base_agent_conf::private_data_name() const {
  // @@protoc_insertion_point(field_get:baseagent.base_agent_conf.private_data_name)
  return *private_data_name_;
}
inline void base_agent_conf::set_private_data_name(const ::std::string& value) {
  set_has_private_data_name();
  if (private_data_name_ == _default_private_data_name_) {
    private_data_name_ = new ::std::string;
  }
  private_data_name_->assign(value);
  // @@protoc_insertion_point(field_set:baseagent.base_agent_conf.private_data_name)
}
inline void base_agent_conf::set_private_data_name(const char* value) {
  set_has_private_data_name();
  if (private_data_name_ == _default_private_data_name_) {
    private_data_name_ = new ::std::string;
  }
  private_data_name_->assign(value);
  // @@protoc_insertion_point(field_set_char:baseagent.base_agent_conf.private_data_name)
}
inline void base_agent_conf::set_private_data_name(const char* value, size_t size) {
  set_has_private_data_name();
  if (private_data_name_ == _default_private_data_name_) {
    private_data_name_ = new ::std::string;
  }
  private_data_name_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:baseagent.base_agent_conf.private_data_name)
}
inline ::std::string* base_agent_conf::mutable_private_data_name() {
  set_has_private_data_name();
  if (private_data_name_ == _default_private_data_name_) {
    private_data_name_ = new ::std::string(*_default_private_data_name_);
  }
  // @@protoc_insertion_point(field_mutable:baseagent.base_agent_conf.private_data_name)
  return private_data_name_;
}
inline ::std::string* base_agent_conf::release_private_data_name() {
  clear_has_private_data_name();
  if (private_data_name_ == _default_private_data_name_) {
    return NULL;
  } else {
    ::std::string* temp = private_data_name_;
    private_data_name_ = const_cast< ::std::string*>(_default_private_data_name_);
    return temp;
  }
}
inline void base_agent_conf::set_allocated_private_data_name(::std::string* private_data_name) {
  if (private_data_name_ != _default_private_data_name_) {
    delete private_data_name_;
  }
  if (private_data_name) {
    set_has_private_data_name();
    private_data_name_ = private_data_name;
  } else {
    clear_has_private_data_name();
    private_data_name_ = const_cast< ::std::string*>(_default_private_data_name_);
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.base_agent_conf.private_data_name)
}

// optional .baseagent.busid_map_conf conf_busid_map = 13;
inline bool base_agent_conf::has_conf_busid_map() const {
  return (_has_bits_[0] & 0x00001000u) != 0;
}
inline void base_agent_conf::set_has_conf_busid_map() {
  _has_bits_[0] |= 0x00001000u;
}
inline void base_agent_conf::clear_has_conf_busid_map() {
  _has_bits_[0] &= ~0x00001000u;
}
inline void base_agent_conf::clear_conf_busid_map() {
  if (conf_busid_map_ != NULL) conf_busid_map_->::baseagent::busid_map_conf::Clear();
  clear_has_conf_busid_map();
}
inline const ::baseagent::busid_map_conf& base_agent_conf::conf_busid_map() const {
  // @@protoc_insertion_point(field_get:baseagent.base_agent_conf.conf_busid_map)
  return conf_busid_map_ != NULL ? *conf_busid_map_ : *default_instance_->conf_busid_map_;
}
inline ::baseagent::busid_map_conf* base_agent_conf::mutable_conf_busid_map() {
  set_has_conf_busid_map();
  if (conf_busid_map_ == NULL) conf_busid_map_ = new ::baseagent::busid_map_conf;
  // @@protoc_insertion_point(field_mutable:baseagent.base_agent_conf.conf_busid_map)
  return conf_busid_map_;
}
inline ::baseagent::busid_map_conf* base_agent_conf::release_conf_busid_map() {
  clear_has_conf_busid_map();
  ::baseagent::busid_map_conf* temp = conf_busid_map_;
  conf_busid_map_ = NULL;
  return temp;
}
inline void base_agent_conf::set_allocated_conf_busid_map(::baseagent::busid_map_conf* conf_busid_map) {
  delete conf_busid_map_;
  conf_busid_map_ = conf_busid_map;
  if (conf_busid_map) {
    set_has_conf_busid_map();
  } else {
    clear_has_conf_busid_map();
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.base_agent_conf.conf_busid_map)
}

// optional string busid_map_path = 15 [default = "/dev/shm/baseagent/busidmap/"];
inline bool base_agent_conf::has_busid_map_path() const {
  return (_has_bits_[0] & 0x00002000u) != 0;
}
inline void base_agent_conf::set_has_busid_map_path() {
  _has_bits_[0] |= 0x00002000u;
}
inline void base_agent_conf::clear_has_busid_map_path() {
  _has_bits_[0] &= ~0x00002000u;
}
inline void base_agent_conf::clear_busid_map_path() {
  if (busid_map_path_ != _default_busid_map_path_) {
    busid_map_path_->assign(*_default_busid_map_path_);
  }
  clear_has_busid_map_path();
}
inline const ::std::string& base_agent_conf::busid_map_path() const {
  // @@protoc_insertion_point(field_get:baseagent.base_agent_conf.busid_map_path)
  return *busid_map_path_;
}
inline void base_agent_conf::set_busid_map_path(const ::std::string& value) {
  set_has_busid_map_path();
  if (busid_map_path_ == _default_busid_map_path_) {
    busid_map_path_ = new ::std::string;
  }
  busid_map_path_->assign(value);
  // @@protoc_insertion_point(field_set:baseagent.base_agent_conf.busid_map_path)
}
inline void base_agent_conf::set_busid_map_path(const char* value) {
  set_has_busid_map_path();
  if (busid_map_path_ == _default_busid_map_path_) {
    busid_map_path_ = new ::std::string;
  }
  busid_map_path_->assign(value);
  // @@protoc_insertion_point(field_set_char:baseagent.base_agent_conf.busid_map_path)
}
inline void base_agent_conf::set_busid_map_path(const char* value, size_t size) {
  set_has_busid_map_path();
  if (busid_map_path_ == _default_busid_map_path_) {
    busid_map_path_ = new ::std::string;
  }
  busid_map_path_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:baseagent.base_agent_conf.busid_map_path)
}
inline ::std::string* base_agent_conf::mutable_busid_map_path() {
  set_has_busid_map_path();
  if (busid_map_path_ == _default_busid_map_path_) {
    busid_map_path_ = new ::std::string(*_default_busid_map_path_);
  }
  // @@protoc_insertion_point(field_mutable:baseagent.base_agent_conf.busid_map_path)
  return busid_map_path_;
}
inline ::std::string* base_agent_conf::release_busid_map_path() {
  clear_has_busid_map_path();
  if (busid_map_path_ == _default_busid_map_path_) {
    return NULL;
  } else {
    ::std::string* temp = busid_map_path_;
    busid_map_path_ = const_cast< ::std::string*>(_default_busid_map_path_);
    return temp;
  }
}
inline void base_agent_conf::set_allocated_busid_map_path(::std::string* busid_map_path) {
  if (busid_map_path_ != _default_busid_map_path_) {
    delete busid_map_path_;
  }
  if (busid_map_path) {
    set_has_busid_map_path();
    busid_map_path_ = busid_map_path;
  } else {
    clear_has_busid_map_path();
    busid_map_path_ = const_cast< ::std::string*>(_default_busid_map_path_);
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.base_agent_conf.busid_map_path)
}

// repeated .baseagent.extern_proto_conf proto_conf = 21;
inline int base_agent_conf::proto_conf_size() const {
  return proto_conf_.size();
}
inline void base_agent_conf::clear_proto_conf() {
  proto_conf_.Clear();
}
inline const ::baseagent::extern_proto_conf& base_agent_conf::proto_conf(int index) const {
  // @@protoc_insertion_point(field_get:baseagent.base_agent_conf.proto_conf)
  return proto_conf_.Get(index);
}
inline ::baseagent::extern_proto_conf* base_agent_conf::mutable_proto_conf(int index) {
  // @@protoc_insertion_point(field_mutable:baseagent.base_agent_conf.proto_conf)
  return proto_conf_.Mutable(index);
}
inline ::baseagent::extern_proto_conf* base_agent_conf::add_proto_conf() {
  // @@protoc_insertion_point(field_add:baseagent.base_agent_conf.proto_conf)
  return proto_conf_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::baseagent::extern_proto_conf >&
base_agent_conf::proto_conf() const {
  // @@protoc_insertion_point(field_list:baseagent.base_agent_conf.proto_conf)
  return proto_conf_;
}
inline ::google::protobuf::RepeatedPtrField< ::baseagent::extern_proto_conf >*
base_agent_conf::mutable_proto_conf() {
  // @@protoc_insertion_point(field_mutable_list:baseagent.base_agent_conf.proto_conf)
  return &proto_conf_;
}

// repeated .baseagent.stat_conf conf_stat = 22;
inline int base_agent_conf::conf_stat_size() const {
  return conf_stat_.size();
}
inline void base_agent_conf::clear_conf_stat() {
  conf_stat_.Clear();
}
inline const ::baseagent::stat_conf& base_agent_conf::conf_stat(int index) const {
  // @@protoc_insertion_point(field_get:baseagent.base_agent_conf.conf_stat)
  return conf_stat_.Get(index);
}
inline ::baseagent::stat_conf* base_agent_conf::mutable_conf_stat(int index) {
  // @@protoc_insertion_point(field_mutable:baseagent.base_agent_conf.conf_stat)
  return conf_stat_.Mutable(index);
}
inline ::baseagent::stat_conf* base_agent_conf::add_conf_stat() {
  // @@protoc_insertion_point(field_add:baseagent.base_agent_conf.conf_stat)
  return conf_stat_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::baseagent::stat_conf >&
base_agent_conf::conf_stat() const {
  // @@protoc_insertion_point(field_list:baseagent.base_agent_conf.conf_stat)
  return conf_stat_;
}
inline ::google::protobuf::RepeatedPtrField< ::baseagent::stat_conf >*
base_agent_conf::mutable_conf_stat() {
  // @@protoc_insertion_point(field_mutable_list:baseagent.base_agent_conf.conf_stat)
  return &conf_stat_;
}

// -------------------------------------------------------------------

// sync_name_server_info

// optional string sync_depend_list_fail = 1;
inline bool sync_name_server_info::has_sync_depend_list_fail() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void sync_name_server_info::set_has_sync_depend_list_fail() {
  _has_bits_[0] |= 0x00000001u;
}
inline void sync_name_server_info::clear_has_sync_depend_list_fail() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void sync_name_server_info::clear_sync_depend_list_fail() {
  if (sync_depend_list_fail_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    sync_depend_list_fail_->clear();
  }
  clear_has_sync_depend_list_fail();
}
inline const ::std::string& sync_name_server_info::sync_depend_list_fail() const {
  // @@protoc_insertion_point(field_get:baseagent.sync_name_server_info.sync_depend_list_fail)
  return *sync_depend_list_fail_;
}
inline void sync_name_server_info::set_sync_depend_list_fail(const ::std::string& value) {
  set_has_sync_depend_list_fail();
  if (sync_depend_list_fail_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    sync_depend_list_fail_ = new ::std::string;
  }
  sync_depend_list_fail_->assign(value);
  // @@protoc_insertion_point(field_set:baseagent.sync_name_server_info.sync_depend_list_fail)
}
inline void sync_name_server_info::set_sync_depend_list_fail(const char* value) {
  set_has_sync_depend_list_fail();
  if (sync_depend_list_fail_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    sync_depend_list_fail_ = new ::std::string;
  }
  sync_depend_list_fail_->assign(value);
  // @@protoc_insertion_point(field_set_char:baseagent.sync_name_server_info.sync_depend_list_fail)
}
inline void sync_name_server_info::set_sync_depend_list_fail(const char* value, size_t size) {
  set_has_sync_depend_list_fail();
  if (sync_depend_list_fail_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    sync_depend_list_fail_ = new ::std::string;
  }
  sync_depend_list_fail_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:baseagent.sync_name_server_info.sync_depend_list_fail)
}
inline ::std::string* sync_name_server_info::mutable_sync_depend_list_fail() {
  set_has_sync_depend_list_fail();
  if (sync_depend_list_fail_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    sync_depend_list_fail_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:baseagent.sync_name_server_info.sync_depend_list_fail)
  return sync_depend_list_fail_;
}
inline ::std::string* sync_name_server_info::release_sync_depend_list_fail() {
  clear_has_sync_depend_list_fail();
  if (sync_depend_list_fail_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = sync_depend_list_fail_;
    sync_depend_list_fail_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void sync_name_server_info::set_allocated_sync_depend_list_fail(::std::string* sync_depend_list_fail) {
  if (sync_depend_list_fail_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete sync_depend_list_fail_;
  }
  if (sync_depend_list_fail) {
    set_has_sync_depend_list_fail();
    sync_depend_list_fail_ = sync_depend_list_fail;
  } else {
    clear_has_sync_depend_list_fail();
    sync_depend_list_fail_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.sync_name_server_info.sync_depend_list_fail)
}

// optional string register_instance_fail = 2;
inline bool sync_name_server_info::has_register_instance_fail() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void sync_name_server_info::set_has_register_instance_fail() {
  _has_bits_[0] |= 0x00000002u;
}
inline void sync_name_server_info::clear_has_register_instance_fail() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void sync_name_server_info::clear_register_instance_fail() {
  if (register_instance_fail_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    register_instance_fail_->clear();
  }
  clear_has_register_instance_fail();
}
inline const ::std::string& sync_name_server_info::register_instance_fail() const {
  // @@protoc_insertion_point(field_get:baseagent.sync_name_server_info.register_instance_fail)
  return *register_instance_fail_;
}
inline void sync_name_server_info::set_register_instance_fail(const ::std::string& value) {
  set_has_register_instance_fail();
  if (register_instance_fail_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    register_instance_fail_ = new ::std::string;
  }
  register_instance_fail_->assign(value);
  // @@protoc_insertion_point(field_set:baseagent.sync_name_server_info.register_instance_fail)
}
inline void sync_name_server_info::set_register_instance_fail(const char* value) {
  set_has_register_instance_fail();
  if (register_instance_fail_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    register_instance_fail_ = new ::std::string;
  }
  register_instance_fail_->assign(value);
  // @@protoc_insertion_point(field_set_char:baseagent.sync_name_server_info.register_instance_fail)
}
inline void sync_name_server_info::set_register_instance_fail(const char* value, size_t size) {
  set_has_register_instance_fail();
  if (register_instance_fail_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    register_instance_fail_ = new ::std::string;
  }
  register_instance_fail_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:baseagent.sync_name_server_info.register_instance_fail)
}
inline ::std::string* sync_name_server_info::mutable_register_instance_fail() {
  set_has_register_instance_fail();
  if (register_instance_fail_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    register_instance_fail_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:baseagent.sync_name_server_info.register_instance_fail)
  return register_instance_fail_;
}
inline ::std::string* sync_name_server_info::release_register_instance_fail() {
  clear_has_register_instance_fail();
  if (register_instance_fail_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = register_instance_fail_;
    register_instance_fail_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void sync_name_server_info::set_allocated_register_instance_fail(::std::string* register_instance_fail) {
  if (register_instance_fail_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete register_instance_fail_;
  }
  if (register_instance_fail) {
    set_has_register_instance_fail();
    register_instance_fail_ = register_instance_fail;
  } else {
    clear_has_register_instance_fail();
    register_instance_fail_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.sync_name_server_info.register_instance_fail)
}

// optional string cancel_register_instance_fail = 3;
inline bool sync_name_server_info::has_cancel_register_instance_fail() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void sync_name_server_info::set_has_cancel_register_instance_fail() {
  _has_bits_[0] |= 0x00000004u;
}
inline void sync_name_server_info::clear_has_cancel_register_instance_fail() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void sync_name_server_info::clear_cancel_register_instance_fail() {
  if (cancel_register_instance_fail_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    cancel_register_instance_fail_->clear();
  }
  clear_has_cancel_register_instance_fail();
}
inline const ::std::string& sync_name_server_info::cancel_register_instance_fail() const {
  // @@protoc_insertion_point(field_get:baseagent.sync_name_server_info.cancel_register_instance_fail)
  return *cancel_register_instance_fail_;
}
inline void sync_name_server_info::set_cancel_register_instance_fail(const ::std::string& value) {
  set_has_cancel_register_instance_fail();
  if (cancel_register_instance_fail_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    cancel_register_instance_fail_ = new ::std::string;
  }
  cancel_register_instance_fail_->assign(value);
  // @@protoc_insertion_point(field_set:baseagent.sync_name_server_info.cancel_register_instance_fail)
}
inline void sync_name_server_info::set_cancel_register_instance_fail(const char* value) {
  set_has_cancel_register_instance_fail();
  if (cancel_register_instance_fail_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    cancel_register_instance_fail_ = new ::std::string;
  }
  cancel_register_instance_fail_->assign(value);
  // @@protoc_insertion_point(field_set_char:baseagent.sync_name_server_info.cancel_register_instance_fail)
}
inline void sync_name_server_info::set_cancel_register_instance_fail(const char* value, size_t size) {
  set_has_cancel_register_instance_fail();
  if (cancel_register_instance_fail_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    cancel_register_instance_fail_ = new ::std::string;
  }
  cancel_register_instance_fail_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:baseagent.sync_name_server_info.cancel_register_instance_fail)
}
inline ::std::string* sync_name_server_info::mutable_cancel_register_instance_fail() {
  set_has_cancel_register_instance_fail();
  if (cancel_register_instance_fail_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    cancel_register_instance_fail_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:baseagent.sync_name_server_info.cancel_register_instance_fail)
  return cancel_register_instance_fail_;
}
inline ::std::string* sync_name_server_info::release_cancel_register_instance_fail() {
  clear_has_cancel_register_instance_fail();
  if (cancel_register_instance_fail_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = cancel_register_instance_fail_;
    cancel_register_instance_fail_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void sync_name_server_info::set_allocated_cancel_register_instance_fail(::std::string* cancel_register_instance_fail) {
  if (cancel_register_instance_fail_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete cancel_register_instance_fail_;
  }
  if (cancel_register_instance_fail) {
    set_has_cancel_register_instance_fail();
    cancel_register_instance_fail_ = cancel_register_instance_fail;
  } else {
    clear_has_cancel_register_instance_fail();
    cancel_register_instance_fail_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.sync_name_server_info.cancel_register_instance_fail)
}

// -------------------------------------------------------------------

// name_list

// optional string list = 1;
inline bool name_list::has_list() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void name_list::set_has_list() {
  _has_bits_[0] |= 0x00000001u;
}
inline void name_list::clear_has_list() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void name_list::clear_list() {
  if (list_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    list_->clear();
  }
  clear_has_list();
}
inline const ::std::string& name_list::list() const {
  // @@protoc_insertion_point(field_get:baseagent.name_list.list)
  return *list_;
}
inline void name_list::set_list(const ::std::string& value) {
  set_has_list();
  if (list_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    list_ = new ::std::string;
  }
  list_->assign(value);
  // @@protoc_insertion_point(field_set:baseagent.name_list.list)
}
inline void name_list::set_list(const char* value) {
  set_has_list();
  if (list_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    list_ = new ::std::string;
  }
  list_->assign(value);
  // @@protoc_insertion_point(field_set_char:baseagent.name_list.list)
}
inline void name_list::set_list(const char* value, size_t size) {
  set_has_list();
  if (list_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    list_ = new ::std::string;
  }
  list_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:baseagent.name_list.list)
}
inline ::std::string* name_list::mutable_list() {
  set_has_list();
  if (list_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    list_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:baseagent.name_list.list)
  return list_;
}
inline ::std::string* name_list::release_list() {
  clear_has_list();
  if (list_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = list_;
    list_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void name_list::set_allocated_list(::std::string* list) {
  if (list_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete list_;
  }
  if (list) {
    set_has_list();
    list_ = list;
  } else {
    clear_has_list();
    list_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.name_list.list)
}

// -------------------------------------------------------------------

// trans_info

// optional .troute.server_name_and_address src = 1;
inline bool trans_info::has_src() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void trans_info::set_has_src() {
  _has_bits_[0] |= 0x00000001u;
}
inline void trans_info::clear_has_src() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void trans_info::clear_src() {
  if (src_ != NULL) src_->::troute::server_name_and_address::Clear();
  clear_has_src();
}
inline const ::troute::server_name_and_address& trans_info::src() const {
  // @@protoc_insertion_point(field_get:baseagent.trans_info.src)
  return src_ != NULL ? *src_ : *default_instance_->src_;
}
inline ::troute::server_name_and_address* trans_info::mutable_src() {
  set_has_src();
  if (src_ == NULL) src_ = new ::troute::server_name_and_address;
  // @@protoc_insertion_point(field_mutable:baseagent.trans_info.src)
  return src_;
}
inline ::troute::server_name_and_address* trans_info::release_src() {
  clear_has_src();
  ::troute::server_name_and_address* temp = src_;
  src_ = NULL;
  return temp;
}
inline void trans_info::set_allocated_src(::troute::server_name_and_address* src) {
  delete src_;
  src_ = src;
  if (src) {
    set_has_src();
  } else {
    clear_has_src();
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.trans_info.src)
}

// optional .troute.server_name_and_address dst = 2;
inline bool trans_info::has_dst() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void trans_info::set_has_dst() {
  _has_bits_[0] |= 0x00000002u;
}
inline void trans_info::clear_has_dst() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void trans_info::clear_dst() {
  if (dst_ != NULL) dst_->::troute::server_name_and_address::Clear();
  clear_has_dst();
}
inline const ::troute::server_name_and_address& trans_info::dst() const {
  // @@protoc_insertion_point(field_get:baseagent.trans_info.dst)
  return dst_ != NULL ? *dst_ : *default_instance_->dst_;
}
inline ::troute::server_name_and_address* trans_info::mutable_dst() {
  set_has_dst();
  if (dst_ == NULL) dst_ = new ::troute::server_name_and_address;
  // @@protoc_insertion_point(field_mutable:baseagent.trans_info.dst)
  return dst_;
}
inline ::troute::server_name_and_address* trans_info::release_dst() {
  clear_has_dst();
  ::troute::server_name_and_address* temp = dst_;
  dst_ = NULL;
  return temp;
}
inline void trans_info::set_allocated_dst(::troute::server_name_and_address* dst) {
  delete dst_;
  dst_ = dst;
  if (dst) {
    set_has_dst();
  } else {
    clear_has_dst();
  }
  // @@protoc_insertion_point(field_set_allocated:baseagent.trans_info.dst)
}

// -------------------------------------------------------------------

// event_handle

// optional uint32 event_notify_id = 1;
inline bool event_handle::has_event_notify_id() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void event_handle::set_has_event_notify_id() {
  _has_bits_[0] |= 0x00000001u;
}
inline void event_handle::clear_has_event_notify_id() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void event_handle::clear_event_notify_id() {
  event_notify_id_ = 0u;
  clear_has_event_notify_id();
}
inline ::google::protobuf::uint32 event_handle::event_notify_id() const {
  // @@protoc_insertion_point(field_get:baseagent.event_handle.event_notify_id)
  return event_notify_id_;
}
inline void event_handle::set_event_notify_id(::google::protobuf::uint32 value) {
  set_has_event_notify_id();
  event_notify_id_ = value;
  // @@protoc_insertion_point(field_set:baseagent.event_handle.event_notify_id)
}

// optional uint32 version = 2;
inline bool event_handle::has_version() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void event_handle::set_has_version() {
  _has_bits_[0] |= 0x00000002u;
}
inline void event_handle::clear_has_version() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void event_handle::clear_version() {
  version_ = 0u;
  clear_has_version();
}
inline ::google::protobuf::uint32 event_handle::version() const {
  // @@protoc_insertion_point(field_get:baseagent.event_handle.version)
  return version_;
}
inline void event_handle::set_version(::google::protobuf::uint32 value) {
  set_has_version();
  version_ = value;
  // @@protoc_insertion_point(field_set:baseagent.event_handle.version)
}

// -------------------------------------------------------------------

// event_handle_list

// repeated .baseagent.event_handle list = 1;
inline int event_handle_list::list_size() const {
  return list_.size();
}
inline void event_handle_list::clear_list() {
  list_.Clear();
}
inline const ::baseagent::event_handle& event_handle_list::list(int index) const {
  // @@protoc_insertion_point(field_get:baseagent.event_handle_list.list)
  return list_.Get(index);
}
inline ::baseagent::event_handle* event_handle_list::mutable_list(int index) {
  // @@protoc_insertion_point(field_mutable:baseagent.event_handle_list.list)
  return list_.Mutable(index);
}
inline ::baseagent::event_handle* event_handle_list::add_list() {
  // @@protoc_insertion_point(field_add:baseagent.event_handle_list.list)
  return list_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::baseagent::event_handle >&
event_handle_list::list() const {
  // @@protoc_insertion_point(field_list:baseagent.event_handle_list.list)
  return list_;
}
inline ::google::protobuf::RepeatedPtrField< ::baseagent::event_handle >*
event_handle_list::mutable_list() {
  // @@protoc_insertion_point(field_mutable_list:baseagent.event_handle_list.list)
  return &list_;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace baseagent

#ifndef SWIG
namespace google {
namespace protobuf {


}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_source_2fbaseagent_2fproto_2fbase_5fagent_2eproto__INCLUDED
