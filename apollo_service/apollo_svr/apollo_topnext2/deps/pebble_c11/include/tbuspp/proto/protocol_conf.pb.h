// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tbuspp/proto/protocol_conf.proto

#ifndef PROTOBUF_source_2fbaseagent_2fproto_2fprotocol_5fconf_2eproto__INCLUDED
#define PROTOBUF_source_2fbaseagent_2fproto_2fprotocol_5fconf_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2006000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace proto_conf {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fprotocol_5fconf_2eproto();
void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fprotocol_5fconf_2eproto();
void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fprotocol_5fconf_2eproto();

class listen_conf;
class agent_conf;
class tbus_conf;
class user_listen_conf;
class conn_nostate_conf;

// ===================================================================

class listen_conf : public ::google::protobuf::Message {
 public:
  listen_conf();
  virtual ~listen_conf();

  listen_conf(const listen_conf& from);

  inline listen_conf& operator=(const listen_conf& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const listen_conf& default_instance();

  void Swap(listen_conf* other);

  // implements Message ----------------------------------------------

  listen_conf* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const listen_conf& from);
  void MergeFrom(const listen_conf& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string ifx = 1;
  inline bool has_ifx() const;
  inline void clear_ifx();
  static const int kIfxFieldNumber = 1;
  inline const ::std::string& ifx() const;
  inline void set_ifx(const ::std::string& value);
  inline void set_ifx(const char* value);
  inline void set_ifx(const char* value, size_t size);
  inline ::std::string* mutable_ifx();
  inline ::std::string* release_ifx();
  inline void set_allocated_ifx(::std::string* ifx);

  // optional uint32 port = 2;
  inline bool has_port() const;
  inline void clear_port();
  static const int kPortFieldNumber = 2;
  inline ::google::protobuf::uint32 port() const;
  inline void set_port(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:proto_conf.listen_conf)
 private:
  inline void set_has_ifx();
  inline void clear_has_ifx();
  inline void set_has_port();
  inline void clear_has_port();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::std::string* ifx_;
  ::google::protobuf::uint32 port_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fprotocol_5fconf_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fprotocol_5fconf_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fprotocol_5fconf_2eproto();

  void InitAsDefaultInstance();
  static listen_conf* default_instance_;
};
// -------------------------------------------------------------------

class agent_conf : public ::google::protobuf::Message {
 public:
  agent_conf();
  virtual ~agent_conf();

  agent_conf(const agent_conf& from);

  inline agent_conf& operator=(const agent_conf& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const agent_conf& default_instance();

  void Swap(agent_conf* other);

  // implements Message ----------------------------------------------

  agent_conf* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const agent_conf& from);
  void MergeFrom(const agent_conf& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .proto_conf.listen_conf listens = 1;
  inline int listens_size() const;
  inline void clear_listens();
  static const int kListensFieldNumber = 1;
  inline const ::proto_conf::listen_conf& listens(int index) const;
  inline ::proto_conf::listen_conf* mutable_listens(int index);
  inline ::proto_conf::listen_conf* add_listens();
  inline const ::google::protobuf::RepeatedPtrField< ::proto_conf::listen_conf >&
      listens() const;
  inline ::google::protobuf::RepeatedPtrField< ::proto_conf::listen_conf >*
      mutable_listens();

  // optional bool need_ack = 2 [default = true];
  inline bool has_need_ack() const;
  inline void clear_need_ack();
  static const int kNeedAckFieldNumber = 2;
  inline bool need_ack() const;
  inline void set_need_ack(bool value);

  // optional bool use_bussiness_seq = 3 [default = false];
  inline bool has_use_bussiness_seq() const;
  inline void clear_use_bussiness_seq();
  static const int kUseBussinessSeqFieldNumber = 3;
  inline bool use_bussiness_seq() const;
  inline void set_use_bussiness_seq(bool value);

  // @@protoc_insertion_point(class_scope:proto_conf.agent_conf)
 private:
  inline void set_has_need_ack();
  inline void clear_has_need_ack();
  inline void set_has_use_bussiness_seq();
  inline void clear_has_use_bussiness_seq();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::proto_conf::listen_conf > listens_;
  bool need_ack_;
  bool use_bussiness_seq_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fprotocol_5fconf_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fprotocol_5fconf_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fprotocol_5fconf_2eproto();

  void InitAsDefaultInstance();
  static agent_conf* default_instance_;
};
// -------------------------------------------------------------------

class tbus_conf : public ::google::protobuf::Message {
 public:
  tbus_conf();
  virtual ~tbus_conf();

  tbus_conf(const tbus_conf& from);

  inline tbus_conf& operator=(const tbus_conf& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const tbus_conf& default_instance();

  void Swap(tbus_conf* other);

  // implements Message ----------------------------------------------

  tbus_conf* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const tbus_conf& from);
  void MergeFrom(const tbus_conf& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 tbus_addr_prefix_id = 1 [default = 10];
  inline bool has_tbus_addr_prefix_id() const;
  inline void clear_tbus_addr_prefix_id();
  static const int kTbusAddrPrefixIdFieldNumber = 1;
  inline ::google::protobuf::int32 tbus_addr_prefix_id() const;
  inline void set_tbus_addr_prefix_id(::google::protobuf::int32 value);

  // optional string tbus_addr_prefix_name = 2 [default = "tbus_addr"];
  inline bool has_tbus_addr_prefix_name() const;
  inline void clear_tbus_addr_prefix_name();
  static const int kTbusAddrPrefixNameFieldNumber = 2;
  inline const ::std::string& tbus_addr_prefix_name() const;
  inline void set_tbus_addr_prefix_name(const ::std::string& value);
  inline void set_tbus_addr_prefix_name(const char* value);
  inline void set_tbus_addr_prefix_name(const char* value, size_t size);
  inline ::std::string* mutable_tbus_addr_prefix_name();
  inline ::std::string* release_tbus_addr_prefix_name();
  inline void set_allocated_tbus_addr_prefix_name(::std::string* tbus_addr_prefix_name);

  // optional string tbus_id_template = 3 [default = "8.8.8.8,cluster_.app_.server_,9.8.6"];
  inline bool has_tbus_id_template() const;
  inline void clear_tbus_id_template();
  static const int kTbusIdTemplateFieldNumber = 3;
  inline const ::std::string& tbus_id_template() const;
  inline void set_tbus_id_template(const ::std::string& value);
  inline void set_tbus_id_template(const char* value);
  inline void set_tbus_id_template(const char* value, size_t size);
  inline ::std::string* mutable_tbus_id_template();
  inline ::std::string* release_tbus_id_template();
  inline void set_allocated_tbus_id_template(::std::string* tbus_id_template);

  // optional uint32 default_tbusd_port = 4 [default = 1027];
  inline bool has_default_tbusd_port() const;
  inline void clear_default_tbusd_port();
  static const int kDefaultTbusdPortFieldNumber = 4;
  inline ::google::protobuf::uint32 default_tbusd_port() const;
  inline void set_default_tbusd_port(::google::protobuf::uint32 value);

  // repeated .proto_conf.listen_conf listens = 5;
  inline int listens_size() const;
  inline void clear_listens();
  static const int kListensFieldNumber = 5;
  inline const ::proto_conf::listen_conf& listens(int index) const;
  inline ::proto_conf::listen_conf* mutable_listens(int index);
  inline ::proto_conf::listen_conf* add_listens();
  inline const ::google::protobuf::RepeatedPtrField< ::proto_conf::listen_conf >&
      listens() const;
  inline ::google::protobuf::RepeatedPtrField< ::proto_conf::listen_conf >*
      mutable_listens();

  // optional bool need_ack = 6 [default = true];
  inline bool has_need_ack() const;
  inline void clear_need_ack();
  static const int kNeedAckFieldNumber = 6;
  inline bool need_ack() const;
  inline void set_need_ack(bool value);

  // optional bool use_bussiness_seq = 7 [default = false];
  inline bool has_use_bussiness_seq() const;
  inline void clear_use_bussiness_seq();
  static const int kUseBussinessSeqFieldNumber = 7;
  inline bool use_bussiness_seq() const;
  inline void set_use_bussiness_seq(bool value);

  // @@protoc_insertion_point(class_scope:proto_conf.tbus_conf)
 private:
  inline void set_has_tbus_addr_prefix_id();
  inline void clear_has_tbus_addr_prefix_id();
  inline void set_has_tbus_addr_prefix_name();
  inline void clear_has_tbus_addr_prefix_name();
  inline void set_has_tbus_id_template();
  inline void clear_has_tbus_id_template();
  inline void set_has_default_tbusd_port();
  inline void clear_has_default_tbusd_port();
  inline void set_has_need_ack();
  inline void clear_has_need_ack();
  inline void set_has_use_bussiness_seq();
  inline void clear_has_use_bussiness_seq();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  static ::std::string* _default_tbus_addr_prefix_name_;
  ::std::string* tbus_addr_prefix_name_;
  ::google::protobuf::int32 tbus_addr_prefix_id_;
  ::google::protobuf::uint32 default_tbusd_port_;
  static ::std::string* _default_tbus_id_template_;
  ::std::string* tbus_id_template_;
  ::google::protobuf::RepeatedPtrField< ::proto_conf::listen_conf > listens_;
  bool need_ack_;
  bool use_bussiness_seq_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fprotocol_5fconf_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fprotocol_5fconf_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fprotocol_5fconf_2eproto();

  void InitAsDefaultInstance();
  static tbus_conf* default_instance_;
};
// -------------------------------------------------------------------

class user_listen_conf : public ::google::protobuf::Message {
 public:
  user_listen_conf();
  virtual ~user_listen_conf();

  user_listen_conf(const user_listen_conf& from);

  inline user_listen_conf& operator=(const user_listen_conf& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const user_listen_conf& default_instance();

  void Swap(user_listen_conf* other);

  // implements Message ----------------------------------------------

  user_listen_conf* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const user_listen_conf& from);
  void MergeFrom(const user_listen_conf& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string ifx = 1;
  inline bool has_ifx() const;
  inline void clear_ifx();
  static const int kIfxFieldNumber = 1;
  inline const ::std::string& ifx() const;
  inline void set_ifx(const ::std::string& value);
  inline void set_ifx(const char* value);
  inline void set_ifx(const char* value, size_t size);
  inline ::std::string* mutable_ifx();
  inline ::std::string* release_ifx();
  inline void set_allocated_ifx(::std::string* ifx);

  // optional uint32 port = 2;
  inline bool has_port() const;
  inline void clear_port();
  static const int kPortFieldNumber = 2;
  inline ::google::protobuf::uint32 port() const;
  inline void set_port(::google::protobuf::uint32 value);

  // optional string plugin_so_path = 3;
  inline bool has_plugin_so_path() const;
  inline void clear_plugin_so_path();
  static const int kPluginSoPathFieldNumber = 3;
  inline const ::std::string& plugin_so_path() const;
  inline void set_plugin_so_path(const ::std::string& value);
  inline void set_plugin_so_path(const char* value);
  inline void set_plugin_so_path(const char* value, size_t size);
  inline ::std::string* mutable_plugin_so_path();
  inline ::std::string* release_plugin_so_path();
  inline void set_allocated_plugin_so_path(::std::string* plugin_so_path);

  // optional string plugin_conf_path = 4;
  inline bool has_plugin_conf_path() const;
  inline void clear_plugin_conf_path();
  static const int kPluginConfPathFieldNumber = 4;
  inline const ::std::string& plugin_conf_path() const;
  inline void set_plugin_conf_path(const ::std::string& value);
  inline void set_plugin_conf_path(const char* value);
  inline void set_plugin_conf_path(const char* value, size_t size);
  inline ::std::string* mutable_plugin_conf_path();
  inline ::std::string* release_plugin_conf_path();
  inline void set_allocated_plugin_conf_path(::std::string* plugin_conf_path);

  // optional bool is_set = 5 [default = true];
  inline bool has_is_set() const;
  inline void clear_is_set();
  static const int kIsSetFieldNumber = 5;
  inline bool is_set() const;
  inline void set_is_set(bool value);

  // @@protoc_insertion_point(class_scope:proto_conf.user_listen_conf)
 private:
  inline void set_has_ifx();
  inline void clear_has_ifx();
  inline void set_has_port();
  inline void clear_has_port();
  inline void set_has_plugin_so_path();
  inline void clear_has_plugin_so_path();
  inline void set_has_plugin_conf_path();
  inline void clear_has_plugin_conf_path();
  inline void set_has_is_set();
  inline void clear_has_is_set();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::std::string* ifx_;
  ::std::string* plugin_so_path_;
  ::google::protobuf::uint32 port_;
  bool is_set_;
  ::std::string* plugin_conf_path_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fprotocol_5fconf_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fprotocol_5fconf_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fprotocol_5fconf_2eproto();

  void InitAsDefaultInstance();
  static user_listen_conf* default_instance_;
};
// -------------------------------------------------------------------

class conn_nostate_conf : public ::google::protobuf::Message {
 public:
  conn_nostate_conf();
  virtual ~conn_nostate_conf();

  conn_nostate_conf(const conn_nostate_conf& from);

  inline conn_nostate_conf& operator=(const conn_nostate_conf& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const conn_nostate_conf& default_instance();

  void Swap(conn_nostate_conf* other);

  // implements Message ----------------------------------------------

  conn_nostate_conf* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const conn_nostate_conf& from);
  void MergeFrom(const conn_nostate_conf& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .proto_conf.user_listen_conf listens = 1;
  inline int listens_size() const;
  inline void clear_listens();
  static const int kListensFieldNumber = 1;
  inline const ::proto_conf::user_listen_conf& listens(int index) const;
  inline ::proto_conf::user_listen_conf* mutable_listens(int index);
  inline ::proto_conf::user_listen_conf* add_listens();
  inline const ::google::protobuf::RepeatedPtrField< ::proto_conf::user_listen_conf >&
      listens() const;
  inline ::google::protobuf::RepeatedPtrField< ::proto_conf::user_listen_conf >*
      mutable_listens();

  // @@protoc_insertion_point(class_scope:proto_conf.conn_nostate_conf)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::proto_conf::user_listen_conf > listens_;
  friend void  protobuf_AddDesc_source_2fbaseagent_2fproto_2fprotocol_5fconf_2eproto();
  friend void protobuf_AssignDesc_source_2fbaseagent_2fproto_2fprotocol_5fconf_2eproto();
  friend void protobuf_ShutdownFile_source_2fbaseagent_2fproto_2fprotocol_5fconf_2eproto();

  void InitAsDefaultInstance();
  static conn_nostate_conf* default_instance_;
};
// ===================================================================


// ===================================================================

// listen_conf

// optional string ifx = 1;
inline bool listen_conf::has_ifx() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void listen_conf::set_has_ifx() {
  _has_bits_[0] |= 0x00000001u;
}
inline void listen_conf::clear_has_ifx() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void listen_conf::clear_ifx() {
  if (ifx_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ifx_->clear();
  }
  clear_has_ifx();
}
inline const ::std::string& listen_conf::ifx() const {
  // @@protoc_insertion_point(field_get:proto_conf.listen_conf.ifx)
  return *ifx_;
}
inline void listen_conf::set_ifx(const ::std::string& value) {
  set_has_ifx();
  if (ifx_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ifx_ = new ::std::string;
  }
  ifx_->assign(value);
  // @@protoc_insertion_point(field_set:proto_conf.listen_conf.ifx)
}
inline void listen_conf::set_ifx(const char* value) {
  set_has_ifx();
  if (ifx_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ifx_ = new ::std::string;
  }
  ifx_->assign(value);
  // @@protoc_insertion_point(field_set_char:proto_conf.listen_conf.ifx)
}
inline void listen_conf::set_ifx(const char* value, size_t size) {
  set_has_ifx();
  if (ifx_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ifx_ = new ::std::string;
  }
  ifx_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto_conf.listen_conf.ifx)
}
inline ::std::string* listen_conf::mutable_ifx() {
  set_has_ifx();
  if (ifx_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ifx_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:proto_conf.listen_conf.ifx)
  return ifx_;
}
inline ::std::string* listen_conf::release_ifx() {
  clear_has_ifx();
  if (ifx_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = ifx_;
    ifx_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void listen_conf::set_allocated_ifx(::std::string* ifx) {
  if (ifx_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete ifx_;
  }
  if (ifx) {
    set_has_ifx();
    ifx_ = ifx;
  } else {
    clear_has_ifx();
    ifx_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:proto_conf.listen_conf.ifx)
}

// optional uint32 port = 2;
inline bool listen_conf::has_port() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void listen_conf::set_has_port() {
  _has_bits_[0] |= 0x00000002u;
}
inline void listen_conf::clear_has_port() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void listen_conf::clear_port() {
  port_ = 0u;
  clear_has_port();
}
inline ::google::protobuf::uint32 listen_conf::port() const {
  // @@protoc_insertion_point(field_get:proto_conf.listen_conf.port)
  return port_;
}
inline void listen_conf::set_port(::google::protobuf::uint32 value) {
  set_has_port();
  port_ = value;
  // @@protoc_insertion_point(field_set:proto_conf.listen_conf.port)
}

// -------------------------------------------------------------------

// agent_conf

// repeated .proto_conf.listen_conf listens = 1;
inline int agent_conf::listens_size() const {
  return listens_.size();
}
inline void agent_conf::clear_listens() {
  listens_.Clear();
}
inline const ::proto_conf::listen_conf& agent_conf::listens(int index) const {
  // @@protoc_insertion_point(field_get:proto_conf.agent_conf.listens)
  return listens_.Get(index);
}
inline ::proto_conf::listen_conf* agent_conf::mutable_listens(int index) {
  // @@protoc_insertion_point(field_mutable:proto_conf.agent_conf.listens)
  return listens_.Mutable(index);
}
inline ::proto_conf::listen_conf* agent_conf::add_listens() {
  // @@protoc_insertion_point(field_add:proto_conf.agent_conf.listens)
  return listens_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::proto_conf::listen_conf >&
agent_conf::listens() const {
  // @@protoc_insertion_point(field_list:proto_conf.agent_conf.listens)
  return listens_;
}
inline ::google::protobuf::RepeatedPtrField< ::proto_conf::listen_conf >*
agent_conf::mutable_listens() {
  // @@protoc_insertion_point(field_mutable_list:proto_conf.agent_conf.listens)
  return &listens_;
}

// optional bool need_ack = 2 [default = true];
inline bool agent_conf::has_need_ack() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void agent_conf::set_has_need_ack() {
  _has_bits_[0] |= 0x00000002u;
}
inline void agent_conf::clear_has_need_ack() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void agent_conf::clear_need_ack() {
  need_ack_ = true;
  clear_has_need_ack();
}
inline bool agent_conf::need_ack() const {
  // @@protoc_insertion_point(field_get:proto_conf.agent_conf.need_ack)
  return need_ack_;
}
inline void agent_conf::set_need_ack(bool value) {
  set_has_need_ack();
  need_ack_ = value;
  // @@protoc_insertion_point(field_set:proto_conf.agent_conf.need_ack)
}

// optional bool use_bussiness_seq = 3 [default = false];
inline bool agent_conf::has_use_bussiness_seq() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void agent_conf::set_has_use_bussiness_seq() {
  _has_bits_[0] |= 0x00000004u;
}
inline void agent_conf::clear_has_use_bussiness_seq() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void agent_conf::clear_use_bussiness_seq() {
  use_bussiness_seq_ = false;
  clear_has_use_bussiness_seq();
}
inline bool agent_conf::use_bussiness_seq() const {
  // @@protoc_insertion_point(field_get:proto_conf.agent_conf.use_bussiness_seq)
  return use_bussiness_seq_;
}
inline void agent_conf::set_use_bussiness_seq(bool value) {
  set_has_use_bussiness_seq();
  use_bussiness_seq_ = value;
  // @@protoc_insertion_point(field_set:proto_conf.agent_conf.use_bussiness_seq)
}

// -------------------------------------------------------------------

// tbus_conf

// optional int32 tbus_addr_prefix_id = 1 [default = 10];
inline bool tbus_conf::has_tbus_addr_prefix_id() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void tbus_conf::set_has_tbus_addr_prefix_id() {
  _has_bits_[0] |= 0x00000001u;
}
inline void tbus_conf::clear_has_tbus_addr_prefix_id() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void tbus_conf::clear_tbus_addr_prefix_id() {
  tbus_addr_prefix_id_ = 10;
  clear_has_tbus_addr_prefix_id();
}
inline ::google::protobuf::int32 tbus_conf::tbus_addr_prefix_id() const {
  // @@protoc_insertion_point(field_get:proto_conf.tbus_conf.tbus_addr_prefix_id)
  return tbus_addr_prefix_id_;
}
inline void tbus_conf::set_tbus_addr_prefix_id(::google::protobuf::int32 value) {
  set_has_tbus_addr_prefix_id();
  tbus_addr_prefix_id_ = value;
  // @@protoc_insertion_point(field_set:proto_conf.tbus_conf.tbus_addr_prefix_id)
}

// optional string tbus_addr_prefix_name = 2 [default = "tbus_addr"];
inline bool tbus_conf::has_tbus_addr_prefix_name() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void tbus_conf::set_has_tbus_addr_prefix_name() {
  _has_bits_[0] |= 0x00000002u;
}
inline void tbus_conf::clear_has_tbus_addr_prefix_name() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void tbus_conf::clear_tbus_addr_prefix_name() {
  if (tbus_addr_prefix_name_ != _default_tbus_addr_prefix_name_) {
    tbus_addr_prefix_name_->assign(*_default_tbus_addr_prefix_name_);
  }
  clear_has_tbus_addr_prefix_name();
}
inline const ::std::string& tbus_conf::tbus_addr_prefix_name() const {
  // @@protoc_insertion_point(field_get:proto_conf.tbus_conf.tbus_addr_prefix_name)
  return *tbus_addr_prefix_name_;
}
inline void tbus_conf::set_tbus_addr_prefix_name(const ::std::string& value) {
  set_has_tbus_addr_prefix_name();
  if (tbus_addr_prefix_name_ == _default_tbus_addr_prefix_name_) {
    tbus_addr_prefix_name_ = new ::std::string;
  }
  tbus_addr_prefix_name_->assign(value);
  // @@protoc_insertion_point(field_set:proto_conf.tbus_conf.tbus_addr_prefix_name)
}
inline void tbus_conf::set_tbus_addr_prefix_name(const char* value) {
  set_has_tbus_addr_prefix_name();
  if (tbus_addr_prefix_name_ == _default_tbus_addr_prefix_name_) {
    tbus_addr_prefix_name_ = new ::std::string;
  }
  tbus_addr_prefix_name_->assign(value);
  // @@protoc_insertion_point(field_set_char:proto_conf.tbus_conf.tbus_addr_prefix_name)
}
inline void tbus_conf::set_tbus_addr_prefix_name(const char* value, size_t size) {
  set_has_tbus_addr_prefix_name();
  if (tbus_addr_prefix_name_ == _default_tbus_addr_prefix_name_) {
    tbus_addr_prefix_name_ = new ::std::string;
  }
  tbus_addr_prefix_name_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto_conf.tbus_conf.tbus_addr_prefix_name)
}
inline ::std::string* tbus_conf::mutable_tbus_addr_prefix_name() {
  set_has_tbus_addr_prefix_name();
  if (tbus_addr_prefix_name_ == _default_tbus_addr_prefix_name_) {
    tbus_addr_prefix_name_ = new ::std::string(*_default_tbus_addr_prefix_name_);
  }
  // @@protoc_insertion_point(field_mutable:proto_conf.tbus_conf.tbus_addr_prefix_name)
  return tbus_addr_prefix_name_;
}
inline ::std::string* tbus_conf::release_tbus_addr_prefix_name() {
  clear_has_tbus_addr_prefix_name();
  if (tbus_addr_prefix_name_ == _default_tbus_addr_prefix_name_) {
    return NULL;
  } else {
    ::std::string* temp = tbus_addr_prefix_name_;
    tbus_addr_prefix_name_ = const_cast< ::std::string*>(_default_tbus_addr_prefix_name_);
    return temp;
  }
}
inline void tbus_conf::set_allocated_tbus_addr_prefix_name(::std::string* tbus_addr_prefix_name) {
  if (tbus_addr_prefix_name_ != _default_tbus_addr_prefix_name_) {
    delete tbus_addr_prefix_name_;
  }
  if (tbus_addr_prefix_name) {
    set_has_tbus_addr_prefix_name();
    tbus_addr_prefix_name_ = tbus_addr_prefix_name;
  } else {
    clear_has_tbus_addr_prefix_name();
    tbus_addr_prefix_name_ = const_cast< ::std::string*>(_default_tbus_addr_prefix_name_);
  }
  // @@protoc_insertion_point(field_set_allocated:proto_conf.tbus_conf.tbus_addr_prefix_name)
}

// optional string tbus_id_template = 3 [default = "8.8.8.8,cluster_.app_.server_,9.8.6"];
inline bool tbus_conf::has_tbus_id_template() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void tbus_conf::set_has_tbus_id_template() {
  _has_bits_[0] |= 0x00000004u;
}
inline void tbus_conf::clear_has_tbus_id_template() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void tbus_conf::clear_tbus_id_template() {
  if (tbus_id_template_ != _default_tbus_id_template_) {
    tbus_id_template_->assign(*_default_tbus_id_template_);
  }
  clear_has_tbus_id_template();
}
inline const ::std::string& tbus_conf::tbus_id_template() const {
  // @@protoc_insertion_point(field_get:proto_conf.tbus_conf.tbus_id_template)
  return *tbus_id_template_;
}
inline void tbus_conf::set_tbus_id_template(const ::std::string& value) {
  set_has_tbus_id_template();
  if (tbus_id_template_ == _default_tbus_id_template_) {
    tbus_id_template_ = new ::std::string;
  }
  tbus_id_template_->assign(value);
  // @@protoc_insertion_point(field_set:proto_conf.tbus_conf.tbus_id_template)
}
inline void tbus_conf::set_tbus_id_template(const char* value) {
  set_has_tbus_id_template();
  if (tbus_id_template_ == _default_tbus_id_template_) {
    tbus_id_template_ = new ::std::string;
  }
  tbus_id_template_->assign(value);
  // @@protoc_insertion_point(field_set_char:proto_conf.tbus_conf.tbus_id_template)
}
inline void tbus_conf::set_tbus_id_template(const char* value, size_t size) {
  set_has_tbus_id_template();
  if (tbus_id_template_ == _default_tbus_id_template_) {
    tbus_id_template_ = new ::std::string;
  }
  tbus_id_template_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto_conf.tbus_conf.tbus_id_template)
}
inline ::std::string* tbus_conf::mutable_tbus_id_template() {
  set_has_tbus_id_template();
  if (tbus_id_template_ == _default_tbus_id_template_) {
    tbus_id_template_ = new ::std::string(*_default_tbus_id_template_);
  }
  // @@protoc_insertion_point(field_mutable:proto_conf.tbus_conf.tbus_id_template)
  return tbus_id_template_;
}
inline ::std::string* tbus_conf::release_tbus_id_template() {
  clear_has_tbus_id_template();
  if (tbus_id_template_ == _default_tbus_id_template_) {
    return NULL;
  } else {
    ::std::string* temp = tbus_id_template_;
    tbus_id_template_ = const_cast< ::std::string*>(_default_tbus_id_template_);
    return temp;
  }
}
inline void tbus_conf::set_allocated_tbus_id_template(::std::string* tbus_id_template) {
  if (tbus_id_template_ != _default_tbus_id_template_) {
    delete tbus_id_template_;
  }
  if (tbus_id_template) {
    set_has_tbus_id_template();
    tbus_id_template_ = tbus_id_template;
  } else {
    clear_has_tbus_id_template();
    tbus_id_template_ = const_cast< ::std::string*>(_default_tbus_id_template_);
  }
  // @@protoc_insertion_point(field_set_allocated:proto_conf.tbus_conf.tbus_id_template)
}

// optional uint32 default_tbusd_port = 4 [default = 1027];
inline bool tbus_conf::has_default_tbusd_port() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void tbus_conf::set_has_default_tbusd_port() {
  _has_bits_[0] |= 0x00000008u;
}
inline void tbus_conf::clear_has_default_tbusd_port() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void tbus_conf::clear_default_tbusd_port() {
  default_tbusd_port_ = 1027u;
  clear_has_default_tbusd_port();
}
inline ::google::protobuf::uint32 tbus_conf::default_tbusd_port() const {
  // @@protoc_insertion_point(field_get:proto_conf.tbus_conf.default_tbusd_port)
  return default_tbusd_port_;
}
inline void tbus_conf::set_default_tbusd_port(::google::protobuf::uint32 value) {
  set_has_default_tbusd_port();
  default_tbusd_port_ = value;
  // @@protoc_insertion_point(field_set:proto_conf.tbus_conf.default_tbusd_port)
}

// repeated .proto_conf.listen_conf listens = 5;
inline int tbus_conf::listens_size() const {
  return listens_.size();
}
inline void tbus_conf::clear_listens() {
  listens_.Clear();
}
inline const ::proto_conf::listen_conf& tbus_conf::listens(int index) const {
  // @@protoc_insertion_point(field_get:proto_conf.tbus_conf.listens)
  return listens_.Get(index);
}
inline ::proto_conf::listen_conf* tbus_conf::mutable_listens(int index) {
  // @@protoc_insertion_point(field_mutable:proto_conf.tbus_conf.listens)
  return listens_.Mutable(index);
}
inline ::proto_conf::listen_conf* tbus_conf::add_listens() {
  // @@protoc_insertion_point(field_add:proto_conf.tbus_conf.listens)
  return listens_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::proto_conf::listen_conf >&
tbus_conf::listens() const {
  // @@protoc_insertion_point(field_list:proto_conf.tbus_conf.listens)
  return listens_;
}
inline ::google::protobuf::RepeatedPtrField< ::proto_conf::listen_conf >*
tbus_conf::mutable_listens() {
  // @@protoc_insertion_point(field_mutable_list:proto_conf.tbus_conf.listens)
  return &listens_;
}

// optional bool need_ack = 6 [default = true];
inline bool tbus_conf::has_need_ack() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void tbus_conf::set_has_need_ack() {
  _has_bits_[0] |= 0x00000020u;
}
inline void tbus_conf::clear_has_need_ack() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void tbus_conf::clear_need_ack() {
  need_ack_ = true;
  clear_has_need_ack();
}
inline bool tbus_conf::need_ack() const {
  // @@protoc_insertion_point(field_get:proto_conf.tbus_conf.need_ack)
  return need_ack_;
}
inline void tbus_conf::set_need_ack(bool value) {
  set_has_need_ack();
  need_ack_ = value;
  // @@protoc_insertion_point(field_set:proto_conf.tbus_conf.need_ack)
}

// optional bool use_bussiness_seq = 7 [default = false];
inline bool tbus_conf::has_use_bussiness_seq() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void tbus_conf::set_has_use_bussiness_seq() {
  _has_bits_[0] |= 0x00000040u;
}
inline void tbus_conf::clear_has_use_bussiness_seq() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void tbus_conf::clear_use_bussiness_seq() {
  use_bussiness_seq_ = false;
  clear_has_use_bussiness_seq();
}
inline bool tbus_conf::use_bussiness_seq() const {
  // @@protoc_insertion_point(field_get:proto_conf.tbus_conf.use_bussiness_seq)
  return use_bussiness_seq_;
}
inline void tbus_conf::set_use_bussiness_seq(bool value) {
  set_has_use_bussiness_seq();
  use_bussiness_seq_ = value;
  // @@protoc_insertion_point(field_set:proto_conf.tbus_conf.use_bussiness_seq)
}

// -------------------------------------------------------------------

// user_listen_conf

// optional string ifx = 1;
inline bool user_listen_conf::has_ifx() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void user_listen_conf::set_has_ifx() {
  _has_bits_[0] |= 0x00000001u;
}
inline void user_listen_conf::clear_has_ifx() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void user_listen_conf::clear_ifx() {
  if (ifx_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ifx_->clear();
  }
  clear_has_ifx();
}
inline const ::std::string& user_listen_conf::ifx() const {
  // @@protoc_insertion_point(field_get:proto_conf.user_listen_conf.ifx)
  return *ifx_;
}
inline void user_listen_conf::set_ifx(const ::std::string& value) {
  set_has_ifx();
  if (ifx_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ifx_ = new ::std::string;
  }
  ifx_->assign(value);
  // @@protoc_insertion_point(field_set:proto_conf.user_listen_conf.ifx)
}
inline void user_listen_conf::set_ifx(const char* value) {
  set_has_ifx();
  if (ifx_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ifx_ = new ::std::string;
  }
  ifx_->assign(value);
  // @@protoc_insertion_point(field_set_char:proto_conf.user_listen_conf.ifx)
}
inline void user_listen_conf::set_ifx(const char* value, size_t size) {
  set_has_ifx();
  if (ifx_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ifx_ = new ::std::string;
  }
  ifx_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto_conf.user_listen_conf.ifx)
}
inline ::std::string* user_listen_conf::mutable_ifx() {
  set_has_ifx();
  if (ifx_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ifx_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:proto_conf.user_listen_conf.ifx)
  return ifx_;
}
inline ::std::string* user_listen_conf::release_ifx() {
  clear_has_ifx();
  if (ifx_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = ifx_;
    ifx_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void user_listen_conf::set_allocated_ifx(::std::string* ifx) {
  if (ifx_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete ifx_;
  }
  if (ifx) {
    set_has_ifx();
    ifx_ = ifx;
  } else {
    clear_has_ifx();
    ifx_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:proto_conf.user_listen_conf.ifx)
}

// optional uint32 port = 2;
inline bool user_listen_conf::has_port() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void user_listen_conf::set_has_port() {
  _has_bits_[0] |= 0x00000002u;
}
inline void user_listen_conf::clear_has_port() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void user_listen_conf::clear_port() {
  port_ = 0u;
  clear_has_port();
}
inline ::google::protobuf::uint32 user_listen_conf::port() const {
  // @@protoc_insertion_point(field_get:proto_conf.user_listen_conf.port)
  return port_;
}
inline void user_listen_conf::set_port(::google::protobuf::uint32 value) {
  set_has_port();
  port_ = value;
  // @@protoc_insertion_point(field_set:proto_conf.user_listen_conf.port)
}

// optional string plugin_so_path = 3;
inline bool user_listen_conf::has_plugin_so_path() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void user_listen_conf::set_has_plugin_so_path() {
  _has_bits_[0] |= 0x00000004u;
}
inline void user_listen_conf::clear_has_plugin_so_path() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void user_listen_conf::clear_plugin_so_path() {
  if (plugin_so_path_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    plugin_so_path_->clear();
  }
  clear_has_plugin_so_path();
}
inline const ::std::string& user_listen_conf::plugin_so_path() const {
  // @@protoc_insertion_point(field_get:proto_conf.user_listen_conf.plugin_so_path)
  return *plugin_so_path_;
}
inline void user_listen_conf::set_plugin_so_path(const ::std::string& value) {
  set_has_plugin_so_path();
  if (plugin_so_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    plugin_so_path_ = new ::std::string;
  }
  plugin_so_path_->assign(value);
  // @@protoc_insertion_point(field_set:proto_conf.user_listen_conf.plugin_so_path)
}
inline void user_listen_conf::set_plugin_so_path(const char* value) {
  set_has_plugin_so_path();
  if (plugin_so_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    plugin_so_path_ = new ::std::string;
  }
  plugin_so_path_->assign(value);
  // @@protoc_insertion_point(field_set_char:proto_conf.user_listen_conf.plugin_so_path)
}
inline void user_listen_conf::set_plugin_so_path(const char* value, size_t size) {
  set_has_plugin_so_path();
  if (plugin_so_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    plugin_so_path_ = new ::std::string;
  }
  plugin_so_path_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto_conf.user_listen_conf.plugin_so_path)
}
inline ::std::string* user_listen_conf::mutable_plugin_so_path() {
  set_has_plugin_so_path();
  if (plugin_so_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    plugin_so_path_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:proto_conf.user_listen_conf.plugin_so_path)
  return plugin_so_path_;
}
inline ::std::string* user_listen_conf::release_plugin_so_path() {
  clear_has_plugin_so_path();
  if (plugin_so_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = plugin_so_path_;
    plugin_so_path_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void user_listen_conf::set_allocated_plugin_so_path(::std::string* plugin_so_path) {
  if (plugin_so_path_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete plugin_so_path_;
  }
  if (plugin_so_path) {
    set_has_plugin_so_path();
    plugin_so_path_ = plugin_so_path;
  } else {
    clear_has_plugin_so_path();
    plugin_so_path_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:proto_conf.user_listen_conf.plugin_so_path)
}

// optional string plugin_conf_path = 4;
inline bool user_listen_conf::has_plugin_conf_path() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void user_listen_conf::set_has_plugin_conf_path() {
  _has_bits_[0] |= 0x00000008u;
}
inline void user_listen_conf::clear_has_plugin_conf_path() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void user_listen_conf::clear_plugin_conf_path() {
  if (plugin_conf_path_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    plugin_conf_path_->clear();
  }
  clear_has_plugin_conf_path();
}
inline const ::std::string& user_listen_conf::plugin_conf_path() const {
  // @@protoc_insertion_point(field_get:proto_conf.user_listen_conf.plugin_conf_path)
  return *plugin_conf_path_;
}
inline void user_listen_conf::set_plugin_conf_path(const ::std::string& value) {
  set_has_plugin_conf_path();
  if (plugin_conf_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    plugin_conf_path_ = new ::std::string;
  }
  plugin_conf_path_->assign(value);
  // @@protoc_insertion_point(field_set:proto_conf.user_listen_conf.plugin_conf_path)
}
inline void user_listen_conf::set_plugin_conf_path(const char* value) {
  set_has_plugin_conf_path();
  if (plugin_conf_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    plugin_conf_path_ = new ::std::string;
  }
  plugin_conf_path_->assign(value);
  // @@protoc_insertion_point(field_set_char:proto_conf.user_listen_conf.plugin_conf_path)
}
inline void user_listen_conf::set_plugin_conf_path(const char* value, size_t size) {
  set_has_plugin_conf_path();
  if (plugin_conf_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    plugin_conf_path_ = new ::std::string;
  }
  plugin_conf_path_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto_conf.user_listen_conf.plugin_conf_path)
}
inline ::std::string* user_listen_conf::mutable_plugin_conf_path() {
  set_has_plugin_conf_path();
  if (plugin_conf_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    plugin_conf_path_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:proto_conf.user_listen_conf.plugin_conf_path)
  return plugin_conf_path_;
}
inline ::std::string* user_listen_conf::release_plugin_conf_path() {
  clear_has_plugin_conf_path();
  if (plugin_conf_path_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = plugin_conf_path_;
    plugin_conf_path_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void user_listen_conf::set_allocated_plugin_conf_path(::std::string* plugin_conf_path) {
  if (plugin_conf_path_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete plugin_conf_path_;
  }
  if (plugin_conf_path) {
    set_has_plugin_conf_path();
    plugin_conf_path_ = plugin_conf_path;
  } else {
    clear_has_plugin_conf_path();
    plugin_conf_path_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:proto_conf.user_listen_conf.plugin_conf_path)
}

// optional bool is_set = 5 [default = true];
inline bool user_listen_conf::has_is_set() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void user_listen_conf::set_has_is_set() {
  _has_bits_[0] |= 0x00000010u;
}
inline void user_listen_conf::clear_has_is_set() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void user_listen_conf::clear_is_set() {
  is_set_ = true;
  clear_has_is_set();
}
inline bool user_listen_conf::is_set() const {
  // @@protoc_insertion_point(field_get:proto_conf.user_listen_conf.is_set)
  return is_set_;
}
inline void user_listen_conf::set_is_set(bool value) {
  set_has_is_set();
  is_set_ = value;
  // @@protoc_insertion_point(field_set:proto_conf.user_listen_conf.is_set)
}

// -------------------------------------------------------------------

// conn_nostate_conf

// repeated .proto_conf.user_listen_conf listens = 1;
inline int conn_nostate_conf::listens_size() const {
  return listens_.size();
}
inline void conn_nostate_conf::clear_listens() {
  listens_.Clear();
}
inline const ::proto_conf::user_listen_conf& conn_nostate_conf::listens(int index) const {
  // @@protoc_insertion_point(field_get:proto_conf.conn_nostate_conf.listens)
  return listens_.Get(index);
}
inline ::proto_conf::user_listen_conf* conn_nostate_conf::mutable_listens(int index) {
  // @@protoc_insertion_point(field_mutable:proto_conf.conn_nostate_conf.listens)
  return listens_.Mutable(index);
}
inline ::proto_conf::user_listen_conf* conn_nostate_conf::add_listens() {
  // @@protoc_insertion_point(field_add:proto_conf.conn_nostate_conf.listens)
  return listens_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::proto_conf::user_listen_conf >&
conn_nostate_conf::listens() const {
  // @@protoc_insertion_point(field_list:proto_conf.conn_nostate_conf.listens)
  return listens_;
}
inline ::google::protobuf::RepeatedPtrField< ::proto_conf::user_listen_conf >*
conn_nostate_conf::mutable_listens() {
  // @@protoc_insertion_point(field_mutable_list:proto_conf.conn_nostate_conf.listens)
  return &listens_;
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace proto_conf

#ifndef SWIG
namespace google {
namespace protobuf {


}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_source_2fbaseagent_2fproto_2fprotocol_5fconf_2eproto__INCLUDED
