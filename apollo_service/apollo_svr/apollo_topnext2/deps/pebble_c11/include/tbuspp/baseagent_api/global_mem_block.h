/*
 * <PERSON><PERSON> is pleased to support the open source community by making Pebble available.
 * Copyright (C) 2016 THL A29 Limited, a Tencent company. All rights reserved.
 * Licensed under the MIT License (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 * http://opensource.org/licenses/MIT
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 *
 */


#ifndef SOURCE_BASEAGENT_BASEAGENT_API_GLOBAL_MEM_BLOCK_H_
#define SOURCE_BASEAGENT_BASEAGENT_API_GLOBAL_MEM_BLOCK_H_
#include "tbuspp/common/lock_help.h"
#include <cstdatomic>
#include <vector>

#pragma pack(8)

namespace baseagent {
class GlobalBlock {
    public:
    GlobalBlock();
    void Init();

    int GetProtocolIpPort(int proto, std::string* ip, unsigned short* port);

    bool IsProtocolListend(int proto, const std::string& ip, unsigned short port);

    int AddProtocolListen(int proto,
        const std::vector<std::pair<std::string, unsigned short> >& listens);

    public:
    share_lock::TraceLock lock_;
    unsigned long long user_key_;
    std::atomic<unsigned long long> msg_seq_;
    char local_name_path_[1024];

    int listens_num_;
    struct {
        char            ip_[24];
        unsigned short  port_;
        int             proto_;
    } listens_[128];
    volatile unsigned int state_;
    unsigned int time_;
};

#pragma pack()
} // namespace baseagent

#endif /* SOURCE_BASEAGENT_BASEAGENT_API_GLOBAL_MEM_BLOCK_H_ */
