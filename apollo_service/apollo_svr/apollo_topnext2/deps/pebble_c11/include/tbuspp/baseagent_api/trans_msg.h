/*
 * <PERSON><PERSON> is pleased to support the open source community by making Pebble available.
 * Copyright (C) 2016 THL A29 Limited, a Tencent company. All rights reserved.
 * Licensed under the MIT License (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 * http://opensource.org/licenses/MIT
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 *
 */


#ifndef SOURCE_BASEAGENT_BASEAGENT_API_TRANS_MSG_H_
#define SOURCE_BASEAGENT_BASEAGENT_API_TRANS_MSG_H_

namespace baseagent {

enum MsgTypeCmd {
    TbusppSingleDst = 0,
    TbusppManyDst = 1,
};

#pragma pack(push, 1)

#define TMSG_PACK_MASK 0xC35AE196U

class tmsg_head
{
public:
    tmsg_head()
        :   mask_(TMSG_PACK_MASK), head_len_(0), body_len_(0), msg_type_(0),
            uid_(0), send_time_mesc_(0), timeout_msec_(0), sys_msg_seq_(0),
            user_respose_seq_(0), queue_handle_(UINT64_MAX), result_(0), trans_info_len_(0)
    {}

    uint32_t mask_;
    uint32_t head_len_;
    uint32_t body_len_;
    uint32_t msg_type_;
    uint64_t uid_;
    uint64_t send_time_mesc_;
    uint64_t timeout_msec_;
    uint64_t sys_msg_seq_;
    uint64_t user_respose_seq_;
    uint64_t queue_handle_;
    uint32_t result_;
    uint32_t trans_info_len_;
    char     trans_info_[0];
};

#pragma pack(pop)

} // namespace baseagent

#endif /* SOURCE_BASEAGENT_BASEAGENT_API_TRANS_MSG_H_ */
