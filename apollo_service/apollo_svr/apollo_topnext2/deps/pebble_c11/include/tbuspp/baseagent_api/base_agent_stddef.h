/*
 * <PERSON><PERSON> is pleased to support the open source community by making Pebble available.
 * Copyright (C) 2016 THL A29 Limited, a Tencent company. All rights reserved.
 * Licensed under the MIT License (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 * http://opensource.org/licenses/MIT
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 *
 */


#ifndef SOURCE_BASEAGENT_BASEAGENT_API_BASE_AGENT_STDDEF_H_
#define SOURCE_BASEAGENT_BASEAGENT_API_BASE_AGENT_STDDEF_H_
#include "tbuspp/tmsg/queue_stddef.h"

namespace baseagent {

#define default_base_agent_conf_path "/data/baseagent/conf/base_agent.conf"
#define AgentBasePort 1024

#define TmsgProtocolFieldCount 9
#define BaseAgentProtocolFieldCount 7
#define TmsgHeadBlankLen 64

#define RegWaitLockTimeOut 10000

enum ErrCode {
    kConfFileNotExist = -5001,
    kConfNameServerError = -5002,
    kConfTmsgError = -5003,
    kConfTrouteError = -5004,
    kParamErr = -5005,
    kPbDataErr = -5006,
    kCreateGlobalMemFaile = -5007,
    kConfPathTooLong = -5008,
    kSyncToNameServerFail = -5009,
    kInitError = -5010,
    kParsePbPacketErr = -5011,
    kFromDbLoadInsanceListFail = -5012,
    kParsePbFileFail = -5013,
    kWratiePbFileFail = -5014,
    kGetIfxIpError = -5015,
    kAgentPortError = -5016,
    kNotFindInstance = -5017,
    kCreateNotifyHandleError = -5018,
    kNotifyHandleError = -5019,
    kInitRouteDataError = -5020,
    IncompleteEnvironment = -5021,
    KMsg_Field_Count_Error = -5022,
    KBlankLenTooLong = -5023,
    kMsgPacketError = -5024,
    kIncompletePackage = -5025,
    kRecvBuffTooShort = -5026,
    kUserMsgNull = -5027,
    kPidError = -5028,
    kInstanceCleanResource = -5029,
    kServerIsExist = -5030,
    kInstanceNotReg = -5031,
    kGlobalLockTimeOut10s = -5032,
};



enum IOEvent {
    kEvent_EnableRead =  4,
    kEvent_EnableWrite = 8,
};

} // namespace baseagent

#endif /* SOURCE_BASEAGENT_BASEAGENT_API_BASE_AGENT_STDDEF_H_ */
