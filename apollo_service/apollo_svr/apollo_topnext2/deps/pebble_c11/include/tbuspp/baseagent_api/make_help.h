/*
 * <PERSON><PERSON> is pleased to support the open source community by making Pebble available.
 * Copyright (C) 2016 THL A29 Limited, a Tencent company. All rights reserved.
 * Licensed under the MIT License (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 * http://opensource.org/licenses/MIT
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 *
 */


#ifndef SOURCE_BASEAGENT_BASEAGENT_API_MAKE_HELP_H_
#define SOURCE_BASEAGENT_BASEAGENT_API_MAKE_HELP_H_
#include "tbuspp/proto/troute.pb.h"
#include "tbuspp/proto/name_server.pb.h"
#include "tbuspp/troute/server_name_address.h"
namespace baseagent {

int MakeNameOrAddressFromFile(const char* path,
                              troute::server_name_and_address* paddress,
                              std::string* perr_info);

int MakeNameOrAddressListFromFile(const char* path,
                                  troute::server_name_and_address_list* plist,
                                  std::string* perr_info);

int MakeNameOrAddressObjFromPb(
        const troute::server_name_and_address& name_address,
        troute::ServerNameAndAddress** ppobj, std::string* perr_info);

int MakeNameOrAddressObjListFromPb(
        const troute::server_name_and_address_list& name_address_list,
        std::vector<troute::ServerNameAndAddress*>* pobjv,
        std::string* perr_info);

int MakeNameOrAddressObjListFromPb(
        const troute::server_name_and_address& name_address,
        std::vector<troute::ServerNameAndAddress*>* pobjv,
        std::string* perr_info);



int MakeAddressObjFromFile(unsigned long long external_instance_id,
                           const char* path,
                           const troute::ServerNameAndAddress** ppobj,
                           std::string* perr_info);

int MakeXDependYList(const troute::server_name_and_address& me,
                     const troute::server_name_and_address_list& depend_list,
                     db_name_server::x_depend_y_rows* prows,
                     std::string *pme_str, std::string* perr_info);

int MakeXDependYListFromFile(const char* me_path, const char* who_list_path,
                             db_name_server::x_depend_y_rows* prows,
                             std::string* pme_str, std::string* perr_info);

int MakeInstanceObjToNameServerInstance(
        const troute::ServerNameAndAddress& instance,
        db_name_server::server_name_instance_row* pdb_instance);

}  // namespace baseagent

#endif /* SOURCE_BASEAGENT_BASEAGENT_API_MAKE_HELP_H_ */
