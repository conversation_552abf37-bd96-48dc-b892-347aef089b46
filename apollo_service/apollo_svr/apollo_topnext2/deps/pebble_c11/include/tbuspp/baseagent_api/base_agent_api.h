/*
 * <PERSON><PERSON> is pleased to support the open source community by making Pebble available.
 * Copyright (C) 2016 THL A29 Limited, a Tencent company. All rights reserved.
 * Licensed under the MIT License (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 * http://opensource.org/licenses/MIT
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 *
 */


#ifndef SOURCE_BASEAGENT_BASEAGENT_API_BASE_AGENT_API_H_
#define SOURCE_BASEAGENT_BASEAGENT_API_BASE_AGENT_API_H_

#include "tbuspp/troute/server_name_address.h"
#include "tbuspp/proto/base_agent.pb.h"
#include "tbuspp/proto/troute.pb.h"
#include "tbuspp/proto/name_server.pb.h"
#include "tbuspp/common/lock_help.h"

namespace baseagent {
// int TBaseAgentClientInit(const char* path, std::string* perr_info);
int TBaseAgentClientInit(const base_agent_conf& conf, std::string* perr_info);
int TBaseAgentCreate(const base_agent_conf& conf, std::string* perr_info);
int TBaseAgentCreate(const char* path, std::string* perr_info);
int TBaseAgentCreate(std::string* perr_info);
int TBaseAgentDestory(
        const std::string& ip, unsigned short port, const base_agent_conf& conf,
        const db_name_server::ip_field2_rows& online_instance_list,
        std::string* perr_info);

int Slow_IDependWho(const troute::server_name_and_address& me_address,
                    const db_name_server::x_depend_y_rows& src_rows,
                    std::string* perr_info);

int Slow_RegisterServerNameInstanceToLocal(const troute::ServerNameAndAddress& instance,
                                           int* pingore_err_count, std::string* perr_info);

int Slow_RegisterServerNameInstanceToDB(const troute::ServerNameAndAddress& instance,
                                        std::string* perr_info);

int Slow_CancelRegistrationInstance(
        const troute::ServerNameAndAddress& instance, int* pingore_err_count,
        std::string* perr_info);

int Slow_ProcessExit(int pid, int* perr_count, std::string* perr_info);
int Slow_LoadCallMeListFromFile(const troute::ServerNameAndAddress& instance,
                                int* pingore_err_count,
                                std::vector<troute::ServerNameAndAddress*>* paddress_v,
                                std::string* perr_info);
int Slow_LoadAllCalledListFromFile(const troute::ServerNameAndAddress& instance,
                                   int* pingore_err_count,
                                   std::vector<troute::ServerNameAndAddress*>* paddress_v,
                                   std::string* perr_info);
int Slow_LoadBrotherListFromFile(const troute::ServerNameAndAddress& instance,
                                 int* pingore_err_count,
                                 std::vector<troute::ServerNameAndAddress*>* paddress_v,
                                 std::string* perr_info);

int Slow_CloseEventNotify(unsigned long long handle, std::string* perr_info);

int Slow_GetAllRegisterInstanceFromShm(std::vector<troute::ServerNameAndAddress*>* paddress_v,
                                       std::string* perr_info);
int Slow_InstanceExitCheck(const troute::ServerNameAndAddress& instance,
                           std::string* perr_info);

int Slow_CleanExpireNotDependResource(const troute::ServerNameAndAddress& instance,
                                      unsigned int expire_time,
                                      unsigned int now,
                                      std::string* perr_info);

int GetInstanceQueueHandel(const troute::ServerNameAndAddress& instance,
                           unsigned long long* pqhandle,
                           std::string* perr_info);
int AddRoute(const troute::ServerNameAndAddress& instance);
int DelRoute(const troute::ServerNameAndAddress& instance);
int DisableRoute(const troute::ServerNameAndAddress& instance);
int ReFreshInstanceQHandle(const troute::ServerNameAndAddress& instance);
bool IsRegisterInstance(const troute::ServerNameAndAddress& instance);
bool InstanceAddrIsLocal(const troute::ServerNameAndAddress& instance);



share_lock::TraceLock* GetBaseAgentGlobalLock();
}  // namespace baseagent

#endif /* SOURCE_BASEAGENT_BASEAGENT_API_BASE_AGENT_API_H_ */
