/*
 * Tencent is pleased to support the open source community by making Pebble available.
 * Copyright (C) 2016 THL A29 Limited, a Tencent company. All rights reserved.
 * Licensed under the MIT License (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 * http://opensource.org/licenses/MIT
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 *
 */


#ifndef NOTIFY_API_H
#define NOTIFY_API_H

#include <cstdint>
#include <string>
#include "tbuspp/troute/server_name_address.h"

namespace notify {

class WatchFilters;

/// @brief 创建通知svr的地址
const troute::ServerNameAndAddress* MakeNotifyServerAddress(const char* cfg = NULL);

/// @brief 创建通知svr的地址
const troute::ServerNameAndAddress* MakeNotifyServerAddress(const std::string& ip, uint16_t port);

/// @brief 目标路由地址上线时注册到通知svr
/// @param target 目标地址
/// @param notify_svr 通知svr的地址
/// @param api_addr 接口调用者的地址，与notify_svr相等时为单向消息，不需要回复
/// @return 0 成功
int32_t RegisterRouteAddr(const troute::ServerNameAndAddress& target,
                          const troute::ServerNameAndAddress& notify_svr,
                          const troute::ServerNameAndAddress& api_addr,
                          int32_t timeout_ms = 3000);

/// @brief 目标路由地址依赖变化时更新到通知svr
/// @param target 目标地址
/// @param notify_svr 通知svr的地址
/// @param api_addr 接口调用者的地址，与notify_svr相等时为单向消息，不需要回复
/// @return 0 成功
int32_t UpdateRouteAddr(const troute::ServerNameAndAddress& target,
                        const troute::ServerNameAndAddress& notify_svr,
                        const troute::ServerNameAndAddress& api_addr,
                        int32_t timeout_ms = 3000);

/// @brief 目标路由地址下线时取消注册到通知svr
/// @param target 目标地址
/// @param notify_svr 通知svr的地址
/// @param api_addr 接口调用者的地址，与notify_svr相等时为单向消息，不需要回复
/// @return 0 成功
int32_t UnRegisterRouteAddr(const troute::ServerNameAndAddress& target,
                            const troute::ServerNameAndAddress& notify_svr,
                            const troute::ServerNameAndAddress& api_addr,
                            int32_t timeout_ms = 3000);

/// @brief 到通知svr将目标地址摘除
/// @param target 目标地址
/// @param level 摘除时判断地址相等的等级，-1时为摘除ip&port
/// @param timestamp 摘除的判定时刻，如果通知在此时刻后有交互则摘除失败
/// @param notify_svr 通知svr的地址
/// @param api_addr 接口调用者的地址，与notify_svr相等时为单向消息，不需要回复
/// @return 0 成功
int32_t PickUpRouteAddr(const troute::ServerNameAndAddress& target,
                        int32_t level,
                        int64_t timestamp,
                        const troute::ServerNameAndAddress& notify_svr,
                        const troute::ServerNameAndAddress& api_addr,
                        int32_t timeout_ms = 3000);

/// @brief 到通知svr将目标主机的地址摘除
/// @param ip 目标主机的IP
/// @param port 目标主机的PORT
/// @param timestamp 摘除的判定时刻，如果通知在此时刻后有交互则摘除失败
/// @param notify_svr 通知svr的地址
/// @param api_addr 接口调用者的地址，与notify_svr相等时为单向消息，不需要回复
/// @return 0 成功
int32_t PickUpHostAddr(const std::string& ip,
                       uint16_t port,
                       int64_t timestamp,
                       const troute::ServerNameAndAddress& notify_svr,
                       const troute::ServerNameAndAddress& api_addr,
                       int32_t timeout_ms = 3000);

/// @brief 通知svr上的路由的状态信息
struct RouteStateInfo
{
    int32_t _state;     ///< _state 路由的状态
    int64_t _c_time;    ///< _c_time 路由条目的实际创建时间
    int64_t _m_time;    ///< _m_time 路由条目状态更新时间
};

/// @brief 到通知svr上查询目标路由地址的状态
/// @param target 目标地址
/// @param notify_svr 通知svr的地址
/// @param api_addr 接口调用者的地址
/// @param route_state 返回的目标路由的状态
/// @return 0 成功
int32_t QueryRouteState(const troute::ServerNameAndAddress& target,
                        const troute::ServerNameAndAddress& notify_svr,
                        const troute::ServerNameAndAddress& api_addr,
                        RouteStateInfo* route_state,
                        int32_t timeout_ms = 3000);

/// @brief 到通知svr注册通知事件监听
/// @param watcher 监听者的地址
/// @param filter 监听的过滤器规则文件
/// @param notify_svr 通知svr的地址
/// @param api_addr 接口调用者的地址，与notify_svr相等时为单向消息，不需要回复
/// @return 0 成功
int32_t WatchNotify(const troute::ServerNameAndAddress& watcher,
                    const std::string& filter,
                    const troute::ServerNameAndAddress& notify_svr,
                    const troute::ServerNameAndAddress& api_addr,
                    int32_t timeout_ms = 3000);

/// @brief 到通知svr注册通知事件监听
/// @param watcher 监听者的地址
/// @param filters 监听的过滤器规则
/// @param notify_svr 通知svr的地址
/// @param api_addr 接口调用者的地址，与notify_svr相等时为单向消息，不需要回复
/// @return 0 成功
int32_t WatchNotify(const troute::ServerNameAndAddress& watcher,
                    WatchFilters* filters,
                    const troute::ServerNameAndAddress& notify_svr,
                    const troute::ServerNameAndAddress& api_addr,
                    int32_t timeout_ms = 3000);

/// @brief 到通知svr取消注册通知事件监听
/// @param watcher 监听者的地址
/// @param notify_svr 通知svr的地址
/// @param api_addr 接口调用者的地址，与notify_svr相等时为单向消息，不需要回复
/// @return 0 成功
int32_t UnWatchNotify(const troute::ServerNameAndAddress& watcher,
                      const troute::ServerNameAndAddress& notify_svr,
                      const troute::ServerNameAndAddress& api_addr,
                      int32_t timeout_ms = 3000);

const char* GetLastErrInfo();

} // namespace notify

#endif // NOTIFY_API_H
