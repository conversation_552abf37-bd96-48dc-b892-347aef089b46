/*
 * <PERSON><PERSON> is pleased to support the open source community by making Pebble available.
 * Copyright (C) 2016 THL A29 Limited, a Tencent company. All rights reserved.
 * Licensed under the MIT License (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 * http://opensource.org/licenses/MIT
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 *
 */


#ifndef SOURCE_BASEAGENT_COMMON_BUILD_VERSION_H_
#define SOURCE_BASEAGENT_COMMON_BUILD_VERSION_H_


namespace baseagent_comm {

void SetBuildVersionPtr(unsigned int* pbase);
unsigned int BuildVersion();

} // namespace baseagent_comm


#endif /* SOURCE_BASEAGENT_COMMON_BUILD_VERSION_H_ */
