/*
 * Tencent is pleased to support the open source community by making Pebble available.
 * Copyright (C) 2016 THL A29 Limited, a Tencent company. All rights reserved.
 * Licensed under the MIT License (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 * http://opensource.org/licenses/MIT
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 *
 */


#ifndef XDEBUG_HELP_H_
#define XDEBUG_HELP_H_
#include <string>
#include <stdio.h>

#define STRING_APPEND_FORMAT(var, size, format, args...) {   char debugbuff[(size)]; snprintf(debugbuff, (size), (format), ##args); (var).append(debugbuff); } // NOLINT
#define STRINGPTR_APPEND_FORMAT(var, size, format, args...) { if ((var)) {  char debugbuff[(size)]; snprintf(debugbuff, (size), (format), ##args); (var)->append(debugbuff); } } // NOLINT
#define STRING_FORMAT(var, size, format, args...) {   char debugbuff[(size)]; snprintf(debugbuff, (size), (format), ##args); var = debugbuff; } // NOLINT
#define PRINT_STRING_FUNCTION_LINE(a, b, c) printf("%s,%d: %s--->%s\n", b, c, #a, (a).c_str())
#define PRINT_STRING(var) PRINT_STRING_FUNCTION_LINE((var), __FUNCTION__, __LINE__)
#define TRUACE_LINE() printf("%s,%d\n", __FUNCTION__, __LINE__)
#define TRACE_LINE() printf("%s,%d\n", __FUNCTION__, __LINE__)
#endif
