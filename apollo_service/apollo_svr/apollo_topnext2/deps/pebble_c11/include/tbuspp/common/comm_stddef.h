/*
 * Tencent is pleased to support the open source community by making Pebble available.
 * Copyright (C) 2016 THL A29 Limited, a Tencent company. All rights reserved.
 * Licensed under the MIT License (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 * http://opensource.org/licenses/MIT
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 *
 */


#ifndef SOURCE_BASEAGENT_COMMON_COMM_STDDEF_H_
#define SOURCE_BASEAGENT_COMMON_COMM_STDDEF_H_

namespace baseagent_comm {

enum CommErrCode {
    kSUCC = 0,
    kPoolFreeAddressError = -2000,
    kBlockIdError = -2001,
    kBlockDoubleFree = -2002,
    kBucketIdErr = -2003,
    kKeyIdErr = -2004,
    kParamErr = -2005,
    kKeyExist = -2006,
    kNotNode = -2007,
    kKeyNotExist = -2008,
    kNodeIdNotExist = -2017,
    kNodeVsersionDiff = -2018,
    kNodeIdNotValid = -2031,
    kNodeIdDouble = -2032,
    kNodeIdMiss = -2032,
    kBlockIdDouble = -2013,
    kBlockIdMiss = -2014,
    kBusyCountErr = -2015,
    kKeyDiff = -2040,
    kSetDbConnOptionErr = -2041,
    kConnDbErr = -2042,
    kDropDbError = -2043,
    kCreateDbError = -2044,
    kExcuteSqlError = -2045,
    kCreateTableError = -2046,
};

#define ErrCodeToDebugStr(a, errstr) { (errstr) = "code:--->"#a""; }
#define ErrCodeToDebugStrPtr(a, errstr) { if ((errstr)) *(errstr) = "code:--->"#a""; }

#define NullPtrRetrunParamErr(ptr, r) { if (!(ptr)) \
                                        return (r); }

#define NullPtrRetrunParamErrInfo(ptr, r, err_info_ptr) { if (!(ptr)) {\
                                        ErrCodeToDebugStrPtr((r), (err_info_ptr)); \
                                        return (r); }}

#define NotZeroRetrunParamErrInfo(in, r, err_info_ptr) { if ((in)) {\
                                        ErrCodeToDebugStrPtr((r), (err_info_ptr)); \
                                        return (r); }}

#define FalseRetrunParamErrInfo(in, r, err_info_ptr) { if (!(in)) {\
                                        ErrCodeToDebugStrPtr((r), (err_info_ptr)); \
                                        return (r); }}

#define LessThanValueReturnError(a, v, r, errstr )  {\
    if ((a) < (v))  {   (errstr) = #a"<"#v"--->"#r;\
        return r; } }

#define MoreThanValueReturnError(a, v, r, errstr )  {\
    if ((a) > (v))  {   (errstr) = #a">"#v"--->"#r;\
        return r; } }
#define NotRangleReturnError(a, min, max, r, errstr) {\
    LessThanValueReturnError(a, min, r, errstr);\
    MoreThanValueReturnError(a, max, r, errstr); }
#define NotRangleReturnErrorPtr(a, min, max, r, errstr) {\
    if ((errstr)) {  \
        NotRangleReturnError((a), (min), (max), (r), (*errstr)); \
    } else { \
        std::string debug; \
        NotRangleReturnError((a), (min), (max), (r), debug); \
    }; }

#define NotRangleReturnErrorNotInfo(a, min, max, r) {\
        std::string debug; \
        NotRangleReturnError((a), (min), (max), (r), debug); \
    }

#define MoreThanValueReturnErrorPtr(a, v, r, errstr )  {\
    if ((errstr)) {  \
        MoreThanValueReturnError((a), (v), (r), (*errstr)); \
    } else { \
        std::string debug; \
        MoreThanValueReturnError((a), (v), (r), debug); \
    }; }

#define MoreThanValueReturnErrorNotInfo(a, v, r) { \
        std::string debug; \
        MoreThanValueReturnError((a), (v), (r), debug); \
    }

#define LessThanValueReturnErrorPtr(a, v, r, errstr )  {\
    if ((errstr)) {  \
        LessThanValueReturnError((a), (v), (r), (*errstr)); \
    } else { \
        std::string debug; \
        LessThanValueReturnError((a), (v), (r), debug); \
    }; }

#define LessThanValueReturnNotInfo(a, v, r)  { \
        std::string debug; \
        LessThanValueReturnError((a), (v), (r), debug); \
    }

#define NullPtrRetrunParamErrPtr(ptr, r, errstr) {\
    if (!(ptr)) {\
        if ((errstr)) { *(errstr) = "code: kParamErr"; } \
        return (r); \
    }; }

#define EqValueReturnError(a, b, r, errstr)  {\
    if ((a) == (b)) {  \
        (errstr) = #a"=="#b"--->"#r; \
        return r; \
    }; }

#define StringIsNullRetrunParamErr(str, r, err_info_ptr) { if ((str).empty()) {\
                                           ErrCodeToDebugStrPtr((r), (err_info_ptr)) \
                                        return (r); }}

} // namespace baseagent_comm



#endif /* SOURCE_BASEAGENT_COMMON_COMM_STDDEF_H_ */
