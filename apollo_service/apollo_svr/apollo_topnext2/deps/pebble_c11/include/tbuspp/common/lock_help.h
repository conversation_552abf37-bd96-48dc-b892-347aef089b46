/*
 * <PERSON><PERSON> is pleased to support the open source community by making Pebble available.
 * Copyright (C) 2016 THL A29 Limited, a Tencent company. All rights reserved.
 * Licensed under the MIT License (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 * http://opensource.org/licenses/MIT
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 *
 */


#ifndef LOCK_HELP_H_
#define LOCK_HELP_H_
#include <string.h>
#include <string>
#include <stdio.h>
#include <pthread.h>

namespace share_lock {

#pragma pack(8)
class TraceLock {
    public:
    TraceLock();
    ~TraceLock();
    void Init();
    void Lock();
    int timeout_lock(int msec);
    void Unlock();

    public:
    pthread_mutex_t lock_;
    pthread_mutexattr_t mtx_mattr_;
    unsigned int time_;
    unsigned int pid_;
};
#pragma pack()

class Lock {
    public:
    explicit Lock(pthread_mutex_t* pmutex);
    ~Lock();
    void manual_lock();
    void set_lock(pthread_mutex_t *pmutex);
    int lock_state();
    protected:
    pthread_mutex_t *pmutex_;
    int result_;
};

class LifeTimeLock {
    public:
    explicit LifeTimeLock(TraceLock* pmutex);
    ~LifeTimeLock();
    void manual_lock();
    void set_lock(TraceLock *pmutex);
    int timeout_lock(int msec);
    protected:
    TraceLock *pmutex_;
};

void my_clean_up(void* pparm);

void timeout_to_timespec(int msec, struct timespec* ptimeout);

} // namespace share_lock

#endif
