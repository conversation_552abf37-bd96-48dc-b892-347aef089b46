#include "topnext_rank_mgr.h"
#include "topnext_app.h"
#include "topnext_image_coordinator.h"

namespace topnext_app {

TopNextRankMgr TopNextRankMgr::instance_;

class RankInstanceHasher : public shtable_util::IHashFunctor<RankInstance> {
 public:
  inline unsigned int GetCode(const RankInstance& node) const {
    return node.rank_key.business_id ^ node.rank_key.world_id ^ node.rank_key.zone_id ^ node.rank_key.type ^
           node.rank_key.instance_id;
  }

  inline int Compare(const RankInstance& left, const RankInstance& right) const {
    return (left.rank_key.zone_id == right.rank_key.zone_id && left.rank_key.type == right.rank_key.type &&
            left.rank_key.world_id == right.rank_key.world_id &&
            left.rank_key.instance_id == right.rank_key.instance_id &&
            left.rank_key.business_id == right.rank_key.business_id)
               ? 0
               : -1;
  }
};

static RankInstanceHasher g_rank_instance_hasher;

TopNextRankMgr::TopNextRankMgr() { current_tick_pos_ = -1; }

int TopNextRankMgr::Init(TOPNEXTCFG* cfg) {
  int ret = InitDataDir();
  if (0 != ret) {
    TOPNEXT_LOG_ERROR("init main rank shm manager fail, ret:%d", ret);
    return -1;
  }

  // 初始化全局镜像协调器
  ret = TopNextImageCoordinator::Instance().Init(g_tlogcat);
  if (0 != ret) {
    TOPNEXT_LOG_ERROR("init image coordinator fail, ret:%d", ret);
    return -1;
  }
  TOPNEXT_LOG_INFO("init image coordinator success");

  // 创建榜单映射表
  ret = rank_instance_ht_.Create(TOPNEXT_MAX_RANK_NUM * 2, TOPNEXT_MAX_RANK_NUM, &g_rank_instance_hasher);
  if (0 != ret) {
    TOPNEXT_LOG_ERROR("create rank instance hashtable failed, ret:%d", ret);
    return -1;
  }
  // 加载所有配置的榜单
  for (uint32_t i = 0; i < cfg->rank_cfg_num; ++i) {
    const RANKCFG& rank_cfg = cfg->rank_cfg[i];
    TypeKey type_key;
    type_key.business_id = rank_cfg.business_id;
    type_key.type = rank_cfg.type;
    topnext_cfg::TYPECFG type_cfg;
    ret = GetTypeCfg(cfg, type_key, type_cfg);
    if (0 != ret) {
      TOPNEXT_LOG_INFO(
          "get type cfg fail,"
          "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u",
          rank_cfg.business_id, rank_cfg.world_id, rank_cfg.zone_id, rank_cfg.type, rank_cfg.instance_id);
      return -1;
    }
    ret = AddRankInstance(cfg, rank_cfg, type_cfg);
    if (0 != ret) {
      TOPNEXT_LOG_ERROR(
          "init rank instance succ,"
          "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u, "
          "ret:%d",
          rank_cfg.business_id, rank_cfg.world_id, rank_cfg.zone_id, rank_cfg.type, rank_cfg.instance_id, ret);
      return -1;
    }
    TOPNEXT_LOG_INFO(
        "init rank instance succ,"
        "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u",
        rank_cfg.business_id, rank_cfg.world_id, rank_cfg.zone_id, rank_cfg.type, rank_cfg.instance_id);
  }
  return 0;
}

int TopNextRankMgr::Reload(TOPNEXTCFG* cfg) {
  int ret = 0;
  for (uint32_t i = 0; i < cfg->rank_cfg_num; ++i) {
    RankKey rank_key;
    const RANKCFG& rank_cfg = cfg->rank_cfg[i];
    CastRankCfg2RankKey(rank_cfg, rank_key);
    TypeKey type_key;
    type_key.business_id = rank_cfg.business_id;
    type_key.type = rank_cfg.type;
    topnext_cfg::TYPECFG type_cfg;
    ret = GetTypeCfg(cfg, type_key, type_cfg);
    if (0 != ret) {
      TOPNEXT_LOG_INFO(
          "get type cfg fail,"
          "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u",
          rank_cfg.business_id, rank_cfg.world_id, rank_cfg.zone_id, rank_cfg.type, rank_cfg.instance_id);
      return -1;
    }
    TopNextRankInstance* rank_instance = GetRankInstance(rank_key);
    if (nullptr == rank_instance) {
      // 新增的榜单
      ret = AddRankInstance(cfg, rank_cfg, type_cfg);
      if (0 != ret) {
        TOPNEXT_LOG_ERROR(
            "init rank instance fail,"
            "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u, "
            "ret:%d",
            rank_cfg.business_id, rank_cfg.world_id, rank_cfg.zone_id, rank_cfg.type, rank_cfg.instance_id, ret);
        return -1;
      }
      TOPNEXT_LOG_INFO(
          "init rank instance succ,"
          "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u",
          rank_cfg.business_id, rank_cfg.world_id, rank_cfg.zone_id, rank_cfg.type, rank_cfg.instance_id);
    } else {
      // 完全一样的榜单
      if (rank_instance->GetRankKey().id == rank_cfg.id) {
        // 重载当前榜单
        ret = rank_instance->Reload(type_cfg);
        if (0 != ret) {
          TOPNEXT_LOG_ERROR("reload rank fail, ret:%d", ret);
          return -1;
        }
      } else {
        // 内部ID变化了，需要重建
        // 需要删除之前的榜单再添加新榜单
        ret = DeleteRankInstance(rank_key);
        if (0 != ret) {
          TOPNEXT_LOG_ERROR(
              "delete rank instance fail,"
              "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u, "
              "id:%u",
              rank_cfg.business_id, rank_cfg.world_id, rank_cfg.zone_id, rank_cfg.type, rank_cfg.instance_id,
              rank_cfg.id);
          return -1;
        }
        TOPNEXT_LOG_INFO(
            "delete rank instance succ,"
            "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u, "
            "id:%u",
            rank_cfg.business_id, rank_cfg.world_id, rank_cfg.zone_id, rank_cfg.type, rank_cfg.instance_id,
            rank_cfg.id);
        ret = AddRankInstance(cfg, rank_cfg, type_cfg);
        if (0 != ret) {
          TOPNEXT_LOG_INFO(
              "add rank instance succ,"
              "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u, "
              "id:%u",
              rank_cfg.business_id, rank_cfg.world_id, rank_cfg.zone_id, rank_cfg.type, rank_cfg.instance_id,
              rank_cfg.id);
          return -1;
        }
        TOPNEXT_LOG_INFO(
            "add rank instance succ,"
            "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u, "
            "id:%u",
            rank_cfg.business_id, rank_cfg.world_id, rank_cfg.zone_id, rank_cfg.type, rank_cfg.instance_id,
            rank_cfg.id);
      }
    }
  }
  // 删除不再使用的榜单,为安全起见，目前不删除共享内存等
  int pos = rank_instance_ht_.head();
  while (pos >= 0) {
    RankInstance* rank_instance = rank_instance_ht_.GetData(pos);
    bool found = false;
    for (uint32_t i = 0; i < cfg->rank_cfg_num; ++i) {
      RankKey rank_key;
      const RANKCFG& rank_cfg = cfg->rank_cfg[i];
      CastRankCfg2RankKey(rank_cfg, rank_key);
      if (rank_instance->rank_key.business_id == rank_key.business_id &&
          rank_instance->rank_key.world_id == rank_key.world_id &&
          rank_instance->rank_key.zone_id == rank_key.zone_id && rank_instance->rank_key.type == rank_key.type &&
          rank_instance->rank_key.instance_id == rank_key.instance_id) {
        found = true;
        break;
      }
    }
    if (!found) {
      pos = rank_instance_ht_.next(pos);
      const RankKey& rank_key = rank_instance->rank_key;
      ret = DeleteRankInstance(rank_key);
      if (0 != ret) {
        TOPNEXT_LOG_ERROR(
            "delete rank instance fail,"
            "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u, "
            "id:%u",
            rank_key.business_id, rank_key.world_id, rank_key.zone_id, rank_key.type, rank_key.instance_id,
            rank_key.id);
        return -1;
      }
      TOPNEXT_LOG_INFO(
          "delete rank instance succ,"
          "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u, id:%u",
          rank_key.business_id, rank_key.world_id, rank_key.zone_id, rank_key.type, rank_key.instance_id, rank_key.id);
    } else {
      pos = rank_instance_ht_.next(pos);
    }
  }
  return 0;
}

int TopNextRankMgr::InitDataDir() {
  std::string data_dir = TopNextApp::Instance()->DataDir();
  if ("" == data_dir) {
    TOPNEXT_LOG_ERROR("init main rank shm manager fail, data dir is empty");
    return -1;
  }
  int ret = tos_mkdir_fast(const_cast<char*>(data_dir.c_str()));
  if (0 != ret) {
    TOPNEXT_LOG_ERROR("fail to create directory[%s] for data file", data_dir.c_str());
    return -1;
  }
  return 0;
}

int TopNextRankMgr::AddRankInstance(topnext_cfg::TOPNEXTCFG* cfg, const topnext_cfg::RANKCFG& rank_cfg,
                                    const topnext_cfg::TYPECFG& type_cfg) {
  auto inst = new TopNextRankInstance();
  // 这里主要是希望默认榜单共享统一的共享内存记录文件
  int ret = inst->Init(rank_cfg, type_cfg);
  if (0 != ret) {
    TOPNEXT_LOG_ERROR(
        "init rank instance fail, "
        "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u, ret:%d",
        rank_cfg.business_id, rank_cfg.world_id, rank_cfg.zone_id, rank_cfg.type, rank_cfg.instance_id, ret);
    delete inst;
    return -1;
  }
  RankInstance rank_instance;
  CastRankCfg2RankKey(rank_cfg, rank_instance.rank_key);
  rank_instance.rank_instance = inst;
  // 添加到榜单hashtable
  int pos = rank_instance_ht_.Insert(rank_instance, nullptr);
  if (pos < 0) {
    TOPNEXT_LOG_ERROR(
        "insert rank instance ht fail,"
        "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u, pos:%d",
        rank_cfg.business_id, rank_cfg.world_id, rank_cfg.zone_id, rank_cfg.type, rank_cfg.instance_id, pos);
    return -1;
  }
  if (nullptr != TopNextApp::Instance()->RankCumu()) {
    TopNextApp::Instance()->RankCumu()->Insert(rank_instance.rank_key, IndexableConstructType(rank_instance.rank_key));
    TopNextApp::Instance()->Cumu()->rank_cumu_info_num++;
  }
  return 0;
}

int TopNextRankMgr::DeleteRankInstance(const RankKey& rank_key) {
  RankInstance* p_rank_instance = nullptr;
  RankInstance rank_instance;
  rank_instance.rank_key = rank_key;
  rank_instance.rank_instance = nullptr;
  int pos = rank_instance_ht_.Find(rank_instance, &p_rank_instance);
  if (pos < 0) {
    return 0;
  }
  if (nullptr == p_rank_instance) {
    return -1;
  }
  if (nullptr == p_rank_instance->rank_instance) {
    return -2;
  }
  p_rank_instance->rank_instance->Fini();
  delete p_rank_instance->rank_instance;
  if (nullptr != TopNextApp::Instance()->RankCumu()) {
    TopNextApp::Instance()->RankCumu()->Remove(rank_key);
    TopNextApp::Instance()->Cumu()->rank_cumu_info_num--;
  }
  return rank_instance_ht_.Remove(rank_instance);
}

TopNextRankInstance* TopNextRankMgr::GetRankInstance(const RankKey& rank_key) {
  RankInstance* p_rank_instance = nullptr;
  RankInstance rank_instance;
  rank_instance.rank_key = rank_key;
  rank_instance.rank_instance = nullptr;
  int pos = rank_instance_ht_.Find(rank_instance, &p_rank_instance);
  if (pos < 0) {
    return nullptr;
  }
  if (nullptr == p_rank_instance) {
    return nullptr;
  }
  return p_rank_instance->rank_instance;
}

uint32_t TopNextRankMgr::GetShmKeySum() {
  uint32_t shm_group_sum = 0;

  int current_pos = rank_instance_ht_.head();
  if (current_pos < 0) {
    TOPNEXT_LOG_DEBUG("current_pos < 0");
    return 0;
  }

  while (current_pos >= 0) {
    const RankInstance* rank_instance = rank_instance_ht_.GetData(current_pos);
    if (nullptr == rank_instance || nullptr == rank_instance->rank_instance) {
      TOPNEXT_LOG_INFO("rank_instance is null");
      return 0;
    }

    size_t size = rank_instance->rank_instance->GetShmGroupSize();
    shm_group_sum += size;

    current_pos = rank_instance_ht_.next(current_pos);
  }

  return shm_group_sum;
}

void TopNextRankMgr::OnTickTimer() {
  // 首先更新全局镜像协调器
  TopNextImageCoordinator::Instance().Update();

  if (current_tick_pos_ < 0) {
    current_tick_pos_ = rank_instance_ht_.head();
  }

  if (current_tick_pos_ < 0) {
    return;
  }

  const RankInstance* rank_instance = rank_instance_ht_.GetData(current_tick_pos_);
  if (nullptr == rank_instance || nullptr == rank_instance->rank_instance) {
    // 重置
    current_tick_pos_ = rank_instance_ht_.head();
    return;
  }

  // 先调用tick定时器
  rank_instance->rank_instance->OnTickTimer();
  if (!rank_instance->rank_instance->HasDoneGenerateImageAndReduce()) {
    return;
  }
  // 继续下一个
  current_tick_pos_ = rank_instance_ht_.next(current_tick_pos_);
}

}  // namespace topnext_app
