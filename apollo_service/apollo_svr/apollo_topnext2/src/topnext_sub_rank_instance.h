#ifndef TOPNEXT_SUB_RANK_INSTANCE_H_
#define TOPNEXT_SUB_RANK_INSTANCE_H_

#include "apollo_service_error_code.h"
#include "kernel_data_type.h"
#include "shm_util.h"
#include "shtable_skiplist.h"
#include "topnext_common.h"
#include "topnext_define.h"
#include "topnext_log.h"
#include "topnext_reduce.h"

namespace topnext_app {
using namespace apollo_service_api;

class TopNextUserInfoHasher;

typedef struct {
  const UserInfo* user_info;
  uint32_t rank_no;
  uint32_t total_count;
} UserRankInfo;

class TopNextSubRankInstance {
 public:
  TopNextSubRankInstance();
  ~TopNextSubRankInstance();

 public:
  // 主榜单(无子榜的情况)
  int Init(void* data, size_t len, const RankKey& rank_key, topnext_cfg::TYPECFG& type_cfg, bool is_image);
  int Attach(void* data, size_t len, const RankKey& rank_key, TYPECFG& type_cfg, bool is_image);

  // 子榜单
  int Init(void* data, size_t len, const RankKey& rank_key, const SubRankKey& sub_rank_key,
           topnext_cfg::TYPECFG& type_cfg, topnext_cfg::SUBTYPECFG& sub_type_cfg, bool is_image);
  int Attach(void* data, size_t len, const RankKey& rank_key, const SubRankKey& sub_rank_key,
             topnext_cfg::TYPECFG& type_cfg, topnext_cfg::SUBTYPECFG& sub_type_cfg, bool is_image);

  uint32_t GetSubRankTypeCfgN() const {
    SubTypeCfg subtype_cfg;
    GetSubTypeCfg(type_cfg_, sub_rank_key_.type, subtype_cfg);
    return subtype_cfg.n;
  }

  int Reload(const topnext_cfg::TYPECFG& type_cfg);
  int Fini();

  int Update(const UserInfo& user_info, int* rank_pos = NULL);
  int Replace(const UserInfo& user_info);
  int Remove(const char* open_id);
  int Reset();
  int QueryTop(uint32_t from, uint32_t count, std::vector<UserRankInfo>& user_rank_info);
  int QueryOne(const char* open_id, UserRankInfo& user_rank_info);
  int GenerateImage(TopNextSubRankInstance* image, bool by_request);
  int Copy(TopNextSubRankInstance* other);
  int Reduce();
  void UpdateState(RANK_DATA_STATE state);
  RANK_DATA_STATE GetState();

  shm_util::ShmUtil* Shm() { return &subrank_shm_; }

  size_t Used();
  size_t Total() { return skiplist_.Total(); };
  static size_t GetSize(uint32_t n);

  inline bool IsFull() { return skiplist_.Used() == skiplist_.Total(); };

  void SetLastImageTime(uint64_t time_ms) { rank_data_->head.last_image_timestamp = time_ms; };
  void SetLastReduceTime(uint64_t time_ms) { rank_data_->head.last_reduce_timestamp = time_ms; };

  void SetLastCleanTime(uint64_t time_ms);
  uint64_t GetLastCleanTime();
  uint64_t GetLastImageTime();
  uint64_t GetLastReduceTime();

 private:
  int DoInit();
  int DoAttach();
  int InitHeader();
  void UpdateImageAndReduceHeader();
  int CheckHeader();
  int InitSortMethodInfo();
  int InitReduceFunctor();

  friend class TopNextRankInstance;
  friend class TopNextGradeReduceFunctor;
  friend class TopNextNBReduceFunctor;
  friend class TopNextLGameReduceFunctor;

  RankKey rank_key_;
  SubRankKey sub_rank_key_;
  topnext_cfg::TYPECFG type_cfg_;
  topnext_cfg::SUBTYPECFG sub_type_cfg_;
  SortMethodInfo sort_method_info_;
  bool is_subrank_;
  bool is_image_;

  shtable_util::ShtableSkipList<topnext_app::UserInfo> skiplist_;
  int min_user_object_pos_;
  TopNextUserInfoHasher* rank_hasher_;
  TopNextReduceFunctor* reduce_functor_;
  RankData* rank_data_;
  shm_util::ShmUtil subrank_shm_;
  void* data_;
  size_t len_;
  uint32_t reduce_min_score_;
};

}  // namespace topnext_app
#endif
