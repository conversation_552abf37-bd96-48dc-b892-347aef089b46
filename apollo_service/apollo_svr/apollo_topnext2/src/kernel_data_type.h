#ifndef TOPNEXT_DATA_TYPE_H_
#define TOPNEXT_DATA_TYPE_H_

#include "topnext_conf_desc.h"

using namespace topnext_cfg;

namespace topnext_app {

#define TOPNEXT_OPENID_LEN 128
#define TOPNEXT_MAX_EXTEND_SIZE 1024

// 默认子榜类型
#define TOPNEXT_DEFAULT_SUB_RANK_TYPE 0
// 默认子榜ID
#define TOPNEXT_DEFAULT_SUB_RANK_INSTANCE_ID 0

enum REQ_DATA_SOURCE {
  REQ_DATA_SOURCE_REAL = 0,
  REQ_DATA_SOURCE_IMAGE = 1,
};

enum REQ_CLIENT {
  REQ_CLIENT_FROM_API = 0,
  REQ_CLIENT_FROM_INNER = 1,
};

enum {
  MAX_OPENID_LEN = 128,
  MAX_ADDR_LEN = 64,
  MAX_RANK_INFO_COUNT = 100,
  MAX_SUB_RANK_INFO_COUNT = 8,
  MAX_EXT_DATA_LEN = 1024,
  MAX_CRON_LEN = 256,
  MAX_ARGS_LEN = 256,
  MAX_RET_MSG_LEN = 128,
};

// 用户信息
struct UserInfo {
  char open_id[TOPNEXT_OPENID_LEN];
  uint32_t score;
  // TODO待确定时间戳使用us还是ms
  uint64_t last_report_timestamp;  // 用户记录最后上报时间戳(us级别)
  uint64_t timestamp;  // 参数排序的时间戳(us级别)（业务选择最大值更新方式的情况下，可以选择是否更新timestamp）
  // 排序：参与排序的字段，默认值0
  uint32_t sort_field1;
  uint32_t sort_field2;
  uint32_t sort_field3;
  uint32_t sort_field4;
  uint32_t sort_field5;

  // 该字段已被使用，用于传递衰减时间
  uint64_t reduce_timestamp;

  uint64_t ext_field2;  // 王者v63版本中已经使用，用于存储额外的战力
  uint64_t ext_field3;

  uint32_t reduce_field1;  // 已使用，衰减系数1
  uint32_t reduce_field2;  // 已使用，衰减系数2
  uint32_t reduce_field3;

  uint32_t reserved1;  // 保留字段，初始化成0
  uint32_t reserved2;  // 保留字段，初始化成0
  uint32_t reserved3;  // 保留字段，初始化成0
  uint32_t reduce_round;
  uint32_t ext_data_len;
  uint8_t ext_data_info[TOPNEXT_MAX_EXTEND_SIZE];
};

// 定时周期（镜像和衰减）
typedef struct {
  char dayofweek[7]; /* 0-6, beginning sunday */
  char month[12];    /* 0-11 */
  char hour[24];     /* 0-23 */
  char day[32];      /* 1-31 */
  char minute[60];   /* 0-59 */
} CronPeriodInfo;

typedef struct {
  uint32_t business_id;
  uint32_t type;
} TypeKey;

// 榜单类型:子榜单配置
typedef struct {
  uint32_t type;  // 类型
  uint32_t max_instance_count;
  uint32_t n;
  uint32_t use_image;
  CronPeriodInfo image_cron_info;
  uint32_t real_data_op_after_image;
} SubTypeInfo;

// 榜单类型信息
typedef struct {
  uint32_t business_id;
  uint32_t type;
  uint32_t n;
  int32_t score_proc_type;                   // 最大或者最新值
  int32_t update_timestamp_when_same_score;  // 是否更新榜单
  int32_t sort_method_on_score;
  int32_t sort_method_on_timestamp;
  int32_t sort_field_num;
  int32_t sort_method_on_field1;
  int32_t sort_method_on_field2;
  int32_t sort_method_on_field3;
  int32_t sort_method_on_field4;
  int32_t sort_method_on_field5;

  // 镜像设定
  int32_t use_image;
  CronPeriodInfo image_cron_info;
  int32_t real_data_op_after_image;

  // 衰减设定
  int32_t use_reduce;
  CronPeriodInfo reduce_cron_info;
  int32_t reduce_func;
  char reduce_args[TOPNEXT_MAX_SHORT_STR_LEN];

  // 子榜类型
  uint32_t sub_type_count;
  SubTypeInfo sub_type_info[TOPNEXT_MAX_SUB_TYPE_COUNT];
} TypeInfo;

// 由于榜单内存依赖此结构，所以不能在此结构增加成员
struct RankKey {
  uint32_t business_id;
  uint32_t world_id;
  uint32_t zone_id;
  uint32_t type;
  uint32_t instance_id;
  // 内部id，参与文件和共享内存key计算
  uint32_t id;
};

// 由于榜单内存依赖此结构，所以不能在此结构增加成员
struct SubRankKey {
  uint32_t type;
  uint32_t instance_id;
};

// 由于榜单内存依赖此结构，所以不能在此结构增加成员
// 影响排序的配置
typedef struct {
  int32_t sort_method_on_score;
  int32_t sort_method_on_timestamp;
  uint32_t sort_field_num;
  int32_t sort_method_on_field1;
  int32_t sort_method_on_field2;
  int32_t sort_method_on_field3;
  int32_t sort_method_on_field4;
  int32_t sort_method_on_field5;
} SortMethodInfo;

#define TOPNEXT_RANK_MEM_MAGIC (0x1234567890)
#define TOPNEXT_RANK_MEM_VERSION_1 (0x01)
#define TOPNEXT_RANK_MEM_STATE_OK (0x00)
#define TOPNEXT_RANK_MEM_NOT_DIRTY (-1)

enum RANK_DATA_STATE {
  RANK_DATA_INIT_DONE = 0,  // 完成了初始化
  RANK_DATA_SYNCING = 1,    // 正在同步中
  RANK_DATA_SYNC_DONE = 2,  // 同步完成
};

enum IMAGE_STATUS {
  IMAGE_INIT = 0,           // 完成了初始化
  IMAGE_GENERATING = 1,     // 镜像生成中
  IMAGE_GENERATE_SUCC = 2,  // 镜像生成成功
  IMAGE_GENERATE_FAIL = 3,  // 镜像生成失败
};

enum IMAGE_SWITCH {
  IMAGE_SWITCH_INIT = 0,    // 完成了初始化
  IMAGE_SWITCH_ON = 1,      // 打开镜像
  IMAGE_SWITCH_OFF = 2,     // 关闭镜像
  IMAGE_SWITCH_X = 3,       // 有些开，有些关闭
  IMAGE_SWITCH_UNKNOW = 4,  // 开关未知
};

// 榜单内存结构
typedef struct {
  uint64_t magic;             // 目前固定为:TOPNEXT_RANK_MEM_MAGIC
  uint32_t version;           // 版本号，每次内存格式的变化，需要增加版本号来管理
  int32_t state;              // 榜单当前状态: Ready, IN-SYNC
  uint32_t reserver_ctrl[8];  //

  // 基本配置参数
  RankKey rank_key;
  SubRankKey sub_rank_key;
  uint32_t n;

  // 排序相关配置
  SortMethodInfo sort_method_info;  // 参与排序的信息

  // 镜像相关配置
  int32_t use_image;
  char image_cron[64];

  // 衰减相关配置
  int32_t use_reduce;
  char reduce_cron[64];
  int32_t reduce_func;
  char reduce_args[256];

  // 内部使用的字段或者状态标识
  uint64_t create_timestamp;
  uint64_t last_image_timestamp;   // 最后一次镜像时间
  uint64_t last_reduce_timestamp;  // 最后一次衰减时间
  uint32_t image_round;            // 当前执行到的衰减round
  uint32_t reduce_round;           // 当前执行到的衰减round

  int32_t is_image;    // 是否是镜像榜单
  int32_t is_subrank;  // 是否是子榜单
  int32_t dirty_pos;   // 脏位

  // 20190517,从reserved取出两个int作为uint64最后清理时间戳
  uint64_t last_clean_sec;

  // 20230718, 王者v91，镜像的sequence
  uint64_t image_sequence;
  int32_t clean_real_after_image;
  int32_t image_status;  // 镜像状态
  int32_t image_switch;  // api请求打开或者关闭镜像的开关，需要兼容cfg中的镜像开关， 0.init, 1打开，2关闭

  int32_t reserved[57];  // 预留的字段，方便后续扩展
} RankHead;

typedef struct {
  RankHead head;
  char rank_data[1];
} RankData;

/////////////////
// Mgr的内存格式：mgr_head + group_shtable[1..MAX_GROUP_NUM] +
// shm_info_shtable[1..MAX_SUB_RANK_NUM_PER_TYPE] Group的内存格式：group_head +
// shm_info_shtable[1..count] + TopNextSubRankInstance[1..count]
////////////////
#define MAX_GROUP_NUM 1024
#define MAX_SHM_INFO_NUM_IN_MGR 10240

struct SubRankShmInfo {
  SubRankKey sub_rank_key;  // 子榜
  int32_t is_image;         // 是否是镜像

  uint32_t group_id;    // group info
  int32_t reserved[8];  // 保留字段，初始化为0
};

struct SubRankShmGroup {
  uint32_t version;   // 版本号,初始1
  RankKey rank_key;   // 榜单标识
  uint32_t sub_type;  // 子榜类别
  uint32_t group_id;  // 分组id，从0开始

  uint32_t count;       // 可以容纳的子榜数量，设定后即不可变更
  uint32_t used;        // 已经使用的量
  uint32_t shm_key;     // shm key
  uint32_t n;           // 对应子榜容量，不可以变更
  int32_t reserved[8];  // 保留字段，初始化为0
  char data[1];
};
struct SubRankShmGroupMgr {
  uint32_t version;   // 版本号,初始1
  RankKey rank_key;   // 榜单标识
  uint32_t sub_type;  // 子榜类别

  uint32_t count;        // 最大允许的分组数,即最多使用(1024)个分组, shm key
  uint32_t used;         // 已经使用的分组数,目前来看用途不大
  uint32_t n;            // 本字段废弃，不再使用,(n已经可以变)
  int32_t reserved[32];  // 保留字段，初始化为0
  char data[1];
};

}  // namespace topnext_app

#endif
