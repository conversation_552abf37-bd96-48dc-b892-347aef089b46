#ifndef TOPNEXT_IMAGE_COORDINATOR_H_
#define TOPNEXT_IMAGE_COORDINATOR_H_

#include <map>
#include <string>
#include <vector>
#include "crontab_timer.h"
#include "kernel_data_type.h"
#include "topnext_log.h"

namespace topnext_app {

// 前向声明
class TopNextRankInstance;

// 镜像配置的唯一标识
struct ImageConfigKey {
  uint32_t business_id;
  uint32_t type;      // 榜单类型
  uint32_t sub_type;  // 子榜类型
  std::string cron_expression;

  ImageConfigKey() : business_id(0), type(0), sub_type(0) {}
  ImageConfigKey(uint32_t biz_id, uint32_t rank_type, uint32_t sub_rank_type, const std::string& cron)
      : business_id(biz_id), type(rank_type), sub_type(sub_rank_type), cron_expression(cron) {}

  // 用于map的比较操作
  bool operator<(const ImageConfigKey& other) const {
    if (business_id != other.business_id) return business_id < other.business_id;
    if (type != other.type) return type < other.type;
    if (sub_type != other.sub_type) return sub_type < other.sub_type;
    return cron_expression < other.cron_expression;
  }

  bool operator==(const ImageConfigKey& other) const {
    return business_id == other.business_id && type == other.type && sub_type == other.sub_type &&
           cron_expression == other.cron_expression;
  }
};

// 镜像协调器配置信息
struct ImageCoordinatorConfig {
  ImageConfigKey config_key;
  std::vector<TopNextRankInstance*> instances;

  ImageCoordinatorConfig() {}
  ImageCoordinatorConfig(const ImageConfigKey& key) : config_key(key) {}
};

// 全局镜像协调器
class TopNextImageCoordinator {
 public:
  static TopNextImageCoordinator& Instance();

  // 初始化协调器
  int Init();

  // 注册子榜类型和对应的定时器信息
  int RegisterSubType(uint32_t business_id, uint32_t type, uint32_t sub_type, const std::string& cron_expression,
                      TopNextRankInstance* instance);

  // 取消注册实例
  void UnregisterInstance(TopNextRankInstance* instance);

  // 更新定时器（需要定期调用）
  void Update();

  // 重置协调器
  void Reset();

  // 获取统计信息
  size_t GetRegisteredSubTypeCount() const;
  size_t GetRegisteredInstanceCount() const;

 private:
  TopNextImageCoordinator();
  ~TopNextImageCoordinator();

  // 禁止拷贝和赋值
  TopNextImageCoordinator(const TopNextImageCoordinator&) = delete;
  TopNextImageCoordinator& operator=(const TopNextImageCoordinator&) = delete;

  // 定时器回调函数
  void OnImageTimeReached(uint32_t timer_id, const ImageConfigKey& config_key);

  // 全局设置指定配置的镜像状态
  void SetAllInstancesImageStatus(const ImageConfigKey& config_key, IMAGE_STATUS status);

  // 通知所有相关实例开始镜像
  void NotifyInstancesForImageGeneration(const ImageConfigKey& config_key);

 private:
  // 全局定时器
  apollo_service::CrontabTimer global_timer_;

  // 配置key -> 配置信息的映射
  std::map<ImageConfigKey, ImageCoordinatorConfig> config_map_;

  // 定时器ID -> 配置key的映射
  std::map<uint32_t, ImageConfigKey> timer_id_to_config_;

  // 是否已初始化
  bool initialized_;
};

}  // namespace topnext_app

#endif  // TOPNEXT_IMAGE_COORDINATOR_H_
