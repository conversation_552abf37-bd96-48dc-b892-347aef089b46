#ifndef TOPNEXT_IMAGE_COORDINATOR_H_
#define TOPNEXT_IMAGE_COORDINATOR_H_

#include <map>
#include <mutex>
#include <string>
#include <vector>
#include "crontab_timer.h"
#include "kernel_data_type.h"
#include "topnext_log.h"

namespace topnext_app {

// 前向声明
class TopNextRankInstance;

// 镜像协调器配置信息
struct ImageCoordinatorConfig {
  uint32_t sub_type;
  std::string cron_expression;
  std::vector<TopNextRankInstance*> instances;

  ImageCoordinatorConfig() : sub_type(0) {}
  ImageCoordinatorConfig(uint32_t type, const std::string& cron) : sub_type(type), cron_expression(cron) {}
};

// 全局镜像协调器
class TopNextImageCoordinator {
 public:
  static TopNextImageCoordinator& Instance();

  // 初始化协调器
  int Init(LPTLOGCATEGORYINST logcat = nullptr);

  // 注册子榜类型和对应的定时器信息
  int RegisterSubType(uint32_t sub_type, const std::string& cron_expression, TopNextRankInstance* instance);

  // 取消注册实例
  void UnregisterInstance(TopNextRankInstance* instance);

  // 更新定时器（需要定期调用）
  void Update();

  // 重置协调器
  void Reset();

  // 获取统计信息
  size_t GetRegisteredSubTypeCount() const;
  size_t GetRegisteredInstanceCount() const;

 private:
  TopNextImageCoordinator();
  ~TopNextImageCoordinator();

  // 禁止拷贝和赋值
  TopNextImageCoordinator(const TopNextImageCoordinator&) = delete;
  TopNextImageCoordinator& operator=(const TopNextImageCoordinator&) = delete;

  // 定时器回调函数
  void OnImageTimeReached(uint32_t timer_id, uint32_t sub_type);

  // 全局设置指定子榜类型的镜像状态
  void SetAllInstancesImageStatus(uint32_t sub_type, IMAGE_STATUS status);

  // 通知所有相关实例开始镜像
  void NotifyInstancesForImageGeneration(uint32_t sub_type);

 private:
  // 全局定时器
  apollo_service::CrontabTimer global_timer_;

  // 子榜类型 -> 配置信息的映射
  std::map<uint32_t, ImageCoordinatorConfig> sub_type_configs_;

  // 定时器ID -> 子榜类型的映射
  std::map<uint32_t, uint32_t> timer_id_to_sub_type_;

  // 日志句柄
  LPTLOGCATEGORYINST tlogcat_;

  // 是否已初始化
  bool initialized_;
};

}  // namespace topnext_app

#endif  // TOPNEXT_IMAGE_COORDINATOR_H_
