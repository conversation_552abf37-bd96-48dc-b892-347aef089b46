#include "topnext_rank_instance.h"

#include "kernel_data_type.h"
#include "service_monitor.h"
#include "time_cache.h"
#include "topnext_app.h"
#include "topnext_common.h"
#include "topnext_reduce_for_lgame.h"

#include <glob.h>
#include <algorithm>

namespace topnext_app {

using namespace shm_util;
using namespace apollo_service;

class SubRankInstanceKeyHasher : public shtable_util::IHashFunctor<SubRankInstanceKey> {
 public:
  inline unsigned int GetCode(const SubRankInstanceKey& node) const override {
    return (node.sub_rank_key.instance_id ^ node.sub_rank_key.type) * (node.is_image ? 1 : -1);
  }

  inline int Compare(const SubRankInstanceKey& left, const SubRankInstanceKey& right) const override {
    return (left.sub_rank_key.instance_id == right.sub_rank_key.instance_id &&
            left.sub_rank_key.type == right.sub_rank_key.type && left.is_image == right.is_image)
               ? 0
               : -1;
  }
};

static SubRankInstanceKeyHasher g_subrank_instance_key_hasher;

int TopNextRankInstance::Init(const topnext_cfg::RANKCFG& rank_cfg, const topnext_cfg::TYPECFG& type_cfg) {
  rank_key_.business_id = rank_cfg.business_id;
  rank_key_.world_id = rank_cfg.world_id;
  rank_key_.zone_id = rank_cfg.zone_id;
  rank_key_.type = rank_cfg.type;
  rank_key_.instance_id = rank_cfg.instance_id;
  rank_key_.id = rank_cfg.id;
  type_key_.business_id = rank_key_.business_id;
  type_key_.type = rank_key_.type;
  type_cfg_ = type_cfg;
  if (0 == rank_cfg.business_id || 0 == rank_cfg.world_id || 0 == rank_cfg.zone_id || 0 == rank_cfg.type ||
      0 == rank_cfg.instance_id || 0 == rank_cfg.id) {
    TOPNEXT_RANK_LOG_ERROR("invalid rank id");
    return -1;
  }
  if (type_cfg_.sub_type_cfg_num == 0) {
    TOPNEXT_RANK_LOG_ERROR("sub type cfg num is zero");
    return -1;
  }
  int ret = 0;
  // 初始化子榜
  ret = InitSubRankInstance();
  if (0 != ret) {
    TOPNEXT_RANK_LOG_ERROR("init sub rank instance failed, ret:%d", ret);
    return -1;
  }
  ret = InitImageCrontabTimer(type_cfg);
  if (0 != ret) {
    TOPNEXT_RANK_LOG_ERROR("init image crontab failed, ret:%d", ret);
    return -1;
  }
  ret = InitReduceCrontabTimer(type_cfg);
  if (0 != ret) {
    TOPNEXT_RANK_LOG_ERROR("init reduce crontab failed, ret:%d", ret);
    return -1;
  }
  // check whether the size is changed for subtype
  ret = InitResizeOnSubRank();
  if (0 != ret) {
    TOPNEXT_RANK_LOG_ERROR("init reduce crontab failed, ret:%d", ret);
    return -1;
  }
  TOPNEXT_RANK_LOG_INFO("init rank instance succ");
  return 0;
}

int TopNextRankInstance::InitSubRankShmGroupMgr(const topnext_cfg::SUBTYPECFG& sub_type_cfg) {
  int ret = 0;
  uint32_t sub_type = sub_type_cfg.type;
  TopNextSubRankShmGroupManager* shm_group_mgr = new TopNextSubRankShmGroupManager();
  size_t shm_size = shm_group_mgr->GetSize();
  SubRankShmGroupMgr shm_group_mgr_id;
  shm_group_mgr_id.rank_key = rank_key_;
  shm_group_mgr_id.sub_type = sub_type;
  bool existed = false;
  ret = sub_rank_shm_manager_.LoadShm(&shm_group_mgr->shm_, shm_size, shm_group_mgr_id, existed);
  if (0 != ret) {
    delete shm_group_mgr;
    TOPNEXT_RANK_LOG_ERROR("load from shm manager fail, subtype:%u, ret:%d", sub_type, ret);
    return -1;
  }
  if (!existed) {
    ret = shm_group_mgr->Init(&sub_rank_shm_manager_, rank_key_, sub_type_cfg);
  } else {
    ret = shm_group_mgr->Attach(&sub_rank_shm_manager_, rank_key_, sub_type_cfg);
  }
  if (0 != ret) {
    delete shm_group_mgr;
    TOPNEXT_RANK_LOG_ERROR("init shm group manager fail, subtype:%u, ret:%d", sub_type, ret);
    return -1;
  }
  shm_group_mgr_map_[sub_type] = shm_group_mgr;
  std::map<SubRankShmInfo, uint32_t>::iterator it;
  for (it = shm_group_mgr->shm_info_group_id_map_.begin(); it != shm_group_mgr->shm_info_group_id_map_.end(); ++it) {
    const SubRankShmInfo& sub_rank_shm_info = it->first;
    ret = CreateSubInstanceIncludeImage(sub_rank_shm_info.sub_rank_key);
    if (0 != ret) {
      TOPNEXT_RANK_LOG_ERROR("recover sub rank key from shm manager fail, ret:%d", ret);
      return -1;
    }
    TOPNEXT_RANK_LOG_INFO(
        "recover sub rank from shm manager succ, "
        "sub_type:%u, sub_instance_id:%u, is_image:%d",
        sub_rank_shm_info.sub_rank_key.type, sub_rank_shm_info.sub_rank_key.instance_id, sub_rank_shm_info.is_image);
  }
  return 0;
}

int TopNextRankInstance::Reload(const topnext_cfg::TYPECFG& type_cfg) {
  int ret = 0;
  // 目前只处理有子榜的情况
  if (type_cfg.sub_type_cfg_num > 0) {
    // 1. 镜像功能的开关
    for (uint32_t i = 0; i < type_cfg.sub_type_cfg_num; ++i) {
      const topnext_cfg::SUBTYPECFG& new_sub_type_cfg = type_cfg.sub_type_cfg[i];

      // 校验instance_num是否大于或者等于已经使用了数量，否则失败
      if (subrank_count_map_.find(new_sub_type_cfg.type) != subrank_count_map_.end()) {
        if (subrank_count_map_[new_sub_type_cfg.type] > new_sub_type_cfg.instance_num) {
          TOPNEXT_RANK_LOG_ERROR("new sub_type_cfg.instance_num[%u] < subrank instance", new_sub_type_cfg.instance_num);
          return -1;
        }
      }

      topnext_cfg::SUBTYPECFG old_sub_type_cfg;
      ret = GetSubTypeCfg(type_cfg_, new_sub_type_cfg.type, old_sub_type_cfg);
      if (0 != ret) {
        // 新增加的子榜类型
        ret = InitSubRankShmGroupMgr(new_sub_type_cfg);
        if (0 != ret) {
          TOPNEXT_RANK_LOG_INFO("init subrank shm manager fail, sub_type:%u, ret:%d", new_sub_type_cfg.type, ret);
          return -1;
        }
        TOPNEXT_RANK_LOG_INFO("init subrank shm manager succ, sub_type:%u", new_sub_type_cfg.type);
        continue;
      }
      // 开镜像
      if (old_sub_type_cfg.use_image == 0 && new_sub_type_cfg.use_image == 1) {
        std::vector<TopNextSubRankInstance*> sub_instances;
        GetSubRankList(new_sub_type_cfg.type, false, sub_instances);
        std::vector<TopNextSubRankInstance*>::iterator it;
        for (it = sub_instances.begin(); it != sub_instances.end(); ++it) {
          TopNextSubRankInstance* subrank_instance = *it;
          ret = CreateSubInstanceIncludeImage(subrank_instance->sub_rank_key_, true);
          if (0 != ret) {
            TOPNEXT_RANK_LOG_ERROR(
                "create image subrank failed, subtype:%u, subinstance:%u, "
                "ret:%d",
                subrank_instance->sub_rank_key_.type, subrank_instance->sub_rank_key_.instance_id, ret);
            return -1;
          }
          TOPNEXT_RANK_LOG_INFO("create image subrank succ, subtype:%u, subinstance:%u",
                                subrank_instance->sub_rank_key_.type, subrank_instance->sub_rank_key_.instance_id);
        }
      }
      // 关镜像
      else if (old_sub_type_cfg.use_image == 1 && new_sub_type_cfg.use_image == 0) {
        std::vector<TopNextSubRankInstance*> sub_instances;
        GetSubRankList(new_sub_type_cfg.type, true, sub_instances);
        std::vector<TopNextSubRankInstance*>::iterator it;
        for (it = sub_instances.begin(); it != sub_instances.end(); ++it) {
          TopNextSubRankInstance* subrank_instance = *it;
          ret = RemoveSubInstance(subrank_instance->sub_rank_key_, true);
          if (0 != ret) {
            TOPNEXT_RANK_LOG_ERROR(
                "remove image subrank failed, subtype:%u, subinstance:%u, "
                "ret:%d",
                subrank_instance->sub_rank_key_.type, subrank_instance->sub_rank_key_.instance_id, ret);
            return -1;
          }
          TOPNEXT_RANK_LOG_INFO("remove image subrank succ, subtype:%u, subinstance:%u",
                                subrank_instance->sub_rank_key_.type, subrank_instance->sub_rank_key_.instance_id);
        }
      }
    }
    // 所有子榜全部reload一遍
    std::tr1::unordered_map<SubRankInstanceKey, TopNextSubRankInstance*, Hash, EqualTo>::iterator it;
    for (it = subrank_instance_map_.begin(); it != subrank_instance_map_.end(); ++it) {
      TopNextSubRankInstance* subrank_instance = it->second;
      ret = subrank_instance->Reload(type_cfg);
      if (0 != ret) {
        TOPNEXT_RANK_LOG_DEBUG("reload subrank fail, subtype:%u, subinstance:%u, is_image:%d, ret:%d",
                               subrank_instance->sub_rank_key_.type, subrank_instance->sub_rank_key_.instance_id,
                               subrank_instance->is_image_, ret);
        return -1;
      }
      TOPNEXT_RANK_LOG_INFO("reload subrank succ, subtype:%u, subinstance:%u, is_image:%d",
                            subrank_instance->sub_rank_key_.type, subrank_instance->sub_rank_key_.instance_id,
                            subrank_instance->is_image_);
    }
    type_cfg_ = type_cfg;
    // 4. Image and ReduceCrontabTimer重新初始化
    ret = InitImageCrontabTimer(type_cfg);
    if (0 != ret) {
      TOPNEXT_RANK_LOG_ERROR("init image crontab failed, ret:%d", ret);
      return -1;
    }
    ret = InitReduceCrontabTimer(type_cfg);
    if (0 != ret) {
      TOPNEXT_RANK_LOG_ERROR("init reduce crontab failed, ret:%d", ret);
      return -1;
    }
    // 5. check whether the size is changed for subtype
    ret = InitResizeOnSubRank();
    if (0 != ret) {
      TOPNEXT_RANK_LOG_ERROR("InitResizeOnSubRank failed, ret:%d", ret);
      return -1;
    }
    // 6. 所有共享内存管理对象全部重载一次
    std::map<uint32_t, TopNextSubRankShmGroupManager*>::iterator it_shm_group_mgr;
    for (it_shm_group_mgr = shm_group_mgr_map_.begin(); it_shm_group_mgr != shm_group_mgr_map_.end();
         ++it_shm_group_mgr) {
      uint32_t sub_type = it_shm_group_mgr->first;
      topnext_cfg::SUBTYPECFG sub_type_cfg;
      int ret = GetSubTypeCfg(type_cfg_, sub_type, sub_type_cfg);
      if (0 != ret) {
        continue;
      }
      it_shm_group_mgr->second->Reload(sub_type_cfg);
    }
  }
  return 0;
}

int TopNextRankInstance::InitResizeOnSubRank() {
  need_resize_sub_rank_.clear();
  // 按分组调整顺序
  std::multimap<uint32_t, TopNextSubRankInstance*> resize_sub_rank_map;
  for (uint32_t i = 0; i < type_cfg_.sub_type_cfg_num; ++i) {
    const topnext_cfg::SUBTYPECFG& new_sub_type_cfg = type_cfg_.sub_type_cfg[i];
    topnext_cfg::SUBTYPECFG sub_type_cfg;
    int ret = GetSubTypeCfg(type_cfg_, new_sub_type_cfg.type, sub_type_cfg);
    if (0 != ret) {
      continue;
    }
    std::vector<TopNextSubRankInstance*> sub_instances;
    GetSubRankList(new_sub_type_cfg.type, sub_instances);
    std::vector<TopNextSubRankInstance*>::iterator instance_it;
    for (instance_it = sub_instances.begin(); instance_it != sub_instances.end(); ++instance_it) {
      TopNextSubRankInstance* sub_instance = *instance_it;
      if (sub_type_cfg.n != (uint32_t)sub_instance->Total()) {
        SubRankKey sub_rank_key = sub_instance->sub_rank_key_;
        bool is_image = sub_instance->is_image_;
        uint32_t group_id = 0;
        ret = GetSubRankShmGroupId(sub_rank_key, is_image, group_id);
        if (0 != ret) {
          TOPNEXT_RANK_LOG_ERROR("get subrank group id fail, sub_type:%u, sub_instance:%u", sub_rank_key.type,
                                 sub_rank_key.instance_id);
          return -1;
        }
        resize_sub_rank_map.insert(std::pair<uint32_t, TopNextSubRankInstance*>(group_id, sub_instance));
      }
    }
  }
  std::multimap<uint32_t, TopNextSubRankInstance*>::iterator map_it;
  for (map_it = resize_sub_rank_map.begin(); map_it != resize_sub_rank_map.end(); ++map_it) {
    TopNextSubRankInstance* sub_instance = map_it->second;
    need_resize_sub_rank_.insert(need_resize_sub_rank_.end(), sub_instance);
    TOPNEXT_RANK_LOG_INFO(
        "need resize sub rank, sub_type:%u, sub_instance:%u, is_image:%d, "
        "group_id:%u",
        sub_instance->sub_rank_key_.type, sub_instance->sub_rank_key_.instance_id, sub_instance->is_image_,
        map_it->first);
  }
  return 0;
}

int TopNextRankInstance::Fini() {
  std::tr1::unordered_map<SubRankInstanceKey, TopNextSubRankInstance*, Hash, EqualTo>::iterator it;
  for (it = subrank_instance_map_.begin(); it != subrank_instance_map_.end(); ++it) {
    TopNextSubRankInstance* sub_instance = it->second;
    sub_instance->Fini();
    delete it->second;
  }
  std::map<uint32_t, TopNextSubRankShmGroupManager*>::iterator it_mgr;
  for (it_mgr = shm_group_mgr_map_.begin(); it_mgr != shm_group_mgr_map_.begin(); ++it_mgr) {
    it_mgr->second->Fini();
    delete it_mgr->second;
  }
  return 0;
}

int TopNextRankInstance::InitImageCrontabTimer(const topnext_cfg::TYPECFG& type_cfg) {
  int ret = 0;
  image_crontab_timer_.Reset();
  ret = image_crontab_timer_.Init(TOPNEXT_MAX_SUB_TYPE_COUNT, g_tlogcat);
  if (0 != ret) {
    TOPNEXT_RANK_LOG_ERROR("init image crontab timer fail");
    return -1;
  }
  // 处理有子榜的情况
  for (uint32_t i = 0; i < type_cfg.sub_type_cfg_num; ++i) {
    const topnext_cfg::SUBTYPECFG& sub_type_cfg = type_cfg.sub_type_cfg[i];
    if (sub_type_cfg.use_image) {
      uint32_t timer_id;
      CrontabTimerCallback cb =
          std::bind(&TopNextRankInstance::NeedGenerateImageForSubType, this, std::placeholders::_1, sub_type_cfg.type);
      ret = image_crontab_timer_.StartTimer(sub_type_cfg.image_cron, cb, timer_id);
      if (0 != ret) {
        TOPNEXT_RANK_LOG_ERROR("start timer fail, sub_type:%u, cron:%s", sub_type_cfg.type, sub_type_cfg.image_cron);
        return -1;
      }
    }
  }
  return 0;
}

int TopNextRankInstance::InitReduceCrontabTimer(const topnext_cfg::TYPECFG& type_cfg) {
  int ret = 0;
  reduce_crontab_timer_.Reset();
  ret = reduce_crontab_timer_.Init(TOPNEXT_MAX_SUB_TYPE_COUNT, g_tlogcat);
  if (0 != ret) {
    TOPNEXT_RANK_LOG_ERROR("init reduce crontab timer fail");
    return -1;
  }
  // 处理有子榜的情况
  if (type_cfg.use_reduce > 0) {
    if (type_cfg.sub_type_cfg_num > 0) {
      uint32_t timer_id;
      CrontabTimerCallback cb = std::bind(&TopNextRankInstance::NeedReduceForSubType, this, std::placeholders::_1);
      ret = reduce_crontab_timer_.StartTimer(type_cfg.reduce_cron, cb, timer_id);
      if (0 != ret) {
        TOPNEXT_RANK_LOG_ERROR("start timer fail, cron:%s", type_cfg.reduce_cron);
        return -1;
      }
    }
  }
  return 0;
}

int TopNextRankInstance::InitSubRankInstance() {
  int ret = 0;
  std::string data_dir = TopNextApp::Instance()->DataDir();
  std::string tappid = TopNextApp::Instance()->TappId();
  std::string file_name =
      format_string("busi_%u_world_%u_zone_%u_type_%u_instance_%u_id_%u_shm_manager.xml", rank_key_.business_id,
                    rank_key_.world_id, rank_key_.zone_id, rank_key_.type, rank_key_.instance_id, rank_key_.id);
  std::string file_path = data_dir + "/" + file_name;
  is_sub_rank_shm_manager_existed_ = (0 == tfexist(file_path.c_str()));
  ret = sub_rank_shm_manager_.Init(g_tlogcat, file_path, tappid);
  if (0 != ret) {
    TOPNEXT_RANK_LOG_ERROR("init subrank shm manager file fail, ret:%d", ret);
    return -1;
  }
  TOPNEXT_RANK_LOG_INFO("init subrank shm manager file succ");
  for (uint32_t i = 0; i < type_cfg_.sub_type_cfg_num; ++i) {
    // 每个类型的子榜数量
    const topnext_cfg::SUBTYPECFG& sub_type_cfg = type_cfg_.sub_type_cfg[i];
    ret = InitSubRankShmGroupMgr(sub_type_cfg);
    if (0 != ret) {
      TOPNEXT_RANK_LOG_INFO("init subrank shm manager fail, sub_type:%u, ret:%d", sub_type_cfg.type, ret);
      return -1;
    }
    TOPNEXT_RANK_LOG_INFO("init subrank shm manager succ, sub_type:%u", sub_type_cfg.type);
  }
  // try reload from file
  if (!is_sub_rank_shm_manager_existed_) {
    TOPNEXT_RANK_LOG_INFO("recover sub rank from dump file start");
    ret = RecoverSubRankFromDumpFile();
    if (0 != ret) {
      TOPNEXT_RANK_LOG_INFO("init subrank shm manager fail, ret:%d", ret);
      return -1;
    }
    TOPNEXT_RANK_LOG_INFO("recover sub rank from dump file success");
  }
  return 0;
}

int TopNextRankInstance::RecoverSubRankFromDumpFile() {
  glob_t buf;
  // busi_%u_world_%u_zone_%u_type_%u_instance_%u_subtype_%u_subinstance_%u_image_%u_id_%u.dump",
  std::string dir =
      format_string("busi_%u_world_%u_zone_%u_type_%u_instance_%u_id_%u", rank_key_.business_id, rank_key_.world_id,
                    rank_key_.zone_id, rank_key_.type, rank_key_.instance_id, rank_key_.id);
  std::string pattern =
      TopNextApp::Instance()->DataDir() + "/" + dir + "/" +
      format_string("busi_%u_world_%u_zone_%u_type_%u_instance_%u_subtype_*_id_%u.dump", rank_key_.business_id,
                    rank_key_.world_id, rank_key_.zone_id, rank_key_.type, rank_key_.instance_id, rank_key_.id);
  glob(pattern.c_str(), GLOB_NOSORT, NULL, &buf);
  for (uint32_t i = 0; i < buf.gl_pathc; i++) {
    int ret = RecoverSubRankFromDumpFile(buf.gl_pathv[i]);
    if (0 != ret) {
      TOPNEXT_RANK_LOG_ERROR("recover from file fail, file:%s, ret:%d", buf.gl_pathv[i], ret);
      return -1;
    }
    TOPNEXT_RANK_LOG_INFO("recover from file succ, file:%s", buf.gl_pathv[i]);
  }
  return 0;
}

int TopNextRankInstance::RecoverSubRankFromDumpFile(const std::string& file_path) {
  int ret;
  int is_image;
  std::string file_name = basename((char*)file_path.c_str());
  RankKey rank_key;
  SubRankKey sub_rank_key;
  ret = sscanf(file_name.c_str(),
               "busi_%u_world_%u_zone_%u_type_%u_instance_%u_subtype_%u_"
               "subinstance_%u_image_%d_id_%u",
               &rank_key.business_id, &rank_key.world_id, &rank_key.zone_id, &rank_key.type, &rank_key.instance_id,
               &sub_rank_key.type, &sub_rank_key.instance_id, &is_image, &rank_key.id);
  if (ret < 0) {
    TOPNEXT_RANK_LOG_ERROR("invalid dump file name:%s", file_name.c_str());
    return -1;
  }
  if (rank_key_.business_id != rank_key.business_id || rank_key_.world_id != rank_key.world_id ||
      rank_key_.zone_id != rank_key.zone_id || rank_key_.type != rank_key.type ||
      rank_key_.instance_id != rank_key.instance_id || rank_key_.id != rank_key.id) {
    TOPNEXT_RANK_LOG_ERROR("invalid dump file name:%s", file_name.c_str());
    return -1;
  }
  if (sub_rank_key.type == 0 || sub_rank_key.instance_id == 0 || (is_image != 0 && is_image != 1)) {
    TOPNEXT_RANK_LOG_ERROR("invalid dump file name:%s", file_name.c_str());
    return -1;
  }
  ret = CreateSubInstanceIncludeImage(sub_rank_key);
  if (0 != ret) {
    TOPNEXT_RANK_LOG_ERROR("create sub rank fail:%s", file_name.c_str());
    return -1;
  }
  bool image = (is_image == 1);
  TopNextSubRankInstance* sub_instance = FindSubInstance(sub_rank_key, image);
  if (NULL == sub_instance) {
    TOPNEXT_RANK_LOG_ERROR("get sub rank fail:%s", file_name.c_str());
    return -1;
  }
  // read data file
  int fd = -1;
  fd = open(file_path.c_str(), O_RDONLY);
  if (fd < 0) {
    TOPNEXT_RANK_LOG_ERROR("open file fail, file:%s, ret: %d, err:%s", file_path.c_str(), fd, strerror(errno));
    return -1;
  }
  // 文件大小
  ret = tfsize((HANDLE)fd);
  if (ret <= 0) {
    TOPNEXT_RANK_LOG_ERROR("get file size fail, file:%s, err:%s", file_path.c_str(), strerror(errno));
    close(fd);
    return -1;
  }
  void* buff;
  uint32_t file_len = (uint32_t)ret;
  // 注意:4这个字节是uint32_t的长度
  buff = (void*)malloc(file_len + sizeof(uint32_t));
  if (NULL == buff) {
    TOPNEXT_RANK_LOG_ERROR("malloc memory fail, err:%s", strerror(errno));
    close(fd);
    return -1;
  }
  ret = read(fd, buff, file_len);
  if ((int)file_len != ret) {
    TOPNEXT_RANK_LOG_ERROR("read data from file fail, file:%s, ret:%d, err:%s", file_path.c_str(), ret,
                           strerror(errno));
    free((void*)buff);
    close(fd);
    return -1;
  }
  close(fd);
  uint32_t content_len = *((uint32_t*)(char*)buff);
  if (content_len != file_len) {
    TOPNEXT_RANK_LOG_ERROR("incomplete rank data file, file:%s, file_len:%u, content_len:%u", file_path.c_str(),
                           file_len, content_len);
    free((void*)buff);
    return -1;
  }
  uint32_t user_num = *((uint32_t*)((char*)buff + sizeof(uint32_t)));
  TOPNEXT_RANK_LOG_DEBUG("user_num: %d", user_num);
  char* user_info_buff = (char*)buff + 2 * sizeof(uint32_t);
  uint32_t remain_buff_len = content_len - 2 * sizeof(uint32_t);
  for (uint32_t i = 0; i < user_num; ++i) {
    topnext_app::UserInfo user_info;
    uint32_t used_len = 0;
    ret = ReadUserInfoFromDumpBuff(user_info_buff, remain_buff_len, user_info, used_len);
    if (0 == ret) {
      // 指向下一个用户
      remain_buff_len -= used_len;
      user_info_buff += used_len;
      TOPNEXT_RANK_LOG_DEBUG("user open_id: %s", user_info.open_id);
      ret = sub_instance->Update(user_info);
      if (0 != ret) {
        TOPNEXT_RANK_LOG_ERROR("update user info fail, file:%s, file_len:%u, user_num:%u", file_path.c_str(), file_len,
                               user_num);
        free((void*)buff);
        return -1;
      }
    } else {
      TOPNEXT_RANK_LOG_ERROR("invalid file fail, file:%s, file_len:%u, user_num:%u", file_path.c_str(), file_len,
                             user_num);
      free((void*)buff);
      return -1;
    }
  }
  return 0;
}

int TopNextRankInstance::ReadUserInfoFromDumpBuff(const char* buff, uint32_t remain_buff_len,
                                                  topnext_app::UserInfo& user_info, uint32_t& used_len) {
  used_len = 0;
  uint32_t user_info_without_ext_data_len = (uint32_t)(sizeof(user_info) - sizeof(user_info.ext_data_info));
  if (remain_buff_len >= user_info_without_ext_data_len) {
    memcpy(&user_info, buff, user_info_without_ext_data_len);
    if (user_info.ext_data_len > sizeof(user_info.ext_data_info)) {
      TOPNEXT_RANK_LOG_ERROR("invalid rank data buff, ext_data_len:%u", user_info.ext_data_len);
      return -1;
    }
    if (remain_buff_len >= user_info_without_ext_data_len + user_info.ext_data_len &&
        user_info.ext_data_len <= sizeof(user_info.ext_data_info)) {
      memcpy(user_info.ext_data_info, buff + user_info_without_ext_data_len, user_info.ext_data_len);
    } else {
      TOPNEXT_RANK_LOG_ERROR(
          "invalid rank data buff, remain_buff_len:%u, user_info_len:%u, "
          "ext_data_len:%u",
          remain_buff_len, user_info_without_ext_data_len, user_info.ext_data_len);
      return -1;
    }
  } else {
    TOPNEXT_RANK_LOG_ERROR("invalid rank data buff, remain_buff_len:%u, user_info_len:%u", remain_buff_len,
                           user_info_without_ext_data_len);
    return -1;
  }
  used_len = user_info_without_ext_data_len + user_info.ext_data_len;
  return 0;
}

int TopNextRankInstance::AddToSubRankInstanceMap(const SubRankKey& sub_rank_key, bool is_image,
                                                 TopNextSubRankInstance* sub_instance) {
  SubRankInstanceKey sub_rank_instance_key;
  sub_rank_instance_key.rank_key = rank_key_;
  sub_rank_instance_key.sub_rank_key = sub_rank_key;
  sub_rank_instance_key.is_image = is_image;
  sub_rank_instance_key.subrank_instance = sub_instance;
  subrank_instance_map_[sub_rank_instance_key] = sub_instance;
  subrank_instance_keys_[sub_rank_instance_key] = true;
  return 0;
}

TopNextSubRankInstance* TopNextRankInstance::FindSubInstance(const SubRankKey& sub_rank_key, bool is_image) {
  SubRankInstanceKey sub_rank_instance;
  sub_rank_instance.rank_key = rank_key_;
  sub_rank_instance.sub_rank_key = sub_rank_key;
  sub_rank_instance.is_image = is_image;
  if (subrank_instance_map_.find(sub_rank_instance) == subrank_instance_map_.end()) {
    return NULL;
  }
  return subrank_instance_map_[sub_rank_instance];
}

bool TopNextRankInstance::AddToNeedResizeSubRank(TopNextSubRankInstance* sub_rank_instance) {
  if (nullptr == sub_rank_instance) {
    return false;
  }
  need_resize_sub_rank_.push_back(sub_rank_instance);
  return true;
}

int TopNextRankInstance::CreateSubInstanceIncludeImage(const SubRankKey& sub_rank_key, bool force_image) {
  int ret = 0;
  //  TypeKey type_key;
  //  type_key.business_id = rank_key_.business_id;
  //  type_key.type = rank_key_.type;
  topnext_cfg::SUBTYPECFG sub_type_cfg;
  ret = GetSubTypeCfg(type_cfg_, sub_rank_key.type, sub_type_cfg);
  if (0 != ret) {
    TOPNEXT_RANK_LOG_ERROR("get sub type cfg fail, sub_type:%u, ret:%d", sub_rank_key.type, ret);
    return -1;
  }
  TopNextSubRankInstance* sub_rank_instance = FindSubInstance(sub_rank_key, false);
  if (NULL == sub_rank_instance) {
    ret = CreateSubInstance(sub_rank_key, false);
    if (0 != ret) {
      TOPNEXT_RANK_LOG_ERROR("create real sub instance fail, sub_type:%u, sub_instance_id:%u, ret:%d",
                             sub_rank_key.type, sub_rank_key.instance_id, ret);
      return -2;
    }
    TOPNEXT_RANK_LOG_INFO("create real sub instance succ, sub_type:%u, sub_instance_id:%u", sub_rank_key.type,
                          sub_rank_key.instance_id);
  }
  // 如果配置了使用镜像
  bool use_image = force_image ? true : sub_type_cfg.use_image;
  if (use_image) {
    sub_rank_instance = FindSubInstance(sub_rank_key, true);
    if (NULL == sub_rank_instance) {
      ret = CreateSubInstance(sub_rank_key, true);
      if (0 != ret) {
        TOPNEXT_RANK_LOG_ERROR(
            "create image sub instance fail, sub_type:%u, sub_instance_id:%u, "
            "ret:%d",
            sub_rank_key.type, sub_rank_key.instance_id, ret);
        return -3;
      }
      TOPNEXT_RANK_LOG_INFO("create image sub instance succ, sub_type:%u, sub_instance_id:%u", sub_rank_key.type,
                            sub_rank_key.instance_id);
    }
  }
  return 0;
}

int TopNextRankInstance::RemoveSubInstance(const SubRankKey& sub_rank_key, bool is_image) {
  if (!is_image) {
    // 实时榜单，目前不允许remove掉
    return -1;
  }
  TopNextSubRankInstance* sub_instance = FindSubInstance(sub_rank_key, is_image);
  TopNextSubRankShmGroupManager* shm_group_mgr = shm_group_mgr_map_[sub_rank_key.type];
  if (NULL == sub_instance || NULL == shm_group_mgr) {
    TOPNEXT_RANK_LOG_ERROR("invalid sub instance fail, sub_type:%u, sub_instance_id:%u", sub_rank_key.type,
                           sub_rank_key.instance_id);
    return -1;
  }
  sub_instance->Fini();
  shm_group_mgr->RemoveSubRankShmInfo(sub_rank_key, is_image);
  SubRankInstanceKey subrank_instance_key;
  subrank_instance_key.sub_rank_key = sub_rank_key;
  subrank_instance_key.is_image = is_image;
  subrank_instance_map_.erase(subrank_instance_key);
  subrank_instance_keys_.erase(subrank_instance_key);
  return 0;
}

int TopNextRankInstance::CreateSubInstance(const SubRankKey& sub_rank_key, bool is_image, bool check_capacity) {
  int ret = 0;
  topnext_cfg::SUBTYPECFG sub_type_cfg;
  ret = GetSubTypeCfg(type_cfg_, sub_rank_key.type, sub_type_cfg);
  if (0 != ret) {
    TOPNEXT_RANK_LOG_ERROR(
        "get sub rank instance cfg fail, sub_type:%u, sub_instance_id:%u, "
        "is_image:%d, ret:%d",
        sub_rank_key.type, sub_rank_key.instance_id, is_image, ret);
    return -1;
  }
  if (check_capacity && !is_image && subrank_count_map_.find(sub_rank_key.type) != subrank_count_map_.end() &&
      subrank_count_map_[sub_rank_key.type] >= sub_type_cfg.instance_num) {
    TOPNEXT_RANK_LOG_ERROR(
        "exceed limit of sub instance num, sub_type:%u, sub_instance_id:%u, "
        "instance_num:%u",
        sub_rank_key.type, sub_rank_key.instance_id, sub_type_cfg.instance_num);
    ServiceMonitor::Instance()->SubRankExceedLimit();
    return -1;
  }
  if (shm_group_mgr_map_.find(sub_rank_key.type) == shm_group_mgr_map_.end()) {
    TOPNEXT_RANK_LOG_ERROR("cannot find shm group mgr, sub_type:%u, sub_instance_id:%u", sub_rank_key.type,
                           sub_rank_key.instance_id);
    return -1;
  }
  TopNextSubRankShmGroupManager* shm_group_mgr = shm_group_mgr_map_[sub_rank_key.type];
  TopNextSubRankInstance* sub_instance = new TopNextSubRankInstance();
  SubRankInstanceKey sub_rank_instance_key;
  sub_rank_instance_key.rank_key = rank_key_;
  sub_rank_instance_key.sub_rank_key = sub_rank_key;
  sub_rank_instance_key.is_image = is_image;
  bool existed = false;
  SubRankShmPtr shm_ptr;
  ret = shm_group_mgr->GetSubRankShmInfo(sub_rank_key, is_image, shm_ptr, existed);
  if (0 != ret) {
    TOPNEXT_RANK_LOG_ERROR(
        "get sub rank shm ptr fail, sub_type:%u, sub_instance_id:%u, "
        "is_image:%d, ret:%d",
        sub_rank_key.type, sub_rank_key.instance_id, is_image, ret);
    delete sub_instance;
    return -1;
  }
  if (!existed) {
    ret = sub_instance->Init(shm_ptr.ptr, shm_ptr.len, rank_key_, sub_rank_key, type_cfg_, sub_type_cfg, is_image);
  } else {
    ret = sub_instance->Attach(shm_ptr.ptr, shm_ptr.len, rank_key_, sub_rank_key, type_cfg_, sub_type_cfg, is_image);
  }
  if (0 != ret) {
    TOPNEXT_RANK_LOG_ERROR(
        "init subrank fail, sub_type:%u, sub_instance_id:%u, is_image:%d, "
        "ret:%d",
        sub_rank_key.type, sub_rank_key.instance_id, is_image, ret);
    delete sub_instance;
    return -1;
  }
  if (!is_image) {
    ++subrank_count_map_[sub_rank_key.type];
  }
  // add to hashtable
  ret = AddToSubRankInstanceMap(sub_rank_key, is_image, sub_instance);
  if (0 != ret) {
    TOPNEXT_RANK_LOG_ERROR(
        "add sub rank instance to hashtable fail, "
        "sub_type:%u, sub_instance_id:%u, is_image:%d, ret:%d",
        sub_rank_key.type, sub_rank_key.instance_id, is_image, ret);
    return -1;
  }
  if (!existed) {
    TOPNEXT_RANK_BILL_LOG_INFO(
        "create sub rank instance succ, sub_type:%u, sub_instance_id:%u, "
        "image:%d, "
        "ptr:%p",
        sub_rank_key.type, sub_rank_key.instance_id, is_image, shm_ptr.ptr);
  } else {
    TOPNEXT_RANK_LOG_INFO(
        "recover sub rank instance succ, sub_type:%u, sub_instance_id:%u, "
        "image:%d, "
        "ptr:%p",
        sub_rank_key.type, sub_rank_key.instance_id, is_image, shm_ptr.ptr);
  }
  return 0;
}

int TopNextRankInstance::GenerateImageByRequest(TopNextSubRankInstance* real) {
  if (NULL == real) {
    TOPNEXT_RANK_LOG_ERROR("real rank is null");
    return -1;
  }

  TopNextSubRankInstance* image = FindSubInstance(real->sub_rank_key_, true);
  if (NULL == image) {
    TOPNEXT_RANK_LOG_ERROR("cannot find real sub rank instance, sub_type:%u, sub_instance_id:%d",
                           real->sub_rank_key_.type, real->sub_rank_key_.instance_id);
    return -1;
  }

  bool need_reset = false;
  if (image->rank_data_->head.clean_real_after_image == 1) {
    need_reset = true;
    image->rank_data_->head.clean_real_after_image = 0;  // 重置镜像后清除实时榜单数据的标志位
  }

  int ret = real->GenerateImage(image, true);
  if (0 != ret) {
    TOPNEXT_RANK_LOG_ERROR("generate image fail, sub_type:%u, sub_instance_id:%d", real->sub_rank_key_.type,
                           real->sub_rank_key_.instance_id);
    TOPNEXT_RANK_BILL_LOG_INFO("generate image fail, sub_type:%u, sub_instance_id:%d", real->sub_rank_key_.type,
                               real->sub_rank_key_.instance_id);
    return -1;
  }

  TOPNEXT_RANK_LOG_DEBUG("generate image succ, sub_type:%u, sub_instance_id:%d", real->sub_rank_key_.type,
                         real->sub_rank_key_.instance_id);
  TOPNEXT_RANK_BILL_LOG_INFO("generate image succ.sub_type:%u,sub_instance_id:%u", real->sub_rank_key_.type,
                             real->sub_rank_key_.instance_id);

  if (need_reset) {
    ret = real->Reset();
    if (0 != ret) {
      TOPNEXT_RANK_LOG_ERROR("reset real sub rank fail, sub_type:%u, sub_instance_id:%d", real->sub_rank_key_.type,
                             real->sub_rank_key_.instance_id);
      TOPNEXT_RANK_BILL_LOG_INFO("reset real sub rank fail.sub_type:%u,sub_instance_id:%u", real->sub_rank_key_.type,
                                 real->sub_rank_key_.instance_id);
      return -1;
    }

    TOPNEXT_RANK_BILL_LOG_INFO("reset real sub rank succ.sub_type:%u,sub_instance_id:%u", real->sub_rank_key_.type,
                               real->sub_rank_key_.instance_id);
    TOPNEXT_RANK_LOG_DEBUG("reset real sub rank succ, sub_type:%u, sub_instance_id:%d", real->sub_rank_key_.type,
                           real->sub_rank_key_.instance_id);
  }
  return 0;
};

int TopNextRankInstance::GenerateImage(TopNextSubRankInstance* real) {
  int ret = 0;
  if (NULL == real) {
    return -1;
  }
  TopNextSubRankInstance* image = NULL;
  bool need_reset = false;
  topnext_cfg::SUBTYPECFG sub_type_cfg;
  ret = GetSubTypeCfg(type_cfg_, real->sub_rank_key_.type, sub_type_cfg);
  if (0 != ret) {
    TOPNEXT_RANK_LOG_ERROR("get sub type cfg fail, sub_type:%u, sub_instance_id:%d", real->sub_rank_key_.type,
                           real->sub_rank_key_.instance_id);
    return -1;
  }

  // 使用过api主动请求关闭该榜单镜像
  if (real->rank_data_->head.image_switch == IMAGE_SWITCH_OFF) {
    TOPNEXT_RANK_LOG_INFO("image swith close by api, sub_type:%u, sub_instance_id:%d", real->sub_rank_key_.type,
                          real->sub_rank_key_.instance_id);
    return 0;
  }

  // 如果image_switch开关处于init(表示并未使用api请求过打开或者关闭), 那么使用cfg中的开关配置
  if (real->rank_data_->head.image_switch == IMAGE_SWITCH_INIT && !sub_type_cfg.use_image) {
    TOPNEXT_RANK_LOG_INFO("image swith in mem is %d, image swith in cfg is:%d", real->rank_data_->head.image_switch,
                          sub_type_cfg.use_image);
    return 0;
  }

  image = FindSubInstance(real->sub_rank_key_, true);
  if (NULL == image) {
    TOPNEXT_RANK_LOG_ERROR("cannot find image sub rank instance, sub_type:%u, sub_instance_id:%d",
                           real->sub_rank_key_.type, real->sub_rank_key_.instance_id);
    return -1;
  }

  if (sub_type_cfg.real_data_op_after_image == 1) {
    need_reset = true;
  } else {
    need_reset = false;
  }

  ret = real->GenerateImage(image, false);
  if (0 != ret) {
    TOPNEXT_RANK_LOG_ERROR("generate image fail, sub_type:%u, sub_instance_id:%d", real->sub_rank_key_.type,
                           real->sub_rank_key_.instance_id);
    TOPNEXT_RANK_BILL_LOG_INFO("generate image fail, sub_type:%u, sub_instance_id:%d", real->sub_rank_key_.type,
                               real->sub_rank_key_.instance_id);
    return -1;
  }
  TOPNEXT_RANK_LOG_DEBUG("generate image succ, sub_type:%u, sub_instance_id:%d", real->sub_rank_key_.type,
                         real->sub_rank_key_.instance_id);
  TOPNEXT_RANK_BILL_LOG_INFO("generate image succ.sub_type:%u,sub_instance_id:%u", real->sub_rank_key_.type,
                             real->sub_rank_key_.instance_id);
  if (need_reset) {
    ret = real->Reset();
    if (0 != ret) {
      TOPNEXT_RANK_LOG_ERROR("reset real sub rank fail, sub_type:%u, sub_instance_id:%d", real->sub_rank_key_.type,
                             real->sub_rank_key_.instance_id);
      TOPNEXT_RANK_BILL_LOG_INFO("reset real sub rank fail.sub_type:%u,sub_instance_id:%u", real->sub_rank_key_.type,
                                 real->sub_rank_key_.instance_id);
      return -1;
    }
    TOPNEXT_RANK_BILL_LOG_INFO("reset real sub rank succ.sub_type:%u,sub_instance_id:%u", real->sub_rank_key_.type,
                               real->sub_rank_key_.instance_id);
    TOPNEXT_RANK_LOG_DEBUG("reset real sub rank succ, sub_type:%u, sub_instance_id:%d", real->sub_rank_key_.type,
                           real->sub_rank_key_.instance_id);
  }
  return 0;
}

// 0,无可做的事务
// 1,做了一些事情
int TopNextRankInstance::CpuTimeControlForResizeSubRank() {
  if (need_resize_sub_rank_.size() == 0) {
    return 0;
  }

  if (TopNextApp::Instance()->GetCpuTimeFlowControl().IsTimeToDoAction()) {
    TopNextApp::Instance()->GetCpuTimeFlowControl().StartAction();
    DoResizeSubRank();
    TopNextApp::Instance()->GetCpuTimeFlowControl().EndAction();
    return 1;
  }
  return 0;
}

void TopNextRankInstance::DoResizeSubRank() {
  TopNextSubRankInstance* old_instance = *(need_resize_sub_rank_.begin());
  if (NULL == old_instance) {
    need_resize_sub_rank_.erase(need_resize_sub_rank_.begin());
    return;
  }
  topnext_cfg::SUBTYPECFG sub_type_cfg;
  int ret = GetSubTypeCfg(type_cfg_, old_instance->sub_rank_key_.type, sub_type_cfg);
  if (sub_type_cfg.n == old_instance->rank_data_->head.n) {
    need_resize_sub_rank_.erase(need_resize_sub_rank_.begin());
    return;
  }
  TopNextSubRankShmGroupManager* shm_group_mgr = shm_group_mgr_map_[old_instance->sub_rank_key_.type];
  if (NULL == shm_group_mgr) {
    TOPNEXT_RANK_LOG_ERROR("invalid sub group mgr, sub_type:%u", old_instance->sub_rank_key_.type);
    return;
  }
  // 先确保内存分配成功
  ret = shm_group_mgr->CreateGroupShmIfNeed();
  if (ret != 0) {
    // 这里sleep为了保证程序在出错的情况下，不会以为频繁调用这里的逻辑，阻塞业务请求
    usleep(100000);
    TOPNEXT_RANK_LOG_ERROR("create group shm failed, sub_type:%u, n:%u", old_instance->sub_rank_key_.type,
                           sub_type_cfg.n);
    return;
  }
  ret = shm_group_mgr->RemoveSubRankShmInfo(old_instance->sub_rank_key_, old_instance->is_image_);
  if (0 != ret) {
    TOPNEXT_RANK_LOG_ERROR("remove sub instance from shm group, sub_type:%u, sub_instance_id:%u",
                           old_instance->sub_rank_key_.type, old_instance->sub_rank_key_.instance_id);
    return;
  }
  ret = CreateSubInstance(old_instance->sub_rank_key_, old_instance->is_image_, false);
  if (0 != ret) {
    need_resize_sub_rank_.erase(need_resize_sub_rank_.begin());
    TOPNEXT_LOG_ERROR("create sub instance failed, ret:%d", ret);
    return;
  }
  need_resize_sub_rank_.erase(need_resize_sub_rank_.begin());
  TopNextSubRankInstance* new_instance = FindSubInstance(old_instance->sub_rank_key_, old_instance->is_image_);
  ret = new_instance->Copy(old_instance);
  if (0 != ret) {
    TOPNEXT_LOG_ERROR("copy sub instance failed, ret:%d", ret);
    return;
  }

  std::vector<TopNextSubRankInstance*>::iterator instance_it;
  instance_it = std::find(need_image_sub_rank_.begin(), need_image_sub_rank_.end(), old_instance);
  if (instance_it != need_image_sub_rank_.end()) {
    need_image_sub_rank_.erase(instance_it);
    need_image_sub_rank_.push_back(new_instance);
  }

  // 如果榜单被调整，那么需要重新将新的子榜实例加入需要生成镜像的队列中
  std::vector<TopNextSubRankInstance*>::iterator request_instance_it =
      std::find(need_image_sub_rank_from_request_.begin(), need_image_sub_rank_from_request_.end(), old_instance);
  if (request_instance_it != need_image_sub_rank_from_request_.end()) {
    need_image_sub_rank_from_request_.erase(request_instance_it);
    need_image_sub_rank_from_request_.push_back(new_instance);
  }

  instance_it = std::find(need_reduce_sub_rank_.begin(), need_reduce_sub_rank_.end(), old_instance);
  if (instance_it != need_reduce_sub_rank_.end()) {
    need_reduce_sub_rank_.erase(instance_it);
    need_reduce_sub_rank_.push_back(new_instance);
  }
  // 把CreateSubInstance内存实现的容量减掉
  if (!new_instance->is_image_) {
    --subrank_count_map_[old_instance->sub_rank_key_.type];
  }
  size_t old_size = old_instance->Total();
  size_t new_size = new_instance->Total();
  old_instance->Fini();
  TOPNEXT_RANK_BILL_LOG_INFO(
      "resize sub instance succ, sub_type:%u, sub_instance_id:%u, is_image:%d,"
      "old size:%lu, new size:%lu",
      new_instance->sub_rank_key_.type, new_instance->sub_rank_key_.instance_id, new_instance->is_image_, old_size,
      new_size);
  // 清理已经为空的group
  shm_group_mgr->ClearEmptyShmGroup();
}

// 0,无可做的事务
// 1,做了一些事情
int TopNextRankInstance::CpuTimeControlForImageAndReduce() {
  if (need_image_sub_rank_.empty() && need_reduce_sub_rank_.empty() && need_image_sub_rank_from_request_.empty()) {
    return 0;
  }
  // 流控, cpu busy
  if (1 && TopNextApp::Instance()->GetCpuTimeFlowControl().IsTimeToDoAction()) {
    TopNextApp::Instance()->GetCpuTimeFlowControl().StartAction();
    DoImageAndReduce();
    TopNextApp::Instance()->GetCpuTimeFlowControl().EndAction();
    return 1;
  }
  return 0;
}

void TopNextRankInstance::DoImageAndReduce() {
  if (!need_image_sub_rank_from_request_.empty()) {
    TopNextSubRankInstance* sub_rank_instance = need_image_sub_rank_from_request_.front();
    GenerateImageByRequest(sub_rank_instance);
    // 不管成功失败，跳到下一个
    need_image_sub_rank_from_request_.erase(need_image_sub_rank_from_request_.begin());
  }

  if (!need_image_sub_rank_.empty()) {
    TopNextSubRankInstance* sub_rank_instance = need_image_sub_rank_.front();
    GenerateImage(sub_rank_instance);
    // 不管成功失败，跳到下一个
    need_image_sub_rank_.erase(need_image_sub_rank_.begin());
  }

  if (!need_reduce_sub_rank_.empty()) {
    TopNextSubRankInstance* reduce_sub_rank_instance = need_reduce_sub_rank_.front();
    Reduce(reduce_sub_rank_instance);
    // 不管成功失败，跳到下一个
    need_reduce_sub_rank_.erase(need_reduce_sub_rank_.begin());
  }
}

void TopNextRankInstance::OnTickTimer() {
  if (need_resize_sub_rank_.size() > 0) {
    CpuTimeControlForResizeSubRank();
    return;
  }

  CpuTimeControlForImageAndReduce();
  image_crontab_timer_.Update();
  reduce_crontab_timer_.Update();

  if (!HasSubRankWaitImageAndReduce()) {
    uint32_t current_num = CacheOfRequestTotal();
    if (current_num > 0) {
      TOPNEXT_LOG_INFO("cache req num , current_num:%u, limit:%u", current_num,
                       TopNextApp::Instance()->Cfg()->ctrl_param.max_cache_req_num_on_image_and_reduce);
      std::vector<ReportSubRankCache>::iterator it_report;
      for (it_report = subrank_report_cache_.begin(); it_report != subrank_report_cache_.end();) {
        std::vector<int> codes;
        std::vector<int> rank_nos;
        RetInfo ret_info;
        ProcReportSubRank(it_report->data_source, it_report->from, it_report->user_info, it_report->sub_rank_keys,
                          codes, rank_nos, ret_info);
        subrank_report_cache_.erase(it_report);
        it_report = subrank_report_cache_.begin();
      }

      std::vector<ChangeSubRankCache>::iterator it_change;
      for (it_change = subrank_change_cache_.begin(); it_change != subrank_change_cache_.end();) {
        std::vector<int> old_codes;
        std::vector<int> new_codes;
        RetInfo ret_info;
        ProcChangeSubRank(it_change->user_info, it_change->old_sub_rank_keys, it_change->new_sub_rank_keys, old_codes,
                          new_codes, ret_info);
        subrank_change_cache_.erase(it_change);
        it_change = subrank_change_cache_.begin();
      }

      std::vector<ScoreCache>::iterator it_increase;
      for (it_increase = increase_score_cache_.begin(); it_increase != increase_score_cache_.end(); ++it_increase) {
        std::vector<int> codes;
        RetInfo ret_info;
        ProcIncreaseScore(it_increase->data_source, it_increase->from, it_increase->delta_score,
                          it_increase->no_need_update_ext, it_increase->user_info, it_increase->sub_rank_keys, codes,
                          ret_info);
        increase_score_cache_.erase(it_increase);
        it_increase = increase_score_cache_.begin();
      }

      std::vector<ScoreCache>::iterator it_decrease;
      for (it_decrease = decrease_score_cache_.begin(); it_decrease != decrease_score_cache_.end(); ++it_decrease) {
        std::vector<int> codes;
        RetInfo ret_info;
        ProcDecreaseScore(it_decrease->data_source, it_decrease->from, it_decrease->delta_score,
                          it_decrease->no_need_update_ext, it_decrease->user_info, it_decrease->sub_rank_keys, codes,
                          ret_info);
        decrease_score_cache_.erase(it_decrease);
        it_decrease = decrease_score_cache_.begin();
      }

      uint32_t new_current_num = CacheOfRequestTotal();
      TOPNEXT_LOG_INFO("cache req num,current_num:%u, limit:%u", new_current_num,
                       TopNextApp::Instance()->Cfg()->ctrl_param.max_cache_req_num_on_image_and_reduce);
    }
  }
}

int TopNextRankInstance::OnIdle() {
  if (need_resize_sub_rank_.size() != 0) {
    return CpuTimeControlForResizeSubRank();
  }
  return CpuTimeControlForImageAndReduce();
}

bool TopNextRankInstance::HasDoneGenerateImageAndReduce() {
  if (!need_resize_sub_rank_.empty()) {
    return false;
  }
  if (HasSubRankWaitImageAndReduce()) {
    return false;
  }
  if (!subrank_report_cache_.empty() || !subrank_change_cache_.empty()) {
    return false;
  }
  return true;
}

bool TopNextRankInstance::HasSubRankWaitImageAndReduce() {
  if (!need_image_sub_rank_.empty() || !need_reduce_sub_rank_.empty() || !need_image_sub_rank_from_request_.empty()) {
    return true;
  }
  return false;
}

void TopNextRankInstance::NeedGenerateImageForSubType(uint32_t timer_id, uint32_t sub_type) {
  int add_count = 0;
  std::vector<TopNextSubRankInstance*> sub_instances;
  GetSubRankList(sub_type, false, sub_instances);
  std::vector<TopNextSubRankInstance*>::iterator it;
  for (it = sub_instances.begin(); it != sub_instances.end(); ++it) {
    // 配置中该榜单需要镜像，但是通过api请求关闭镜像，那么跳过
    if ((*it)->rank_data_->head.image_switch == IMAGE_SWITCH_OFF) {
      continue;
    }
    need_image_sub_rank_.push_back(*it);
    ++add_count;
  }
  TOPNEXT_RANK_LOG_INFO("it is time to image for subtype:%u, count:%d", sub_type, add_count);
}

void TopNextRankInstance::NeedReduceForSubType(uint32_t timer_id) { AddToReduceQueue(false); }

void TopNextRankInstance::AddToReduceQueue(bool image) {
  std::vector<TopNextSubRankInstance*> sub_instances;
  int add_count = 0;
  GetSubRankList(image, sub_instances);
  std::vector<TopNextSubRankInstance*>::iterator it;
  for (it = sub_instances.begin(); it != sub_instances.end(); ++it) {
    need_reduce_sub_rank_.push_back(*it);
    ++add_count;
  }
  TOPNEXT_RANK_LOG_INFO("it is time to reduce, subrank count:%d", add_count);
}

int TopNextRankInstance::Reduce(TopNextSubRankInstance* real) {
  if (type_cfg_.use_reduce) {
    return real->Reduce();
  }
  return 0;
}

int TopNextRankInstance::GetSubRankShmGroupId(const topnext_app::SubRankKey& sub_rank_key, bool is_image,
                                              uint32_t& group_id) {
  if (shm_group_mgr_map_.find(sub_rank_key.type) == shm_group_mgr_map_.end()) {
    return -1;
  }
  TopNextSubRankShmGroupManager* shm_group_mgr = shm_group_mgr_map_[sub_rank_key.type];
  return shm_group_mgr->GetSubRankShmGroupId(sub_rank_key, is_image, group_id);
}

void TopNextRankInstance::GetSubRankList(bool is_image, std::vector<TopNextSubRankInstance*>& instances) {
  std::tr1::unordered_map<SubRankInstanceKey, TopNextSubRankInstance*, Hash, EqualTo>::iterator it;
  for (it = subrank_instance_map_.begin(); it != subrank_instance_map_.end(); ++it) {
    if (it->first.is_image == is_image) {
      instances.push_back(it->second);
    }
  }
}

void TopNextRankInstance::GetSubRankList(uint32_t sub_type, bool is_image,
                                         std::vector<TopNextSubRankInstance*>& instances) {
  std::tr1::unordered_map<SubRankInstanceKey, TopNextSubRankInstance*, Hash, EqualTo>::iterator it;
  for (it = subrank_instance_map_.begin(); it != subrank_instance_map_.end(); ++it) {
    if (it->first.is_image == is_image && it->first.sub_rank_key.type == sub_type) {
      instances.push_back(it->second);
    }
  }
}

void TopNextRankInstance::GetSubRankList(uint32_t sub_type, std::vector<TopNextSubRankInstance*>& instances) {
  std::tr1::unordered_map<SubRankInstanceKey, TopNextSubRankInstance*, Hash, EqualTo>::iterator it;
  for (it = subrank_instance_map_.begin(); it != subrank_instance_map_.end(); ++it) {
    if (it->first.sub_rank_key.type == sub_type) {
      instances.push_back(it->second);
    }
  }
}

void TopNextRankInstance::GetSubRankInstanceKeys(bool is_image,
                                                 std::vector<SubRankInstanceKey>& sub_rank_instance_keys) {
  for (auto& sub_rank_instance_key : subrank_instance_keys_) {
    if (is_image == sub_rank_instance_key.first.is_image) {
      sub_rank_instance_keys.push_back(sub_rank_instance_key.first);
    }
  }
}

static const uint32_t kDefaultSyncTimeAfterClean = 10;  // 10s

// 实现新增的辅助方法
bool TopNextRankInstance::ValidateUserInfo(const UserInfo& user_info, RetInfo& ret_info) {
  if (user_info.open_id[0] == '\0') {
    TOPNEXT_RANK_LOG_ERROR("req openid is empty");
    ret_info.iRet = APOLLO_TOPNEXT_SVR_OPEN_ID_IS_EMPTY;
    snprintf(ret_info.szMsg, MAX_RET_MSG_LEN, "req openid is empty");
    return false;
  }
  return true;
}

bool TopNextRankInstance::ValidateDataSource(int32_t data_source, int32_t from, RetInfo& ret_info) {
  if (REQ_DATA_SOURCE_IMAGE == data_source && REQ_CLIENT_FROM_INNER != from) {
    TOPNEXT_RANK_LOG_ERROR("report image data is not allow from api");
    ret_info.iRet = APOLLO_TOPNEXT_REQ_IS_NOT_ALLOWED;
    snprintf(ret_info.szMsg, MAX_RET_MSG_LEN, "report image data is not allow from api");
    return false;
  }
  return true;
}

bool TopNextRankInstance::HandleWaitingImageAndReduce(int32_t data_source, int32_t from, const UserInfo& user_info,
                                                      const std::vector<SubRankKey>& sub_rank_keys,
                                                      std::vector<int>& codes, std::vector<int>& rank_nos) {
  if (HasSubRankWaitImageAndReduce()) {
    for (size_t i = 0; i < sub_rank_keys.size(); ++i) {
      codes.push_back(APOLLO_TOPNEXT_SVR_SUCCESS);
      rank_nos.push_back(0);
    }

    uint32_t current_num = CacheOfRequestTotal();
    if (current_num > TopNextApp::Instance()->Cfg()->ctrl_param.max_cache_req_num_on_image_and_reduce) {
      TOPNEXT_LOG_DEBUG("cache req num exceed limit, current_num:%u, limit:%u", current_num,
                        TopNextApp::Instance()->Cfg()->ctrl_param.max_cache_req_num_on_image_and_reduce);
      return true;
    }

    ReportSubRankCache cache;
    cache.from = from;
    cache.data_source = data_source;
    cache.sub_rank_keys = sub_rank_keys;
    cache.user_info = user_info;
    subrank_report_cache_.push_back(cache);
    return true;
  }
  return false;
}

bool TopNextRankInstance::HandleSyncRequest(const SubRankKey& key, const UserInfo& user_info,
                                            TopNextSubRankInstance* sub_rank_inst, int32_t from,
                                            std::vector<int>& codes, std::vector<int>& rank_nos) {
  uint64_t current_sec = TimeCache::Instance()->CurrentSEC();
  uint32_t interval = TopNextApp::Instance()->Cfg()->sync_time_after_clean > 0
                          ? TopNextApp::Instance()->Cfg()->sync_time_after_clean
                          : kDefaultSyncTimeAfterClean;

  if ((uint32_t)abs(static_cast<int>(current_sec - sub_rank_inst->GetLastCleanTime())) < interval) {
    TOPNEXT_RANK_LOG_ERROR(
        "sync subrank failed because not ready to sync after clean,"
        "last_cleantime:%" PRIu64 ",current_time:%" PRIu64
        ",interval:%u,"
        "open_id:%s,score:%u,sub_type:%u,sub_instance_id:%u",
        sub_rank_inst->GetLastCleanTime(), current_sec, interval, user_info.open_id, user_info.score, key.type,
        key.instance_id);
    codes.push_back(APOLLO_TOPNEXT_SVR_REPORT_SUBRANK_FAIL);
    rank_nos.push_back(0);
    return false;
  }

  TOPNEXT_RANK_LOG_DEBUG(
      "sync subrank "
      "last_cleantime:%" PRIu64 ",current_time:%" PRIu64
      ",interval:%u,"
      "open_id:%s,score:%u,sub_type:%u,sub_instance_id:%u",
      sub_rank_inst->GetLastCleanTime(), current_sec, interval, user_info.open_id, user_info.score, key.type,
      key.instance_id);
  return true;
}

// ret为非0时, 只需要关注retinfo
int TopNextRankInstance::ProcReportSubRank(int32_t data_source, int32_t from, const UserInfo& user_info,
                                           const std::vector<SubRankKey>& sub_rank_keys, std::vector<int>& codes,
                                           std::vector<int>& rank_nos, RetInfo& ret_info) {
  if (!ValidateDataSource(data_source, from, ret_info) || !ValidateUserInfo(user_info, ret_info)) {
    TOPNEXT_RANK_LOG_ERROR("validate data source or user info failed");
    return -1;
  }

  if (HandleWaitingImageAndReduce(data_source, from, user_info, sub_rank_keys, codes, rank_nos)) {
    TOPNEXT_RANK_LOG_DEBUG("handle waiting image and reduce");
    return 0;
  }

  int ret = 0;
  // 子榜单
  std::vector<SubRankKey>::const_iterator it = sub_rank_keys.begin();
  for (; it != sub_rank_keys.end(); it++) {
    // 查找实时榜单
    TopNextSubRankInstance* sub_rank_inst = FindSubInstance(*it, data_source == REQ_DATA_SOURCE_IMAGE);
    if (NULL == sub_rank_inst) {
      ret = CreateSubInstanceIncludeImage(*it);
      if (0 != ret) {
        TOPNEXT_RANK_LOG_ERROR("create subrank fail, sub_type:%u, instance_id:%u", it->type, it->instance_id);
        codes.push_back(APOLLO_TOPNEXT_SVR_CREATE_SUBRANK_FAIL);
        rank_nos.push_back(0);
        continue;
      }
      sub_rank_inst = FindSubInstance(*it, data_source == REQ_DATA_SOURCE_IMAGE);
      if (NULL == sub_rank_inst) {
        TOPNEXT_RANK_LOG_ERROR("report subrank, invalid instance, sub_type:%u, instance_id:%u", it->type,
                               it->instance_id);
        codes.push_back(APOLLO_TOPNEXT_SVR_SUBRANK_NOT_EXISTS);
        rank_nos.push_back(0);
        continue;
      }
    }
    // 从check工具的同步请求,在清理一定时间内不进行同步
    if (from == REQ_CLIENT_FROM_INNER && !HandleSyncRequest(*it, user_info, sub_rank_inst, from, codes, rank_nos)) {
      continue;
    }

    int rank_pos = 0;
    ret = sub_rank_inst->Update(user_info, &rank_pos);
    if (0 != ret) {
      TOPNEXT_RANK_LOG_ERROR(
          "report subrank fail, open_id:%s, sub_type:%u, sub_instance_id:%u, "
          "ret:%d",
          user_info.open_id, user_info.score, it->type, it->instance_id);
      codes.push_back(APOLLO_TOPNEXT_SVR_REPORT_SUBRANK_FAIL);
      rank_nos.push_back(0);
      continue;
    }

    if (Loggable(TLOG_PRIORITY_DEBUG)) {
      TOPNEXT_RANK_LOG_DEBUG(
          "report subrank succ, open_id:%s, score:%u, sub_type:%u, "
          "sub_instance_id:%u, rank_pos:%d",
          user_info.open_id, user_info.score, it->type, it->instance_id, rank_pos);
    }
    codes.push_back(APOLLO_TOPNEXT_SVR_SUCCESS);
    rank_nos.push_back(rank_pos);
  }
  return 0;
}

int TopNextRankInstance::ProcChangeSubRank(UserInfo& user_info, const std::vector<SubRankKey>& old_sub_rank_keys,
                                           const std::vector<SubRankKey>& new_sub_rank_keys,
                                           std::vector<int>& old_codes, std::vector<int>& new_codes,
                                           RetInfo& ret_info) {
  int ret;
  if ('\0' == user_info.open_id[0]) {
    TOPNEXT_RANK_LOG_ERROR("req openid is empty");
    ret_info.iRet = APOLLO_TOPNEXT_SVR_OPEN_ID_IS_EMPTY;
    snprintf(ret_info.szMsg, topnext_proto_tdr::MAX_RET_MSG_LEN, "req openid is empty");
    return -1;
  }

  if (HasSubRankWaitImageAndReduce()) {
    std::vector<SubRankKey>::const_iterator it;
    for (it = old_sub_rank_keys.begin(); it != old_sub_rank_keys.end(); ++it) {
      old_codes.push_back(APOLLO_TOPNEXT_SVR_SUCCESS);  // todo
    }
    for (it = new_sub_rank_keys.begin(); it != new_sub_rank_keys.end(); ++it) {
      new_codes.push_back(APOLLO_TOPNEXT_SVR_SUCCESS);  // todo
    }
    uint32_t current_num = CacheOfRequestTotal();
    if (current_num > TopNextApp::Instance()->Cfg()->ctrl_param.max_cache_req_num_on_image_and_reduce) {
      TOPNEXT_LOG_ERROR("cache req num exceed limit, current_num:%u, limit:%u", current_num,
                        TopNextApp::Instance()->Cfg()->ctrl_param.max_cache_req_num_on_image_and_reduce);
      return 0;
    }
    ChangeSubRankCache cache;
    cache.new_sub_rank_keys = new_sub_rank_keys;
    cache.old_sub_rank_keys = old_sub_rank_keys;
    cache.user_info = user_info;
    subrank_change_cache_.push_back(cache);
    return 0;
  }

  std::vector<SubRankKey>::const_iterator it;
  // 从old子榜删除用户
  for (it = old_sub_rank_keys.begin(); it != old_sub_rank_keys.end(); ++it) {
    // 查找实时榜单
    TopNextSubRankInstance* sub_rank_inst = FindSubInstance(*it, false);
    if (NULL == sub_rank_inst) {
      TOPNEXT_RANK_LOG_DEBUG("chanage subrank, invalid instance, sub_type:%u, instance_id:%u", it->type,
                             it->instance_id);
      old_codes.push_back(APOLLO_TOPNEXT_SVR_SUBRANK_NOT_EXISTS);
      continue;
    }
    ret = sub_rank_inst->Remove(user_info.open_id);
    if (0 != ret) {
      TOPNEXT_RANK_LOG_ERROR("change subrank, remove user fail, open_id:%s, sub_type:%u, sub_instance_id:%u, ret:%d",
                             user_info.open_id, it->type, it->instance_id, ret);
      old_codes.push_back(APOLLO_TOPNEXT_SVR_REMOVE_USER_FAIL);
      continue;
    }
    TOPNEXT_RANK_LOG_DEBUG("change subrank, remove user succ, open_id:%s, sub_type:%u, sub_instance_id:%u",
                           user_info.open_id, it->type, it->instance_id);
    old_codes.push_back(APOLLO_TOPNEXT_SVR_SUCCESS);
  }
  // 从添加用户到new子榜
  for (it = new_sub_rank_keys.begin(); it != new_sub_rank_keys.end(); ++it) {
    // 查找实时榜单
    TopNextSubRankInstance* sub_rank_inst = FindSubInstance(*it, false);
    if (NULL == sub_rank_inst) {
      TOPNEXT_RANK_LOG_ERROR("chanage subrank, invalid instance, sub_type:%u, instance_id:%u", it->type,
                             it->instance_id);
      new_codes.push_back(APOLLO_TOPNEXT_SVR_SUBRANK_NOT_EXISTS);
      continue;
    }
    ret = sub_rank_inst->Update(user_info);
    if (0 != ret) {
      TOPNEXT_RANK_LOG_ERROR("change subrank, report user fail, open_id:%s, sub_type:%u, sub_instance_id:%u, ret:%d",
                             user_info.open_id, it->type, it->instance_id, ret);
      new_codes.push_back(APOLLO_TOPNEXT_SVR_REMOVE_USER_FAIL);
      continue;
    }
    TOPNEXT_RANK_LOG_DEBUG("change subrank, report user succ, open_id:%s, sub_type:%u, sub_instance_id:%u",
                           user_info.open_id, it->type, it->instance_id);
    new_codes.push_back(APOLLO_TOPNEXT_SVR_SUCCESS);
  }
  return 0;
}

int TopNextRankInstance::ProcIncreaseScore(int32_t data_source, int32_t from, uint32_t delta_score,
                                           bool no_need_update_ext, UserInfo& user_info,
                                           const std::vector<SubRankKey>& sub_rank_keys, std::vector<int>& codes,
                                           RetInfo& ret_info) {
  if (HasSubRankWaitImageAndReduce()) {
    std::vector<SubRankKey>::const_iterator it;
    for (it = sub_rank_keys.begin(); it != sub_rank_keys.end(); ++it) {
      codes.push_back(APOLLO_TOPNEXT_SVR_SUCCESS);  // todo
    }
    uint32_t current_num = CacheOfRequestTotal();
    if (current_num > TopNextApp::Instance()->Cfg()->ctrl_param.max_cache_req_num_on_image_and_reduce) {
      TOPNEXT_LOG_DEBUG("increase_score cache req num exceed limit, current_num:%u, limit:%u", current_num,
                        TopNextApp::Instance()->Cfg()->ctrl_param.max_cache_req_num_on_image_and_reduce);
      return 0;
    }
    ScoreCache cache;
    cache.from = from;
    cache.data_source = data_source;
    cache.delta_score = delta_score;
    cache.no_need_update_ext = no_need_update_ext;
    cache.user_info = user_info;
    cache.sub_rank_keys = sub_rank_keys;
    increase_score_cache_.push_back(cache);
    return 0;
  }

  if (!ValidateDataSource(data_source, from, ret_info) || !ValidateUserInfo(user_info, ret_info)) {
    TOPNEXT_RANK_LOG_ERROR("increase score, validate data source or user info failed");
    return -1;
  }

  int ret = 0;
  // 递增分数不使用用户结构体中带的分数
  user_info.score = 0;
  // 子榜单
  std::vector<SubRankKey>::const_iterator it = sub_rank_keys.begin();
  for (; it != sub_rank_keys.end(); ++it) {
    // 查找实时榜单
    TopNextSubRankInstance* sub_rank_inst = FindSubInstance(*it, data_source == REQ_DATA_SOURCE_IMAGE);
    if (NULL == sub_rank_inst) {
      ret = CreateSubInstanceIncludeImage(*it);
      if (0 != ret) {
        TOPNEXT_RANK_LOG_ERROR("create subrank fail, sub_type:%u, instance_id:%u", it->type, it->instance_id);
        codes.push_back(APOLLO_TOPNEXT_SVR_CREATE_SUBRANK_FAIL);
        continue;
      }
      sub_rank_inst = FindSubInstance(*it, data_source == REQ_DATA_SOURCE_IMAGE);
      if (NULL == sub_rank_inst) {
        TOPNEXT_RANK_LOG_ERROR("increase score, invalid instance,sub_type:%u, instance_id:%u", it->type,
                               it->instance_id);
        codes.push_back(APOLLO_TOPNEXT_SVR_SUBRANK_NOT_EXISTS);
        continue;
      }
    }
    UserRankInfo user_rank_info;
    ret = sub_rank_inst->QueryOne(user_info.open_id, user_rank_info);
    // 用户不存在,那么分数就等于递增的分数
    if (ret != 0 || user_rank_info.user_info == NULL) {
      user_info.score = delta_score;
    } else {
      // 识别旧分数
      user_info.score = user_rank_info.user_info->score;
      uint32_t new_score = user_info.score + delta_score;
      if (delta_score != 0 && (new_score < user_info.score || new_score < delta_score)) {
        new_score = UINT_MAX;
      }
      user_info.score = new_score;

      // 如果不需要更新extdata, 默认需要更新
      if (no_need_update_ext) {
        user_info.ext_data_len = user_rank_info.user_info->ext_data_len;
        if (user_info.ext_data_len > 0) {
          memcpy(user_info.ext_data_info, user_rank_info.user_info->ext_data_info, user_info.ext_data_len);
        }
      }
    }
    ret = sub_rank_inst->Update(user_info);
    if (0 != ret) {
      TOPNEXT_RANK_LOG_ERROR("increase score fail, open_id:%s, sub_type:%u, sub_instance_id:%u, ret:%d",
                             user_info.open_id, it->type, it->instance_id, ret);
      codes.push_back(APOLLO_TOPNEXT_SVR_REPORT_SUBRANK_FAIL);
      continue;
    }
    if (Loggable(TLOG_PRIORITY_DEBUG)) {
      TOPNEXT_RANK_LOG_DEBUG("increase score succ, open_id:%s, score:%u,sub_type:%u, sub_instance_id:%u",
                             user_info.open_id, user_info.score, it->type, it->instance_id);
    }
    codes.push_back(APOLLO_TOPNEXT_SVR_SUCCESS);
  }
  return 0;
}

int TopNextRankInstance::ProcDecreaseScore(int32_t data_source, int32_t from, uint32_t delta_score,
                                           bool no_need_update_ext, UserInfo& user_info,
                                           const std::vector<SubRankKey>& sub_rank_keys, std::vector<int>& codes,
                                           RetInfo& ret_info) {
  if (HasSubRankWaitImageAndReduce()) {
    std::vector<SubRankKey>::const_iterator it;
    for (it = sub_rank_keys.begin(); it != sub_rank_keys.end(); ++it) {
      codes.push_back(APOLLO_TOPNEXT_SVR_SUCCESS);  // TODO
    }
    uint32_t current_num = CacheOfRequestTotal();
    if (current_num > TopNextApp::Instance()->Cfg()->ctrl_param.max_cache_req_num_on_image_and_reduce) {
      TOPNEXT_LOG_DEBUG(
          "decrease_score cache req num exceed limit,"
          " current_num:%u, limit:%u",
          current_num, TopNextApp::Instance()->Cfg()->ctrl_param.max_cache_req_num_on_image_and_reduce);
      return 0;
    }
    ScoreCache cache;
    cache.from = from;
    cache.data_source = data_source;
    cache.delta_score = delta_score;
    cache.no_need_update_ext = no_need_update_ext;
    cache.user_info = user_info;
    cache.sub_rank_keys = sub_rank_keys;
    decrease_score_cache_.push_back(cache);
    return 0;
  }

  int ret = 0;
  // 递减分数不使用用户结构体中带的分数
  user_info.score = 0;
  // 子榜单
  std::vector<SubRankKey>::const_iterator it;
  for (it = sub_rank_keys.begin(); it != sub_rank_keys.end(); ++it) {
    // 查找实时榜单
    TopNextSubRankInstance* sub_rank_inst = FindSubInstance(*it, data_source == REQ_DATA_SOURCE_IMAGE);
    if (NULL == sub_rank_inst) {
      ret = CreateSubInstanceIncludeImage(*it);
      if (0 != ret) {
        TOPNEXT_RANK_LOG_ERROR("create subrank fail, sub_type:%u, instance_id:%u", it->type, it->instance_id);
        codes.push_back(APOLLO_TOPNEXT_SVR_CREATE_SUBRANK_FAIL);
        continue;
      }
      sub_rank_inst = FindSubInstance(*it, data_source == topnext_proto_tdr::REQ_DATA_SOURCE_IMAGE);
      if (NULL == sub_rank_inst) {
        TOPNEXT_RANK_LOG_ERROR("decrease score, invalid instance, sub_type:%u, instance_id:%u", it->type,
                               it->instance_id);
        codes.push_back(APOLLO_TOPNEXT_SVR_SUBRANK_NOT_EXISTS);
        continue;
      }
    }
    UserRankInfo user_rank_info;
    ret = sub_rank_inst->QueryOne(user_info.open_id, user_rank_info);
    // 用户不存在,那么分数怎么减都是0
    if (ret != 0 || user_rank_info.user_info == NULL) {
      user_info.score = 0;
    } else {
      // 识别旧分数
      user_info.score = user_rank_info.user_info->score;
      uint32_t new_score = user_info.score - delta_score;
      if (user_info.score < delta_score) {
        new_score = 0;
      }
      user_info.score = new_score;

      // 如果不需要更新extdata
      if (no_need_update_ext) {
        user_info.ext_data_len = user_rank_info.user_info->ext_data_len;
        if (user_info.ext_data_len > 0) {
          memcpy(user_info.ext_data_info, user_rank_info.user_info->ext_data_info, user_info.ext_data_len);
        }
      }
    }
    ret = sub_rank_inst->Update(user_info);
    if (0 != ret) {
      TOPNEXT_RANK_LOG_ERROR("decrease score fail, open_id:%s, sub_type:%u, sub_instance_id:%u, ret:%d",
                             user_info.open_id, it->type, it->instance_id, ret);
      codes.push_back(APOLLO_TOPNEXT_SVR_REPORT_SUBRANK_FAIL);
      continue;
    }
    if (Loggable(TLOG_PRIORITY_DEBUG)) {
      TOPNEXT_RANK_LOG_DEBUG(
          "decrease score succ, open_id:%s, score:%u,"
          "sub_type:%u, sub_instance_id:%u",
          user_info.open_id, user_info.score, it->type, it->instance_id);
    }
    codes.push_back(APOLLO_TOPNEXT_SVR_SUCCESS);
  }
  return 0;
}

int TopNextRankInstance::ProcGetTopSubRank(int32_t data_source, int32_t query_from, int32_t query_count,
                                           const SubRankKey& sub_rank_key, int32_t& total_count,
                                           int64_t& image_generate_ts, std::vector<UserRankInfo>& user_rank_infos,
                                           std::vector<int>& codes, RetInfo& ret_info) {
  TopNextSubRankInstance* sub_rank_inst = FindSubInstance(sub_rank_key, data_source == REQ_DATA_SOURCE_IMAGE);
  if (NULL == sub_rank_inst) {
    ret_info.iRet = APOLLO_TOPNEXT_SVR_SUBRANK_NOT_EXISTS;
    snprintf(ret_info.szMsg, MAX_RET_MSG_LEN,
             "gettop, subrank instance do not exists, sub_type:%u, sub_instance_id:%u, is_image:%d", sub_rank_key.type,
             sub_rank_key.instance_id, data_source);
    TOPNEXT_RANK_LOG_ERROR("%s", ret_info.szMsg);
    return -1;
  }

  // 临时版本，LGame来查询镜像的时候，如果is_tmp_version为2，则返回镜像正在生成
  if (10249 == rank_key_.business_id && data_source == REQ_DATA_SOURCE_IMAGE &&
      2 == TopNextApp::Instance()->Cfg()->is_tmp_version) {
    TOPNEXT_RANK_LOG_DEBUG("gettop, business_id:%u, sub_type:%u, sub_instance_id:%u, is_image:%d, is_tmp_version:%d",
                           rank_key_.business_id, sub_rank_key.type, sub_rank_key.instance_id, data_source,
                           TopNextApp::Instance()->Cfg()->is_tmp_version);
    ret_info.iRet = APOLLO_TOPNEXT_SVR_SUBRANK_IMAGE_OR_REDUCE;
    snprintf(ret_info.szMsg, MAX_RET_MSG_LEN, "image is running, sub_type:%u, sub_instance_id:%u, is_image:%d",
             sub_rank_key.type, sub_rank_key.instance_id, data_source);
    return -1;
  }

  int ret = sub_rank_inst->QueryTop(query_from, query_count, user_rank_infos);
  if (0 != ret) {
    ret_info.iRet = APOLLO_TOPNEXT_SVR_INTERNAL_GET_TOP_FAIL;
    snprintf(ret_info.szMsg, MAX_RET_MSG_LEN, "gettop failed, sub_type:%u, sub_instance_id:%u, ret:%d",
             sub_rank_key.type, sub_rank_key.instance_id, ret);
    TOPNEXT_RANK_LOG_ERROR("%s", ret_info.szMsg);
    return -1;
  }
  total_count = (int)sub_rank_inst->Used();
  image_generate_ts = 0;
  if (data_source == REQ_DATA_SOURCE_IMAGE) {
    image_generate_ts = sub_rank_inst->GetLastImageTime();
  }
  std::vector<UserRankInfo>::iterator it;
  for (it = user_rank_infos.begin(); it != user_rank_infos.end(); ++it) {
    codes.push_back(APOLLO_TOPNEXT_SVR_SUCCESS);
  }
  return 0;
}

int TopNextRankInstance::ProcGetSubRankListSubRank(int32_t data_source, int32_t query_from, int32_t query_count,
                                                   int32_t& total_count, std::vector<int>& codes,
                                                   std::vector<SubRankKey>& sub_rank_keys) {
  std::vector<TopNextSubRankInstance*> sub_instances;
  GetSubRankList((data_source == REQ_DATA_SOURCE_IMAGE), sub_instances);

  int start = 1;
  int count = 0;
  std::vector<TopNextSubRankInstance*>::iterator it;
  for (it = sub_instances.begin(); it != sub_instances.end(); ++it) {
    if (start++ < query_from) {  // 跳过query_from个
      continue;
    }

    if (count >= query_count) {  // 最多放query_count个结果
      break;
    }

    sub_rank_keys.push_back((*it)->sub_rank_key_);
    codes.push_back(APOLLO_TOPNEXT_SVR_SUCCESS);
    count++;
  }

  total_count = static_cast<int32_t>(sub_instances.size());

  return 0;
}

int TopNextRankInstance::ProcGetOneUserSubRank(int32_t data_source, const char* open_id,
                                               const std::vector<SubRankKey>& sub_rank_keys,
                                               std::vector<UserRankInfo>& user_rank_infos, std::vector<int>& codes,
                                               RetInfo& ret_info) {
  if (0 == open_id[0]) {
    TOPNEXT_RANK_LOG_ERROR("req openid is empty");
    ret_info.iRet = APOLLO_TOPNEXT_SVR_OPEN_ID_IS_EMPTY;
    snprintf(ret_info.szMsg, MAX_RET_MSG_LEN, "req openid is empty");
    return -1;
  }

  // 子榜单
  std::vector<SubRankKey>::const_iterator it;
  for (it = sub_rank_keys.begin(); it != sub_rank_keys.end(); ++it) {
    TopNextSubRankInstance* sub_rank_inst = FindSubInstance(*it, data_source == REQ_DATA_SOURCE_IMAGE);
    if (NULL == sub_rank_inst) {
      TOPNEXT_RANK_LOG_ERROR("getoneuser subrank, invalid instance, sub_type:%u, instance_id:%u", it->type,
                             it->instance_id);
      codes.push_back(APOLLO_TOPNEXT_SVR_SUBRANK_NOT_EXISTS);
      continue;
    }
    UserRankInfo user_rank_info;
    int ret = sub_rank_inst->QueryOne(open_id, user_rank_info);
    if (APOLLO_TOPNEXT_SVR_USER_NOT_EXISTS == ret) {
      TOPNEXT_RANK_LOG_ERROR("getoneuser subrank, user not exists, open_id:%s, sub_type:%u, sub_instance_id:%u, ret=%d",
                             open_id, it->type, it->instance_id, ret);
      codes.push_back(APOLLO_TOPNEXT_SVR_USER_NOT_EXISTS);
      continue;
    } else if (0 != ret) {
      TOPNEXT_RANK_LOG_ERROR("getoneuser subrank fail, open_id:%s, sub_type:%u, sub_instance_id:%u, ret:%d", open_id,
                             it->type, it->instance_id, ret);
      codes.push_back(APOLLO_TOPNEXT_SVR_INTERNAL_GET_ONE_USER_FAIL);
      continue;
    }
    TOPNEXT_RANK_LOG_DEBUG("getoneuser subrank succ, open_id:%s, sub_type:%u, sub_instance_id:%u, rankno=%d", open_id,
                           it->type, it->instance_id, user_rank_info.rank_no);
    codes.push_back(APOLLO_TOPNEXT_SVR_SUCCESS);
    user_rank_infos.push_back(user_rank_info);
  }
  return 0;
}

int TopNextRankInstance::ProcImageAndReduceStat(int32_t data_source, const std::vector<SubRankKey>& sub_rank_keys,
                                                std::vector<int>& codes, std::vector<int64_t>& image_times,
                                                std::vector<int64_t>& reduce_times) {
  std::vector<SubRankKey>::const_iterator it;
  for (it = sub_rank_keys.begin(); it != sub_rank_keys.end(); ++it) {
    // 查找实时榜单
    TopNextSubRankInstance* sub_rank_inst = FindSubInstance(*it, data_source == REQ_DATA_SOURCE_IMAGE);
    if (nullptr == sub_rank_inst) {
      TOPNEXT_RANK_LOG_ERROR("get image and reduce stat, invalid instance, sub_type:%u, instance_id:%u", it->type,
                             it->instance_id);
      codes.push_back(APOLLO_TOPNEXT_SVR_SUBRANK_NOT_EXISTS);
      image_times.push_back(0);
      reduce_times.push_back(0);
      continue;
    }
    TOPNEXT_RANK_LOG_DEBUG(
        "get image and reduce stat, sub_type:%u, instance_id:%u, last_image_time:%lu, last_reduce_time:%lu", it->type,
        it->instance_id, sub_rank_inst->GetLastImageTime(), sub_rank_inst->GetLastReduceTime());
    codes.push_back(APOLLO_TOPNEXT_SVR_SUCCESS);
    image_times.push_back((int64_t)(sub_rank_inst->GetLastImageTime() / 1000));
    reduce_times.push_back((int64_t)(sub_rank_inst->GetLastReduceTime() / 1000));
  }
  return 0;
}

int TopNextRankInstance::ProcGetOneUserMultiRankMultiSubRank(int32_t data_source, const char* open_id,
                                                             const std::vector<SubRankKey>& sub_rank_keys,
                                                             std::vector<int>& codes,
                                                             std::vector<UserRankInfo>& user_rank_infos,
                                                             RetInfo& ret_info) {
  if (0 == open_id[0]) {
    TOPNEXT_RANK_LOG_ERROR("req openid is empty");
    ret_info.iRet = APOLLO_TOPNEXT_SVR_OPEN_ID_IS_EMPTY;
    snprintf(ret_info.szMsg, topnext_proto_tdr::MAX_RET_MSG_LEN, "req openid is empty");
    return -1;
  }
  // 子榜单
  std::vector<SubRankKey>::const_iterator it;
  for (it = sub_rank_keys.begin(); it != sub_rank_keys.end(); ++it) {
    TopNextSubRankInstance* sub_rank_inst = FindSubInstance(*it, data_source == REQ_DATA_SOURCE_IMAGE);
    if (NULL == sub_rank_inst) {
      TOPNEXT_RANK_LOG_DEBUG(
          "getoneusermultirankmultisubrank invalid instance, sub_type:%u, "
          "instance_id:%u",
          it->type, it->instance_id);
      codes.push_back(APOLLO_TOPNEXT_SVR_SUBRANK_NOT_EXISTS);
      continue;
    }
    topnext_app::UserRankInfo user_rank_info;
    int ret = sub_rank_inst->QueryOne(open_id, user_rank_info);
    if (APOLLO_TOPNEXT_SVR_USER_NOT_EXISTS == ret) {
      codes.push_back(APOLLO_TOPNEXT_SVR_USER_NOT_EXISTS);
      continue;
    } else if (0 != ret) {
      TOPNEXT_RANK_LOG_ERROR(
          "getoneusermultirankmultisubrank fail, open_id:%s, sub_type:%u, "
          "sub_instance_id:%u, ret:%d",
          open_id, it->type, it->instance_id, ret);
      codes.push_back(APOLLO_TOPNEXT_SVR_INTERNAL_GET_ONE_USER_FAIL);
      continue;
    }
    TOPNEXT_RANK_LOG_DEBUG(
        "getoneusermultirankmultisubrank succ, open_id:%s, sub_type:%u, "
        "sub_instance_id:%u",
        open_id, it->type, it->instance_id);
    codes.push_back(APOLLO_TOPNEXT_SVR_SUCCESS);
    user_rank_infos.push_back(user_rank_info);
  }
  return 0;
}

int TopNextRankInstance::ProcClearOneUserSubRank(bool clear_all, const char* open_id,
                                                 const std::vector<SubRankKey>& sub_rank_keys_in,
                                                 std::vector<SubRankKey>& sub_rank_keys_out, std::vector<int>& codes) {
  int ret = 0;
  if (clear_all) {
    std::vector<TopNextSubRankInstance*> sub_instances;
    GetSubRankList(false, sub_instances);
    std::vector<TopNextSubRankInstance*>::iterator it;
    for (it = sub_instances.begin(); it != sub_instances.end(); ++it) {
      TopNextSubRankInstance* sub_rank_instance = *it;
      ret = sub_rank_instance->Remove(open_id);
      if (0 != ret) {
        TOPNEXT_RANK_LOG_ERROR("remove user fail, sub_type:%u, sub_instance_id:%u, open_id:%s",
                               sub_rank_instance->sub_rank_key_.type, sub_rank_instance->sub_rank_key_.instance_id,
                               open_id);
        codes.push_back(APOLLO_TOPNEXT_SVR_INTERNAL_REMOVE_USER_FAIL);
        sub_rank_keys_out.push_back(sub_rank_instance->sub_rank_key_);
        TOPNEXT_RANK_BILL_LOG_INFO("clear user fail.sub_type:%u,sub_instance_id:%u,open_id:%s",
                                   sub_rank_instance->sub_rank_key_.type, sub_rank_instance->sub_rank_key_.instance_id,
                                   open_id);
        continue;
      }
      TOPNEXT_RANK_BILL_LOG_INFO("clear user succ.sub_type:%u,sub_instance_id:%u,open_id:%s",
                                 sub_rank_instance->sub_rank_key_.type, sub_rank_instance->sub_rank_key_.instance_id,
                                 open_id);
    }
  } else {
    std::vector<SubRankKey>::const_iterator it;
    for (it = sub_rank_keys_in.begin(); it != sub_rank_keys_in.end(); ++it) {
      TopNextSubRankInstance* sub_rank_inst = FindSubInstance(*it, false);
      if (NULL == sub_rank_inst) {
        TOPNEXT_RANK_LOG_ERROR(
            "remove user subrank, invalid instance, sub_type:%u, "
            "instance_id:%u",
            it->type, it->instance_id);
        codes.push_back(APOLLO_TOPNEXT_SVR_SUBRANK_NOT_EXISTS);
        TOPNEXT_RANK_BILL_LOG_INFO(
            "clear user fail.invalid "
            "subinstance,sub_type:%u,sub_instance_id:%u,open_id:%s",
            it->type, it->instance_id, open_id);
        continue;
      }
      ret = sub_rank_inst->Remove(open_id);
      if (0 != ret) {
        TOPNEXT_RANK_LOG_ERROR("remove user fail, sub_type:%u, sub_instance_id:%u, open_id:%s", it->type,
                               it->instance_id, open_id);
        codes.push_back(APOLLO_TOPNEXT_SVR_INTERNAL_REMOVE_USER_FAIL);
        TOPNEXT_RANK_BILL_LOG_INFO("clear user fail.sub_type:%u,sub_instance_id:%u,open_id:%s",
                                   sub_rank_inst->sub_rank_key_.type, sub_rank_inst->sub_rank_key_.instance_id,
                                   open_id);
        continue;
      }
      codes.push_back(APOLLO_TOPNEXT_SVR_SUCCESS);
      TOPNEXT_RANK_BILL_LOG_INFO("clear user succ.sub_type:%u,sub_instance_id:%u,open_id:%s",
                                 sub_rank_inst->sub_rank_key_.type, sub_rank_inst->sub_rank_key_.instance_id, open_id);
    }
  }
  return 0;
}

int TopNextRankInstance::ProcClearSubRank(int32_t data_source, bool clear_all,
                                          const std::vector<SubRankKey>& sub_rank_keys_in,
                                          std::vector<SubRankKey>& sub_rank_keys_out, std::vector<int>& codes) {
  int ret = 0;
  // 支持清理镜像榜
  bool image = data_source;
  if (clear_all) {
    std::vector<TopNextSubRankInstance*> sub_instances;
    GetSubRankList(image, sub_instances);
    std::vector<TopNextSubRankInstance*>::iterator it;
    for (it = sub_instances.begin(); it != sub_instances.end(); ++it) {
      TopNextSubRankInstance* sub_rank_instance = *it;
      ret = (*it)->Reset();
      if (0 != ret) {
        TOPNEXT_RANK_LOG_ERROR("reset subrank fail, sub_type:%u, sub_instance_id:%u",
                               sub_rank_instance->sub_rank_key_.type, sub_rank_instance->sub_rank_key_.instance_id);
        TOPNEXT_RANK_BILL_LOG_INFO("clear subrank fail,sub_type:%u,sub_instance_id:%u,image:%d",
                                   sub_rank_instance->sub_rank_key_.type, sub_rank_instance->sub_rank_key_.instance_id,
                                   data_source);
        codes.push_back(APOLLO_TOPNEXT_SVR_INTERNAL_RESET_FAIL);
        sub_rank_keys_out.push_back((*it)->sub_rank_key_);
        continue;
      }
      sub_rank_instance->SetLastCleanTime(TimeCache::Instance()->CurrentSEC());
      TOPNEXT_RANK_BILL_LOG_INFO("clear subrank succ,sub_type:%u,sub_instance_id:%u,image:%d",
                                 sub_rank_instance->sub_rank_key_.type, sub_rank_instance->sub_rank_key_.instance_id,
                                 data_source);
    }
  } else {
    std::vector<SubRankKey>::const_iterator it;
    for (it = sub_rank_keys_in.begin(); it != sub_rank_keys_in.end(); ++it) {
      TopNextSubRankInstance* sub_rank_instance = FindSubInstance(*it, image);
      if (NULL == sub_rank_instance) {
        TOPNEXT_RANK_LOG_ERROR("reset subrank, invalid instance, sub_type:%u, instance_id:%u", it->type,
                               it->instance_id);
        codes.push_back(APOLLO_TOPNEXT_SVR_SUBRANK_NOT_EXISTS);
        TOPNEXT_RANK_BILL_LOG_INFO(
            "clear subrank fail,invalid "
            "subinstance.sub_type:%u,sub_instance_id:%u,image:%d",
            it->type, it->instance_id, data_source);
        continue;
      }
      ret = sub_rank_instance->Reset();
      if (0 != ret) {
        TOPNEXT_RANK_LOG_ERROR("reset subrank fail, sub_type:%u, sub_instance_id:%u", it->type, it->instance_id);
        codes.push_back(APOLLO_TOPNEXT_SVR_INTERNAL_RESET_FAIL);
        TOPNEXT_RANK_BILL_LOG_INFO("clear subrank fail.sub_type:%u,sub_instance_id:%u,image:%d",
                                   sub_rank_instance->sub_rank_key_.type, sub_rank_instance->sub_rank_key_.instance_id,
                                   data_source);
        continue;
      }
      TOPNEXT_RANK_BILL_LOG_INFO("clear subrank succ.sub_type:%u,sub_instance_id:%u,image:%d",
                                 sub_rank_instance->sub_rank_key_.type, sub_rank_instance->sub_rank_key_.instance_id,
                                 data_source);
      sub_rank_instance->SetLastCleanTime(TimeCache::Instance()->CurrentSEC());
      codes.push_back(APOLLO_TOPNEXT_SVR_SUCCESS);
    }
  }
  return 0;
}

int TopNextRankInstance::ProcInnerGetSubRankListSubRank(int32_t data_source, std::vector<SubRankKey>& sub_rank_keys) {
  std::vector<TopNextSubRankInstance*> sub_instances;
  GetSubRankList((data_source == REQ_DATA_SOURCE_IMAGE), sub_instances);
  std::vector<TopNextSubRankInstance*>::iterator it;
  for (it = sub_instances.begin(); it != sub_instances.end(); ++it) {
    sub_rank_keys.push_back((*it)->sub_rank_key_);
  }
  return 0;
}

int TopNextRankInstance::ProcInnerGetSubRankListSubRank(int32_t data_source, int32_t query_from, int32_t query_count,
                                                        int32_t& total_count, std::vector<SubRankKey>& sub_rank_keys) {
  std::vector<SubRankInstanceKey> sub_rank_instance_keys;
  GetSubRankInstanceKeys((data_source == REQ_DATA_SOURCE_IMAGE), sub_rank_instance_keys);
  total_count = sub_rank_instance_keys.size();

  // 校验 query_from 的正确性
  if (query_from < 1) {
    TOPNEXT_RANK_LOG_ERROR("Invalid query_from: %d. It should be greater than or equal to 1.", query_from);
    return -1;  // 返回错误码
  }

  // 由于query_from从1开始，我们需要减1来匹配0-based索引
  int32_t start_index = query_from - 1;

  // 确保start_index不超过总数
  if (start_index >= total_count) {
    return 0;  // 没有结果可返回
  }

  // 计算实际可以返回的数量
  int32_t available_count = std::min(query_count, total_count - start_index);

  // 从start_index开始，添加available_count个结果到sub_rank_keys
  for (int32_t i = start_index; i < start_index + available_count; ++i) {
    sub_rank_keys.push_back(sub_rank_instance_keys[i].sub_rank_key);
  }

  return 0;
}

int TopNextRankInstance::ProcGetAndClearOneUserSubRank(bool clear_all, const char* open_id,
                                                       const std::vector<SubRankKey>& sub_rank_keys_in,
                                                       std::vector<SubRankKey>& sub_rank_keys_out,
                                                       std::vector<UserRankInfo>& user_rank_infos,
                                                       std::vector<int>& codes, RetInfo& ret_info) {
  if ('\0' == open_id[0]) {
    TOPNEXT_RANK_LOG_ERROR("req openid is empty");
    ret_info.iRet = APOLLO_TOPNEXT_SVR_OPEN_ID_IS_EMPTY;
    snprintf(ret_info.szMsg, topnext_proto_tdr::MAX_RET_MSG_LEN, "req openid is empty");
    return -1;
  }
  int query_ret = 0;
  int remove_ret = 0;
  if (clear_all) {
    std::vector<TopNextSubRankInstance*> sub_instances;
    GetSubRankList(false, sub_instances);
    std::vector<TopNextSubRankInstance*>::iterator it;
    for (it = sub_instances.begin(); it != sub_instances.end(); ++it) {
      TopNextSubRankInstance* sub_rank_instance = *it;
      UserRankInfo user_rank_info;
      query_ret = sub_rank_instance->QueryOne(open_id, user_rank_info);
      if (query_ret == 0) {
        // 获取排名成功
        TOPNEXT_RANK_LOG_DEBUG(
            "getoneuser subrank succ, open_id:%s, sub_type:%u, "
            "sub_instance_id:%u",
            open_id, sub_rank_instance->sub_rank_key_.type, sub_rank_instance->sub_rank_key_.instance_id);
        remove_ret = sub_rank_instance->Remove(open_id);
        if (remove_ret == 0) {
          // 清除用户排名成功
          TOPNEXT_RANK_BILL_LOG_INFO("Getandclear user succ.sub_type:%u,sub_instance_id:%u,open_id:%s",
                                     sub_rank_instance->sub_rank_key_.type,
                                     sub_rank_instance->sub_rank_key_.instance_id, open_id);
        } else {
          // 清除用户排名失败
          TOPNEXT_RANK_LOG_ERROR("remove user fail, sub_type:%u, sub_instance_id:%u, open_id:%s",
                                 sub_rank_instance->sub_rank_key_.type, sub_rank_instance->sub_rank_key_.instance_id,
                                 open_id);
          codes.push_back(APOLLO_TOPNEXT_SVR_INTERNAL_REMOVE_USER_FAIL);
          sub_rank_keys_out.push_back(sub_rank_instance->sub_rank_key_);
          TOPNEXT_RANK_BILL_LOG_INFO("clear user fail.sub_type:%u,sub_instance_id:%u,open_id:%s",
                                     sub_rank_instance->sub_rank_key_.type,
                                     sub_rank_instance->sub_rank_key_.instance_id, open_id);
        }
      } else if (APOLLO_TOPNEXT_SVR_USER_NOT_EXISTS == query_ret) {
        // 获取排名失败
        codes.push_back(APOLLO_TOPNEXT_SVR_USER_NOT_EXISTS);
        sub_rank_keys_out.push_back(sub_rank_instance->sub_rank_key_);
      } else {
        // 获取排名失败
        TOPNEXT_RANK_LOG_ERROR(
            "getoneuser subrank fail, open_id:%s, sub_type:%u, "
            "sub_instance_id:%u, ret:%d",
            open_id, sub_rank_instance->sub_rank_key_.type, sub_rank_instance->sub_rank_key_.instance_id, query_ret);
        codes.push_back(APOLLO_TOPNEXT_SVR_INTERNAL_GET_ONE_USER_FAIL);
        sub_rank_keys_out.push_back(sub_rank_instance->sub_rank_key_);
      }
    }
  } else {
    std::vector<SubRankKey>::const_iterator it;
    for (it = sub_rank_keys_in.begin(); it != sub_rank_keys_in.end(); ++it) {
      TopNextSubRankInstance* sub_rank_instance = FindSubInstance(*it, false);
      // 获取榜单实例失败
      if (NULL == sub_rank_instance) {
        TOPNEXT_RANK_LOG_ERROR("getoneuser subrank, invalid instance, sub_type:%u, instance_id:%u", it->type,
                               it->instance_id);
        codes.push_back(APOLLO_TOPNEXT_SVR_SUBRANK_NOT_EXISTS);
        continue;
      }
      topnext_app::UserRankInfo user_rank_info;
      query_ret = sub_rank_instance->QueryOne(open_id, user_rank_info);
      if (query_ret == 0) {
        // 获取排名成功
        TOPNEXT_RANK_LOG_DEBUG(
            "getoneuser subrank succ, open_id:%s, sub_type:%u, "
            "sub_instance_id:%u",
            open_id, it->type, it->instance_id);
        remove_ret = sub_rank_instance->Remove(open_id);
        if (remove_ret == 0) {
          // 删除用户榜单信息成功
          TOPNEXT_RANK_BILL_LOG_INFO("Getandclear user succ.sub_type:%u,sub_instance_id:%u,open_id:%s", it->type,
                                     it->instance_id, open_id);
          codes.push_back(APOLLO_TOPNEXT_SVR_SUCCESS);
          user_rank_infos.push_back(user_rank_info);
        } else {
          // 删除用户信息失败
          TOPNEXT_RANK_LOG_ERROR("remove user fail, sub_type:%u, sub_instance_id:%u, open_id:%s", it->type,
                                 it->instance_id, open_id);
          codes.push_back(APOLLO_TOPNEXT_SVR_INTERNAL_REMOVE_USER_FAIL);
          TOPNEXT_RANK_BILL_LOG_INFO("clear user fail.sub_type:%u,sub_instance_id:%u,open_id:%s", it->type,
                                     it->instance_id, open_id);
        }
      } else if (APOLLO_TOPNEXT_SVR_USER_NOT_EXISTS == query_ret) {
        TOPNEXT_RANK_LOG_ERROR("getandclear user fail. ret=%d, sub_type:%u,sub_instance_id:%u,open_id:%s", query_ret,
                               it->type, it->instance_id, open_id);
        codes.push_back(APOLLO_TOPNEXT_SVR_USER_NOT_EXISTS);
      } else {
        // 获取排名失败
        TOPNEXT_RANK_LOG_ERROR(
            "getoneuser subrank fail, open_id:%s, sub_type:%u, "
            "sub_instance_id:%u, ret:%d",
            open_id, sub_rank_instance->sub_rank_key_.type, sub_rank_instance->sub_rank_key_.instance_id, query_ret);
        codes.push_back(APOLLO_TOPNEXT_SVR_INTERNAL_GET_ONE_USER_FAIL);
      }
    }
  }
  return 0;
}

int TopNextRankInstance::ProcSetUserExtField(int32_t modify_flags, const char* open_id, int64_t ext_field1,
                                             int64_t ext_field2, int64_t ext_field3,
                                             const std::vector<SubRankKey>& sub_rank_keys, std::vector<int>& codes) {
  int queryone_ret = 0;
  int replace_ret = 0;
  std::vector<SubRankKey>::const_iterator it;
  for (it = sub_rank_keys.begin(); it != sub_rank_keys.end(); ++it) {
    TopNextSubRankInstance* sub_rank_inst = FindSubInstance(*it, false);
    if (NULL == sub_rank_inst) {
      TOPNEXT_RANK_LOG_ERROR("getoneuser subrank, invalid instance, sub_type:%u, instance_id:%u", it->type,
                             it->instance_id);
      codes.push_back(APOLLO_TOPNEXT_SVR_SUBRANK_NOT_EXISTS);
      continue;
    }
    topnext_app::UserRankInfo user_rank_info;
    queryone_ret = sub_rank_inst->QueryOne(open_id, user_rank_info);
    if (queryone_ret == 0) {
      // 获取排名成功
      TOPNEXT_RANK_LOG_DEBUG("getoneuser subrank succ, open_id:%s, sub_type:%u, sub_instance_id:%u", open_id, it->type,
                             it->instance_id);

      // 设定user的ext field
      switch (modify_flags) {
        case 1:
          const_cast<UserInfo*>(user_rank_info.user_info)->reduce_timestamp = ext_field1;
          break;
        case 2:
          const_cast<UserInfo*>(user_rank_info.user_info)->ext_field2 = ext_field2;
          break;
        case 4:
          const_cast<UserInfo*>(user_rank_info.user_info)->ext_field3 = ext_field3;
          break;
        case 3:
          const_cast<UserInfo*>(user_rank_info.user_info)->reduce_timestamp = ext_field1;
          const_cast<UserInfo*>(user_rank_info.user_info)->ext_field2 = ext_field2;
          break;
        case 6:
          const_cast<UserInfo*>(user_rank_info.user_info)->ext_field2 = ext_field2;
          const_cast<UserInfo*>(user_rank_info.user_info)->ext_field3 = ext_field3;
          break;
        case 7:
          const_cast<UserInfo*>(user_rank_info.user_info)->reduce_timestamp = ext_field1;
          const_cast<UserInfo*>(user_rank_info.user_info)->ext_field2 = ext_field2;
          const_cast<UserInfo*>(user_rank_info.user_info)->ext_field3 = ext_field3;
          break;
        default:
          TOPNEXT_RANK_LOG_ERROR();
      }

      replace_ret = sub_rank_inst->Replace(*user_rank_info.user_info);
      if (0 != replace_ret) {
        TOPNEXT_RANK_LOG_ERROR(
            "set user extfield, replace user failed, open_id:%s, sub_type:%u, "
            "sub_instance_id:%u, ret:%d",
            user_rank_info.user_info->open_id, it->type, it->instance_id, replace_ret);
        codes.push_back(APOLLO_TOPNEXT_SVR_SET_EXTFIELD_FAIL);
        continue;
      }
      TOPNEXT_RANK_LOG_DEBUG("set user extfield, replace user succ, open_id:%s, sub_type:%u, sub_instance_id:%u",
                             user_rank_info.user_info->open_id, it->type, it->instance_id);
      codes.push_back(APOLLO_TOPNEXT_SVR_SUCCESS);
    } else if (APOLLO_TOPNEXT_SVR_USER_NOT_EXISTS == queryone_ret) {
      codes.push_back(APOLLO_TOPNEXT_SVR_USER_NOT_EXISTS);
    } else {
      // 获取排名失败
      TOPNEXT_RANK_LOG_ERROR("getoneuser subrank fail, open_id:%s, sub_type:%u, sub_instance_id:%u, ret:%d", open_id,
                             sub_rank_inst->sub_rank_key_.type, sub_rank_inst->sub_rank_key_.instance_id, queryone_ret);
      codes.push_back(APOLLO_TOPNEXT_SVR_INTERNAL_GET_ONE_USER_FAIL);
    }
  }

  return 0;
}

int TopNextRankInstance::ProcGenerateSubRankImage(uint64_t image_sequence, bool clean, bool close_image_switch,
                                                  bool generate_all, const std::vector<SubRankKey>& sub_rank_keys_in,
                                                  std::vector<SubRankKey>& sub_rank_keys_out, std::vector<int>& codes,
                                                  std::vector<int64_t>& image_sequences, RetInfo& ret_info) {
  SubRankKey empty_subrank_key;
  empty_subrank_key.type = 0;
  empty_subrank_key.instance_id = 0;

  image_sequences.push_back(image_sequence);

  if (!HasSubRankWaitImageAndReduce()) {
    if (generate_all == true) {
      TOPNEXT_RANK_LOG_DEBUG("generate all subrank image");

      // 检查子榜是否全部打开了镜像开关
      bool cfg_has_error = false;
      for (uint32_t i = 0; i < type_cfg_.sub_type_cfg_num; i++) {
        if (type_cfg_.sub_type_cfg[i].use_image != 1) {
          TOPNEXT_RANK_LOG_ERROR(
              "generate sub rank image failed, subtype's image swith in cfg is off, invalid "
              "sub_type:%u",
              type_cfg_.sub_type_cfg[i].type);
          SubRankKey sub_rank_key;
          sub_rank_key.type = type_cfg_.sub_type_cfg[i].type;
          sub_rank_key.instance_id = 0;
          // AddSubRankOpResult(APOLLO_TOPNEXT_SVR_SUBRANK_IMAGE_IS_CLOSE_IN_CFG, sub_rank_key, rsp.op_result);
          codes.push_back(APOLLO_TOPNEXT_SVR_SUBRANK_IMAGE_IS_CLOSE_IN_CFG);
          sub_rank_keys_out.push_back(sub_rank_key);
          cfg_has_error = true;
          break;
        }
      }
      if (cfg_has_error == true) {
        // ret_info.iRet = APOLLO_TOPNEXT_SVR_SUBRANK_IMAGE_IS_CLOSE_IN_CFG;
        // snprintf(ret_info.szMsg, MAX_RET_MSG_LEN, "have subrank image switch is off in cfg");
        return 0;
      }

      std::vector<TopNextSubRankInstance*> real_sub_instances;
      GetSubRankList(false, real_sub_instances);
      if (real_sub_instances.empty()) {
        TOPNEXT_RANK_LOG_INFO("real subrank instances is empty");
        // AddSubRankOpResult(APOLLO_TOPNEXT_SVR_SUBRANK_IMAGE_IS_EMPTY, empty_subrank_key, rsp.op_result);
        codes.push_back(APOLLO_TOPNEXT_SVR_SUBRANK_IMAGE_IS_EMPTY);
        sub_rank_keys_out.push_back(empty_subrank_key);
        return 0;
      }

      std::vector<TopNextSubRankInstance*>::iterator it;
      TopNextSubRankInstance* image_instance = nullptr;
      for (it = real_sub_instances.begin(); it != real_sub_instances.end(); ++it) {
        TopNextSubRankInstance* real_instance = *it;

        // 查找镜像榜单
        image_instance = FindSubInstance(real_instance->sub_rank_key_, true);
        if (NULL == image_instance) {
          // AddSubRankOpResult(APOLLO_TOPNEXT_SVR_SUBRANK_NOT_EXISTS, real_instance->sub_rank_key_, rsp.op_result);
          codes.push_back(APOLLO_TOPNEXT_SVR_SUBRANK_NOT_EXISTS);
          sub_rank_keys_out.push_back(real_instance->sub_rank_key_);
          TOPNEXT_RANK_LOG_ERROR("generate sub rank image failed, invalid sub instance, sub_type:%u, instance_id:%u",
                                 real_instance->sub_rank_key_.type, real_instance->sub_rank_key_.instance_id);
          return 0;
        }

        // 在镜像榜中保存镜像相关状态
        image_instance->rank_data_->head.image_sequence = image_sequence;                 // 保存镜像的sequence
        image_instance->rank_data_->head.clean_real_after_image = clean == true ? 1 : 0;  // 镜像完数据后清楚实时榜单
        image_instance->rank_data_->head.image_status = IMAGE_GENERATING;                 // 镜像生成中

        // 关闭配置文件中的定时自动生成镜像开关
        if (close_image_switch) {
          image_instance->rank_data_->head.image_switch = IMAGE_SWITCH_OFF;
          real_instance->rank_data_->head.image_switch = IMAGE_SWITCH_OFF;
        }

        // 在实时榜中也保存镜像相关状态
        real_instance->rank_data_->head.image_sequence = image_sequence;
        real_instance->rank_data_->head.clean_real_after_image = clean == true ? 1 : 0;  // 镜像完数据后清楚实时榜单
        real_instance->rank_data_->head.image_status = IMAGE_GENERATING;                 // 镜像生成中

        // 找到镜像实例
        // 加入需要生成镜像的队列中
        need_image_sub_rank_from_request_.push_back(real_instance);
      }

      TOPNEXT_RANK_LOG_DEBUG("generate all subrank image success, image_sequence: %lu", image_sequence);
      SubRankKey subrank_key;
      subrank_key.type = 0;
      subrank_key.instance_id = 0;
      codes.push_back(APOLLO_TOPNEXT_SVR_SUCCESS);
      sub_rank_keys_out.push_back(subrank_key);
      // AddSubRankOpResult(APOLLO_TOPNEXT_SVR_SUCCESS, subrank_key, rsp.op_result);
    } else {
      TOPNEXT_RANK_LOG_DEBUG("generate some subrank image");
      TOPNEXT_RANK_LOG_INFO("generate_all = false is not complete");
      codes.push_back(APOLLO_TOPNEXT_SVR_INTERNAL_ERROR);
      sub_rank_keys_out.push_back(empty_subrank_key);
      // AddSubRankOpResult(APOLLO_TOPNEXT_SVR_INTERNAL_ERROR, empty_subrank_key, rsp.op_result);
    }
  } else {
    // 该榜单有子榜即将镜像或者衰减
    codes.push_back(APOLLO_TOPNEXT_SVR_SUBRANK_IMAGE_OR_REDUCE);
    sub_rank_keys_out.push_back(empty_subrank_key);
    TOPNEXT_LOG_DEBUG("has sub rank wait image or reduce");
    // AddSubRankOpResult(APOLLO_TOPNEXT_SVR_SUBRANK_IMAGE_OR_REDUCE, empty_subrank_key, rsp.op_result);
  }

  return 0;
}
int TopNextRankInstance::ProcGetSubRankImageStatus(bool get_all, const std::vector<SubRankKey>& sub_rank_keys_in,
                                                   SubRankKey& sub_rank_key_out, uint64_t& image_sequence,
                                                   int32_t& image_status, uint64_t& image_generate_ts,
                                                   uint32_t& image_switch, int& code, RetInfo& ret_info) {
  if (get_all) {
    TOPNEXT_RANK_LOG_DEBUG("get all subrank image status");

    // 检查子榜类型是否全部打开了镜像开关，有子榜类型未开启镜像功能，返回失败
    std::map<uint32_t, int32_t> image_switchs;
    // 检查子榜是否全部打开了镜像开关
    bool cfg_has_error = false;
    for (uint32_t i = 0; i < type_cfg_.sub_type_cfg_num; i++) {
      image_switchs[type_cfg_.sub_type_cfg[i].type] = type_cfg_.sub_type_cfg[i].use_image;
      if (type_cfg_.sub_type_cfg[i].use_image != 1) {
        TOPNEXT_RANK_LOG_ERROR(
            "get sub rank image status failed, subtype's image swith in cfg is off, invalid "
            "sub_type:%u",
            type_cfg_.sub_type_cfg[i].type);
        sub_rank_key_out.type = type_cfg_.sub_type_cfg[i].type;
        sub_rank_key_out.instance_id = 0;
        // AddSubRankOpResult(APOLLO_TOPNEXT_SVR_SUBRANK_IMAGE_IS_CLOSE_IN_CFG, sub_rank_key, rsp.op_result);
        code = APOLLO_TOPNEXT_SVR_SUBRANK_IMAGE_IS_CLOSE_IN_CFG;
        cfg_has_error = true;
        break;
      }
    }
    if (cfg_has_error == true) {
      // ret_info.iRet = APOLLO_TOPNEXT_SVR_SUBRANK_IMAGE_IS_CLOSE_IN_CFG;
      // snprintf(ret_info.szMsg, MAX_RET_MSG_LEN, "have subrank image switch is off in cfg");
      return 0;
    }

    // get_all，获取该榜单的所有子榜（所有类型，所有实例）
    // 如果同类型下有子榜实例没有生成过镜像或者即包含cfg生成的榜单又包含通过API生成的榜单，只需本地记录。返回API请求镜像的结果
    // 只有某个类型的所有子榜实例都没有生成过镜像。则需要返回给API

    // 当get_all为true时，有一个子榜失败则视为失败
    // 如果子榜中有image_sequence不一样（通常是有通过api请求生成的镜像，也有通过cfg生成的镜像[image_sequence=0])

    std::vector<TopNextSubRankInstance*> sub_instances;
    GetSubRankList(true, sub_instances);
    if (sub_instances.empty()) {
      // 该榜单没有子榜镜像实例，可能是因为配置中关闭镜像
      TOPNEXT_RANK_LOG_INFO("image sub instances is empty");
      code = APOLLO_TOPNEXT_SVR_SUBRANK_IMAGE_IS_EMPTY;
      sub_rank_key_out.type = 0;
      sub_rank_key_out.instance_id = 0;
      // AddImageStatusResult(APOLLO_TOPNEXT_SVR_SUBRANK_IMAGE_IS_EMPTY, empty_subrank_key, 0, 0, 0, 0,
      // rsp.status_result);
      return 0;
    }

    TopNextSubRankInstance* image_instance = NULL;
    std::set<uint64_t> image_sequences;
    image_sequence = 0;
    image_status = IMAGE_INIT;
    image_generate_ts = 0;
    image_switch = IMAGE_SWITCH_INIT;

    std::map<uint32_t, uint32_t> sub_type_instance_num;  // sub_rank_type -> instance num
    std::map<uint32_t, uint32_t> sub_type_image_status;  // sub_rank_type -> image_status
    std::map<uint32_t, uint32_t> sub_type_image_switch;  // sub_rank_type -> image_switch

    std::vector<TopNextSubRankInstance*>::iterator it;
    for (it = sub_instances.begin(); it != sub_instances.end(); ++it) {
      image_instance = *it;

      // 有一个实例在生成中就表示整个榜单在生成中，有一个实例生成失败就代表整个榜单生成失败
      if (image_instance->rank_data_->head.image_status == IMAGE_GENERATE_FAIL ||
          image_instance->rank_data_->head.image_status == IMAGE_GENERATING) {
        // 有一子榜的镜像生成失败或者正在生成中，则直接返回镜像状态
        TOPNEXT_RANK_LOG_ERROR(
            "get sub rank image status success, image is generating or generate faile, image status: %d "
            "subinstance.sub_type:%u,sub_instance_id:%u",
            image_instance->rank_data_->head.image_status, image_instance->sub_rank_key_.type,
            image_instance->sub_rank_key_.instance_id);

        // image_status = IMAGE_GENETATE_FAIL 或者 IMAGE_GENERATING，镜像生成失败或者生成中，直接返回
        // 就以当前子榜实例的image_sequence, image_status，image_genenrate_ts为整体的结果
        image_sequence = image_instance->rank_data_->head.image_sequence;
        image_status = image_instance->rank_data_->head.image_status;
        image_generate_ts = image_instance->rank_data_->head.last_image_timestamp;
        image_switch = image_instance->rank_data_->head.image_switch;
        code = APOLLO_TOPNEXT_SVR_SUCCESS;
        sub_rank_key_out.type = 0;
        sub_rank_key_out.instance_id = 0;
        // AddImageStatusResult(APOLLO_TOPNEXT_SVR_SUCCESS, empty_subrank_key, image_instance, rsp.status_result);
        return 0;
      }

      // image_status == IMAGE_INIT  or  image_status == IMAGE_GENERATE_SUCCESS
      sub_type_instance_num[image_instance->sub_rank_key_.type]++;
      sub_type_image_status[image_instance->sub_rank_key_.type] += image_instance->rank_data_->head.image_status;

      // 镜像生成成功
      image_status = IMAGE_GENERATE_SUCC;

      if (image_instance->rank_data_->head.image_switch != IMAGE_SWITCH_INIT) {
        sub_type_image_switch[image_instance->sub_rank_key_.type] += image_instance->rank_data_->head.image_switch;
        image_switch = image_instance->rank_data_->head.image_switch;
      } else {
        // 如果共享内存中的镜像开关没有生效，那就使用配置中的开关，这里不可能为关闭，因为前面已经检测过开关必须为开
        sub_type_image_switch[image_instance->sub_rank_key_.type] += image_switchs[image_instance->sub_rank_key_.type];
        image_switch = image_switchs[image_instance->sub_rank_key_.type];
      }

      // 获取所有子榜镜像中最晚生成的那个镜像的时间
      if (image_instance->rank_data_->head.last_image_timestamp > image_generate_ts) {
        image_generate_ts = image_instance->rank_data_->head.last_image_timestamp;
      }

      // image_sequence有三种情况，1. 所有子榜的sequence都为0。
      // 2. 所有子榜的sequence不为0且相同。3.子榜的sequence不为0且不同
      image_sequences.insert(image_instance->rank_data_->head.image_sequence);
    }

    // 子类型的镜像状态情况，1. 全部都是初始状态，2. 有些是初始，有些是完成。其他情况已经在前面排出
    std::map<uint32_t, uint32_t>::iterator map_iter;
    for (map_iter = sub_type_image_status.begin(); map_iter != sub_type_image_status.end(); ++map_iter) {
      TOPNEXT_RANK_LOG_DEBUG("subtype = %d , image_status sum = %d", map_iter->first, map_iter->second);
      if (map_iter->second == IMAGE_INIT) {
        // 该子榜类型的所有子榜实例的镜像都是初始状态，返回错误
        code = APOLLO_TOPNEXT_SVR_SUBTYPE_IMAGE_IS_EMPTY;
        sub_rank_key_out.type = map_iter->first;
        sub_rank_key_out.instance_id = 0;
        // AddImageStatusResult(APOLLO_TOPNEXT_SVR_SUBTYPE_IMAGE_IS_EMPTY, empty_subrank_key, 0, 0, 0, 0,
        // rsp.status_result);
        return 0;
      }

      if (map_iter->second != IMAGE_GENERATE_SUCC * sub_type_instance_num[map_iter->first]) {
        // 表示不是所有的子榜实例的镜像状态都是 IMAGE_GENERATE_SUCC，
        // 通常是有些子榜实例没有生成过镜像，产生的情况可能是通过api关闭了镜像，但之后又通过report创建了新的子榜实例，导致新的子榜实例的镜像状态是IMAGE_INTI
        TOPNEXT_RANK_LOG_INFO(
            "some instance image status is 0, subtype = %d, image_status sum = %d, subtype instance num=%d",
            map_iter->first, map_iter->second, sub_type_instance_num[map_iter->first]);
      }
    }

    // 获得镜像开关情况1. 全部为开； 2. 全部为关； 3. 有开也有关闭， 4.异常
    for (map_iter = sub_type_image_switch.begin(); map_iter != sub_type_image_switch.end(); ++map_iter) {
      TOPNEXT_RANK_LOG_DEBUG("subtype = %d , image_switch sum = %d", map_iter->first, map_iter->second);
      uint32_t all_on = IMAGE_SWITCH_ON * sub_type_instance_num[map_iter->first];
      uint32_t all_off = IMAGE_SWITCH_OFF * sub_type_instance_num[map_iter->first];
      if (map_iter->second == all_on) {
        image_switch = IMAGE_SWITCH_ON;
      } else if (map_iter->second == all_off) {
        image_switch = IMAGE_SWITCH_OFF;
      } else if (map_iter->second > all_on && map_iter->second < all_off) {
        TOPNEXT_RANK_LOG_INFO(
            "instance image switch is not consistent, subtype = %d , image_switch sum = %d, subtype instance num=%d",
            map_iter->first, map_iter->second, sub_type_instance_num[map_iter->first]);
        image_switch = IMAGE_SWITCH_X;
      } else {
        TOPNEXT_RANK_LOG_ERROR("image_switch error, subtype = %d , image_switch sum = %d", map_iter->first,
                               map_iter->second);
        image_switch = IMAGE_SWITCH_UNKNOW;
      }
    }

    // 镜像seq情况， 1. 只有一个（全为0，或者全为API请求过的某个seq）2. 有两个（有0，也有api请求的seq）。3.
    // 超过2个（情况未知）。4. 0个，说明没有子榜实例
    if (image_sequences.size() == 1) {
      image_sequence = *image_sequences.begin();
      TOPNEXT_RANK_LOG_DEBUG("all subrank image sequence is equal, image_sequence: %lu", image_sequence);

      // 所有子榜的sequence都相等，要么是0，要么是api请求生成镜像时的sequence
      code = APOLLO_TOPNEXT_SVR_SUCCESS;
      sub_rank_key_out.type = 0;
      sub_rank_key_out.instance_id = 0;
      // AddImageStatusResult(APOLLO_TOPNEXT_SVR_SUCCESS, empty_subrank_key, image_sequence, image_status,
      // image_generate_ts, image_switch, rsp.status_result);
    } else if (image_sequences.size() == 2) {
      // 表示有sequnce不同相同的情况，表示子榜镜像有些是通过cfg生成的， 有些是通过请求生成的。
      TOPNEXT_RANK_LOG_DEBUG("subrank image has 2 image_sequence");

      // 获取通过请求过来的sequence
      image_sequence = 0;
      for (std::set<uint64_t>::const_iterator it = image_sequences.begin(); it != image_sequences.end(); ++it) {
        if (*it != 0) {
          image_sequence = *it;
          break;
        }
      }

      TOPNEXT_RANK_LOG_INFO("subrank image has 2 image_sequence, the non-zero image_sequence is %lu", image_sequence);
      code = APOLLO_TOPNEXT_SVR_SUCCESS;
      sub_rank_key_out.type = 0;
      sub_rank_key_out.instance_id = 0;
      // AddImageStatusResult(APOLLO_TOPNEXT_SVR_SUCCESS, empty_subrank_key, image_sequence, image_status,
      //  image_generate_ts, image_switch, rsp.status_result);

    } else if (image_sequences.size() > 2) {
      TOPNEXT_RANK_LOG_ERROR("there is too much image sequence in this rank");
      code = APOLLO_TOPNEXT_SVR_SUBRANK_IMAGE_SEQ_TOO_MUCH;
      sub_rank_key_out.type = 0;
      sub_rank_key_out.instance_id = 0;
      // AddImageStatusResult(APOLLO_TOPNEXT_SVR_SUBRANK_IMAGE_SEQ_TOO_MUCH, empty_subrank_key, 0, 0, 0, 0,
      //                      rsp.status_result);
    } else {  // image_sequences.size() == 0
      // 表示没有子榜，不应该出现
      TOPNEXT_RANK_LOG_ERROR("there is no sub instance in this rank");
      code = APOLLO_TOPNEXT_SVR_SUBRANK_IMAGE_IS_EMPTY;
      sub_rank_key_out.type = 0;
      sub_rank_key_out.instance_id = 0;
      // AddImageStatusResult(APOLLO_TOPNEXT_SVR_SUBRANK_IMAGE_IS_EMPTY, empty_subrank_key, 0, 0, 0, 0,
      // rsp.status_result);
    }
  } else {
    TOPNEXT_RANK_LOG_DEBUG("get some subrank image status");
    TOPNEXT_RANK_LOG_INFO("get_all = false is not complete");
    code = APOLLO_TOPNEXT_SVR_INTERNAL_ERROR;
    sub_rank_key_out.type = 0;
    sub_rank_key_out.instance_id = 0;
    // AddImageStatusResult(APOLLO_TOPNEXT_SVR_INTERNAL_ERROR, empty_subrank_key, 0, 0, 0, 0, rsp.status_result);
  }

  return 0;
}

int TopNextRankInstance::ProcOpenOrCloseSubRankImage(bool image_switch_in, bool open_all,
                                                     const std::vector<SubRankKey>& sub_rank_keys_in,
                                                     std::vector<SubRankKey>& sub_rank_keys_out,
                                                     std::vector<int>& codes, RetInfo& ret_info) {
  SubRankKey empty_subrank_key;
  empty_subrank_key.type = 0;
  empty_subrank_key.instance_id = 0;

  // 该榜单实例下的所有子榜都打开镜像
  bool image = false;
  int32_t image_switch = image_switch_in == true ? IMAGE_SWITCH_ON : IMAGE_SWITCH_OFF;
  std::string switch_str = image_switch_in == true ? "open" : "close";

  if (open_all) {
    TOPNEXT_RANK_LOG_DEBUG("%s all subrank image switch", switch_str.c_str());

    // 检查子榜是否全部打开了镜像开关
    // 检查子榜是否全部打开了镜像开关
    bool cfg_has_error = false;
    for (uint32_t i = 0; i < type_cfg_.sub_type_cfg_num; i++) {
      if (type_cfg_.sub_type_cfg[i].use_image != 1) {
        TOPNEXT_RANK_LOG_ERROR(
            "generate sub rank image failed, subtype's image swith in cfg is off, invalid "
            "sub_type:%u",
            type_cfg_.sub_type_cfg[i].type);
        SubRankKey sub_rank_key;
        sub_rank_key.type = type_cfg_.sub_type_cfg[i].type;
        sub_rank_key.instance_id = 0;
        // AddSubRankOpResult(APOLLO_TOPNEXT_SVR_SUBRANK_IMAGE_IS_CLOSE_IN_CFG, sub_rank_key, rsp.op_result);
        codes.push_back(APOLLO_TOPNEXT_SVR_SUBRANK_IMAGE_IS_CLOSE_IN_CFG);
        sub_rank_keys_out.push_back(sub_rank_key);
        cfg_has_error = true;
        break;
      }
    }
    if (cfg_has_error == true) {
      // ret_info.iRet = APOLLO_TOPNEXT_SVR_SUBRANK_IMAGE_IS_CLOSE_IN_CFG;
      // snprintf(ret_info.szMsg, MAX_RET_MSG_LEN, "have subrank image switch is off in cfg");
      return 0;
    }

    std::vector<TopNextSubRankInstance*> real_sub_instances;
    GetSubRankList(image, real_sub_instances);
    if (real_sub_instances.empty()) {
      // 该榜单没有子榜镜像实例，可能是因为配置中关闭镜像
      TOPNEXT_RANK_LOG_INFO("real sub instances is empty");
      codes.push_back(APOLLO_TOPNEXT_SVR_SUBRANK_REAL_IS_EMPTY);
      sub_rank_keys_out.push_back(empty_subrank_key);
      // AddSubRankOpResult(APOLLO_TOPNEXT_SVR_SUBRANK_REAL_IS_EMPTY, empty_subrank_key, rsp.op_result);
      return 0;
    }

    std::vector<TopNextSubRankInstance*>::iterator it;
    for (it = real_sub_instances.begin(); it != real_sub_instances.end(); ++it) {
      TopNextSubRankInstance* real_sub_instance = *it;
      real_sub_instance->rank_data_->head.image_switch = image_switch;

      // 查找镜像榜单
      TopNextSubRankInstance* image_sub_instance = FindSubInstance(real_sub_instance->sub_rank_key_, true);
      if (NULL == image_sub_instance) {
        codes.push_back(APOLLO_TOPNEXT_SVR_SUBRANK_NOT_EXISTS);
        sub_rank_keys_out.push_back(real_sub_instance->sub_rank_key_);
        // AddSubRankOpResult(APOLLO_TOPNEXT_SVR_SUBRANK_NOT_EXISTS, real_sub_instance->sub_rank_key_, rsp.op_result);
        TOPNEXT_RANK_LOG_ERROR("%s subrank image switch fail, invalid sub instance, sub_type:%u, instance_id:%u",
                               switch_str.c_str(), real_sub_instance->sub_rank_key_.type,
                               real_sub_instance->sub_rank_key_.instance_id);
        return 0;
      }

      image_sub_instance->rank_data_->head.image_switch = image_switch;

      TOPNEXT_RANK_BILL_LOG_INFO("%s subrank image switch succ, sub_type:%u, sub_instance_id:%u", switch_str.c_str(),
                                 real_sub_instance->sub_rank_key_.type, real_sub_instance->sub_rank_key_.instance_id);
    }

    TOPNEXT_RANK_BILL_LOG_INFO("%s all subrank image switch succ", switch_str.c_str());
    codes.push_back(APOLLO_TOPNEXT_SVR_SUCCESS);
    sub_rank_keys_out.push_back(empty_subrank_key);
    // AddSubRankOpResult(APOLLO_TOPNEXT_SVR_SUCCESS, rsp.op_result);
  } else {
    TOPNEXT_RANK_LOG_DEBUG("%s some subrank image switch", switch_str.c_str());
    TOPNEXT_RANK_LOG_INFO("open_all = false is not complete");
    codes.push_back(APOLLO_TOPNEXT_SVR_INTERNAL_ERROR);
    sub_rank_keys_out.push_back(empty_subrank_key);
    // AddSubRankOpResult(APOLLO_TOPNEXT_SVR_INTERNAL_ERROR, empty_subrank_key, rsp.op_result);
  }

  return 0;
}

bool TopNextRankInstance::DeleteSubRankInstance(TopNextSubRankInstance* sub_rank_instance) {
  if (NULL == sub_rank_instance) {
    TOPNEXT_RANK_LOG_ERROR("sub rank instance is null");
    return false;
  }

  SUBTYPECFG sub_type_cfg;
  int ret = GetSubTypeCfg(type_cfg_, sub_rank_instance->sub_rank_key_.type, sub_type_cfg);

  TopNextSubRankShmGroupManager* shm_group_mgr = shm_group_mgr_map_[sub_rank_instance->sub_rank_key_.type];
  if (NULL == shm_group_mgr) {
    TOPNEXT_RANK_LOG_ERROR("invalid sub group mgr, sub_type:%u", sub_rank_instance->sub_rank_key_.type);
    return false;
  }

  // 删除共享内存信息
  ret = shm_group_mgr->RemoveSubRankShmInfo(sub_rank_instance->sub_rank_key_, sub_rank_instance->is_image_);
  if (0 != ret) {
    TOPNEXT_RANK_LOG_ERROR("remove sub instance from shm group, sub_type:%u, sub_instance_id:%u",
                           sub_rank_instance->sub_rank_key_.type, sub_rank_instance->sub_rank_key_.instance_id);
    return false;
  }

  // 把CreateSubInstance内存实现的容量减掉
  if (!sub_rank_instance->is_image_) {
    --subrank_count_map_[sub_rank_instance->sub_rank_key_.type];
  }

  // 如果榜单被删除，那么需要将新的子榜实例从镜像列表中删除
  std::vector<TopNextSubRankInstance*>::iterator instance_it;
  instance_it = std::find(need_image_sub_rank_.begin(), need_image_sub_rank_.end(), sub_rank_instance);
  if (instance_it != need_image_sub_rank_.end()) {
    need_image_sub_rank_.erase(instance_it);
  }

  instance_it = std::find(need_reduce_sub_rank_.begin(), need_reduce_sub_rank_.end(), sub_rank_instance);
  if (instance_it != need_reduce_sub_rank_.end()) {
    need_reduce_sub_rank_.erase(instance_it);
  }

  TOPNEXT_RANK_BILL_LOG_INFO("delete sub instance succ, sub_type:%u, sub_instance_id:%u, is_image:%d",
                             sub_rank_instance->sub_rank_key_.type, sub_rank_instance->sub_rank_key_.instance_id,
                             sub_rank_instance->is_image_);

  SubRankInstanceKey subrank_instance_key;
  subrank_instance_key.sub_rank_key = sub_rank_instance->sub_rank_key_;
  subrank_instance_key.is_image = sub_rank_instance->is_image_;
  delete sub_rank_instance;
  subrank_instance_map_.erase(subrank_instance_key);
  subrank_instance_keys_.erase(subrank_instance_key);

  // 清理已经为空的group
  shm_group_mgr->ClearEmptyShmGroup();

  return true;
}

int TopNextRankInstance::ProcDeleteSubRank(bool delete_all, const std::vector<SubRankKey>& sub_rank_keys_in,
                                           std::vector<SubRankKey>& sub_rank_keys_out, std::vector<int>& codes,
                                           RetInfo& ret_info) {
  SubRankKey empty_subrank_key;
  empty_subrank_key.type = 0;
  empty_subrank_key.instance_id = 0;

  // 删除该榜单实例下的所有子榜
  std::vector<TopNextSubRankInstance*> real_sub_instances;
  std::vector<TopNextSubRankInstance*> image_sub_instances;
  if (delete_all) {
    GetSubRankList(false, real_sub_instances);
    if (real_sub_instances.empty()) {
      // 该榜单没有子榜实例
      TOPNEXT_RANK_LOG_INFO("delete sub rank, sub instances is empty");
      codes.push_back(APOLLO_TOPNEXT_SVR_SUBRANK_NOT_EXISTS);
      sub_rank_keys_out.push_back(empty_subrank_key);
      return 0;
    }

    GetSubRankList(true, image_sub_instances);
    if (image_sub_instances.empty()) {
      // 该榜单没有子榜镜像实例
      TOPNEXT_RANK_LOG_DEBUG("image sub instances is empty");
    }
  } else {  // 删除指定子榜实例
    // 子榜单
    std::vector<SubRankKey>::const_iterator it = sub_rank_keys_in.begin();
    for (; it != sub_rank_keys_in.end(); it++) {
      // 查找实时榜单
      TopNextSubRankInstance* real_sub_instance = FindSubInstance(*it, false);
      if (real_sub_instance == nullptr) {
        TOPNEXT_RANK_LOG_INFO("delete sub rank, invalid instance, sub_type:%u, instance_id:%u", it->type,
                              it->instance_id);
        codes.push_back(APOLLO_TOPNEXT_SVR_SUBRANK_NOT_EXISTS);
        sub_rank_keys_out.push_back(*it);
      } else {
        real_sub_instances.push_back(real_sub_instance);
      }

      // 查找镜像榜单
      TopNextSubRankInstance* image_sub_instance = FindSubInstance(*it, true);
      if (image_sub_instance == nullptr) {
        TOPNEXT_RANK_LOG_DEBUG("image sub rank instance is null");
      } else {
        image_sub_instances.push_back(image_sub_instance);
      }
    }
  }

  // 删除子榜
  std::vector<TopNextSubRankInstance*>::iterator it;
  for (it = real_sub_instances.begin(); it != real_sub_instances.end(); ++it) {
    SubRankKey sub_rank_key = (*it)->sub_rank_key_;
    TOPNEXT_RANK_LOG_DEBUG("delete real sub rank, invalid instance, sub_type:%u, instance_id:%u", sub_rank_key.type,
                           sub_rank_key.instance_id);
    bool succ = DeleteSubRankInstance(*it);
    if (!succ) {
      TOPNEXT_RANK_LOG_ERROR("delete real sub rank, invalid instance, sub_type:%u, instance_id:%u", sub_rank_key.type,
                             sub_rank_key.instance_id);
      codes.push_back(APOLLO_TOPNEXT_SVR_INTERNAL_ERROR);
      sub_rank_keys_out.push_back(sub_rank_key);
    } else {
      codes.push_back(APOLLO_TOPNEXT_SVR_SUCCESS);
      sub_rank_keys_out.push_back(sub_rank_key);
    }
  }

  // 删除子榜镜像, 结果不需要返回
  for (it = image_sub_instances.begin(); it != image_sub_instances.end(); ++it) {
    SubRankKey sub_rank_key = (*it)->sub_rank_key_;
    TOPNEXT_RANK_LOG_DEBUG("delete image sub rank, invalid instance, sub_type:%u, instance_id:%u", sub_rank_key.type,
                           sub_rank_key.instance_id);
    bool succ = DeleteSubRankInstance(*it);
    if (!succ) {
      TOPNEXT_RANK_LOG_ERROR("delete image sub rank, invalid instance, sub_type:%u, instance_id:%u", sub_rank_key.type,
                             sub_rank_key.instance_id);
    }
  }

  return 0;
}

}  // namespace topnext_app
