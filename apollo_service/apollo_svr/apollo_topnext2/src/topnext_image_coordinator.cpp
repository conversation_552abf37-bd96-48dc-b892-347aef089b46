#include "topnext_image_coordinator.h"
#include "topnext_rank_instance.h"
#include <functional>
#include <algorithm>

namespace topnext_app {

TopNextImageCoordinator& TopNextImageCoordinator::Instance() {
  static TopNextImageCoordinator instance;
  return instance;
}

TopNextImageCoordinator::TopNextImageCoordinator() 
  : tlogcat_(nullptr), initialized_(false) {
}

TopNextImageCoordinator::~TopNextImageCoordinator() {
  Reset();
}

int TopNextImageCoordinator::Init(LPTLOGCATEGORYINST logcat) {
  std::lock_guard<std::mutex> lock(mutex_);
  
  if (initialized_) {
    return 0;
  }
  
  tlogcat_ = logcat;
  
  int ret = global_timer_.Init(100, tlogcat_);  // 最多支持100个定时器
  if (ret != 0) {
    if (tlogcat_) {
      tlog_log(tlogcat_, TLOG_PRIORITY_ERROR, 0, 0, 
               "TopNextImageCoordinator init global timer failed, ret:%d", ret);
    }
    return -1;
  }
  
  initialized_ = true;
  
  if (tlogcat_) {
    tlog_log(tlogcat_, TLOG_PRIORITY_INFO, 0, 0, 
             "TopNextImageCoordinator init success");
  }
  
  return 0;
}

int TopNextImageCoordinator::RegisterSubType(uint32_t business_id, uint32_t type, uint32_t sub_type,
                                             const std::string& cron_expression, TopNextRankInstance* instance) {
  if (!instance) {
    if (tlogcat_) {
      tlog_log(tlogcat_, TLOG_PRIORITY_ERROR, 0, 0, 
               "RegisterSubType failed, instance is null, business_id:%u, type:%u, sub_type:%u", 
               business_id, type, sub_type);
    }
    return -1;
  }
  
  std::lock_guard<std::mutex> lock(mutex_);
  
  if (!initialized_) {
    if (tlogcat_) {
      tlog_log(tlogcat_, TLOG_PRIORITY_ERROR, 0, 0, 
               "RegisterSubType failed, coordinator not initialized, business_id:%u, type:%u, sub_type:%u", 
               business_id, type, sub_type);
    }
    return -2;
  }
  
  ImageConfigKey config_key(business_id, type, sub_type, cron_expression);
  auto it = config_map_.find(config_key);
  
  if (it == config_map_.end()) {
    // 第一次注册这个配置，需要创建定时器
    ImageCoordinatorConfig config(config_key);
    config.instances.push_back(instance);
    
    uint32_t timer_id;
    auto callback = std::bind(&TopNextImageCoordinator::OnImageTimeReached, this, 
                              std::placeholders::_1, config_key);
    
    int ret = global_timer_.StartTimer(cron_expression.c_str(), callback, timer_id);
    if (ret != 0) {
      if (tlogcat_) {
        tlog_log(tlogcat_, TLOG_PRIORITY_ERROR, 0, 0, 
                 "RegisterSubType failed, start timer failed, business_id:%u, type:%u, sub_type:%u, cron:%s, ret:%d", 
                 business_id, type, sub_type, cron_expression.c_str(), ret);
      }
      return -3;
    }
    
    config_map_[config_key] = config;
    timer_id_to_config_[timer_id] = config_key;
    
    if (tlogcat_) {
      tlog_log(tlogcat_, TLOG_PRIORITY_INFO, 0, 0, 
               "RegisterSubType success, create new timer, business_id:%u, type:%u, sub_type:%u, cron:%s, timer_id:%u", 
               business_id, type, sub_type, cron_expression.c_str(), timer_id);
    }
  } else {
    // 已存在这个配置，直接添加实例
    it->second.instances.push_back(instance);
    
    if (tlogcat_) {
      tlog_log(tlogcat_, TLOG_PRIORITY_INFO, 0, 0, 
               "RegisterSubType success, add to existing timer, business_id:%u, type:%u, sub_type:%u, instance_count:%zu", 
               business_id, type, sub_type, it->second.instances.size());
    }
  }
  
  return 0;
}

void TopNextImageCoordinator::UnregisterInstance(TopNextRankInstance* instance) {
  if (!instance) {
    return;
  }
  
  std::lock_guard<std::mutex> lock(mutex_);
  
  for (auto& pair : config_map_) {
    auto& instances = pair.second.instances;
    instances.erase(std::remove(instances.begin(), instances.end(), instance), instances.end());
  }
  
  if (tlogcat_) {
    tlog_log(tlogcat_, TLOG_PRIORITY_INFO, 0, 0, 
             "UnregisterInstance success, instance:%p", static_cast<void*>(instance));
  }
}

void TopNextImageCoordinator::Update() {
  if (!initialized_) {
    return;
  }
  
  global_timer_.Update();
}

void TopNextImageCoordinator::Reset() {
  std::lock_guard<std::mutex> lock(mutex_);
  
  global_timer_.Reset();
  config_map_.clear();
  timer_id_to_config_.clear();
  initialized_ = false;
  
  if (tlogcat_) {
    tlog_log(tlogcat_, TLOG_PRIORITY_INFO, 0, 0, "TopNextImageCoordinator reset");
  }
}

size_t TopNextImageCoordinator::GetRegisteredSubTypeCount() const {
  std::lock_guard<std::mutex> lock(mutex_);
  return config_map_.size();
}

size_t TopNextImageCoordinator::GetRegisteredInstanceCount() const {
  std::lock_guard<std::mutex> lock(mutex_);
  size_t total = 0;
  for (const auto& pair : config_map_) {
    total += pair.second.instances.size();
  }
  return total;
}

void TopNextImageCoordinator::OnImageTimeReached(uint32_t timer_id, const ImageConfigKey& config_key) {
  if (tlogcat_) {
    tlog_log(tlogcat_, TLOG_PRIORITY_INFO, 0, 0, 
             "Image time reached for business_id:%u, type:%u, sub_type:%u, timer_id:%u", 
             config_key.business_id, config_key.type, config_key.sub_type, timer_id);
  }
  
  // 首先全局设置镜像状态
  SetAllInstancesImageStatus(config_key, IMAGE_GENERATING);
  
  // 然后通知所有实例开始镜像处理
  NotifyInstancesForImageGeneration(config_key);
}

void TopNextImageCoordinator::SetAllInstancesImageStatus(const ImageConfigKey& config_key, IMAGE_STATUS status) {
  std::lock_guard<std::mutex> lock(mutex_);
  
  auto it = config_map_.find(config_key);
  if (it == config_map_.end()) {
    return;
  }
  
  int total_count = 0;
  for (TopNextRankInstance* instance : it->second.instances) {
    if (instance) {
      int count = instance->SetSubRankImageStatusForType(config_key.sub_type, status);
      total_count += count;
    }
  }
  
  if (tlogcat_) {
    tlog_log(tlogcat_, TLOG_PRIORITY_INFO, 0, 0, 
             "SetAllInstancesImageStatus success, business_id:%u, type:%u, sub_type:%u, status:%d, "
             "instance_count:%zu, sub_rank_count:%d", 
             config_key.business_id, config_key.type, config_key.sub_type, status,
             it->second.instances.size(), total_count);
  }
}

void TopNextImageCoordinator::NotifyInstancesForImageGeneration(const ImageConfigKey& config_key) {
  std::lock_guard<std::mutex> lock(mutex_);
  
  auto it = config_map_.find(config_key);
  if (it == config_map_.end()) {
    return;
  }
  
  for (TopNextRankInstance* instance : it->second.instances) {
    if (instance) {
      instance->AddSubRankToImageQueue(config_key.sub_type);
    }
  }
  
  if (tlogcat_) {
    tlog_log(tlogcat_, TLOG_PRIORITY_INFO, 0, 0, 
             "NotifyInstancesForImageGeneration success, business_id:%u, type:%u, sub_type:%u, instance_count:%zu", 
             config_key.business_id, config_key.type, config_key.sub_type, it->second.instances.size());
  }
}

}  // namespace topnext_app
