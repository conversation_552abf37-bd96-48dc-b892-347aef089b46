#include "topnext_image_coordinator.h"
#include <algorithm>
#include <functional>
#include "topnext_rank_instance.h"

namespace topnext_app {

TopNextImageCoordinator& TopNextImageCoordinator::Instance() {
  static TopNextImageCoordinator instance;
  return instance;
}

TopNextImageCoordinator::TopNextImageCoordinator() : tlogcat_(nullptr), initialized_(false) {}

TopNextImageCoordinator::~TopNextImageCoordinator() { Reset(); }

int TopNextImageCoordinator::Init(LPTLOGCATEGORYINST logcat) {
  if (initialized_) {
    return 0;
  }

  tlogcat_ = logcat;

  int ret = global_timer_.Init(100, tlogcat_);  // 最多支持100个定时器
  if (ret != 0) {
    if (tlogcat_) {
      tlog_log(tlogcat_, TLOG_PRIORITY_ERROR, 0, 0, "TopNextImageCoordinator init global timer failed, ret:%d", ret);
    }
    return -1;
  }

  initialized_ = true;

  if (tlogcat_) {
    tlog_log(tlogcat_, TLOG_PRIORITY_INFO, 0, 0, "TopNextImageCoordinator init success");
  }

  return 0;
}

int TopNextImageCoordinator::RegisterSubType(uint32_t sub_type, const std::string& cron_expression,
                                             TopNextRankInstance* instance) {
  if (!instance) {
    if (tlogcat_) {
      tlog_log(tlogcat_, TLOG_PRIORITY_ERROR, 0, 0, "RegisterSubType failed, instance is null, sub_type:%u", sub_type);
    }
    return -1;
  }

  if (!initialized_) {
    if (tlogcat_) {
      tlog_log(tlogcat_, TLOG_PRIORITY_ERROR, 0, 0, "RegisterSubType failed, coordinator not initialized, sub_type:%u",
               sub_type);
    }
    return -2;
  }

  auto it = sub_type_configs_.find(sub_type);
  if (it == sub_type_configs_.end()) {
    // 第一次注册这个子榜类型，需要创建定时器
    ImageCoordinatorConfig config(sub_type, cron_expression);
    config.instances.push_back(instance);

    uint32_t timer_id;
    auto callback = std::bind(&TopNextImageCoordinator::OnImageTimeReached, this, std::placeholders::_1, sub_type);

    int ret = global_timer_.StartTimer(cron_expression.c_str(), callback, timer_id);
    if (ret != 0) {
      if (tlogcat_) {
        tlog_log(tlogcat_, TLOG_PRIORITY_ERROR, 0, 0,
                 "RegisterSubType failed, start timer failed, sub_type:%u, cron:%s, ret:%d", sub_type,
                 cron_expression.c_str(), ret);
      }
      return -3;
    }

    sub_type_configs_[sub_type] = config;
    timer_id_to_sub_type_[timer_id] = sub_type;

    if (tlogcat_) {
      tlog_log(tlogcat_, TLOG_PRIORITY_INFO, 0, 0,
               "RegisterSubType success, create new timer, sub_type:%u, cron:%s, timer_id:%u", sub_type,
               cron_expression.c_str(), timer_id);
    }
  } else {
    // 已存在这个子榜类型，检查cron表达式是否一致
    if (it->second.cron_expression != cron_expression) {
      if (tlogcat_) {
        tlog_log(tlogcat_, TLOG_PRIORITY_WARN, 0, 0,
                 "RegisterSubType warning, cron expression mismatch, sub_type:%u, "
                 "existing:%s, new:%s",
                 sub_type, it->second.cron_expression.c_str(), cron_expression.c_str());
      }
    }

    // 添加实例到列表中
    it->second.instances.push_back(instance);

    if (tlogcat_) {
      tlog_log(tlogcat_, TLOG_PRIORITY_INFO, 0, 0,
               "RegisterSubType success, add to existing timer, sub_type:%u, instance_count:%zu", sub_type,
               it->second.instances.size());
    }
  }

  return 0;
}

void TopNextImageCoordinator::UnregisterInstance(TopNextRankInstance* instance) {
  if (!instance) {
    return;
  }

  for (auto& pair : sub_type_configs_) {
    auto& instances = pair.second.instances;
    instances.erase(std::remove(instances.begin(), instances.end(), instance), instances.end());
  }

  if (tlogcat_) {
    tlog_log(tlogcat_, TLOG_PRIORITY_INFO, 0, 0, "UnregisterInstance success, instance:%p",
             static_cast<void*>(instance));
  }
}

void TopNextImageCoordinator::Update() {
  if (!initialized_) {
    return;
  }

  global_timer_.Update();
}

void TopNextImageCoordinator::Reset() {
  global_timer_.Reset();
  sub_type_configs_.clear();
  timer_id_to_sub_type_.clear();
  initialized_ = false;

  if (tlogcat_) {
    tlog_log(tlogcat_, TLOG_PRIORITY_INFO, 0, 0, "TopNextImageCoordinator reset");
  }
}

size_t TopNextImageCoordinator::GetRegisteredSubTypeCount() const { return sub_type_configs_.size(); }

size_t TopNextImageCoordinator::GetRegisteredInstanceCount() const {
  size_t total = 0;
  for (const auto& pair : sub_type_configs_) {
    total += pair.second.instances.size();
  }
  return total;
}

void TopNextImageCoordinator::OnImageTimeReached(uint32_t timer_id, uint32_t sub_type) {
  if (tlogcat_) {
    tlog_log(tlogcat_, TLOG_PRIORITY_INFO, 0, 0, "Image time reached for sub_type:%u, timer_id:%u", sub_type, timer_id);
  }

  // 首先全局设置镜像状态
  SetAllInstancesImageStatus(sub_type, IMAGE_GENERATING);

  // 然后通知所有实例开始镜像处理
  NotifyInstancesForImageGeneration(sub_type);
}

void TopNextImageCoordinator::SetAllInstancesImageStatus(uint32_t sub_type, IMAGE_STATUS status) {
  auto it = sub_type_configs_.find(sub_type);
  if (it == sub_type_configs_.end()) {
    return;
  }

  int total_count = 0;
  for (TopNextRankInstance* instance : it->second.instances) {
    if (instance) {
      int count = instance->SetSubRankImageStatusForType(sub_type, status);
      total_count += count;
    }
  }

  if (tlogcat_) {
    tlog_log(tlogcat_, TLOG_PRIORITY_INFO, 0, 0,
             "SetAllInstancesImageStatus success, sub_type:%u, status:%d, "
             "instance_count:%zu, sub_rank_count:%d",
             sub_type, status, it->second.instances.size(), total_count);
  }
}

void TopNextImageCoordinator::NotifyInstancesForImageGeneration(uint32_t sub_type) {
  auto it = sub_type_configs_.find(sub_type);
  if (it == sub_type_configs_.end()) {
    return;
  }

  for (TopNextRankInstance* instance : it->second.instances) {
    if (instance) {
      instance->AddSubRankToImageQueue(sub_type);
    }
  }

  if (tlogcat_) {
    tlog_log(tlogcat_, TLOG_PRIORITY_INFO, 0, 0,
             "NotifyInstancesForImageGeneration success, sub_type:%u, instance_count:%zu", sub_type,
             it->second.instances.size());
  }
}

}  // namespace topnext_app
