#include "topnext_image_coordinator.h"
#include <algorithm>
#include <functional>
#include "topnext_log.h"
#include "topnext_rank_instance.h"

namespace topnext_app {

TopNextImageCoordinator& TopNextImageCoordinator::Instance() {
  static TopNextImageCoordinator instance;
  return instance;
}

TopNextImageCoordinator::TopNextImageCoordinator() : initialized_(false) {}

TopNextImageCoordinator::~TopNextImageCoordinator() { Reset(); }

int TopNextImageCoordinator::Init() {
  if (initialized_) {
    return 0;
  }

  // FIXME: 最多支持100个定时器，一个进程最多能有多少种业务+榜单类型+子榜类型？
  int ret = global_timer_.Init(100, g_tlogcat);  // 最多支持100个定时器
  if (ret != 0) {
    TOPNEXT_LOG_ERROR("TopNextImageCoordinator init global timer failed, ret:%d", ret);
    return -1;
  }

  initialized_ = true;
  TOPNEXT_LOG_INFO("TopNextImageCoordinator init success");

  return 0;
}

int TopNextImageCoordinator::RegisterSubType(uint32_t business_id, uint32_t type, uint32_t sub_type,
                                             const std::string& cron_expression, TopNextRankInstance* instance) {
  if (!instance) {
    TOPNEXT_LOG_ERROR("RegisterSubType failed, instance is null, business_id:%u, type:%u, sub_type:%u", business_id,
                      type, sub_type);
    return -1;
  }

  if (!initialized_) {
    TOPNEXT_LOG_ERROR("RegisterSubType failed, coordinator not initialized, business_id:%u, type:%u, sub_type:%u",
                      business_id, type, sub_type);
    return -2;
  }

  ImageConfigKey config_key(business_id, type, sub_type, cron_expression);
  auto it = config_map_.find(config_key);

  if (it == config_map_.end()) {
    // 第一次注册这个配置，需要创建定时器
    ImageCoordinatorConfig config(config_key);
    config.instances.push_back(instance);

    uint32_t timer_id;
    auto callback = std::bind(&TopNextImageCoordinator::OnImageTimeReached, this, std::placeholders::_1, config_key);

    int ret = global_timer_.StartTimer(cron_expression.c_str(), callback, timer_id);
    if (ret != 0) {
      TOPNEXT_LOG_ERROR(
          "RegisterSubType failed, start timer failed, business_id:%u, type:%u, sub_type:%u, cron:%s, ret:%d",
          business_id, type, sub_type, cron_expression.c_str(), ret);
      return -3;
    }

    config_map_[config_key] = config;
    timer_id_to_config_[timer_id] = config_key;

    TOPNEXT_LOG_INFO(
        "RegisterSubType success, create new timer, business_id:%u, type:%u, sub_type:%u, cron:%s, timer_id:%u",
        business_id, type, sub_type, cron_expression.c_str(), timer_id);
  } else {
    // 已存在这个配置，直接添加实例
    it->second.instances.push_back(instance);

    TOPNEXT_LOG_INFO(
        "RegisterSubType success, add to existing timer, business_id:%u, type:%u, sub_type:%u, instance_count:%zu",
        business_id, type, sub_type, it->second.instances.size());
  }

  return 0;
}

void TopNextImageCoordinator::UnregisterInstance(TopNextRankInstance* instance) {
  if (!instance) {
    return;
  }

  for (auto& pair : config_map_) {
    auto& instances = pair.second.instances;
    instances.erase(std::remove(instances.begin(), instances.end(), instance), instances.end());
  }

  TOPNEXT_LOG_INFO("UnregisterInstance success, instance:%p", static_cast<void*>(instance));
}

void TopNextImageCoordinator::Update() {
  if (!initialized_) {
    return;
  }

  global_timer_.Update();
}

void TopNextImageCoordinator::Reset() {
  global_timer_.Reset();
  config_map_.clear();
  timer_id_to_config_.clear();
  initialized_ = false;

  TOPNEXT_LOG_INFO("TopNextImageCoordinator reset");
}

size_t TopNextImageCoordinator::GetRegisteredSubTypeCount() const { return config_map_.size(); }

size_t TopNextImageCoordinator::GetRegisteredInstanceCount() const {
  size_t total = 0;
  for (const auto& pair : config_map_) {
    total += pair.second.instances.size();
  }
  return total;
}

void TopNextImageCoordinator::OnImageTimeReached(uint32_t timer_id, const ImageConfigKey& config_key) {
  TOPNEXT_LOG_INFO("Image time reached for business_id:%u, type:%u, sub_type:%u, timer_id:%u", config_key.business_id,
                   config_key.type, config_key.sub_type, timer_id);

  // 首先全局设置镜像状态
  SetAllInstancesImageStatus(config_key, IMAGE_GENERATING);

  // 然后通知所有实例开始镜像处理
  NotifyInstancesForImageGeneration(config_key);
}

void TopNextImageCoordinator::SetAllInstancesImageStatus(const ImageConfigKey& config_key, IMAGE_STATUS status) {
  auto it = config_map_.find(config_key);
  if (it == config_map_.end()) {
    return;
  }

  int total_count = 0;
  for (TopNextRankInstance* instance : it->second.instances) {
    if (instance) {
      int count = instance->SetSubRankImageStatusForType(config_key.sub_type, status);
      total_count += count;
    }
  }

  TOPNEXT_LOG_INFO(
      "SetAllInstancesImageStatus success, business_id:%u, type:%u, sub_type:%u, status:%d, "
      "instance_count:%zu, sub_rank_count:%d",
      config_key.business_id, config_key.type, config_key.sub_type, status, it->second.instances.size(), total_count);
}

void TopNextImageCoordinator::NotifyInstancesForImageGeneration(const ImageConfigKey& config_key) {
  auto it = config_map_.find(config_key);
  if (it == config_map_.end()) {
    return;
  }

  for (TopNextRankInstance* instance : it->second.instances) {
    if (instance) {
      instance->AddSubRankToImageQueue(config_key.sub_type);
    }
  }

  TOPNEXT_LOG_INFO(
      "NotifyInstancesForImageGeneration success, business_id:%u, type:%u, sub_type:%u, instance_count:%zu",
      config_key.business_id, config_key.type, config_key.sub_type, it->second.instances.size());
}

}  // namespace topnext_app
