#ifndef TOPNEXT_RANK_INSTANCE_H_
#define TOPNEXT_RANK_INSTANCE_H_

#include "crontab_timer.h"
#include "kernel_data_type.h"
#include "shm_manager.h"
#include "shtable_util.h"
#include "tlog/tlog.h"
#include "topnext_conf_desc.h"
#include "topnext_sub_rank_instance.h"
#include "topnext_sub_rank_shm_group_manager.h"

namespace topnext_app {

struct RetInfo  // 返回信息(成功，失败，失败的原因)
{
  int32_t iRet;
  char szMsg[128];
  void Clear() {
    iRet = 0;
    szMsg[0] = '\0';
  }
  void Reset() {
    iRet = 0;
    memset(szMsg, 0, 128);
  }
};

struct ReportSubRankCache {
  int32_t data_source;
  int32_t from;
  UserInfo user_info;
  std::vector<SubRankKey> sub_rank_keys;
};

struct ChangeSubRankCache {
  UserInfo user_info;
  std::vector<SubRankKey> old_sub_rank_keys;
  std::vector<SubRankKey> new_sub_rank_keys;
};

struct ScoreCache {
  int32_t data_source;
  int32_t from;
  uint32_t delta_score;
  bool no_need_update_ext;
  UserInfo user_info;
  std::vector<SubRankKey> sub_rank_keys;
};

class TopNextRankInstance {
 public:
  TopNextRankInstance() {
    is_sub_rank_shm_manager_existed_ = false;
    memset(&type_cfg_, 0, sizeof(type_cfg_));
    memset(&type_key_, 0, sizeof(type_key_));
    need_pull_reduce_args_ = false;
    last_pull_reduce_ = 0;
  }

  virtual ~TopNextRankInstance() = default;

  int Init(const RANKCFG& rank_cfg, const topnext_cfg::TYPECFG& type_cfg);
  int Reload(const TYPECFG& type_cfg);
  int Fini();

  int AddToSubRankInstanceMap(const SubRankKey& sub_rank_key, bool is_image, TopNextSubRankInstance* sub_instance);
  int RecoverSubRankFromDumpFile();
  int RecoverSubRankFromDumpFile(const std::string& file_path);
  int ReadUserInfoFromDumpBuff(const char* buff, uint32_t remain_buff_len, topnext_app::UserInfo& user_info,
                               uint32_t& used_len);
  int CreateSubInstanceIncludeImage(const SubRankKey& sub_rank_key, bool force_image = false);
  int RemoveSubInstance(const SubRankKey& sub_rank_key, bool is_image);
  int CreateSubInstance(const SubRankKey& sub_rank_key, bool is_image, bool check_capacity = true);
  TopNextSubRankInstance* FindSubInstance(const SubRankKey& sub_rank_key, bool is_image);

  bool AddToNeedResizeSubRank(TopNextSubRankInstance* sub_rank_instance);

  // 处理协议
  virtual int ProcReportSubRank(int32_t data_source, int32_t from, const UserInfo& user_info,
                                const std::vector<SubRankKey>& sub_rank_keys, std::vector<int>& codes,
                                std::vector<int>& rank_nos, RetInfo& ret_info);
  virtual int ProcChangeSubRank(UserInfo& user_info, const std::vector<SubRankKey>& old_sub_rank_keys,
                                const std::vector<SubRankKey>& new_sub_rank_keys, std::vector<int>& old_codes,
                                std::vector<int>& new_codes, RetInfo& ret_info);
  virtual int ProcIncreaseScore(int32_t data_source, int32_t from, uint32_t delta_score, bool no_need_update_ext,
                                UserInfo& user_info, const std::vector<SubRankKey>& sub_rank_keys,
                                std::vector<int>& codes, RetInfo& ret_info);
  virtual int ProcDecreaseScore(int32_t data_source, int32_t from, uint32_t delta_score, bool no_need_update_ext,
                                UserInfo& user_info, const std::vector<SubRankKey>& sub_rank_keys,
                                std::vector<int>& codes, RetInfo& ret_info);

  virtual int ProcGetTopSubRank(int32_t data_source, int32_t query_from, int32_t query_count,
                                const SubRankKey& sub_rank_key, int32_t& total_count, int64_t& image_generate_ts,
                                std::vector<UserRankInfo>& user_rank_infos, std::vector<int>& codes, RetInfo& ret_info);
  virtual int ProcGetOneUserSubRank(int32_t data_source, const char* open_id,
                                    const std::vector<SubRankKey>& sub_rank_keys,
                                    std::vector<UserRankInfo>& user_rank_infos, std::vector<int>& codes,
                                    RetInfo& ret_info);
  virtual int ProcImageAndReduceStat(int32_t data_source, const std::vector<SubRankKey>& sub_rank_keys,
                                     std::vector<int>& codes, std::vector<int64_t>& image_times,
                                     std::vector<int64_t>& reduce_times);
  virtual int ProcGetOneUserMultiRankMultiSubRank(int32_t data_source, const char* open_id,
                                                  const std::vector<SubRankKey>& sub_rank_keys, std::vector<int>& codes,
                                                  std::vector<UserRankInfo>& user_rank_infos, RetInfo& ret_info);
  virtual int ProcSetUserExtField(int32_t modify_flags, const char* open_id, int64_t ext_field1, int64_t ext_field2,
                                  int64_t ext_field3, const std::vector<SubRankKey>& sub_rank_keys,
                                  std::vector<int>& codes);
  virtual int ProcGetSubRankListSubRank(int32_t data_source, int32_t query_from, int32_t query_count,
                                        int32_t& total_count, std::vector<int>& codes,
                                        std::vector<SubRankKey>& sub_rank_keys);
  virtual int ProcClearOneUserSubRank(bool clear_all, const char* open_id,
                                      const std::vector<SubRankKey>& sub_rank_keys_in,
                                      std::vector<SubRankKey>& sub_rank_keys_out, std::vector<int>& codes);
  virtual int ProcClearSubRank(int32_t data_source, bool clear_all, const std::vector<SubRankKey>& sub_rank_keys_in,
                               std::vector<SubRankKey>& sub_rank_keys_out, std::vector<int>& codes);

  virtual int ProcInnerGetSubRankListSubRank(int32_t data_source, std::vector<SubRankKey>& sub_rank_keys);
  virtual int ProcInnerGetSubRankListSubRank(int32_t data_source, int32_t query_from, int32_t query_count,
                                             int32_t& total_count, std::vector<SubRankKey>& sub_rank_keys);

  virtual int ProcGetAndClearOneUserSubRank(bool clear_all, const char* open_id,
                                            const std::vector<SubRankKey>& sub_rank_keys_in,
                                            std::vector<SubRankKey>& sub_rank_keys_out,
                                            std::vector<UserRankInfo>& user_rank_infos, std::vector<int>& codes,
                                            RetInfo& ret_info);

  // 处理协议，主动生成镜像
  // image_sequence: 镜像序列号, clean: 是否清理, close_image_switch: 是否关闭镜像开关, generate_all: 是否生成所有
  virtual int ProcGenerateSubRankImage(uint64_t image_sequence, bool clean, bool close_image_switch, bool generate_all,
                                       const std::vector<SubRankKey>& sub_rank_keys_in,
                                       std::vector<SubRankKey>& sub_rank_keys_out, std::vector<int>& codes,
                                       std::vector<int64_t>& image_sequences, RetInfo& ret_info);
  virtual int ProcGetSubRankImageStatus(bool get_all, const std::vector<SubRankKey>& sub_rank_keys_in,
                                        SubRankKey& sub_rank_key_out, uint64_t& image_sequence, int32_t& image_status,
                                        uint64_t& image_generate_ms, uint32_t& image_switch, int& code,
                                        RetInfo& ret_info);
  virtual int ProcOpenOrCloseSubRankImage(bool image_switch, bool open_all,
                                          const std::vector<SubRankKey>& sub_rank_keys_in,
                                          std::vector<SubRankKey>& sub_rank_keys_out, std::vector<int>& codes,
                                          RetInfo& ret_info);
  // 删除子榜实例
  virtual int ProcDeleteSubRank(bool delete_all, const std::vector<SubRankKey>& sub_rank_keys_in,
                                std::vector<SubRankKey>& sub_rank_keys_out, std::vector<int>& codes, RetInfo& ret_info);

  int CpuTimeControlForImageAndReduce();
  int CpuTimeControlForResizeSubRank();
  void DoResizeSubRank();
  void DoImageAndReduce();
  // 镜像和衰减处理相关
  void OnTickTimer();
  int OnIdle();
  bool HasDoneGenerateImageAndReduce();
  bool HasSubRankWaitImageAndReduce();

  void NeedGenerateImageForSubType(uint32_t timer_id, uint32_t sub_type);

  // 全局镜像协调器相关方法
  int SetSubRankImageStatusForType(uint32_t sub_type, IMAGE_STATUS status);
  void AddSubRankToImageQueue(uint32_t sub_type);

  // 王者v91
  int GenerateImageByRequest(TopNextSubRankInstance* real);

  int GenerateImage(TopNextSubRankInstance* real);
  void NeedReduceForSubType(uint32_t timer_id);
  int Reduce(TopNextSubRankInstance* real);
  void ParseReduceArgs(const std::string& resp);

  virtual const TYPECFG& GetTypeCfg() const { return type_cfg_; };
  const RankKey& GetRankKey() const { return rank_key_; };

  size_t GetShmGroupSize() const { return sub_rank_shm_manager_.GetShmKeyCount(); }

  // 删除子榜实例，子榜ID也会被回收
  bool DeleteSubRankInstance(TopNextSubRankInstance* sub_rank_instance);
  void GetSubRankList(bool is_image, std::vector<TopNextSubRankInstance*>& instances);

 private:
  int InitSubRankInstance();
  int InitSubRankShmGroupMgr(const SUBTYPECFG& sub_type_cfg);
  int InitImageCrontabTimer(const TYPECFG& type_cfg);
  int InitReduceCrontabTimer(const TYPECFG& type_cfg);
  int InitResizeOnSubRank();

  bool ValidateUserInfo(const UserInfo& user_info, RetInfo& ret_info);
  bool ValidateDataSource(int32_t data_source, int32_t from, RetInfo& ret_info);
  bool HandleWaitingImageAndReduce(int32_t data_source, int32_t from, const UserInfo& user_info,
                                   const std::vector<SubRankKey>& sub_rank_keys, std::vector<int>& codes,
                                   std::vector<int>& rank_nos);
  bool HandleSyncRequest(const SubRankKey& key, const UserInfo& user_info, TopNextSubRankInstance* sub_rank_inst,
                         int32_t from, std::vector<int>& codes, std::vector<int>& rank_nos);

  void AddToReduceQueue(bool image);

  int GetSubRankShmGroupId(const topnext_app::SubRankKey& sub_rank_key, bool is_image, uint32_t& group_id);

  void GetSubRankList(uint32_t sub_type, bool is_image, std::vector<TopNextSubRankInstance*>& instances);
  void GetSubRankList(uint32_t sub_type, std::vector<TopNextSubRankInstance*>& instances);

  void GetSubRankInstanceKeys(bool is_image, std::vector<SubRankInstanceKey>& sub_rank_instance_keys);

  uint32_t CacheOfRequestTotal() {
    return (uint32_t)(subrank_report_cache_.size() + subrank_change_cache_.size() + increase_score_cache_.size() +
                      decrease_score_cache_.size());
  }

  // 子榜榜单
  bool is_sub_rank_shm_manager_existed_;
  shm_manager::ShmManager sub_rank_shm_manager_;
  std::map<uint32_t, TopNextSubRankShmGroupManager*> shm_group_mgr_map_;

  struct Hash {
    std::size_t operator()(const SubRankInstanceKey& k) const {
      return (k.sub_rank_key.instance_id ^ k.sub_rank_key.type) * (k.is_image ? 1 : 23);
    }
  };
  struct EqualTo {
    bool operator()(const SubRankInstanceKey& left, const SubRankInstanceKey& right) const {
      return (left.sub_rank_key.instance_id == right.sub_rank_key.instance_id &&
              left.sub_rank_key.type == right.sub_rank_key.type && left.is_image == right.is_image);
    }
  };

  std::map<SubRankInstanceKey, bool> subrank_instance_keys_;
  std::tr1::unordered_map<SubRankInstanceKey, TopNextSubRankInstance*, Hash, EqualTo> subrank_instance_map_;
  // 每个类型，动态创建的榜单计数
  std::map<uint32_t, uint32_t> subrank_count_map_;
  // 错误消息
  std::string error_msg_;

  // 榜单配置
  topnext_cfg::TYPECFG type_cfg_;
  RankKey rank_key_;
  // 所属的主榜类型
  TypeKey type_key_;

  // resize
  std::vector<TopNextSubRankInstance*> need_resize_sub_rank_;
  // 镜像周期定时器
  apollo_service::CrontabTimer image_crontab_timer_;
  std::vector<TopNextSubRankInstance*> need_image_sub_rank_;

  // 来自客户端请求，需要镜像的子榜
  std::vector<TopNextSubRankInstance*> need_image_sub_rank_from_request_;

  // 衰减周期定时器
  apollo_service::CrontabTimer reduce_crontab_timer_;
  std::vector<TopNextSubRankInstance*> need_reduce_sub_rank_;

  // cache for report and change event if image and reduce happen
  std::vector<ReportSubRankCache> subrank_report_cache_;
  std::vector<ChangeSubRankCache> subrank_change_cache_;
  std::vector<ScoreCache> increase_score_cache_;
  std::vector<ScoreCache> decrease_score_cache_;

  bool need_pull_reduce_args_;
  uint64_t last_pull_reduce_;
};

};  // namespace topnext_app
#endif
