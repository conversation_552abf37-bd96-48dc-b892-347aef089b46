#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/time.h>
#include <time.h>
#include "ccronexpr.h"

// 测试结果：
// === ccronexpr Performance Benchmark (Chinese Cron Expressions) ===
// Iterations per test: 100000

// Benchmarking cron_parse_expr...
// Parse operations: 4700000
// Total time: 3.501853 seconds
// Operations per second: 1342146.57
// Average time per parse: 0.000001 seconds

// Benchmarking cron_next...
// Next calculations: 100000
// Total time: 0.156912 seconds
// Operations per second: 637299.89
// Average time per calculation: 0.000002 seconds

// Benchmarking cron_prev...
// Previous calculations: 100000
// Total time: 0.223988 seconds
// Operations per second: 446452.49
// Average time per calculation: 0.000002 seconds

// Benchmarking Chinese cron expressions with cron_next...
// Chinese cron operations: 100000
// Total time: 0.910563 seconds
// Operations per second: 109822.16
// Average time per operation: 0.000009 seconds

// Benchmarking different expression types...
// 每天执行 ('0 0 * * *'): 624298 ops/sec
// 每周执行 ('0 0 * * 1'): 530166 ops/sec
// 每月执行 ('0 0 1 * *'): 484872 ops/sec
// 每年执行 ('0 0 1 1 *'): 351124 ops/sec
// 每分钟执行 ('*/1 * * * *'): 1185115 ops/sec
// 每10分钟执行 ('*/10 * * * *'): 956206 ops/sec
// 通配符表达式 ('* */1 * * *'): 1228199 ops/sec
// 工作日执行 ('5 0 * * 1,2,3,4,5'): 426512 ops/sec
// 特定日期执行 ('0 1 30 4 0'): 13345 ops/sec

#define ITERATIONS 100000
#define NUM_EXPRESSIONS 47

static double get_time_diff(struct timeval start, struct timeval end) {
  return (end.tv_sec - start.tv_sec) + (end.tv_usec - start.tv_usec) / 1000000.0;
}

static void benchmark_cron_parse_expr() {
  const char* expressions[NUM_EXPRESSIONS] = {
      "0 1 1 * *",   /* 每月1号1点0分 */
      "0 1 * * 1",   /* 每周一1点0分 */
      "0 0 * * *",   /* 每天0点0分 */
      "1 0 * * 1",   /* 每周一0点1分 */
      "1 0 * * *",   /* 每天0点1分 */
      "0 0 * * 1",   /* 每周一0点0分 */
      "0 0 * * 0",   /* 每周日0点0分 */
      "59 23 * * 0", /* 每周日23点59分 */
      "59 23 * * *", /* 每天23点59分 */
      "1 * * * *",   /* 每小时第1分钟 */
      "30 3 * * 1",  /* 每周一3点30分 */
      "30 4 1 * *",  /* 每月1号4点30分 */
      "0 0 1 * *",   /* 每月1号0点0分 */
      "5 0 * * 1",   /* 每周一0点5分 */
      "5 0 * * 2",   /* 每周二0点5分 */
      "5 0 * * 3",   /* 每周三0点5分 */
      "5 0 * * 4",   /* 每周四0点5分 */
      "5 0 * * 5",   /* 每周五0点5分 */
      "5 0 * * 6",   /* 每周六0点5分 */
      "5 0 * * 0",   /* 每周日0点5分 */
      "15 * * * *",  /* 每小时第15分钟 */
      "0 5 * * *",   /* 每天5点0分 */
      "59 23 * * 1", /* 每周一23点59分 */
      "1 5 * * 1",   /* 每周一5点1分 */
      "0 5 * * 1",   /* 每周一5点0分 */
      "30 2 * * *",  /* 每天2点30分 */
      "30 21 * * *", /* 每天21点30分 */
      "0 3 * * 1",   /* 每周一3点0分 */
      "55 23 * * *", /* 每天23点55分 */
      "00 00 * * 1", /* 每周一0点0分 */
      "0 5 * * 5",   /* 每周五5点0分 */
      "*/1 * * * *", /* 每分钟执行 */
      "0 3 * * *",   /* 每天3点0分 */
      "* */1 * * *", /* 每分钟执行（另一种写法） */
      "5 0 * * *",   /* 每天0点5分 */
      "30 20 * * 0", /* 每周日20点30分 */
      "1 5 * * 5",   /* 每周五5点1分 */
      "5 * * * *",   /* 每小时第5分钟 */
      "20 0 * * *",  /* 每天0点20分 */
      "0 0 1 1 *",   /* 每年1月1日0点0分 */
      "59 0 * * 1",  /* 每周一0点59分 */
      "55 23 * * 0", /* 每周日23点55分 */
      "05 00 * * 1", /* 每周一0点5分 */
      "10 0 28 5 *", /* 每年5月28日0点10分 */
      "0 1 30 4 0",  /* 每年4月30日周日1点0分 */
      "0 4 * * 1",   /* 每周一4点0分 */
      "*/10 * * * *" /* 每10分钟执行 */
  };

  struct timeval start, end;
  cron_expr expr;
  const char* error;
  int total_parsed = 0;

  printf("Benchmarking cron_parse_expr...\n");
  gettimeofday(&start, NULL);

  for (int i = 0; i < ITERATIONS; i++) {
    for (int j = 0; j < NUM_EXPRESSIONS; j++) {
      cron_parse_expr(expressions[j], &expr, &error);
      if (!error) {
        total_parsed++;
      }
    }
  }

  gettimeofday(&end, NULL);
  double elapsed = get_time_diff(start, end);

  printf("Parse operations: %d\n", total_parsed);
  printf("Total time: %.6f seconds\n", elapsed);
  printf("Operations per second: %.2f\n", total_parsed / elapsed);
  printf("Average time per parse: %.6f seconds\n\n", elapsed / total_parsed);
}

static void benchmark_cron_next() {
  const char* expression = "0 0 12 * * ?"; /* Every day at noon */
  cron_expr expr;
  const char* error;

  cron_parse_expr(expression, &expr, &error);
  if (error) {
    printf("Failed to parse expression: %s\n", error);
    return;
  }

  struct timeval start, end;
  time_t base_time = time(NULL);
  int total_calculated = 0;

  printf("Benchmarking cron_next...\n");
  gettimeofday(&start, NULL);

  for (int i = 0; i < ITERATIONS; i++) {
    time_t next_time = cron_next(&expr, base_time + i);
    if (next_time != CRON_INVALID_INSTANT) {
      total_calculated++;
    }
  }

  gettimeofday(&end, NULL);
  double elapsed = get_time_diff(start, end);

  printf("Next calculations: %d\n", total_calculated);
  printf("Total time: %.6f seconds\n", elapsed);
  printf("Operations per second: %.2f\n", total_calculated / elapsed);
  printf("Average time per calculation: %.6f seconds\n\n", elapsed / total_calculated);
}

static void benchmark_cron_prev() {
  const char* expression = "0 0 12 * * ?"; /* Every day at noon */
  cron_expr expr;
  const char* error;

  cron_parse_expr(expression, &expr, &error);
  if (error) {
    printf("Failed to parse expression: %s\n", error);
    return;
  }

  struct timeval start, end;
  time_t base_time = time(NULL);
  int total_calculated = 0;

  printf("Benchmarking cron_prev...\n");
  gettimeofday(&start, NULL);

  for (int i = 0; i < ITERATIONS; i++) {
    time_t prev_time = cron_prev(&expr, base_time + i);
    if (prev_time != CRON_INVALID_INSTANT) {
      total_calculated++;
    }
  }

  gettimeofday(&end, NULL);
  double elapsed = get_time_diff(start, end);

  printf("Previous calculations: %d\n", total_calculated);
  printf("Total time: %.6f seconds\n", elapsed);
  printf("Operations per second: %.2f\n", total_calculated / elapsed);
  printf("Average time per calculation: %.6f seconds\n\n", elapsed / total_calculated);
}

static void benchmark_chinese_cron_next() {
  const char* chinese_expressions[] = {
      "0 1 1 * *",    /* 每月1号1点0分 */
      "0 1 * * 1",    /* 每周一1点0分 */
      "0 0 * * *",    /* 每天0点0分 */
      "1 0 * * 1",    /* 每周一0点1分 */
      "1 0 * * *",    /* 每天0点1分 */
      "*/1 * * * *",  /* 每分钟执行 */
      "* */1 * * *",  /* 每分钟执行（另一种写法） */
      "*/10 * * * *", /* 每10分钟执行 */
      "0 0 1 1 *",    /* 每年1月1日0点0分 */
      "0 1 30 4 0"    /* 每年4月30日周日1点0分 */
  };

  printf("Benchmarking Chinese cron expressions with cron_next...\n");
  struct timeval start, end;
  gettimeofday(&start, NULL);

  int total_operations = 0;
  time_t base_time = time(NULL);

  for (int expr_idx = 0; expr_idx < 10; expr_idx++) {
    cron_expr expr;
    const char* error;

    cron_parse_expr(chinese_expressions[expr_idx], &expr, &error);
    if (error) {
      printf("Error parsing '%s': %s\n", chinese_expressions[expr_idx], error);
      continue;
    }

    for (int i = 0; i < ITERATIONS / 10; i++) {
      time_t next_time = cron_next(&expr, base_time + i);
      if (next_time != CRON_INVALID_INSTANT) {
        total_operations++;
      }
    }
  }

  gettimeofday(&end, NULL);
  double elapsed = get_time_diff(start, end);

  printf("Chinese cron operations: %d\n", total_operations);
  printf("Total time: %.6f seconds\n", elapsed);
  printf("Operations per second: %.2f\n", total_operations / elapsed);
  printf("Average time per operation: %.6f seconds\n\n", elapsed / total_operations);
}

static void benchmark_expression_types() {
  printf("Benchmarking different expression types...\n");

  struct {
    const char* expression;
    const char* description;
  } test_cases[] = {
      {"0 0 * * *", "每天执行"},       {"0 0 * * 1", "每周执行"},           {"0 0 1 * *", "每月执行"},
      {"0 0 1 1 *", "每年执行"},       {"*/1 * * * *", "每分钟执行"},       {"*/10 * * * *", "每10分钟执行"},
      {"* */1 * * *", "通配符表达式"}, {"5 0 * * 1,2,3,4,5", "工作日执行"}, {"0 1 30 4 0", "特定日期执行"}};

  int num_test_cases = sizeof(test_cases) / sizeof(test_cases[0]);
  time_t base_time = time(NULL);

  for (int t = 0; t < num_test_cases; t++) {
    cron_expr expr;
    const char* error;
    struct timeval start, end;

    cron_parse_expr(test_cases[t].expression, &expr, &error);
    if (error) {
      printf("Error parsing '%s': %s\n", test_cases[t].expression, error);
      continue;
    }

    gettimeofday(&start, NULL);
    int operations = 0;

    for (int i = 0; i < ITERATIONS / 20; i++) {
      time_t next_time = cron_next(&expr, base_time + i);
      if (next_time != CRON_INVALID_INSTANT) {
        operations++;
      }
    }

    gettimeofday(&end, NULL);
    double elapsed = get_time_diff(start, end);

    printf("%s ('%s'): %.0f ops/sec\n", test_cases[t].description, test_cases[t].expression, operations / elapsed);
  }
  printf("\n");
}

int main() {
  printf("=== ccronexpr Performance Benchmark (Chinese Cron Expressions) ===\n");
  printf("Iterations per test: %d\n\n", ITERATIONS);

  benchmark_cron_parse_expr();
  benchmark_cron_next();
  benchmark_cron_prev();
  benchmark_chinese_cron_next();
  benchmark_expression_types();

  printf("=== Benchmark Complete ===\n");
  return 0;
}