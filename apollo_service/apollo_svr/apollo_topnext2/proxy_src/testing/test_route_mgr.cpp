#include <gmock/gmock-matchers.h>
#include <gmock/gmock-more-actions.h>
#include <gmock/gmock.h>
#include <gtest/gtest.h>
#include <cstdio>

// Include the header file for the code you want to test
#include "backend_mgr.h"
#include "route_mgr.h"

using namespace topnext_proxy;

using ::testing::Return;

class BackendMgrMock : public BackendMgr {
 public:
  MOCK_METHOD1(BuildTBusChannel, int(const std::vector<int>& tbus_addrs));
  MOCK_METHOD2(GetTbusChannel, int(int dest_tbus_addr, TBusAddress& tbus_address));
};

class MockRouteMgr : public RouteMgr {
 public:
  MOCK_METHOD2(ParseTbusAddr, int(const char* tbus_addr_str, int& tbus_addr));
};

// Define the test fixture
class RouteMgrTest : public ::testing::Test {
 protected:
  void SetUp() override {
    // Create a mock object for the logger and proxy config
    logger_ = new TLOGCATEGORYINST;
    proxy_config_ = new PROXYCFG;
    backend_mgr_ = std::make_shared<BackendMgrMock>();

    // Initialize the RouteMgr object
    // route_mgr_.Init(logger_, proxy_config_, backend_mgr_);
  }

  void TearDown() override {
    // Clean up the objects
    delete logger_;
    delete proxy_config_;
  }

  MockRouteMgr route_mgr_;

  TLOGCATEGORYINST* logger_;
  PROXYCFG* proxy_config_;
  std::shared_ptr<BackendMgrMock> backend_mgr_;
};

TEST_F(RouteMgrTest, InitSuccess) {
  EXPECT_CALL(*backend_mgr_, BuildTBusChannel(testing::_)).WillOnce(::testing::Return(0));

  int ret = route_mgr_.Init(nullptr, proxy_config_, backend_mgr_);

  EXPECT_EQ(ret, 0);
}

TEST_F(RouteMgrTest, Init_AddBusinessRouteSuccess) {
  EXPECT_CALL(*backend_mgr_, BuildTBusChannel(testing::_)).WillOnce(::testing::Return(0));
  EXPECT_CALL(route_mgr_, ParseTbusAddr(testing::_, testing::_))
      .WillOnce(::testing::DoAll(::testing::SetArgReferee<1>(12345), ::testing::Return(0)));

  proxy_config_->dwInst_num = 1;
  proxy_config_->astRoute_info[0].dwBusiness_id = 1;
  proxy_config_->astRoute_info[0].dwWorld_id = 1;
  proxy_config_->astRoute_info[0].dwZone_id = 1;
  proxy_config_->astRoute_info[0].dwType = 1;
  proxy_config_->astRoute_info[0].dwInstance_id = 1;
  proxy_config_->astRoute_info[0].dwTbus_server_count = 1;
  proxy_config_->astRoute_info[0].astTbus_server_config[0].bUpdate_permission = 1;
  proxy_config_->astRoute_info[0].astTbus_server_config[0].bQuery_permission = 1;
  snprintf(proxy_config_->astRoute_info[0].astTbus_server_config[0].szTbus_server_addr,
           sizeof(proxy_config_->astRoute_info[0].astTbus_server_config[0].szTbus_server_addr), "tbus://**********");

  int ret = route_mgr_.Init(nullptr, proxy_config_, backend_mgr_);

  EXPECT_EQ(ret, 0);

  BusinessRoute business_route;
  RouteKey route_key;
  route_key.business_id = 1;
  route_key.world_id = 1;
  route_key.zone_id = 1;
  route_key.rank_type_id = 1;
  route_key.instance_id = 1;
  ret = route_mgr_.GetBusinessRoute(route_key, business_route);
  EXPECT_EQ(ret, 0);

  EXPECT_EQ(business_route.route_info_num, 1);
  EXPECT_EQ(business_route.route_info[0].query_permission, 1);
  EXPECT_EQ(business_route.route_info[0].update_permission, 1);
  EXPECT_STREQ(business_route.route_info[0].tbus_addr_str, "tbus://**********");
  EXPECT_EQ(business_route.route_info[0].tbus_addr, 12345);

  const std::vector<int>& tbus_addrs = route_mgr_.GetTbusAddrs();
  EXPECT_EQ(tbus_addrs.size(), 1);
  EXPECT_EQ(tbus_addrs[0], 12345);
}

TEST_F(RouteMgrTest, Init_AddBusinessRoute_ParseTbusAddrFailed) {
  EXPECT_CALL(route_mgr_, ParseTbusAddr(testing::_, testing::_)).WillOnce(::testing::Return(-1));

  proxy_config_->dwInst_num = 1;
  proxy_config_->astRoute_info[0].dwBusiness_id = 1;
  proxy_config_->astRoute_info[0].dwWorld_id = 1;
  proxy_config_->astRoute_info[0].dwZone_id = 1;
  proxy_config_->astRoute_info[0].dwType = 1;
  proxy_config_->astRoute_info[0].dwInstance_id = 1;
  proxy_config_->astRoute_info[0].dwTbus_server_count = 1;
  proxy_config_->astRoute_info[0].astTbus_server_config[0].bUpdate_permission = 1;
  proxy_config_->astRoute_info[0].astTbus_server_config[0].bQuery_permission = 1;
  snprintf(proxy_config_->astRoute_info[0].astTbus_server_config[0].szTbus_server_addr,
           sizeof(proxy_config_->astRoute_info[0].astTbus_server_config[0].szTbus_server_addr), "tbus://**********");

  int ret = route_mgr_.Init(nullptr, proxy_config_, backend_mgr_);

  EXPECT_EQ(ret, -2);
}

TEST_F(RouteMgrTest, ReloadSuccess) {
  EXPECT_CALL(*backend_mgr_, BuildTBusChannel(testing::_)).Times(2).WillRepeatedly(testing::Return(0));

  int ret = route_mgr_.Init(nullptr, proxy_config_, backend_mgr_);
  EXPECT_EQ(ret, 0);

  ret = route_mgr_.Reload(proxy_config_);
  EXPECT_EQ(ret, 0);
}

TEST_F(RouteMgrTest, Reload_BuildRouteMap_AddBusinessRouteSuccess) {
  EXPECT_CALL(*backend_mgr_, BuildTBusChannel(testing::_)).Times(2).WillRepeatedly(testing::Return(0));

  EXPECT_CALL(route_mgr_, ParseTbusAddr(testing::_, testing::_))
      .Times(3)
      .WillRepeatedly(::testing::DoAll(::testing::SetArgReferee<1>(12345), ::testing::Return(0)));

  proxy_config_->dwInst_num = 1;
  proxy_config_->astRoute_info[0].dwBusiness_id = 1;
  proxy_config_->astRoute_info[0].dwWorld_id = 1;
  proxy_config_->astRoute_info[0].dwZone_id = 1;
  proxy_config_->astRoute_info[0].dwType = 1;
  proxy_config_->astRoute_info[0].dwInstance_id = 1;
  proxy_config_->astRoute_info[0].dwTbus_server_count = 1;
  proxy_config_->astRoute_info[0].astTbus_server_config[0].bUpdate_permission = 1;
  proxy_config_->astRoute_info[0].astTbus_server_config[0].bQuery_permission = 1;
  snprintf(proxy_config_->astRoute_info[0].astTbus_server_config[0].szTbus_server_addr,
           sizeof(proxy_config_->astRoute_info[0].astTbus_server_config[0].szTbus_server_addr), "tbus://**********");

  int ret = route_mgr_.Init(nullptr, proxy_config_, backend_mgr_);
  EXPECT_EQ(ret, 0);

  proxy_config_->dwInst_num = 2;
  proxy_config_->astRoute_info[1].dwBusiness_id = 2;
  proxy_config_->astRoute_info[1].dwWorld_id = 2;
  proxy_config_->astRoute_info[1].dwZone_id = 2;
  proxy_config_->astRoute_info[1].dwType = 2;
  proxy_config_->astRoute_info[1].dwInstance_id = 2;
  proxy_config_->astRoute_info[1].dwTbus_server_count = 1;
  proxy_config_->astRoute_info[1].astTbus_server_config[0].bUpdate_permission = 1;
  proxy_config_->astRoute_info[1].astTbus_server_config[0].bQuery_permission = 1;
  snprintf(proxy_config_->astRoute_info[1].astTbus_server_config[0].szTbus_server_addr,
           sizeof(proxy_config_->astRoute_info[1].astTbus_server_config[0].szTbus_server_addr), "tbus://**********");

  ret = route_mgr_.Reload(proxy_config_);
  EXPECT_EQ(ret, 0);

  BusinessRoute business_route;
  RouteKey route_key;
  route_key.business_id = 1;
  route_key.world_id = 1;
  route_key.zone_id = 1;
  route_key.rank_type_id = 1;
  route_key.instance_id = 1;
  ret = route_mgr_.GetBusinessRoute(route_key, business_route);
  EXPECT_EQ(ret, 0);
  EXPECT_EQ(business_route.route_info_num, 1);
  EXPECT_EQ(business_route.route_info[0].query_permission, 1);
  EXPECT_EQ(business_route.route_info[0].update_permission, 1);
  EXPECT_STREQ(business_route.route_info[0].tbus_addr_str, "tbus://**********");
  EXPECT_EQ(business_route.route_info[0].tbus_addr, 12345);

  route_key.business_id = 2;
  route_key.world_id = 2;
  route_key.zone_id = 2;
  route_key.rank_type_id = 2;
  route_key.instance_id = 2;
  ret = route_mgr_.GetBusinessRoute(route_key, business_route);
  EXPECT_EQ(ret, 0);
  EXPECT_EQ(business_route.route_info_num, 1);
  EXPECT_EQ(business_route.route_info[0].query_permission, 1);
  EXPECT_EQ(business_route.route_info[0].update_permission, 1);
  EXPECT_EQ(business_route.route_info[0].update_permission, 1);
  EXPECT_STREQ(business_route.route_info[0].tbus_addr_str, "tbus://**********");
  EXPECT_EQ(business_route.route_info[0].tbus_addr, 12345);

  const std::vector<int>& tbus_addrs = route_mgr_.GetTbusAddrs();
  EXPECT_EQ(tbus_addrs.size(), 1);
  EXPECT_EQ(tbus_addrs[0], 12345);
}

TEST_F(RouteMgrTest, Reload_BuildRouteMap_UpdateBussinessRouteInfoSuccess) {
  EXPECT_CALL(*backend_mgr_, BuildTBusChannel(testing::_)).Times(2).WillRepeatedly(testing::Return(0));

  EXPECT_CALL(route_mgr_, ParseTbusAddr(testing::_, testing::_))
      .Times(2)
      .WillRepeatedly(::testing::DoAll(::testing::SetArgReferee<1>(12345), ::testing::Return(0)));

  proxy_config_->dwInst_num = 1;
  proxy_config_->astRoute_info[0].dwBusiness_id = 1;
  proxy_config_->astRoute_info[0].dwWorld_id = 1;
  proxy_config_->astRoute_info[0].dwZone_id = 1;
  proxy_config_->astRoute_info[0].dwType = 1;
  proxy_config_->astRoute_info[0].dwInstance_id = 1;
  proxy_config_->astRoute_info[0].dwTbus_server_count = 1;
  proxy_config_->astRoute_info[0].astTbus_server_config[0].bUpdate_permission = 1;
  proxy_config_->astRoute_info[0].astTbus_server_config[0].bQuery_permission = 1;
  snprintf(proxy_config_->astRoute_info[0].astTbus_server_config[0].szTbus_server_addr,
           sizeof(proxy_config_->astRoute_info[0].astTbus_server_config[0].szTbus_server_addr), "tbus://**********");

  int ret = route_mgr_.Init(nullptr, proxy_config_, backend_mgr_);
  EXPECT_EQ(ret, 0);

  BusinessRoute business_route;
  RouteKey route_key;
  route_key.business_id = 1;
  route_key.world_id = 1;
  route_key.zone_id = 1;
  route_key.rank_type_id = 1;
  route_key.instance_id = 1;
  ret = route_mgr_.GetBusinessRoute(route_key, business_route);
  EXPECT_EQ(ret, 0);
  EXPECT_EQ(business_route.route_info_num, 1);
  EXPECT_EQ(business_route.route_info[0].query_permission, 1);
  EXPECT_EQ(business_route.route_info[0].update_permission, 1);
  EXPECT_STREQ(business_route.route_info[0].tbus_addr_str, "tbus://**********");
  EXPECT_EQ(business_route.route_info[0].tbus_addr, 12345);

  proxy_config_->dwInst_num = 1;
  proxy_config_->astRoute_info[0].dwBusiness_id = 1;
  proxy_config_->astRoute_info[0].dwWorld_id = 1;
  proxy_config_->astRoute_info[0].dwZone_id = 1;
  proxy_config_->astRoute_info[0].dwType = 1;
  proxy_config_->astRoute_info[0].dwInstance_id = 1;
  proxy_config_->astRoute_info[0].dwTbus_server_count = 1;
  proxy_config_->astRoute_info[0].astTbus_server_config[0].bUpdate_permission = 1;
  proxy_config_->astRoute_info[0].astTbus_server_config[0].bQuery_permission = 1;
  snprintf(proxy_config_->astRoute_info[0].astTbus_server_config[0].szTbus_server_addr,
           sizeof(proxy_config_->astRoute_info[0].astTbus_server_config[0].szTbus_server_addr), "tbus://**********");

  ret = route_mgr_.Reload(proxy_config_);
  EXPECT_EQ(ret, 0);

  route_key.business_id = 1;
  route_key.world_id = 1;
  route_key.zone_id = 1;
  route_key.rank_type_id = 1;
  route_key.instance_id = 1;
  ret = route_mgr_.GetBusinessRoute(route_key, business_route);
  EXPECT_EQ(ret, 0);
  EXPECT_STREQ(business_route.route_info[0].tbus_addr_str, "tbus://**********");
}

TEST_F(RouteMgrTest, GetAllRouteSuccess) {
  TBusAddress tbus_address;
  tbus_address.bus_channel = nullptr;
  tbus_address.dest_addr = 12345;
  tbus_address.status = RouteStatus::Reachable;
  tbus_address.heartbeat_times = 0;
  tbus_address.last_send_time = 0;

  EXPECT_CALL(*backend_mgr_, BuildTBusChannel(testing::_)).Times(1).WillRepeatedly(testing::Return(0));
  // 设置 GetTbusChannel 的期望
  EXPECT_CALL(*backend_mgr_, GetTbusChannel(testing::_, testing::_))
      .WillRepeatedly(testing::DoAll(testing::SetArgReferee<1>(tbus_address), testing::Return(0)));
  EXPECT_CALL(route_mgr_, ParseTbusAddr(testing::_, testing::_))
      .Times(1)
      .WillRepeatedly(::testing::DoAll(::testing::SetArgReferee<1>(12345), ::testing::Return(0)));

  proxy_config_->dwInst_num = 1;
  proxy_config_->astRoute_info[0].dwBusiness_id = 1;
  proxy_config_->astRoute_info[0].dwWorld_id = 1;
  proxy_config_->astRoute_info[0].dwZone_id = 1;
  proxy_config_->astRoute_info[0].dwType = 1;
  proxy_config_->astRoute_info[0].dwInstance_id = 1;
  proxy_config_->astRoute_info[0].dwTbus_server_count = 1;
  proxy_config_->astRoute_info[0].astTbus_server_config[0].bUpdate_permission = 1;
  proxy_config_->astRoute_info[0].astTbus_server_config[0].bQuery_permission = 1;
  snprintf(proxy_config_->astRoute_info[0].astTbus_server_config[0].szTbus_server_addr,
           sizeof(proxy_config_->astRoute_info[0].astTbus_server_config[0].szTbus_server_addr), "tbus://**********");

  int ret = route_mgr_.Init(nullptr, proxy_config_, backend_mgr_);
  EXPECT_EQ(ret, 0);

  RouteKey route_key;
  route_key.business_id = 1;
  route_key.world_id = 1;
  route_key.zone_id = 1;
  route_key.rank_type_id = 1;
  route_key.instance_id = 1;

  RouteAddrArray tbus_addr_array;
  ret = route_mgr_.GetAllRoute(route_key, OperateType::Query, tbus_addr_array);
  EXPECT_EQ(ret, 0);
  EXPECT_EQ(tbus_addr_array.route_addr_num, 1);
  EXPECT_EQ(tbus_addr_array.route_addr[0].tbus_address, 12345);
}

TEST_F(RouteMgrTest, GetAllRoute_NotExistsThisBusiness) {
  EXPECT_CALL(*backend_mgr_, BuildTBusChannel(testing::_)).Times(1).WillRepeatedly(testing::Return(0));

  EXPECT_CALL(route_mgr_, ParseTbusAddr(testing::_, testing::_))
      .Times(1)
      .WillRepeatedly(::testing::DoAll(::testing::SetArgReferee<1>(12345), ::testing::Return(0)));

  proxy_config_->dwInst_num = 1;
  proxy_config_->astRoute_info[0].dwBusiness_id = 1;
  proxy_config_->astRoute_info[0].dwWorld_id = 1;
  proxy_config_->astRoute_info[0].dwZone_id = 1;
  proxy_config_->astRoute_info[0].dwType = 1;
  proxy_config_->astRoute_info[0].dwInstance_id = 1;
  proxy_config_->astRoute_info[0].dwTbus_server_count = 1;
  proxy_config_->astRoute_info[0].astTbus_server_config[0].bUpdate_permission = 1;
  proxy_config_->astRoute_info[0].astTbus_server_config[0].bQuery_permission = 1;
  snprintf(proxy_config_->astRoute_info[0].astTbus_server_config[0].szTbus_server_addr,
           sizeof(proxy_config_->astRoute_info[0].astTbus_server_config[0].szTbus_server_addr), "tbus://**********");

  int ret = route_mgr_.Init(nullptr, proxy_config_, backend_mgr_);
  EXPECT_EQ(ret, 0);

  RouteKey route_key;
  route_key.business_id = 2;
  route_key.world_id = 1;
  route_key.zone_id = 1;
  route_key.rank_type_id = 1;
  route_key.instance_id = 1;

  RouteAddrArray tbus_addr_array;
  ret = route_mgr_.GetAllRoute(route_key, OperateType::Query, tbus_addr_array);
  EXPECT_EQ(ret, NotExistsThisBusiness);
}

TEST_F(RouteMgrTest, GetUsefulRouteLoopSuccess) {
  TBusAddress tbus_address;
  tbus_address.bus_channel = nullptr;
  tbus_address.dest_addr = 12345;
  tbus_address.status = RouteStatus::Reachable;
  tbus_address.heartbeat_times = 0;
  tbus_address.last_send_time = 0;

  EXPECT_CALL(*backend_mgr_, BuildTBusChannel(testing::_)).Times(1).WillRepeatedly(testing::Return(0));
  // 设置 GetTbusChannel 的期望
  EXPECT_CALL(*backend_mgr_, GetTbusChannel(testing::_, testing::_))
      .WillRepeatedly(testing::DoAll(testing::SetArgReferee<1>(tbus_address), testing::Return(0)));
  EXPECT_CALL(route_mgr_, ParseTbusAddr(testing::_, testing::_))
      .Times(1)
      .WillRepeatedly(::testing::DoAll(::testing::SetArgReferee<1>(12345), ::testing::Return(0)));

  proxy_config_->dwInst_num = 1;
  proxy_config_->astRoute_info[0].dwBusiness_id = 1;
  proxy_config_->astRoute_info[0].dwWorld_id = 1;
  proxy_config_->astRoute_info[0].dwZone_id = 1;
  proxy_config_->astRoute_info[0].dwType = 1;
  proxy_config_->astRoute_info[0].dwInstance_id = 1;
  proxy_config_->astRoute_info[0].dwTbus_server_count = 1;
  proxy_config_->astRoute_info[0].astTbus_server_config[0].bUpdate_permission = 1;
  proxy_config_->astRoute_info[0].astTbus_server_config[0].bQuery_permission = 1;
  snprintf(proxy_config_->astRoute_info[0].astTbus_server_config[0].szTbus_server_addr,
           sizeof(proxy_config_->astRoute_info[0].astTbus_server_config[0].szTbus_server_addr), "tbus://**********");

  int ret = route_mgr_.Init(nullptr, proxy_config_, backend_mgr_);
  EXPECT_EQ(ret, 0);

  RouteKey route_key;
  route_key.business_id = 1;
  route_key.world_id = 1;
  route_key.zone_id = 1;
  route_key.rank_type_id = 1;
  route_key.instance_id = 1;

  RouteAddrArray tbus_addr_array;
  ret = route_mgr_.GetUsefulRouteLoop(route_key, OperateType::Query, tbus_addr_array);
  EXPECT_EQ(ret, 0);
  EXPECT_EQ(tbus_addr_array.route_addr_num, 1);
  EXPECT_EQ(tbus_addr_array.route_addr[0].tbus_address, 12345);
}

TEST_F(RouteMgrTest, GetUsefulRouteLoop_Unreachable) {
  TBusAddress tbus_address;
  tbus_address.bus_channel = nullptr;
  tbus_address.dest_addr = 12345;
  tbus_address.status = RouteStatus::Unreachable;
  tbus_address.heartbeat_times = 0;
  tbus_address.last_send_time = 0;

  EXPECT_CALL(*backend_mgr_, BuildTBusChannel(testing::_)).Times(1).WillRepeatedly(testing::Return(0));
  // 设置 GetTbusChannel 的期望
  EXPECT_CALL(*backend_mgr_, GetTbusChannel(testing::_, testing::_))
      .WillRepeatedly(testing::DoAll(testing::SetArgReferee<1>(tbus_address), testing::Return(0)));
  EXPECT_CALL(route_mgr_, ParseTbusAddr(testing::_, testing::_))
      .Times(1)
      .WillRepeatedly(::testing::DoAll(::testing::SetArgReferee<1>(12345), ::testing::Return(0)));

  proxy_config_->dwInst_num = 1;
  proxy_config_->astRoute_info[0].dwBusiness_id = 1;
  proxy_config_->astRoute_info[0].dwWorld_id = 1;
  proxy_config_->astRoute_info[0].dwZone_id = 1;
  proxy_config_->astRoute_info[0].dwType = 1;
  proxy_config_->astRoute_info[0].dwInstance_id = 1;
  proxy_config_->astRoute_info[0].dwTbus_server_count = 1;
  proxy_config_->astRoute_info[0].astTbus_server_config[0].bUpdate_permission = 1;
  proxy_config_->astRoute_info[0].astTbus_server_config[0].bQuery_permission = 1;
  snprintf(proxy_config_->astRoute_info[0].astTbus_server_config[0].szTbus_server_addr,
           sizeof(proxy_config_->astRoute_info[0].astTbus_server_config[0].szTbus_server_addr), "tbus://**********");

  int ret = route_mgr_.Init(nullptr, proxy_config_, backend_mgr_);
  EXPECT_EQ(ret, 0);

  RouteKey route_key;
  route_key.business_id = 1;
  route_key.world_id = 1;
  route_key.zone_id = 1;
  route_key.rank_type_id = 1;
  route_key.instance_id = 1;

  RouteAddrArray tbus_addr_array;
  ret = route_mgr_.GetUsefulRouteLoop(route_key, OperateType::Query, tbus_addr_array);
  EXPECT_EQ(ret, ThisBusinessHasNoRoute);
  EXPECT_EQ(tbus_addr_array.route_addr_num, 0);
}

TEST_F(RouteMgrTest, GetUsefulRouteLoop_NotExistsThisBusiness) {
  EXPECT_CALL(*backend_mgr_, BuildTBusChannel(testing::_)).Times(1).WillRepeatedly(testing::Return(0));

  EXPECT_CALL(route_mgr_, ParseTbusAddr(testing::_, testing::_))
      .Times(1)
      .WillRepeatedly(::testing::DoAll(::testing::SetArgReferee<1>(12345), ::testing::Return(0)));

  proxy_config_->dwInst_num = 1;
  proxy_config_->astRoute_info[0].dwBusiness_id = 1;
  proxy_config_->astRoute_info[0].dwWorld_id = 1;
  proxy_config_->astRoute_info[0].dwZone_id = 1;
  proxy_config_->astRoute_info[0].dwType = 1;
  proxy_config_->astRoute_info[0].dwInstance_id = 1;
  proxy_config_->astRoute_info[0].dwTbus_server_count = 1;
  proxy_config_->astRoute_info[0].astTbus_server_config[0].bUpdate_permission = 1;
  proxy_config_->astRoute_info[0].astTbus_server_config[0].bQuery_permission = 1;
  snprintf(proxy_config_->astRoute_info[0].astTbus_server_config[0].szTbus_server_addr,
           sizeof(proxy_config_->astRoute_info[0].astTbus_server_config[0].szTbus_server_addr), "tbus://**********");

  int ret = route_mgr_.Init(nullptr, proxy_config_, backend_mgr_);
  EXPECT_EQ(ret, 0);

  RouteKey route_key;
  route_key.business_id = 2;
  route_key.world_id = 1;
  route_key.zone_id = 1;
  route_key.rank_type_id = 1;
  route_key.instance_id = 1;

  RouteAddrArray tbus_addr_array;
  ret = route_mgr_.GetUsefulRouteLoop(route_key, OperateType::Query, tbus_addr_array);
  EXPECT_EQ(ret, NotExistsThisBusiness);
}

// Run the tests
int main(int argc, char** argv) {
  ::testing::InitGoogleTest(&argc, argv);
  return RUN_ALL_TESTS();
}