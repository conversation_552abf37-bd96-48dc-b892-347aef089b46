#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include "backend_mgr.h"
#include "tbus_handler.h"

namespace topnext_proxy {

// Mock TBusHandler
class MockTBusHandler : public TBusHandler {
 public:
  MOCK_METHOD4(OnResponse, int(TBusAddress&, TopnextProxyHeader&, const char*, uint32_t));
};

class MockBackendMgr : public BackendMgr {
 public:
  MOCK_METHOD1(TBusNew, int(int*));
  MOCK_METHOD1(TBusDelete, void(int*));
  MOCK_METHOD2(TBusBind, int(int, int));
  MOCK_METHOD4(TBusSetHandleOpt, int(int, TBUSHANDLEOPTIONAME, const void*, unsigned int));
  MOCK_METHOD2(TBusConnect, int(const int, const TBUSADDR));
  MOCK_METHOD4(TBusGetChannel, int(const int, LPTBUSCHANNEL*, TBUSADDR, TBUSADDR));
  MOCK_METHOD4(TBusChannelSend, int(LPTBUSCHANNEL, const void*, const int, const int));
  MOCK_METHOD4(TBusChannelPeekMsg, int(LPTBUSCHANNEL, const char**, size_t*, const int));
  MOCK_METHOD1(TBusChannelDeleteMsg, int(LPTBUSCHANNEL));
  MOCK_METHOD1(TdrNtoh16, uint16_t(uint16_t));
  MOCK_METHOD0(ShouldRefreshTbus, bool());
};

// Define the test fixture
class BackendMgrTest : public ::testing::Test {
 protected:
  void SetUp() override {
    logger_ = new TLOGCATEGORYINST;
    proxy_config_ = new PROXYCFG;
    tbus_handler_ = std::make_shared<MockTBusHandler>();
  }

  void TearDown() override {
    delete logger_;
    delete proxy_config_;
  }

  MockBackendMgr backend_mgr_;

  TLOGCATEGORYINST* logger_;
  PROXYCFG* proxy_config_;
  std::shared_ptr<MockTBusHandler> tbus_handler_;
};

TEST_F(BackendMgrTest, InitSuccess) {
  EXPECT_CALL(backend_mgr_, TBusNew(testing::_)).WillOnce(::testing::Return(0));
  EXPECT_CALL(backend_mgr_, TBusBind(testing::_, testing::_)).WillOnce(::testing::Return(0));
  EXPECT_CALL(backend_mgr_, TBusSetHandleOpt(testing::_, testing::_, testing::_, testing::_))
      .WillOnce(::testing::Return(0));

  int ret = backend_mgr_.Init(nullptr, 12345, proxy_config_, tbus_handler_);

  EXPECT_EQ(ret, 0);
}

TEST_F(BackendMgrTest, ReloadSuccess) {
  EXPECT_CALL(backend_mgr_, TBusNew(testing::_)).WillOnce(::testing::Return(0));
  EXPECT_CALL(backend_mgr_, TBusBind(testing::_, testing::_)).WillOnce(::testing::Return(0));
  EXPECT_CALL(backend_mgr_, TBusSetHandleOpt(testing::_, testing::_, testing::_, testing::_))
      .WillOnce(::testing::Return(0));
  int ret = backend_mgr_.Init(nullptr, 12345, proxy_config_, tbus_handler_);
  EXPECT_EQ(ret, 0);

  ret = backend_mgr_.Reload(proxy_config_);
  EXPECT_EQ(ret, 0);
}

TEST_F(BackendMgrTest, BuildTBusChannel_EmptyTbusAddrs) {
  EXPECT_CALL(backend_mgr_, TBusNew(testing::_)).WillOnce(::testing::Return(0));
  EXPECT_CALL(backend_mgr_, TBusBind(testing::_, testing::_)).WillOnce(::testing::Return(0));
  EXPECT_CALL(backend_mgr_, TBusSetHandleOpt(testing::_, testing::_, testing::_, testing::_))
      .WillOnce(::testing::Return(0));
  int ret = backend_mgr_.Init(nullptr, 12345, proxy_config_, tbus_handler_);
  EXPECT_EQ(ret, 0);

  std::vector<int> tbus_addrs;
  ret = backend_mgr_.BuildTBusChannel(tbus_addrs);
  EXPECT_EQ(ret, 0);
}

TEST_F(BackendMgrTest, BuildTBusChannelSuccess) {
  EXPECT_CALL(backend_mgr_, TBusNew(testing::_)).WillOnce(::testing::Return(0));
  EXPECT_CALL(backend_mgr_, TBusBind(testing::_, testing::_)).WillOnce(::testing::Return(0));
  EXPECT_CALL(backend_mgr_, TBusSetHandleOpt(testing::_, testing::_, testing::_, testing::_))
      .WillOnce(::testing::Return(0));
  int ret = backend_mgr_.Init(nullptr, 12345, proxy_config_, tbus_handler_);
  EXPECT_EQ(ret, 0);

  EXPECT_CALL(backend_mgr_, TBusConnect(testing::_, testing::_)).Times(2).WillRepeatedly(::testing::Return(0));
  EXPECT_CALL(backend_mgr_, TBusGetChannel(testing::_, testing::_, testing::_, testing::_))
      .Times(2)
      .WillRepeatedly(::testing::Return(0));

  std::vector<int> tbus_addrs;
  tbus_addrs.push_back(11111);
  tbus_addrs.push_back(22222);
  ret = backend_mgr_.BuildTBusChannel(tbus_addrs);
  EXPECT_EQ(ret, 0);
}

TEST_F(BackendMgrTest, BuildTBusChannel_DeleteInvalidTbusAddr) {
  EXPECT_CALL(backend_mgr_, TBusNew(testing::_)).WillOnce(::testing::Return(0));
  EXPECT_CALL(backend_mgr_, TBusBind(testing::_, testing::_)).WillOnce(::testing::Return(0));
  EXPECT_CALL(backend_mgr_, TBusSetHandleOpt(testing::_, testing::_, testing::_, testing::_))
      .WillOnce(::testing::Return(0));
  int ret = backend_mgr_.Init(nullptr, 12345, proxy_config_, tbus_handler_);
  EXPECT_EQ(ret, 0);

  EXPECT_CALL(backend_mgr_, TBusConnect(testing::_, testing::_)).Times(3).WillRepeatedly(::testing::Return(0));
  EXPECT_CALL(backend_mgr_, TBusGetChannel(testing::_, testing::_, testing::_, testing::_))
      .Times(3)
      .WillRepeatedly(::testing::Return(0));

  std::vector<int> tbus_addrs;
  tbus_addrs.push_back(11111);
  tbus_addrs.push_back(22222);
  ret = backend_mgr_.BuildTBusChannel(tbus_addrs);
  EXPECT_EQ(ret, 0);

  tbus_addrs.clear();
  tbus_addrs.push_back(11111);
  tbus_addrs.push_back(33333);
  ret = backend_mgr_.BuildTBusChannel(tbus_addrs);
  EXPECT_EQ(ret, 0);
}

TEST_F(BackendMgrTest, SendRequestSuccess) {
  EXPECT_CALL(backend_mgr_, TBusNew(testing::_)).WillOnce(::testing::Return(0));
  EXPECT_CALL(backend_mgr_, TBusBind(testing::_, testing::_)).WillOnce(::testing::Return(0));
  EXPECT_CALL(backend_mgr_, TBusSetHandleOpt(testing::_, testing::_, testing::_, testing::_))
      .WillOnce(::testing::Return(0));
  int ret = backend_mgr_.Init(nullptr, 12345, proxy_config_, tbus_handler_);
  EXPECT_EQ(ret, 0);

  EXPECT_CALL(backend_mgr_, TBusChannelSend(testing::_, testing::_, testing::_, testing::_))
      .WillOnce(::testing::Return(0));

  std::string request_buffer = "test_request";
  ret = backend_mgr_.SendRequest(nullptr, request_buffer.c_str(), request_buffer.size(), false);
  EXPECT_EQ(ret, 0);
}

TEST_F(BackendMgrTest, OnRecvResponseS_ProcessSingleResponse_Success) {
  EXPECT_CALL(backend_mgr_, TBusNew(testing::_)).WillOnce(::testing::Return(0));
  EXPECT_CALL(backend_mgr_, TBusBind(testing::_, testing::_)).WillOnce(::testing::Return(0));
  EXPECT_CALL(backend_mgr_, TBusSetHandleOpt(testing::_, testing::_, testing::_, testing::_))
      .WillOnce(::testing::Return(0));
  int ret = backend_mgr_.Init(nullptr, 12345, proxy_config_, tbus_handler_);
  EXPECT_EQ(ret, 0);

  // add tbus channel
  EXPECT_CALL(backend_mgr_, TBusConnect(testing::_, testing::_)).Times(2).WillRepeatedly(::testing::Return(0));
  EXPECT_CALL(backend_mgr_, TBusGetChannel(testing::_, testing::_, testing::_, testing::_))
      .Times(2)
      .WillRepeatedly(::testing::Return(0));

  std::vector<int> tbus_addrs;
  tbus_addrs.push_back(11111);
  tbus_addrs.push_back(22222);
  ret = backend_mgr_.BuildTBusChannel(tbus_addrs);
  EXPECT_EQ(ret, 0);

  // TOPNEXT_PROXY_MAGIC
  EXPECT_CALL(backend_mgr_, TdrNtoh16(testing::_)).Times(200).WillRepeatedly(::testing::Return(TOPNEXT_PROXY_MAGIC));
  EXPECT_CALL(backend_mgr_, TBusChannelDeleteMsg(testing::_)).Times(200).WillRepeatedly(::testing::Return(0));

  TopnextProxyHeader proxy_header;
  proxy_header.bCmd = 123;
  proxy_header.dwBusiness_id = 1;
  std::array<char, 8096> buffer;
  size_t used = 0;
  proxy_header.pack(buffer.data(), buffer.size(), &used);

  // peek msg
  EXPECT_CALL(backend_mgr_, TBusChannelPeekMsg(testing::_, testing::_, testing::_, testing::_))
      .Times(200)
      .WillRepeatedly(DoAll(
          // 将msg参数设置为mock_msg
          testing::SetArgPointee<1>(buffer.data()),
          // 将msg_size参数设置为mock_msg_size
          testing::SetArgPointee<2>(used),
          // 返回0,表示成功
          testing::Return(0)));
  EXPECT_CALL(*tbus_handler_, OnResponse(testing::_, testing::_, testing::_, testing::_))
      .Times(200)
      .WillRepeatedly(::testing::Return(-1));

  ret = backend_mgr_.Update();
  EXPECT_EQ(ret, 200);
}

TEST_F(BackendMgrTest, OnRecvResponseS_ProcessSingleResponse_Heartbeat_Success) {
  EXPECT_CALL(backend_mgr_, TBusNew(testing::_)).WillOnce(::testing::Return(0));
  EXPECT_CALL(backend_mgr_, TBusBind(testing::_, testing::_)).WillOnce(::testing::Return(0));
  EXPECT_CALL(backend_mgr_, TBusSetHandleOpt(testing::_, testing::_, testing::_, testing::_))
      .WillOnce(::testing::Return(0));
  int ret = backend_mgr_.Init(nullptr, 12345, proxy_config_, tbus_handler_);
  EXPECT_EQ(ret, 0);

  // add tbus channel
  EXPECT_CALL(backend_mgr_, TBusConnect(testing::_, testing::_)).Times(2).WillRepeatedly(::testing::Return(0));
  EXPECT_CALL(backend_mgr_, TBusGetChannel(testing::_, testing::_, testing::_, testing::_))
      .Times(2)
      .WillRepeatedly(::testing::Return(0));

  std::vector<int> tbus_addrs;
  tbus_addrs.push_back(11111);
  tbus_addrs.push_back(22222);
  ret = backend_mgr_.BuildTBusChannel(tbus_addrs);
  EXPECT_EQ(ret, 0);

  // TOPNEXT_PROXY_MAGIC
  EXPECT_CALL(backend_mgr_, TdrNtoh16(testing::_)).Times(200).WillRepeatedly(::testing::Return(TOPNEXT_PROXY_MAGIC));
  EXPECT_CALL(backend_mgr_, TBusChannelDeleteMsg(testing::_)).Times(200).WillRepeatedly(::testing::Return(0));

  struct timeval tv;
  gettimeofday(&tv, nullptr);
  uint64_t now = (uint64_t)tv.tv_sec * 1000 + tv.tv_usec / 1000;

  TopnextProxyHeader proxy_header;
  proxy_header.bCmd = HEARTBEAT;
  proxy_header.dwBusiness_id = 1;
  proxy_header.ullTimestamp = now - 1000;
  std::array<char, 8096> buffer;
  size_t used = 0;
  proxy_header.pack(buffer.data(), buffer.size(), &used);

  // peek msg
  EXPECT_CALL(backend_mgr_, TBusChannelPeekMsg(testing::_, testing::_, testing::_, testing::_))
      .Times(200)
      .WillRepeatedly(DoAll(
          // 将msg参数设置为mock_msg
          testing::SetArgPointee<1>(buffer.data()),
          // 将msg_size参数设置为mock_msg_size
          testing::SetArgPointee<2>(used),
          // 返回0,表示成功
          testing::Return(0)));

  ret = backend_mgr_.Update();
  EXPECT_EQ(ret, 200);
  auto& tbus_addr_map = backend_mgr_.GetTbusAddrMap();
  EXPECT_EQ(tbus_addr_map.size(), 2);
  for (auto& pair : tbus_addr_map) {
    EXPECT_EQ(pair.second.status, RouteStatus::Reachable);
  }
}

TEST_F(BackendMgrTest, OnRecvResponse_PeekMsgChannelEmpty) {
  EXPECT_CALL(backend_mgr_, TBusNew(testing::_)).WillOnce(::testing::Return(0));
  EXPECT_CALL(backend_mgr_, TBusBind(testing::_, testing::_)).WillOnce(::testing::Return(0));
  EXPECT_CALL(backend_mgr_, TBusSetHandleOpt(testing::_, testing::_, testing::_, testing::_))
      .WillOnce(::testing::Return(0));
  int ret = backend_mgr_.Init(nullptr, 12345, proxy_config_, tbus_handler_);
  EXPECT_EQ(ret, 0);

  // add tbus channel
  EXPECT_CALL(backend_mgr_, TBusConnect(testing::_, testing::_)).Times(2).WillRepeatedly(::testing::Return(0));
  EXPECT_CALL(backend_mgr_, TBusGetChannel(testing::_, testing::_, testing::_, testing::_))
      .Times(2)
      .WillRepeatedly(::testing::Return(0));

  std::vector<int> tbus_addrs;
  tbus_addrs.push_back(11111);
  tbus_addrs.push_back(22222);
  ret = backend_mgr_.BuildTBusChannel(tbus_addrs);
  EXPECT_EQ(ret, 0);

  // peek msg
  EXPECT_CALL(backend_mgr_, TBusChannelPeekMsg(testing::_, testing::_, testing::_, testing::_))
      .Times(2)
      .WillRepeatedly(::testing::Return(TBUS_ERR_CHANNEL_EMPTY));

  ret = backend_mgr_.Update();
  EXPECT_EQ(ret, 0);
}

TEST_F(BackendMgrTest, Tick_ShouldRefreshTbus) {
  EXPECT_CALL(backend_mgr_, TBusNew(testing::_)).WillOnce(::testing::Return(0));
  EXPECT_CALL(backend_mgr_, TBusBind(testing::_, testing::_)).WillOnce(::testing::Return(0));
  EXPECT_CALL(backend_mgr_, TBusSetHandleOpt(testing::_, testing::_, testing::_, testing::_))
      .WillOnce(::testing::Return(0));
  int ret = backend_mgr_.Init(nullptr, 12345, proxy_config_, tbus_handler_);
  EXPECT_EQ(ret, 0);

  EXPECT_CALL(backend_mgr_, ShouldRefreshTbus()).WillOnce(::testing::Return(true));

  ret = backend_mgr_.Tick();
  EXPECT_EQ(ret, 0);
}

}  // namespace topnext_proxy

int main(int argc, char** argv) {
  ::testing::InitGoogleTest(&argc, argv);
  return RUN_ALL_TESTS();
}