cmake_minimum_required(VERSION 3.14)

# 设置C++标准
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 编译选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -O2")

# 包含目录
include_directories(.)


set(GEN_SRC
    ${CMAKE_CURRENT_LIST_DIR}/protocol/proxy_protocol.h
    ${CMAKE_CURRENT_LIST_DIR}/protocol/proxy_protocol.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/protocol/apollo_service_base_header_topnext.h
    ${CMAKE_CURRENT_SOURCE_DIR}/protocol/apollo_service_base_header_topnext.cpp
    ${CMAKE_CURRENT_LIST_DIR}/protocol/topnext_protocol_tdr.cpp
    ${CMAKE_CURRENT_LIST_DIR}/protocol/topnext_protocol_tdr.h)

add_custom_command(
  OUTPUT ${GEN_SRC}
  COMMAND mkdir -p ${CMAKE_CURRENT_SOURCE_DIR}/protocol
  COMMAND
    cp -rf
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../apollo_xml/apollo_topnext2/topnext_protocol.xml
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../apollo_xml/apollo_topnext2/topnext_protocol_tdr.xml
  COMMAND
    ${TDR} --xml2h
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../apollo_xml/apollo_topnext2/proxy_protocol.xml
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../apollo_xml/apollo_topnext2/apollo_service_base_header_topnext.xml
    --out_path=${CMAKE_CURRENT_SOURCE_DIR}/protocol/
  COMMAND
    ${TDR} --xml2cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../apollo_xml/apollo_topnext2/proxy_protocol.xml
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../apollo_xml/apollo_topnext2/apollo_service_base_header_topnext.xml
    --no_comm_files --out_path=${CMAKE_CURRENT_SOURCE_DIR}/protocol/
  COMMAND
    ${TDR} --xml2h
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../apollo_xml/apollo_topnext2/topnext_protocol_tdr.xml
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../apollo_xml/apollo_topnext2/apollo_service_base_header_topnext_proto.xml
    --out_path=${CMAKE_CURRENT_SOURCE_DIR}/protocol/
  COMMAND
    ${TDR} --xml2cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../apollo_xml/apollo_topnext2/topnext_protocol_tdr.xml
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../apollo_xml/apollo_topnext2/apollo_service_base_header_topnext_proto.xml
    --no_comm_files -p --out_path=${CMAKE_CURRENT_SOURCE_DIR}/protocol/
  COMMAND rm -rf
    ${CMAKE_CURRENT_SOURCE_DIR}/protocol/topnext_proto_tdr_metalib.h
  COMMAND rm -rf
    ${CMAKE_CURRENT_SOURCE_DIR}/protocol/topnext_proxy_proto_metalib.h
  WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
  COMMENT "generate protocol files"
  VERBATIM)

add_custom_target(fast_check_proto_gen DEPENDS ${GEN_SRC})

# 检查topnext_protocol_tdr.cpp文件是否存在
# set(TDR_CPP_FILE "${CMAKE_CURRENT_LIST_DIR}/../protocol/topnext_protocol_tdr.cpp")
# if(EXISTS ${TDR_CPP_FILE})
#     message(STATUS "找到TDR协议文件: ${TDR_CPP_FILE}")
#     set(TDR_SOURCES ${TDR_CPP_FILE})
# else()
#     message(WARNING "TDR协议文件不存在: ${TDR_CPP_FILE}")
#     set(TDR_SOURCES "")
# endif()

# 快速检测榜单数据一致性
add_executable(fast_check fast_check.cpp ${GEN_SRC})
add_dependencies(fast_check fast_check_proto_gen)
target_link_libraries(fast_check 
    Threads::Threads 
    asio 
    tsf4g 
    trapidxml 
    linenoise 
    pal 
    ncurses 
    tdr_comm 
    gflags
    ${LIB_GCOV})
target_include_directories(
      fast_check
      PUBLIC ${CMAKE_CURRENT_LIST_DIR}
             ${CMAKE_CURRENT_LIST_DIR}/protocol)

# 显示编译信息
# message(STATUS "项目: ${PROJECT_NAME}")
# message(STATUS "C++标准: ${CMAKE_CXX_STANDARD}")
# message(STATUS "编译器: ${CMAKE_CXX_COMPILER}")
# message(STATUS "编译选项: ${CMAKE_CXX_FLAGS}")