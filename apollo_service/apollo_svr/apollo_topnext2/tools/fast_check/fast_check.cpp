#include <arpa/inet.h>
#include <errno.h>
#include <netdb.h>
#include <netinet/in.h>
#include <sys/socket.h>
#include <unistd.h>
#include <algorithm>
#include <atomic>
#include <chrono>
#include <cstdint>
#include <cstring>
#include <functional>
#include <iomanip>
#include <iostream>
#include <iterator>
#include <map>
#include <memory>
#include <numeric>
#include <random>
#include <set>
#include <sstream>
#include <string>
#include <thread>
#include <vector>

#include <gflags/gflags.h>

#include "../protocol/apollo_service_base_header_topnext_proto.h"
#include "../protocol/proxy_protocol.h"
#include "../protocol/topnext_protocol_tdr.h"

using namespace topnext_proto_tdr;

// 定义命令行参数
DEFINE_string(server1, "127.0.0.1:10301", "第一个服务器地址 (格式: IP:port)");
DEFINE_string(server1_name, "TopNextServer1", "第一个服务器的名称");

DEFINE_string(server2, "127.0.0.1:10302", "第二个服务器地址 (格式: IP:port)");
DEFINE_string(server2_name, "TopNextServer2", "第二个服务器的名称");

DEFINE_uint64(business_id, 10000, "业务ID");
DEFINE_uint64(world_id, 1, "世界ID");
DEFINE_uint64(zone_id, 1, "区域ID");
DEFINE_uint64(rank_type, 1, "排行榜类型");
DEFINE_uint64(rank_instance_id, 1, "排行榜实例ID");
DEFINE_uint64(sub_rank_type, 1, "子排行榜类型");
DEFINE_uint64(sub_rank_instance_id, 1, "子排行榜实例ID");
DEFINE_uint64(query_from, 1, "查询起始位置");
DEFINE_uint64(query_count, 100, "查询数量");
DEFINE_bool(use_image_data, false, "是否使用镜像数据源 (false=实时数据, true=镜像数据)");
DEFINE_uint64(image_index, 0, "镜像索引 (当data_source=1时使用)");
DEFINE_bool(check_all_sub_ranks, false, "是否获取所有子榜并逐个校验");
DEFINE_double(random_sample_ratio, 0.0, "随机抽样校验比例 (0.0-1.0, 0表示不随机抽样, 0.1表示抽样10%)");
DEFINE_uint64(random_seed, 0, "随机数种子 (0表示使用当前时间作为种子)");
DEFINE_string(sample_sub_rank_types, "", "指定抽样的子榜类型列表 (逗号分隔, 如: 1001,1002,1003, 空表示所有类型)");
DEFINE_bool(auto_report_inconsistent, false, "是否自动上报不一致的数据到服务器进行修复");
DEFINE_bool(only_show_summary, false, "是否只显示最后的统计信息");
DEFINE_bool(use_default_config, false, "是否使用默认配置");

DEFINE_bool(test_local, false, "是否连接到本地测试服务器");
DEFINE_bool(verbose, false, "是否显示详细输出");

// 排行榜配置结构
struct RankConfig {
  int score_proc_type;
  int sort_method_on_timestamp;
  int sort_method_on_score;
  int sort_field_num;
  int sort_method_on_field[5];  // 5个扩展排序字段的排序方向配置

  RankConfig() : score_proc_type(1), sort_method_on_timestamp(0), sort_method_on_score(0) {
    for (int i = 0; i < 5; ++i) {
      sort_method_on_field[i] = 0;  // 默认降序
    }
  }
};

// 解析子榜类型列表
std::set<uint32_t> parse_sub_rank_types(const std::string& types_str) {
  std::set<uint32_t> types;
  if (types_str.empty()) {
    return types;  // 返回空集合表示所有类型
  }

  std::stringstream ss(types_str);
  std::string item;
  while (std::getline(ss, item, ',')) {
    // 去除前后空格
    item.erase(0, item.find_first_not_of(" \t"));
    item.erase(item.find_last_not_of(" \t") + 1);

    if (!item.empty()) {
      try {
        uint32_t type = std::stoul(item);
        types.insert(type);
      } catch (const std::invalid_argument& e) {
        std::cerr << "警告: 无效的子榜类型格式 '" << item << "' - " << e.what() << ", 已忽略" << std::endl;
      } catch (const std::out_of_range& e) {
        std::cerr << "警告: 子榜类型超出范围 '" << item << "' - " << e.what() << ", 已忽略" << std::endl;
      } catch (const std::exception& e) {
        std::cerr << "警告: 解析子榜类型时发生未知错误 '" << item << "' - " << e.what() << ", 已忽略" << std::endl;
      }
    }
  }
  return types;
}

// 解析IP:port格式的服务器地址
std::pair<std::string, int> parse_server_address(const std::string& address) {
  size_t colon_pos = address.find(':');
  if (colon_pos == std::string::npos) {
    std::cerr << "错误的服务器地址格式: " << address << " (应该是 IP:port)" << std::endl;
    return std::make_pair("127.0.0.1", 10301);
  }

  std::string host = address.substr(0, colon_pos);
  std::string port_str = address.substr(colon_pos + 1);

  int port = 10301;  // 默认端口
  try {
    port = std::stoi(port_str);
    if (port <= 0 || port > 65535) {
      std::cerr << "错误的端口号: " << port << " (应该在1-65535之间)" << std::endl;
      return std::make_pair(host, 10301);
    }
  } catch (const std::invalid_argument& e) {
    std::cerr << "无效的端口号格式: " << port_str << " - " << e.what() << std::endl;
    return std::make_pair(host, 10301);
  } catch (const std::out_of_range& e) {
    std::cerr << "端口号超出范围: " << port_str << " - " << e.what() << std::endl;
    return std::make_pair(host, 10301);
  }

  return std::make_pair(host, port);
}

// 服务器连接信息结构体
struct ServerInfo {
  std::string host;
  int port;
  std::string name;

  ServerInfo(const std::string& h, int p, const std::string& n) : host(h), port(p), name(n) {}
};

// 排名数据结构
struct RankData {
  std::string openid;
  int64_t score;
  int32_t rank;
  uint64_t timestamp;              // 时间戳，用于最新值模式比较
  uint64_t last_report_timestamp;  // 最后一次上报的时间,不参与排序

  // 扩展排序字段
  uint32_t sort_field1;
  uint32_t sort_field2;
  uint32_t sort_field3;
  uint32_t sort_field4;
  uint32_t sort_field5;

  uint64_t ext_field1;  // 扩展字段1
  uint64_t ext_field2;  // 扩展字段2
  uint64_t ext_field3;  // 扩展字段3

  uint32_t reduce_field1;  // 参与衰减的字段1，具体使用规则见衰减的实现
  uint32_t reduce_field2;  // 参与衰减的字段2，具体使用规则见衰减的实现
  uint32_t reduce_field3;  // 参与衰减的字段3，具体使用规则见衰减的实现

  // 扩展数据（二进制数据，可能包含\0）
  std::vector<uint8_t> ext_data;

  // 构造函数
  RankData()
      : score(0),
        rank(0),
        timestamp(0),
        last_report_timestamp(0),
        sort_field1(0),
        sort_field2(0),
        sort_field3(0),
        sort_field4(0),
        sort_field5(0),
        ext_field1(0),
        ext_field2(0),
        ext_field3(0),
        reduce_field1(0),
        reduce_field2(0),
        reduce_field3(0) {}
};

// 简化的子榜信息结构
struct SimpleSubRankInfo {
  uint32_t type;
  uint32_t instance_id;

  SimpleSubRankInfo() : type(0), instance_id(0) {}
  SimpleSubRankInfo(uint32_t t, uint32_t id) : type(t), instance_id(id) {}
};

// 双服务器TopNext客户端类
class FastCheckTopNextClient {
 public:
  FastCheckTopNextClient(const ServerInfo& server1, const ServerInfo& server2)
      : server1_(server1), server2_(server2), socket1_(-1), socket2_(-1), connected1_(false), connected2_(false) {
    // 原子变量需要在构造函数体中初始化
    report_to_server1_count_.store(0);
    report_to_server2_count_.store(0);
  }

  ~FastCheckTopNextClient() { close_connections(); }

  // 连接到两个服务器
  bool connect_both_servers() {
    bool success1 = connect_to_server(server1_, socket1_, connected1_);
    bool success2 = connect_to_server(server2_, socket2_, connected2_);

    if (success1 && success2) {
      std::cout << "成功连接到两个服务器" << std::endl;
      return true;
    } else {
      std::cerr << "连接服务器失败: Server1=" << (success1 ? "成功" : "失败")
                << ", Server2=" << (success2 ? "成功" : "失败") << std::endl;
      return false;
    }
  }

  // 获取子榜列表并对每个子榜进行校验
  bool get_and_check_all_sub_ranks(int score_proc_type, int sort_method_on_timestamp, int sort_method_on_score,
                                   int sort_field_num, const int sort_method_on_field[5], uint32_t business_id = 10000,
                                   uint32_t world_id = 1, uint32_t zone_id = 1, uint32_t rank_type = 1,
                                   uint32_t rank_instance_id = 1, uint32_t query_from = 1, uint32_t query_count = 100) {
    std::cout << "\n=== 开始获取子榜列表并进行校验 ===" << std::endl;
    std::cout << "请求参数: business_id=" << business_id << ", world_id=" << world_id << ", zone_id=" << zone_id
              << ", rank_type=" << rank_type << ", rank_instance_id=" << rank_instance_id << std::endl;

    // 从两个服务器分别获取子榜列表
    std::vector<SimpleSubRankInfo> sub_rank_list1, sub_rank_list2;
    bool success1 = get_sub_rank_list(socket1_, server1_.name, sub_rank_list1, business_id, world_id, zone_id,
                                      rank_type, rank_instance_id);
    bool success2 = get_sub_rank_list(socket2_, server2_.name, sub_rank_list2, business_id, world_id, zone_id,
                                      rank_type, rank_instance_id);

    if (!success1 && !success2) {
      std::cerr << "从两个服务器获取子榜列表都失败" << std::endl;
      return false;
    }

    std::cout << "服务器1获取到 " << sub_rank_list1.size() << " 个子榜" << std::endl;
    std::cout << "服务器2获取到 " << sub_rank_list2.size() << " 个子榜" << std::endl;

    // 比较两个服务器的子榜列表是否一致
    std::vector<SimpleSubRankInfo> sub_rank_list = merge_and_compare_sub_rank_lists(sub_rank_list1, sub_rank_list2);

    if (sub_rank_list.empty()) {
      std::cout << "合并后的子榜列表为空, 不需要进行校验" << std::endl;
      return true;
    }

    std::cout << "合并后共有 " << sub_rank_list.size() << " 个子榜需要校验" << std::endl;

    // 解析指定的子榜类型
    std::set<uint32_t> target_types = parse_sub_rank_types(FLAGS_sample_sub_rank_types);

    // 根据指定的子榜类型过滤
    std::vector<SimpleSubRankInfo> filtered_sub_ranks;
    if (!target_types.empty()) {
      for (const auto& sub_rank : sub_rank_list) {
        if (target_types.count(sub_rank.type) > 0) {
          filtered_sub_ranks.push_back(sub_rank);
        }
      }
      std::cout << "根据指定类型过滤后剩余 " << filtered_sub_ranks.size() << " 个子榜 (指定类型: ";
      bool first = true;
      for (uint32_t type : target_types) {
        if (!first) std::cout << ", ";
        std::cout << type;
        first = false;
      }
      std::cout << ")" << std::endl;
    } else {
      filtered_sub_ranks = sub_rank_list;
      std::cout << "未指定子榜类型过滤，使用所有子榜" << std::endl;
    }

    // 根据随机抽样比例选择要校验的子榜
    std::vector<SimpleSubRankInfo> sub_ranks_to_check;
    if (FLAGS_random_sample_ratio > 0.0 && FLAGS_random_sample_ratio <= 1.0) {
      // 随机抽样 - 防止整数溢出
      size_t sample_count = 0;
      if (filtered_sub_ranks.size() > 0) {
        double sample_count_double = static_cast<double>(filtered_sub_ranks.size()) * FLAGS_random_sample_ratio;
        // 确保不会溢出
        if (sample_count_double > static_cast<double>(SIZE_MAX)) {
          sample_count = filtered_sub_ranks.size();  // 如果溢出，使用全部
        } else {
          sample_count = static_cast<size_t>(sample_count_double);
        }
        if (sample_count == 0 && !filtered_sub_ranks.empty()) {
          sample_count = 1;  // 至少抽样1个
        }
      }

      std::cout << "随机抽样模式: 从 " << filtered_sub_ranks.size() << " 个子榜中抽取 " << sample_count
                << " 个进行校验 (抽样比例: " << (FLAGS_random_sample_ratio * 100) << "%)" << std::endl;

      // 设置随机数种子
      uint64_t seed = FLAGS_random_seed;
      if (seed == 0) {
        seed = std::chrono::system_clock::now().time_since_epoch().count();
      }
      std::cout << "使用随机数种子: " << seed << std::endl;

      std::mt19937 rng(seed);
      std::vector<size_t> indices(filtered_sub_ranks.size());
      std::iota(indices.begin(), indices.end(), 0);
      std::shuffle(indices.begin(), indices.end(), rng);

      // 选择前sample_count个索引对应的子榜
      for (size_t i = 0; i < sample_count && i < indices.size(); ++i) {
        sub_ranks_to_check.push_back(filtered_sub_ranks[indices[i]]);
      }
    } else {
      // 校验所有子榜
      std::cout << "全量校验模式: 校验所有 " << filtered_sub_ranks.size() << " 个子榜" << std::endl;
      sub_ranks_to_check = filtered_sub_ranks;
    }

    std::cout << "开始校验 " << sub_ranks_to_check.size() << " 个子榜..." << std::endl;

    int total_checked = 0;
    int total_passed = 0;
    int total_failed = 0;
    std::vector<SimpleSubRankInfo> failed_sub_ranks;  // 记录失败的子榜信息

    // 对选中的子榜进行校验
    for (const auto& sub_rank : sub_ranks_to_check) {
      std::cout << "\n--- 校验子榜 [type=" << sub_rank.type << ", instance_id=" << sub_rank.instance_id << "] ---"
                << std::endl;

      bool check_result = send_and_compare_get_top_requests(
          score_proc_type, sort_method_on_timestamp, sort_method_on_score, sort_field_num, sort_method_on_field,
          business_id, world_id, zone_id, rank_type, rank_instance_id, sub_rank.type, sub_rank.instance_id, query_from,
          query_count);
      total_checked++;
      if (check_result) {
        total_passed++;
        std::cout << "子榜校验通过" << std::endl;
      } else {
        total_failed++;
        failed_sub_ranks.push_back(sub_rank);  // 记录失败的子榜
        std::cout << "子榜校验失败" << std::endl;
      }
    }

    // 输出总体统计
    std::cerr << "\n=== 榜单信息 ===" << std::endl;
    std::cerr << "business_id: " << business_id << ", world_id: " << world_id << ", zone_id: " << zone_id
              << ", rank_type: " << rank_type << ", rank_instance_id: " << rank_instance_id << std::endl;
    std::cerr << "\n=== 榜单校验总结 ===" << std::endl;
    std::cerr << "总共校验子榜数: " << total_checked << std::endl;
    std::cerr << "校验通过: " << total_passed << std::endl;
    std::cerr << "校验失败: " << total_failed << std::endl;

    // 输出上报统计
    if (FLAGS_auto_report_inconsistent) {
      std::cerr << "\n--- 上报统计 ---" << std::endl;
      std::cerr << "上报到Server1[" << server1_.name << "]数量: " << report_to_server1_count_.load() << std::endl;
      std::cerr << "上报到Server2[" << server2_.name << "]数量: " << report_to_server2_count_.load() << std::endl;
      std::cerr << "总上报数量: " << (report_to_server1_count_.load() + report_to_server2_count_.load()) << std::endl;
    }

    if (total_failed == 0) {
      std::cerr << "所有子榜数据一致性校验通过！" << std::endl;
    } else {
      std::cerr << "有 " << total_failed << " 个子榜存在数据不一致" << std::endl;
      std::cerr << "server1: " << server1_.host << ":" << server1_.port << ", server2: " << server2_.host << ":"
                << server2_.port << std::endl;
      std::cerr << "\n=== 不一致子榜详情 ===" << std::endl;
      for (size_t i = 0; i < failed_sub_ranks.size(); ++i) {
        const auto& sub_rank = failed_sub_ranks[i];
        std::cerr << "  " << (i + 1) << ". type=" << sub_rank.type << ", instance_id=" << sub_rank.instance_id
                  << std::endl;
      }
    }
    // 校验失败超过5个，则认为校验失败
    if (total_failed > 5) {
      return false;
    }

    return true;
  }

  // 向两个服务器发送GetTop请求并比较结果
  bool send_and_compare_get_top_requests(int score_proc_type, int sort_method_on_timestamp, int sort_method_on_score,
                                         int sort_field_num, const int sort_method_on_field[5],
                                         uint32_t business_id = 10000, uint32_t world_id = 1, uint32_t zone_id = 1,
                                         uint32_t rank_type = 1, uint32_t rank_instance_id = 1,
                                         uint32_t sub_rank_type = 1, uint32_t sub_rank_instance_id = 1,
                                         uint32_t query_from = 1, uint32_t query_count = 10) {
    std::cout << "\n=== 向两个服务器发送GetTop请求 ===" << std::endl;
    std::cout << "请求参数: business_id=" << business_id << ", world_id=" << world_id << ", zone_id=" << zone_id
              << ", rank_type=" << rank_type << ", rank_instance_id=" << rank_instance_id
              << ", sub_rank_type=" << sub_rank_type << ", sub_rank_instance_id=" << sub_rank_instance_id
              << ", query_from=" << query_from << ", query_count=" << query_count << std::endl;

    // 向服务器1发送请求（支持分批获取）
    std::cout << "\n--- 向 " << server1_.name << " 发送请求 ---" << std::endl;
    std::vector<RankData> results1;
    uint32_t total_count1 = 0;
    bool success1 = send_get_top_request_with_pagination(socket1_, server1_.name, results1, total_count1, business_id,
                                                         world_id, zone_id, rank_type, rank_instance_id, sub_rank_type,
                                                         sub_rank_instance_id, query_from, query_count);

    // 向服务器2发送请求（支持分批获取）
    std::cout << "\n--- 向 " << server2_.name << " 发送请求 ---" << std::endl;
    std::vector<RankData> results2;
    uint32_t total_count2 = 0;
    bool success2 = send_get_top_request_with_pagination(socket2_, server2_.name, results2, total_count2, business_id,
                                                         world_id, zone_id, rank_type, rank_instance_id, sub_rank_type,
                                                         sub_rank_instance_id, query_from, query_count);

    if (success1 && success2) {
      std::cout << "\n=== 数据获取完成 ===" << std::endl;
      std::cout << "服务器1总记录数: " << total_count1 << ", 实际获取: " << results1.size() << std::endl;
      std::cout << "服务器2总记录数: " << total_count2 << ", 实际获取: " << results2.size() << std::endl;

      // 比较两个服务器的结果
      bool data_consistent =
          compare_rank_results(results1, results2, score_proc_type, sort_method_on_timestamp, sort_method_on_score,
                               sort_field_num, sort_method_on_field, business_id, world_id, zone_id, rank_type,
                               rank_instance_id, sub_rank_type, sub_rank_instance_id);
      return data_consistent;
    } else {
      std::cerr << "请求失败: Server1=" << (success1 ? "成功" : "失败") << ", Server2=" << (success2 ? "成功" : "失败")
                << std::endl;
      return false;
    }
  }

  // 关闭连接
  void close_connections() {
    if (socket1_ >= 0) {
      close(socket1_);
      socket1_ = -1;
    }
    if (socket2_ >= 0) {
      close(socket2_);
      socket2_ = -1;
    }
    connected1_ = connected2_ = false;
  }

  // 重置上报统计
  void reset_report_statistics() {
    report_to_server1_count_.store(0);
    report_to_server2_count_.store(0);
  }

  // 获取上报统计
  std::pair<int, int> get_report_statistics() const {
    return std::make_pair(report_to_server1_count_.load(), report_to_server2_count_.load());
  }

  // 获取排行榜配置
  bool get_rank_config(uint32_t business_id, uint32_t rank_type, uint32_t rank_instance_id, int& score_proc_type,
                       int& sort_method_on_timestamp, int& sort_method_on_score, int& sort_field_num,
                       int sort_method_on_field[5]) {
    // 构建缓存key
    std::string cache_key =
        std::to_string(business_id) + ":" + std::to_string(rank_type) + ":" + std::to_string(rank_instance_id);

    // 检查缓存
    auto it = rank_config_cache_.find(cache_key);
    if (it != rank_config_cache_.end()) {
      score_proc_type = it->second.score_proc_type;
      sort_method_on_timestamp = it->second.sort_method_on_timestamp;
      sort_method_on_score = it->second.sort_method_on_score;
      sort_field_num = it->second.sort_field_num;
      for (int i = 0; i < 5; ++i) {
        sort_method_on_field[i] = it->second.sort_method_on_field[i];
      }
      if (FLAGS_verbose) {
        std::cout << "使用缓存的排行榜配置: " << cache_key << " -> score_proc_type=" << score_proc_type
                  << ", sort_method_on_timestamp=" << sort_method_on_timestamp
                  << ", sort_method_on_score=" << sort_method_on_score;
        std::cout << ", sort_method_on_field=[" << sort_method_on_field[0] << "," << sort_method_on_field[1] << ","
                  << sort_method_on_field[2] << "," << sort_method_on_field[3] << "," << sort_method_on_field[4] << "]"
                  << std::endl;
      }
      return true;
    }

    // 从服务器获取配置
    bool success = fetch_rank_config_from_server(business_id, rank_type, rank_instance_id, score_proc_type,
                                                 sort_method_on_timestamp, sort_method_on_score, sort_field_num,
                                                 sort_method_on_field);
    if (success) {
      // 缓存结果
      RankConfig config;
      config.score_proc_type = score_proc_type;
      config.sort_method_on_timestamp = sort_method_on_timestamp;
      config.sort_method_on_score = sort_method_on_score;
      for (int i = 0; i < 5; ++i) {
        config.sort_method_on_field[i] = sort_method_on_field[i];
      }
      rank_config_cache_[cache_key] = config;
      if (FLAGS_verbose) {
        std::cout << "获取并缓存排行榜配置: " << cache_key << " -> score_proc_type=" << score_proc_type
                  << ", sort_method_on_timestamp=" << sort_method_on_timestamp
                  << ", sort_method_on_score=" << sort_method_on_score << std::endl;
      }
    }

    return success;
  }

 private:
  // 从服务器获取排行榜配置
  bool fetch_rank_config_from_server(uint32_t business_id, uint32_t rank_type, uint32_t rank_instance_id,
                                     int& score_proc_type, int& sort_method_on_timestamp, int& sort_method_on_score,
                                     int& sort_field_num, int sort_method_on_field[5]) {
    char buffer[1024];
    size_t packet_size;

    // 构建InnerGetTypeCfgReq请求包
    bool success =
        build_inner_get_type_cfg_packet(buffer, sizeof(buffer), packet_size, business_id, rank_type, rank_instance_id);
    if (!success) {
      std::cerr << "构建排行榜配置请求包失败" << std::endl;
      return false;
    }

    // 优先使用server1获取配置
    int socket_fd = socket1_;
    std::string server_name = server1_.name;

    // 发送数据
    ssize_t bytes_sent = send(socket_fd, buffer, packet_size, 0);
    if (bytes_sent < 0) {
      std::cerr << "发送排行榜配置请求失败到 " << server_name << ": " << strerror(errno) << std::endl;
      return false;
    }

    if (FLAGS_verbose) {
      std::cout << "向 " << server_name << " 发送了排行榜配置请求 " << bytes_sent << " 字节" << std::endl;
    }

    // 接收响应
    char response_buffer[sizeof(TopNextMsg)];
    size_t total_received = 0;

    if (!recv_complete_message(socket_fd, server_name, response_buffer, sizeof(response_buffer), total_received)) {
      return false;
    }

    if (FLAGS_verbose) {
      std::cout << "从 " << server_name << " 接收到完整排行榜配置响应 " << total_received << " 字节" << std::endl;
    }

    // 解析响应
    return parse_rank_config_response(response_buffer, total_received, server_name, score_proc_type,
                                      sort_method_on_timestamp, sort_method_on_score, sort_field_num,
                                      sort_method_on_field);
  }

  // 构建InnerGetTypeCfgReq请求包
  bool build_inner_get_type_cfg_packet(char* buffer, size_t buffer_size, size_t& used_size, uint32_t business_id,
                                       uint32_t rank_type, uint32_t rank_instance_id) {
    TopNextMsg msg;

    // 构建消息头
    msg.head.construct();
    msg.head.magic = TOPNEXT_MAGIC;
    msg.head.version = 1;
    msg.head.command = CMD_INNER_GET_TYPE_CFG_REQ;
    msg.head.businessID = business_id;
    msg.head.headLen = sizeof(TopNextHead);
    msg.head.bodyLen = 0;  // 稍后设置

    // 构建消息体
    msg.body.construct(CMD_INNER_GET_TYPE_CFG_REQ);
    auto& inner_get_type_cfg = msg.body.inner_get_type_cfg_req;

    // 设置rank_type_info
    inner_get_type_cfg.rank_type_info.business_id = business_id;
    inner_get_type_cfg.rank_type_info.type = rank_type;

    // 序列化消息
    TdrError::ErrorType ret = msg.pack(buffer, buffer_size, &used_size);
    if (ret != TdrError::TDR_NO_ERROR) {
      std::cerr << "序列化InnerGetTypeCfgReq失败: " << ret << std::endl;
      return false;
    }

    if (FLAGS_verbose) {
      std::cout << "成功构建InnerGetTypeCfgReq请求包，大小: " << used_size << " 字节" << std::endl;
    }
    return true;
  }

  // 解析排行榜配置响应
  bool parse_rank_config_response(const char* buffer, size_t buffer_size, const std::string& server_name,
                                  int& score_proc_type, int& sort_method_on_timestamp, int& sort_method_on_score,
                                  int& sort_field_num, int sort_method_on_field[5]) {
    if (buffer_size < sizeof(TopNextHead)) {
      std::cerr << "排行榜配置响应数据太小 " << server_name << std::endl;
      return false;
    }

    // 尝试解析TopNext消息
    TopNextMsg response_msg;
    TdrError::ErrorType ret = response_msg.unpack(buffer, buffer_size);
    if (ret != TdrError::TDR_NO_ERROR) {
      std::cerr << "解析排行榜配置响应失败 " << server_name << ": " << ret << std::endl;
      return false;
    }

    if (response_msg.head.command == CMD_INNER_GET_TYPE_CFG_RSP) {
      const auto& rsp = response_msg.body.inner_get_type_cfg_rsp;

      if (FLAGS_verbose) {
        std::cout << "排行榜配置响应详情 " << server_name << ":" << std::endl;
        std::cout << "  返回码: " << rsp.ret_info.ret << std::endl;
        std::cout << "  返回消息: " << rsp.ret_info.msg << std::endl;
      }

      if (rsp.ret_info.ret != 0) {
        std::cerr << "服务器返回错误: " << rsp.ret_info.ret << " - " << rsp.ret_info.msg << std::endl;
        return false;
      }

      // 提取score_proc_type
      score_proc_type = rsp.type_cfg.score_proc_type;
      sort_method_on_timestamp = rsp.type_cfg.sort_method_on_timestamp;
      sort_method_on_score = rsp.type_cfg.sort_method_on_score;

      // 提取扩展排序字段的排序方向配置
      sort_field_num = rsp.type_cfg.sort_field_num;
      sort_method_on_field[0] = rsp.type_cfg.sort_method_on_field1;
      sort_method_on_field[1] = rsp.type_cfg.sort_method_on_field2;
      sort_method_on_field[2] = rsp.type_cfg.sort_method_on_field3;
      sort_method_on_field[3] = rsp.type_cfg.sort_method_on_field4;
      sort_method_on_field[4] = rsp.type_cfg.sort_method_on_field5;

      if (FLAGS_verbose) {
        std::cout << "获取到排行榜配置: score_proc_type=" << score_proc_type << " ("
                  << (score_proc_type == 0 ? "最新值模式" : "最大值模式")
                  << "), sort_method_on_timestamp=" << sort_method_on_timestamp
                  << ", sort_method_on_score=" << sort_method_on_score;
        std::cout << ", sort_method_on_field=[" << sort_method_on_field[0] << "," << sort_method_on_field[1] << ","
                  << sort_method_on_field[2] << "," << sort_method_on_field[3] << "," << sort_method_on_field[4] << "]"
                  << std::endl;
      }

      return true;
    }

    std::cout << "未知的排行榜配置响应命令: " << response_msg.head.command << std::endl;
    return false;
  }

  // 合并和比较两个服务器的子榜列表
  std::vector<SimpleSubRankInfo> merge_and_compare_sub_rank_lists(const std::vector<SimpleSubRankInfo>& list1,
                                                                  const std::vector<SimpleSubRankInfo>& list2) {
    std::cout << "\n=== 比较两个服务器的子榜列表 ===" << std::endl;

    // 使用set来去重和比较
    std::set<std::pair<uint32_t, uint32_t>> set1, set2;
    for (const auto& item : list1) {
      set1.insert({item.type, item.instance_id});
    }
    for (const auto& item : list2) {
      set2.insert({item.type, item.instance_id});
    }

    // 找出差异
    std::set<std::pair<uint32_t, uint32_t>> only_in_server1, only_in_server2, common;
    std::set_difference(set1.begin(), set1.end(), set2.begin(), set2.end(),
                        std::inserter(only_in_server1, only_in_server1.begin()));
    std::set_difference(set2.begin(), set2.end(), set1.begin(), set1.end(),
                        std::inserter(only_in_server2, only_in_server2.begin()));
    std::set_intersection(set1.begin(), set1.end(), set2.begin(), set2.end(), std::inserter(common, common.begin()));

    std::cout << "共同子榜: " << common.size() << " 个" << std::endl;
    std::cout << "仅在服务器1: " << only_in_server1.size() << " 个" << std::endl;
    std::cout << "仅在服务器2: " << only_in_server2.size() << " 个" << std::endl;

    if (!only_in_server1.empty()) {
      std::cout << "仅在服务器1的子榜:" << std::endl;
      for (const auto& item : only_in_server1) {
        std::cout << "  type=" << item.first << ", instance_id=" << item.second << std::endl;
      }
    }

    if (!only_in_server2.empty()) {
      std::cout << "仅在服务器2的子榜:" << std::endl;
      for (const auto& item : only_in_server2) {
        std::cout << "  type=" << item.first << ", instance_id=" << item.second << std::endl;
      }
    }

    // 合并所有子榜（取并集）
    std::set<std::pair<uint32_t, uint32_t>> all_sub_ranks;
    std::set_union(set1.begin(), set1.end(), set2.begin(), set2.end(),
                   std::inserter(all_sub_ranks, all_sub_ranks.begin()));

    // 转换回vector
    std::vector<SimpleSubRankInfo> result;
    for (const auto& item : all_sub_ranks) {
      result.emplace_back(item.first, item.second);
    }

    if (only_in_server1.empty() && only_in_server2.empty()) {
      std::cout << "✅ 两个服务器的子榜列表完全一致" << std::endl;
    } else {
      std::cout << "⚠️  两个服务器的子榜列表存在差异，将校验所有子榜" << std::endl;
    }

    return result;
  }

  // 获取包长度的辅助函数（参考TopnextProtocolParser::GetPackLength）
  int get_packet_length(const void* data, unsigned int data_len) {
    if (nullptr == data || 0 == data_len) {
      return -1;
    }

    // 检查是否有足够的数据来读取头部长度和体长度字段
    size_t body_offset = offsetof(TopNextHead, bodyLen) + sizeof(uint32_t);
    if (data_len >= body_offset) {
      // 安全地读取头部长度和体长度，避免未对齐访问
      uint32_t head_len, body_len;
      memcpy(&head_len, (const unsigned char*)data + offsetof(TopNextHead, headLen), sizeof(uint32_t));
      memcpy(&body_len, (const unsigned char*)data + offsetof(TopNextHead, bodyLen), sizeof(uint32_t));

      head_len = ntohl(head_len);
      body_len = ntohl(body_len);

      // 添加合理性检查，防止恶意数据
      const uint32_t MAX_REASONABLE_SIZE = 10 * 1024 * 1024;  // 10MB
      if (head_len > MAX_REASONABLE_SIZE || body_len > MAX_REASONABLE_SIZE) {
        std::cerr << "警告: 包长度异常 head_len=" << head_len << ", body_len=" << body_len << std::endl;
        return -1;
      }

      if (FLAGS_verbose) {
        std::cout << "解析包长度: head_len=" << head_len << ", body_len=" << body_len
                  << ", total=" << (head_len + body_len) << std::endl;
      }

      return static_cast<int>(head_len + body_len);
    } else {
      return 0;  // 需要更多数据
    }
  }

  // 完整接收TCP数据的辅助函数
  bool recv_complete_message(int socket_fd, const std::string& server_name, char* buffer, size_t buffer_size,
                             size_t& total_received) {
    total_received = 0;

    // 首先接收足够的数据来确定包长度
    size_t min_header_size = sizeof(TopNextHead);

    // 先接收最小头部大小的数据
    while (total_received < min_header_size) {
      ssize_t bytes_received = recv(socket_fd, buffer + total_received, min_header_size - total_received, 0);
      if (bytes_received < 0) {
        std::cerr << "接收响应头失败从 " << server_name << ": " << strerror(errno) << std::endl;
        return false;
      } else if (bytes_received == 0) {
        std::cout << "服务器 " << server_name << " 关闭了连接" << std::endl;
        return false;
      }
      total_received += bytes_received;
    }

    // 使用改进的包长度获取函数
    int packet_length = get_packet_length(buffer, total_received);
    if (packet_length < 0) {
      std::cerr << "无法解析包长度从 " << server_name << std::endl;
      return false;
    }

    if (packet_length == 0) {
      // 需要更多数据来确定包长度，继续接收
      std::cerr << "需要更多数据来确定包长度从 " << server_name << std::endl;
      return false;
    }

    size_t total_msg_size = static_cast<size_t>(packet_length);

    if (total_msg_size > buffer_size) {
      std::cerr << "消息大小超出缓冲区: " << total_msg_size << " > " << buffer_size << std::endl;
      return false;
    }

    // 接收剩余的消息体
    while (total_received < total_msg_size) {
      ssize_t bytes_received = recv(socket_fd, buffer + total_received, total_msg_size - total_received, 0);
      if (bytes_received < 0) {
        std::cerr << "接收响应体失败从 " << server_name << ": " << strerror(errno) << std::endl;
        return false;
      } else if (bytes_received == 0) {
        std::cout << "服务器 " << server_name << " 关闭了连接" << std::endl;
        return false;
      }
      total_received += bytes_received;
    }

    if (FLAGS_verbose) {
      std::cout << "成功接收完整消息从 " << server_name << ": " << total_received << " 字节" << std::endl;
    }

    return true;
  }

  // 连接到单个服务器
  bool connect_to_server(const ServerInfo& server, int& socket_fd, bool& connected) {
    socket_fd = socket(AF_INET, SOCK_STREAM, 0);
    if (socket_fd < 0) {
      std::cerr << "创建socket失败 " << server.name << ": " << strerror(errno) << std::endl;
      return false;
    }

    struct sockaddr_in server_addr;
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(server.port);

    // 使用线程安全的getaddrinfo替代gethostbyname
    struct addrinfo hints, *result;
    memset(&hints, 0, sizeof(hints));
    hints.ai_family = AF_INET;  // IPv4
    hints.ai_socktype = SOCK_STREAM;

    int ret = getaddrinfo(server.host.c_str(), nullptr, &hints, &result);
    if (ret != 0) {
      std::cerr << "解析主机名失败 " << server.name << ": " << server.host << " - " << gai_strerror(ret) << std::endl;
      close(socket_fd);
      socket_fd = -1;
      return false;
    }

    // 复制地址信息
    struct sockaddr_in* addr_in = reinterpret_cast<struct sockaddr_in*>(result->ai_addr);
    server_addr.sin_addr = addr_in->sin_addr;

    freeaddrinfo(result);  // 释放getaddrinfo分配的内存

    if (::connect(socket_fd, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
      std::cerr << "连接服务器失败 " << server.name << " (" << server.host << ":" << server.port
                << "): " << strerror(errno) << std::endl;
      close(socket_fd);
      socket_fd = -1;
      return false;
    }

    connected = true;
    std::cout << "成功连接到服务器 " << server.name << " (" << server.host << ":" << server.port << ")" << std::endl;
    return true;
  }

  // 获取子榜列表（支持分页）
  bool get_sub_rank_list(int socket_fd, const std::string& server_name, std::vector<SimpleSubRankInfo>& sub_rank_list,
                         uint32_t business_id, uint32_t world_id, uint32_t zone_id, uint32_t rank_type,
                         uint32_t rank_instance_id) {
    sub_rank_list.clear();

    uint32_t current_from = 1;
    uint32_t batch_size = 100;  // 每次最多获取100个子榜
    bool first_request = true;
    uint32_t total_count = 0;

    std::cout << "开始分页获取子榜列表，每批次最多 " << batch_size << " 个子榜" << std::endl;

    while (true) {
      std::vector<SimpleSubRankInfo> batch_results;
      uint32_t batch_total_count = 0;

      uint32_t batch_number = (current_from - 1) / 100 + 1;  // 使用固定的100来计算批次号
      std::cout << "第 " << batch_number << " 批次: 从位置 " << current_from << " 开始获取 " << batch_size << " 个子榜"
                << std::endl;

      bool success =
          get_sub_rank_list_single_batch(socket_fd, server_name, batch_results, batch_total_count, business_id,
                                         world_id, zone_id, rank_type, rank_instance_id, current_from, batch_size);

      if (!success) {
        std::cerr << "第 " << batch_number << " 批次子榜列表请求失败" << std::endl;
        return false;
      }

      // 第一次请求时记录总数
      if (first_request) {
        total_count = batch_total_count;
        first_request = false;
        std::cout << "子榜总数量: " << total_count << std::endl;

        // 如果总数为0或者没有数据，直接返回
        if (total_count == 0 || batch_results.empty()) {
          std::cout << "子榜列表为空，结束获取" << std::endl;
          return true;
        }
      }

      // 添加本批次结果到总结果中
      sub_rank_list.insert(sub_rank_list.end(), batch_results.begin(), batch_results.end());
      std::cout << "本批次获取到 " << batch_results.size() << " 个子榜，累计 " << sub_rank_list.size() << " 个"
                << std::endl;

      // 检查是否已经获取完所有数据
      if (batch_results.size() < batch_size || sub_rank_list.size() >= total_count) {
        std::cout << "已获取完所有子榜，总计 " << sub_rank_list.size() << " 个" << std::endl;
        break;
      }

      // 准备下一批次
      current_from += batch_size;

      // 计算下一批次应该获取的数量
      uint32_t remaining = total_count - sub_rank_list.size();
      batch_size = std::min(remaining, uint32_t(100));

      if (batch_size == 0) {
        break;
      }

      // 添加小延迟避免请求过于频繁
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    return true;
  }

  // 获取单批次子榜列表
  bool get_sub_rank_list_single_batch(int socket_fd, const std::string& server_name,
                                      std::vector<SimpleSubRankInfo>& sub_rank_list, uint32_t& total_count,
                                      uint32_t business_id, uint32_t world_id, uint32_t zone_id, uint32_t rank_type,
                                      uint32_t rank_instance_id, uint32_t query_from, uint32_t query_count) {
    char buffer[1024];
    size_t packet_size;

    // 构建InnerGetSubRankListReq请求包
    bool success = build_inner_get_sub_rank_list_packet(buffer, sizeof(buffer), packet_size, business_id, world_id,
                                                        zone_id, rank_type, rank_instance_id, query_from, query_count);
    if (!success) {
      std::cerr << "构建子榜列表请求包失败 " << server_name << std::endl;
      return false;
    }

    // 发送数据
    ssize_t bytes_sent = send(socket_fd, buffer, packet_size, 0);
    if (bytes_sent < 0) {
      std::cerr << "发送子榜列表请求失败到 " << server_name << ": " << strerror(errno) << std::endl;
      return false;
    }

    if (FLAGS_verbose) {
      std::cout << "向 " << server_name << " 发送了子榜列表请求 " << bytes_sent << " 字节" << std::endl;
    }

    // 接收响应 - 确保完整接收
    char response_buffer[sizeof(TopNextMsg)];
    size_t total_received = 0;

    if (!recv_complete_message(socket_fd, server_name, response_buffer, sizeof(response_buffer), total_received)) {
      return false;
    }

    if (FLAGS_verbose) {
      std::cout << "从 " << server_name << " 接收到完整子榜列表响应 " << total_received << " 字节" << std::endl;
    }

    // 解析响应
    return parse_sub_rank_list_response_with_total_count(response_buffer, total_received, server_name, sub_rank_list,
                                                         total_count);
  }

  // 构建InnerGetSubRankListReq请求包
  bool build_inner_get_sub_rank_list_packet(char* buffer, size_t buffer_size, size_t& used_size, uint32_t business_id,
                                            uint32_t world_id, uint32_t zone_id, uint32_t rank_type,
                                            uint32_t rank_instance_id, uint32_t query_from, uint32_t query_count) {
    TopNextMsg msg;

    // 构建消息头
    msg.head.construct();
    msg.head.magic = TOPNEXT_MAGIC;
    msg.head.version = 1;
    msg.head.command = CMD_INNER_GET_SUB_RANK_LIST_SUB_RANK_REQ;
    msg.head.businessID = business_id;
    msg.head.headLen = sizeof(TopNextHead);
    msg.head.bodyLen = 0;  // 稍后设置

    // 构建消息体
    msg.body.construct(CMD_INNER_GET_SUB_RANK_LIST_SUB_RANK_REQ);
    auto& inner_get_sub_rank_list = msg.body.inner_get_sub_rank_list_sub_rank_req;

    // 设置rank_info
    inner_get_sub_rank_list.rank_info.construct();
    inner_get_sub_rank_list.rank_info.business_id = business_id;
    inner_get_sub_rank_list.rank_info.world_id = world_id;
    inner_get_sub_rank_list.rank_info.zone_id = zone_id;
    inner_get_sub_rank_list.rank_info.type = rank_type;
    inner_get_sub_rank_list.rank_info.instance_id = rank_instance_id;

    // 设置查询参数
    inner_get_sub_rank_list.query_from = query_from;
    inner_get_sub_rank_list.query_count = query_count;

    // 序列化消息
    TdrError::ErrorType ret = msg.pack(buffer, buffer_size, &used_size);
    if (ret != TdrError::TDR_NO_ERROR) {
      std::cerr << "序列化InnerGetSubRankListReq失败: " << ret << std::endl;
      return false;
    }

    if (FLAGS_verbose) {
      std::cout << "成功构建InnerGetSubRankListReq请求包，大小: " << used_size << " 字节" << std::endl;
    }
    return true;
  }

  // 解析子榜列表响应
  bool parse_sub_rank_list_response(const char* buffer, size_t buffer_size, const std::string& server_name,
                                    std::vector<SimpleSubRankInfo>& sub_rank_list) {
    if (buffer_size < sizeof(TopNextHead)) {
      std::cerr << "子榜列表响应数据太小 " << server_name << std::endl;
      return false;
    }

    // 尝试解析TopNext消息
    TopNextMsg response_msg;
    TdrError::ErrorType ret = response_msg.unpack(buffer, buffer_size);
    if (ret != TdrError::TDR_NO_ERROR) {
      std::cerr << "解析子榜列表响应失败 " << server_name << ": " << ret << std::endl;
      return false;
    }

    if (response_msg.head.command == CMD_INNER_GET_SUB_RANK_LIST_SUB_RANK_RSP) {
      const auto& rsp = response_msg.body.inner_get_sub_rank_list_sub_rank_rsp;

      if (FLAGS_verbose) {
        std::cout << "子榜列表响应详情 " << server_name << ":" << std::endl;
        std::cout << "  返回码: " << rsp.ret_info.ret << std::endl;
        std::cout << "  返回消息: " << rsp.ret_info.msg << std::endl;
        std::cout << "  子榜数量: " << rsp.sub_rank_info.count << std::endl;
      }

      if (rsp.ret_info.ret != 0) {
        std::cerr << "服务器返回错误: " << rsp.ret_info.ret << " - " << rsp.ret_info.msg << std::endl;
        return false;
      }

      // 提取子榜信息
      sub_rank_list.clear();
      for (uint32_t i = 0; i < rsp.sub_rank_info.count; ++i) {
        const auto& sub_rank = rsp.sub_rank_info.sub_rank_info[i];
        sub_rank_list.emplace_back(sub_rank.type, sub_rank.instance_id);

        if (FLAGS_verbose) {
          std::cout << "  子榜 " << (i + 1) << ": type=" << sub_rank.type << ", instance_id=" << sub_rank.instance_id
                    << std::endl;
        }
      }

      return true;
    }

    std::cout << "未知的子榜列表响应命令: " << response_msg.head.command << std::endl;
    return false;
  }

  // 解析子榜列表响应（带total_count）
  bool parse_sub_rank_list_response_with_total_count(const char* buffer, size_t buffer_size,
                                                     const std::string& server_name,
                                                     std::vector<SimpleSubRankInfo>& sub_rank_list,
                                                     uint32_t& total_count) {
    if (buffer_size < sizeof(TopNextHead)) {
      std::cerr << "子榜列表响应数据太小 " << server_name << std::endl;
      return false;
    }

    // 尝试解析TopNext消息
    TopNextMsg response_msg;
    TdrError::ErrorType ret = response_msg.unpack(buffer, buffer_size);
    if (ret != TdrError::TDR_NO_ERROR) {
      std::cerr << "解析子榜列表响应失败 " << server_name << ": " << ret << std::endl;
      return false;
    }

    if (response_msg.head.command == CMD_INNER_GET_SUB_RANK_LIST_SUB_RANK_RSP) {
      const auto& rsp = response_msg.body.inner_get_sub_rank_list_sub_rank_rsp;

      if (FLAGS_verbose) {
        std::cout << "子榜列表响应详情 " << server_name << ":" << std::endl;
        std::cout << "  返回码: " << rsp.ret_info.ret << std::endl;
        std::cout << "  返回消息: " << rsp.ret_info.msg << std::endl;
        std::cout << "  子榜数量: " << rsp.sub_rank_info.count << std::endl;
        std::cout << "  总子榜数: " << rsp.sub_rank_total_count << std::endl;
      }

      if (rsp.ret_info.ret != 0) {
        std::cerr << "服务器返回错误: " << rsp.ret_info.ret << " - " << rsp.ret_info.msg << std::endl;
        return false;
      }

      // 设置total_count
      total_count = rsp.sub_rank_total_count;

      // 提取子榜信息
      sub_rank_list.clear();
      for (uint32_t i = 0; i < rsp.sub_rank_info.count; ++i) {
        const auto& sub_rank = rsp.sub_rank_info.sub_rank_info[i];
        sub_rank_list.emplace_back(sub_rank.type, sub_rank.instance_id);

        if (FLAGS_verbose) {
          std::cout << "  子榜 " << (i + 1) << ": type=" << sub_rank.type << ", instance_id=" << sub_rank.instance_id
                    << std::endl;
        }
      }

      return true;
    }

    std::cout << "未知的子榜列表响应命令: " << response_msg.head.command << std::endl;
    return false;
  }

  // 构建数据上报请求包（参考PeekUpdateReq）
  bool build_report_sub_rank_packet(char* buffer, size_t buffer_size, size_t& used_size, const RankData& rank_data,
                                    uint32_t business_id, uint32_t world_id, uint32_t zone_id, uint32_t rank_type,
                                    uint32_t rank_instance_id, uint32_t sub_rank_type, uint32_t sub_rank_instance_id) {
    uint64_t startms =
        std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch())
            .count();

    // 构建TopnextProxyHeader（模拟proxy发送协议）
    topnext_proxy_proto::TopnextProxyHeader topnext_proxy_header;
    topnext_proxy_header.wMagic = topnext_proto_tdr::TOPNEXT_PROXY_MAGIC;
    topnext_proxy_header.bCmd = topnext_proxy_proto::COMMON_HEADER;  // 非心跳
    topnext_proxy_header.wCommand = topnext_proto_tdr::CMD_REPORT_SUB_RANK_REQ;
    topnext_proxy_header.ullTimestamp = startms;
    topnext_proxy_header.dwBusiness_id = business_id;
    topnext_proxy_header.dwSequence = 0;  // seq使用proxy本地的seq

    size_t proxy_used = 0;
    TdrError::ErrorType tdr_ret = topnext_proxy_header.pack(buffer, buffer_size, &proxy_used, 0);
    if (tdr_ret != TdrError::TDR_NO_ERROR) {
      std::cerr << "构建TopnextProxyHeader失败: " << tdr_ret << std::endl;
      return false;
    }

    // 构建TopNext消息
    TopNextMsg msg;
    msg.head.construct();
    msg.head.magic = TOPNEXT_MAGIC;
    msg.head.version = 1;
    msg.head.command = CMD_REPORT_SUB_RANK_REQ;
    msg.head.businessID = business_id;
    msg.head.headLen = sizeof(TopNextHead);
    msg.head.bodyLen = 0;  // 稍后设置

    // 构建消息体
    msg.body.construct(CMD_REPORT_SUB_RANK_REQ);
    auto& report_req = msg.body.report_sub_rank_req;

    // 设置client_info
    report_req.client_info.construct();
    report_req.client_info.from = REQ_CLIENT_FROM_INNER;

    report_req.data_source = FLAGS_use_image_data ? REQ_DATA_SOURCE_IMAGE : REQ_DATA_SOURCE_REAL;

    // 设置rank_info
    report_req.rank_info.construct();
    report_req.rank_info.business_id = business_id;
    report_req.rank_info.world_id = world_id;
    report_req.rank_info.zone_id = zone_id;
    report_req.rank_info.type = rank_type;
    report_req.rank_info.instance_id = rank_instance_id;

    // 设置sub_rank_info
    report_req.sub_rank_info.count = 1;
    report_req.sub_rank_info.sub_rank_info[0].type = sub_rank_type;
    report_req.sub_rank_info.sub_rank_info[0].instance_id = sub_rank_instance_id;

    // 设置用户信息 - 使用更安全的字符串复制
    size_t openid_len = std::min(rank_data.openid.length(), sizeof(report_req.user_info.openid) - 1);
    memcpy(report_req.user_info.openid, rank_data.openid.c_str(), openid_len);
    report_req.user_info.openid[openid_len] = '\0';
    // 清零剩余部分以确保安全
    if (openid_len < sizeof(report_req.user_info.openid) - 1) {
      memset(report_req.user_info.openid + openid_len + 1, 0, sizeof(report_req.user_info.openid) - openid_len - 1);
    }

    report_req.user_info.score = rank_data.score;
    report_req.user_info.timestamp = rank_data.timestamp;
    report_req.user_info.last_report_timestamp = rank_data.last_report_timestamp;

    // 设置扩展排序字段
    report_req.user_info.sort_field1 = rank_data.sort_field1;
    report_req.user_info.sort_field2 = rank_data.sort_field2;
    report_req.user_info.sort_field3 = rank_data.sort_field3;
    report_req.user_info.sort_field4 = rank_data.sort_field4;
    report_req.user_info.sort_field5 = rank_data.sort_field5;

    report_req.user_info.ext_field1 = rank_data.ext_field1;
    report_req.user_info.ext_field2 = rank_data.ext_field2;
    report_req.user_info.ext_field3 = rank_data.ext_field3;

    report_req.user_info.reduce_field1 = rank_data.reduce_field1;
    report_req.user_info.reduce_field2 = rank_data.reduce_field2;
    report_req.user_info.reduce_field3 = rank_data.reduce_field3;

    // 设置扩展数据 - 添加安全检查
    report_req.user_info.ext_data.data_len = 0;
    if (!rank_data.ext_data.empty()) {
      size_t max_ext_data_size = sizeof(report_req.user_info.ext_data.data_info);
      size_t ext_data_len = std::min(rank_data.ext_data.size(), max_ext_data_size);

      // 确保不会溢出data_len字段的类型（看起来是uint16_t）
      const size_t max_data_len = UINT16_MAX;
      if (ext_data_len > max_data_len) {
        std::cerr << "警告: 扩展数据长度超出data_len字段范围，已截断到 " << max_data_len << " 字节" << std::endl;
        ext_data_len = max_data_len;
      }

      report_req.user_info.ext_data.data_len = static_cast<uint16_t>(ext_data_len);
      memcpy(report_req.user_info.ext_data.data_info, rank_data.ext_data.data(), ext_data_len);
    }

    // 序列化TopNext消息
    size_t body_len = 0;
    tdr_ret = msg.pack(buffer + proxy_used, buffer_size - proxy_used, &body_len);
    if (tdr_ret != TdrError::TDR_NO_ERROR) {
      std::cerr << "序列化ReportSubRankReq失败: " << tdr_ret << std::endl;
      return false;
    }

    // 更新proxy头中的body长度
    uint32_t body_len_net = htonl(body_len);
    memcpy(buffer + offsetof(topnext_proxy_proto::TopnextProxyHeader, dwBody_length), &body_len_net,
           sizeof(body_len_net));

    used_size = proxy_used + body_len;

    if (FLAGS_verbose) {
      std::cout << "成功构建ReportSubRankReq请求包，大小: " << used_size << " 字节" << std::endl;
      std::cout << "上报用户: " << rank_data.openid << ", 分数: " << rank_data.score
                << ", timestamp: " << rank_data.timestamp
                << ", last_report_timestamp: " << rank_data.last_report_timestamp << std::endl;
    }

    return true;
  }

  // 发送数据上报请求
  bool send_report_sub_rank_request(int socket_fd, const std::string& server_name, const RankData& rank_data,
                                    uint32_t business_id, uint32_t world_id, uint32_t zone_id, uint32_t rank_type,
                                    uint32_t rank_instance_id, uint32_t sub_rank_type, uint32_t sub_rank_instance_id) {
    char buffer[2048];  // 增大缓冲区以容纳proxy头
    size_t packet_size;

    bool success =
        build_report_sub_rank_packet(buffer, sizeof(buffer), packet_size, rank_data, business_id, world_id, zone_id,
                                     rank_type, rank_instance_id, sub_rank_type, sub_rank_instance_id);
    if (!success) {
      std::cerr << "构建上报请求包失败 " << server_name << std::endl;
      return false;
    }

    // 发送数据
    ssize_t bytes_sent = send(socket_fd, buffer, packet_size, 0);
    if (bytes_sent < 0) {
      std::cerr << "发送上报请求失败到 " << server_name << ": " << strerror(errno) << std::endl;
      return false;
    }

    if (FLAGS_verbose) {
      std::cout << "向 " << server_name << " 发送了上报请求 " << bytes_sent << " 字节" << std::endl;
    }

    // 接收响应 - 先接收TopnextProxyHeader
    char response_buffer[2048];  // 增大缓冲区以容纳proxy头
    size_t total_received = 0;
    size_t proxy_header_size = sizeof(topnext_proxy_proto::TopnextProxyHeader);

    // 首先接收完整的TopnextProxyHeader
    while (total_received < proxy_header_size) {
      ssize_t bytes_received = recv(socket_fd, response_buffer + total_received, proxy_header_size - total_received, 0);
      if (bytes_received < 0) {
        std::cerr << "接收上报响应proxy头失败从 " << server_name << ": " << strerror(errno) << std::endl;
        return false;
      } else if (bytes_received == 0) {
        std::cout << "服务器 " << server_name << " 关闭了连接" << std::endl;
        return false;
      }
      total_received += bytes_received;
    }

    // 安全地解析TopnextProxyHeader获取body长度，避免内存对齐问题
    uint32_t body_length;
    memcpy(&body_length, response_buffer + offsetof(topnext_proxy_proto::TopnextProxyHeader, dwBody_length),
           sizeof(uint32_t));
    size_t total_msg_size = proxy_header_size + ntohl(body_length);

    if (total_msg_size > sizeof(response_buffer)) {
      std::cerr << "上报响应消息大小超出缓冲区: " << total_msg_size << " > " << sizeof(response_buffer) << std::endl;
      return false;
    }

    // 接收剩余的消息体
    while (total_received < total_msg_size) {
      ssize_t bytes_received = recv(socket_fd, response_buffer + total_received, total_msg_size - total_received, 0);
      if (bytes_received < 0) {
        std::cerr << "接收上报响应体失败从 " << server_name << ": " << strerror(errno) << std::endl;
        return false;
      } else if (bytes_received == 0) {
        std::cout << "服务器 " << server_name << " 关闭了连接" << std::endl;
        return false;
      }
      total_received += bytes_received;
    }

    if (FLAGS_verbose) {
      std::cout << "从 " << server_name << " 接收到完整上报响应 " << total_received << " 字节" << std::endl;
    }

    // 解析响应：先解析TopnextProxyHeader，再解析TopNext消息体
    topnext_proxy_proto::TopnextProxyHeader proxy_header;
    size_t proxy_used = 0;
    TdrError::ErrorType proxy_ret = proxy_header.unpack(response_buffer, total_received, &proxy_used);
    if (proxy_ret != TdrError::TDR_NO_ERROR) {
      std::cerr << "解析上报响应TopnextProxyHeader失败: " << proxy_ret << std::endl;
      return false;
    }

    if (FLAGS_verbose) {
      std::cout << "TopnextProxyHeader: magic=" << proxy_header.wMagic << ", command=" << proxy_header.wCommand
                << ", body_length=" << proxy_header.dwBody_length << std::endl;
    }

    // 再解析TopNext消息体
    if (total_received > proxy_used) {
      TopNextMsg response_msg;
      TdrError::ErrorType ret = response_msg.unpack(response_buffer + proxy_used, total_received - proxy_used);
      if (ret == TdrError::TDR_NO_ERROR) {
        if (FLAGS_verbose) {
          std::cout << "TopNext响应: command=" << response_msg.head.command << std::endl;
        }

        if (response_msg.head.command == CMD_REPORT_SUB_RANK_RSP) {
          const auto& rsp = response_msg.body.report_sub_rank_rsp;
          if (rsp.ret_info.ret == 0) {
            std::cout << "数据上报成功: " << rank_data.openid << std::endl;
            return true;
          } else {
            std::cerr << "服务器返回错误: " << rsp.ret_info.ret << " - " << rsp.ret_info.msg << std::endl;
          }
        } else {
          std::cerr << "未知的上报响应命令: " << response_msg.head.command << std::endl;
        }
      } else {
        std::cerr << "解析上报响应TopNext消息失败: " << ret << std::endl;
      }
    } else {
      std::cerr << "上报响应数据不足，无法解析TopNext消息体" << std::endl;
    }

    std::cerr << "数据上报失败: " << rank_data.openid << std::endl;
    return false;
  }

  // 构建GetTopSubRankReq请求包
  bool build_get_top_req_packet(char* buffer, size_t buffer_size, size_t& used_size, uint32_t business_id,
                                uint32_t world_id, uint32_t zone_id, uint32_t rank_type, uint32_t rank_instance_id,
                                uint32_t sub_rank_type, uint32_t sub_rank_instance_id, uint32_t query_from,
                                uint32_t query_count, uint32_t image_index, bool use_image_data) {
    TopNextMsg msg;

    // 构建消息头
    msg.head.construct();
    msg.head.magic = TOPNEXT_MAGIC;
    msg.head.version = 1;
    msg.head.command = CMD_GET_TOP_SUB_RANK_REQ;
    msg.head.businessID = business_id;
    msg.head.headLen = sizeof(TopNextHead);
    msg.head.bodyLen = 0;  // 稍后设置

    // 构建消息体
    msg.body.construct(CMD_GET_TOP_SUB_RANK_REQ);
    auto& get_top_req = msg.body.get_top_sub_rank_req;

    // 设置client_info
    get_top_req.client_info.construct();
    get_top_req.client_info.from = REQ_CLIENT_FROM_INNER;
    // strncpy(get_top_req.client_info.client_addr, "127.0.0.1", sizeof(get_top_req.client_info.client_addr) - 1);

    // 设置rank_info
    get_top_req.rank_info.construct();
    get_top_req.rank_info.business_id = business_id;
    get_top_req.rank_info.world_id = world_id;
    get_top_req.rank_info.zone_id = zone_id;
    get_top_req.rank_info.type = rank_type;
    get_top_req.rank_info.instance_id = rank_instance_id;

    // 设置sub_rank_info
    get_top_req.sub_rank_info.construct();
    get_top_req.sub_rank_info.type = sub_rank_type;
    get_top_req.sub_rank_info.instance_id = sub_rank_instance_id;

    // 设置查询参数
    get_top_req.query_from = query_from;
    get_top_req.query_count = query_count;
    get_top_req.data_source = use_image_data ? REQ_DATA_SOURCE_IMAGE : REQ_DATA_SOURCE_REAL;
    // get_top_req.image_index = image_index;

    // 序列化消息
    TdrError::ErrorType ret = msg.pack(buffer, buffer_size, &used_size);
    if (ret != TdrError::TDR_NO_ERROR) {
      std::cerr << "序列化GetTopSubRankReq失败: " << ret << std::endl;
      return false;
    }

    std::cout << "数据源类型: " << (use_image_data ? "镜像数据(IMAGE)" : "实时数据(REAL)")
              << ", 镜像索引: " << image_index << std::endl;
    return true;
  }

  // 向单个服务器发送请求并解析响应
  bool send_get_top_request(int socket_fd, const std::string& server_name, std::vector<RankData>& results,
                            uint32_t business_id, uint32_t world_id, uint32_t zone_id, uint32_t rank_type,
                            uint32_t rank_instance_id, uint32_t sub_rank_type, uint32_t sub_rank_instance_id,
                            uint32_t query_from, uint32_t query_count) {
    char buffer[1024];
    size_t packet_size;
    bool success = build_get_top_req_packet(buffer, sizeof(buffer), packet_size, business_id, world_id, zone_id,
                                            rank_type, rank_instance_id, sub_rank_type, sub_rank_instance_id,
                                            query_from, query_count, FLAGS_image_index, FLAGS_use_image_data);

    if (!success) {
      std::cerr << "构建请求包失败 " << server_name << std::endl;
      return false;
    }

    // 发送数据
    ssize_t bytes_sent = send(socket_fd, buffer, packet_size, 0);
    if (bytes_sent < 0) {
      std::cerr << "发送请求失败到 " << server_name << ": " << strerror(errno) << std::endl;
      return false;
    }

    std::cout << "向 " << server_name << " 发送了 " << bytes_sent << " 字节请求" << std::endl;

    // 接收响应 - 先接收消息头
    char response_buffer[sizeof(TopNextMsg)];
    size_t total_received = 0;
    size_t header_size = sizeof(TopNextHead);

    // 首先接收完整的消息头
    while (total_received < header_size) {
      ssize_t bytes_received = recv(socket_fd, response_buffer + total_received, header_size - total_received, 0);
      if (bytes_received < 0) {
        std::cerr << "接收响应头失败从 " << server_name << ": " << strerror(errno) << std::endl;
        return false;
      } else if (bytes_received == 0) {
        std::cout << "服务器 " << server_name << " 关闭了连接" << std::endl;
        return false;
      }
      total_received += bytes_received;
    }

    // 安全地解析消息头获取总长度，处理网络字节序
    uint32_t head_len, body_len;
    memcpy(&head_len, response_buffer + offsetof(TopNextHead, headLen), sizeof(uint32_t));
    memcpy(&body_len, response_buffer + offsetof(TopNextHead, bodyLen), sizeof(uint32_t));

    // 转换网络字节序到主机字节序
    head_len = ntohl(head_len);
    body_len = ntohl(body_len);

    size_t total_msg_size = head_len + body_len;

    if (total_msg_size > sizeof(TopNextMsg)) {
      std::cerr << "消息大小超出缓冲区: " << total_msg_size << " > " << sizeof(TopNextMsg) << std::endl;
      return false;
    }

    // 接收剩余的消息体
    while (total_received < total_msg_size) {
      ssize_t bytes_received = recv(socket_fd, response_buffer + total_received, total_msg_size - total_received, 0);
      if (bytes_received < 0) {
        std::cerr << "接收响应体失败从 " << server_name << ": " << strerror(errno) << std::endl;
        return false;
      } else if (bytes_received == 0) {
        std::cout << "服务器 " << server_name << " 关闭了连接" << std::endl;
        return false;
      }
      total_received += bytes_received;
    }

    std::cout << "从 " << server_name << " 接收到完整响应 " << total_received << " 字节" << std::endl;

    // 解析响应
    return parse_response(response_buffer, total_received, server_name, results);
  }

  // 支持分页的GetTop请求函数
  bool send_get_top_request_with_pagination(int socket_fd, const std::string& server_name,
                                            std::vector<RankData>& all_results, uint32_t& total_count,
                                            uint32_t business_id, uint32_t world_id, uint32_t zone_id,
                                            uint32_t rank_type, uint32_t rank_instance_id, uint32_t sub_rank_type,
                                            uint32_t sub_rank_instance_id, uint32_t query_from, uint32_t query_count) {
    all_results.clear();
    total_count = 0;

    uint32_t current_from = query_from;
    uint32_t batch_size = std::min(query_count, uint32_t(100));  // 每次最多获取100条
    bool first_request = true;

    std::cout << "开始分页获取榜单数据，每批次最多 " << batch_size << " 条记录" << std::endl;

    while (true) {
      std::vector<RankData> batch_results;
      uint32_t batch_total_count = 0;

      std::cout << "第 " << ((current_from - query_from) / batch_size + 1) << " 批次: 从位置 " << current_from
                << " 开始获取 " << batch_size << " 条记录" << std::endl;

      bool success = send_get_top_request_single_batch(socket_fd, server_name, batch_results, batch_total_count,
                                                       business_id, world_id, zone_id, rank_type, rank_instance_id,
                                                       sub_rank_type, sub_rank_instance_id, current_from, batch_size);

      if (!success) {
        std::cerr << "第 " << ((current_from - query_from) / batch_size + 1) << " 批次请求失败" << std::endl;
        return false;
      }

      // 第一次请求时记录总数
      if (first_request) {
        total_count = batch_total_count;
        first_request = false;
        std::cout << "榜单总记录数: " << total_count << std::endl;

        // 如果总数为0或者没有数据，直接返回
        if (total_count == 0 || batch_results.empty()) {
          std::cout << "榜单为空，结束获取" << std::endl;
          return true;
        }
      }

      // 添加本批次结果到总结果中
      all_results.insert(all_results.end(), batch_results.begin(), batch_results.end());
      std::cout << "本批次获取到 " << batch_results.size() << " 条记录，累计 " << all_results.size() << " 条"
                << std::endl;

      // 检查是否已经获取完所有数据
      if (batch_results.size() < batch_size || all_results.size() >= total_count) {
        std::cout << "已获取完所有数据，总计 " << all_results.size() << " 条记录" << std::endl;
        break;
      }

      // 注释：移除查询数量限制，获取所有数据

      // 准备下一批次
      current_from += batch_results.size();

      // 计算下一批次应该获取的数量（获取剩余的所有数据）
      uint32_t remaining = total_count - all_results.size();
      batch_size = std::min(remaining, uint32_t(100));

      if (batch_size == 0) {
        break;
      }

      // 添加小延迟避免请求过于频繁
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    return true;
  }

  // 发送单批次GetTop请求
  bool send_get_top_request_single_batch(int socket_fd, const std::string& server_name, std::vector<RankData>& results,
                                         uint32_t& total_count, uint32_t business_id, uint32_t world_id,
                                         uint32_t zone_id, uint32_t rank_type, uint32_t rank_instance_id,
                                         uint32_t sub_rank_type, uint32_t sub_rank_instance_id, uint32_t query_from,
                                         uint32_t query_count) {
    char buffer[1024];
    size_t packet_size;
    bool success = build_get_top_req_packet(buffer, sizeof(buffer), packet_size, business_id, world_id, zone_id,
                                            rank_type, rank_instance_id, sub_rank_type, sub_rank_instance_id,
                                            query_from, query_count, FLAGS_image_index, FLAGS_use_image_data);

    if (!success) {
      std::cerr << "构建请求包失败 " << server_name << std::endl;
      return false;
    }

    // 发送数据
    ssize_t bytes_sent = send(socket_fd, buffer, packet_size, 0);
    if (bytes_sent < 0) {
      std::cerr << "发送请求失败到 " << server_name << ": " << strerror(errno) << std::endl;
      return false;
    }

    if (FLAGS_verbose) {
      std::cout << "向 " << server_name << " 发送了 " << bytes_sent << " 字节请求" << std::endl;
    }

    // 接收响应 - 确保完整接收
    char response_buffer[sizeof(TopNextMsg)];
    size_t total_received = 0;

    if (!recv_complete_message(socket_fd, server_name, response_buffer, sizeof(response_buffer), total_received)) {
      return false;
    }

    if (FLAGS_verbose) {
      std::cout << "从 " << server_name << " 接收到完整响应 " << total_received << " 字节" << std::endl;
    }

    // 解析响应并获取total_count
    return parse_response_with_total_count(response_buffer, total_received, server_name, results, total_count);
  }

  // 解析响应数据（带total_count）
  bool parse_response_with_total_count(const char* buffer, size_t buffer_size, const std::string& server_name,
                                       std::vector<RankData>& results, uint32_t& total_count) {
    if (buffer_size < sizeof(TopNextHead)) {
      std::cerr << "响应数据太小 " << server_name << std::endl;
      return false;
    }

    // 尝试解析TopNext消息
    TopNextMsg response_msg;
    TdrError::ErrorType ret = response_msg.unpack(buffer, buffer_size);
    if (ret != TdrError::TDR_NO_ERROR) {
      std::cerr << "解析TopNext响应失败 " << server_name << ": " << ret << std::endl;

      // 输出原始数据用于调试
      std::cout << "原始响应数据 " << server_name << " (前64字节): ";
      for (size_t i = 0; i < std::min(size_t(64), buffer_size); ++i) {
        printf("%02x ", (unsigned char)buffer[i]);
      }
      std::cout << std::endl;
      return false;
    }

    std::cout << "响应头信息 " << server_name << ":" << std::endl;
    std::cout << "  魔数: " << response_msg.head.magic << std::endl;
    std::cout << "  版本: " << response_msg.head.version << std::endl;
    std::cout << "  命令: " << response_msg.head.command << std::endl;
    std::cout << "  业务ID: " << response_msg.head.businessID << std::endl;
    std::cout << "  头长度: " << response_msg.head.headLen << std::endl;
    std::cout << "  体长度: " << response_msg.head.bodyLen << std::endl;

    if (response_msg.head.command == CMD_GET_TOP_SUB_RANK_RSP) {
      std::cout << "收到GetTopSubRankRsp响应 " << server_name << std::endl;

      const auto& rsp = response_msg.body.get_top_sub_rank_rsp;
      std::cout << "响应详情 " << server_name << ":" << std::endl;
      std::cout << "  返回码: " << rsp.ret_info.ret << std::endl;
      std::cout << "  返回消息: " << rsp.ret_info.msg << std::endl;
      std::cout << "  总数量: " << rsp.total_count << std::endl;
      std::cout << "  结果数量: " << rsp.op_result.count << std::endl;

      // 设置total_count
      total_count = rsp.total_count;

      // 提取排名数据 - 添加额外的边界检查
      uint32_t safe_count = std::min(rsp.op_result.count, static_cast<uint32_t>(MAX_RANK_INFO_COUNT));
      for (uint32_t i = 0; i < safe_count; ++i) {
        const auto& result = rsp.op_result.op_result[i];
        RankData rank_data;
        rank_data.openid = result.user_rank_info.user_info.openid;
        rank_data.score = result.user_rank_info.user_info.score;
        rank_data.rank = result.user_rank_info.rank_no;                   // 使用UserRankInfo中的rank_no
        rank_data.timestamp = result.user_rank_info.user_info.timestamp;  // 提取时间戳
        rank_data.last_report_timestamp = result.user_rank_info.user_info.last_report_timestamp;  // 提取时间戳

        // 提取扩展排序字段
        rank_data.sort_field1 = result.user_rank_info.user_info.sort_field1;
        rank_data.sort_field2 = result.user_rank_info.user_info.sort_field2;
        rank_data.sort_field3 = result.user_rank_info.user_info.sort_field3;
        rank_data.sort_field4 = result.user_rank_info.user_info.sort_field4;
        rank_data.sort_field5 = result.user_rank_info.user_info.sort_field5;

        rank_data.ext_field1 = result.user_rank_info.user_info.ext_field1;
        rank_data.ext_field2 = result.user_rank_info.user_info.ext_field2;
        rank_data.ext_field3 = result.user_rank_info.user_info.ext_field3;
        rank_data.reduce_field1 = result.user_rank_info.user_info.reduce_field1;
        rank_data.reduce_field2 = result.user_rank_info.user_info.reduce_field2;
        rank_data.reduce_field3 = result.user_rank_info.user_info.reduce_field3;

        // 提取扩展数据
        if (result.user_rank_info.user_info.ext_data.data_len > 0) {
          rank_data.ext_data.assign(
              result.user_rank_info.user_info.ext_data.data_info,
              result.user_rank_info.user_info.ext_data.data_info + result.user_rank_info.user_info.ext_data.data_len);
        } else {
          rank_data.ext_data.clear();
        }

        results.push_back(rank_data);

        if (FLAGS_verbose) {
          std::cout << "  排名 " << (i + 1) << ": openid=" << rank_data.openid << ", score=" << rank_data.score
                    << ", rank=" << rank_data.rank << ", timestamp=" << rank_data.timestamp
                    << ", last_report_timestamp=" << rank_data.last_report_timestamp << ", sort_fields=["
                    << rank_data.sort_field1 << "," << rank_data.sort_field2 << "," << rank_data.sort_field3 << ","
                    << rank_data.sort_field4 << "," << rank_data.sort_field5 << "]"
                    << ", ext_fields=[" << rank_data.ext_field1 << ", " << rank_data.ext_field2 << ", "
                    << rank_data.ext_field3 << "], reduce_fields=[" << rank_data.reduce_field1 << ", "
                    << rank_data.reduce_field2 << ", " << rank_data.reduce_field3
                    << "], ext_data_len=" << rank_data.ext_data.size() << std::endl;
        } else {
          std::cout << "  排名 " << (i + 1) << ": openid=" << rank_data.openid << ", score=" << rank_data.score
                    << ", rank=" << rank_data.rank << ", timestamp=" << rank_data.timestamp
                    << ", last_report_timestamp=" << rank_data.last_report_timestamp << ", sort_fields=["
                    << rank_data.sort_field1 << "," << rank_data.sort_field2 << "," << rank_data.sort_field3 << ","
                    << rank_data.sort_field4 << "," << rank_data.sort_field5 << "]" << std::endl;
        }
      }

      // 0表示成功，115003表示数据不存在/榜单为空，这两种情况都应该继续处理
      return rsp.ret_info.ret == 0 || rsp.ret_info.ret == 115003;
    }

    std::cout << "未知的响应命令: " << response_msg.head.command << std::endl;
    return false;
  }

  // 解析响应数据（向后兼容版本）
  bool parse_response(const char* buffer, size_t buffer_size, const std::string& server_name,
                      std::vector<RankData>& results) {
    uint32_t total_count = 0;
    return parse_response_with_total_count(buffer, buffer_size, server_name, results, total_count);
  }

  // 比较用户数据，参考CompareUserScoreInfo的逻辑
  // 返回值: 0=相等, >0=data1>data2, <0=data1<data2
  int compare_user_score_info(const RankData& data1, const RankData& data2, int score_proc_type,
                              int sort_method_on_timestamp, int sort_method_on_score, int sort_field_num,
                              const int sort_method_on_field[5]) {
    // 根据排行榜处理类型决定比较策略
    bool is_new_mode = (score_proc_type == 0);  // 0表示最新值模式，1表示最大值模式

    if (is_new_mode) {
      // 最新值模式：只比较时间戳，不比较分数大小
      if (data1.timestamp != data2.timestamp) {
        return (data1.timestamp > data2.timestamp) ? 1 : -1;
      }
    } else {
      // 最大值模式：按照原始CompareUserScoreInfo的顺序进行比较

      // 1. 比较分数 - 使用COMPARE_SORT_FIELD宏的逻辑
      if (data1.score != data2.score) {
        if (sort_method_on_score == 0) {
          // 降序排列：分数大的排在前面
          return (data1.score > data2.score) ? 1 : -1;
        } else {
          // 升序排列：分数小的排在前面
          return (data1.score < data2.score) ? 1 : -1;
        }
      }

      // 2. 比较扩展排序字段1-5 - 使用COMPARE_SORT_FIELD_N宏的逻辑
      if (sort_field_num > 0) {
        if (data1.sort_field1 != data2.sort_field1) {
          if (sort_method_on_field[0] == 0) {
            return (data1.sort_field1 > data2.sort_field1) ? 1 : -1;
          } else {
            return (data1.sort_field1 < data2.sort_field1) ? 1 : -1;
          }
        }
      }
      if (sort_field_num > 1) {
        if (data1.sort_field2 != data2.sort_field2) {
          if (sort_method_on_field[1] == 0) {
            return (data1.sort_field2 > data2.sort_field2) ? 1 : -1;
          } else {
            return (data1.sort_field2 < data2.sort_field2) ? 1 : -1;
          }
        }
      }
      if (sort_field_num > 2) {
        if (data1.sort_field3 != data2.sort_field3) {
          if (sort_method_on_field[2] == 0) {
            return (data1.sort_field3 > data2.sort_field3) ? 1 : -1;
          } else {
            return (data1.sort_field3 < data2.sort_field3) ? 1 : -1;
          }
        }
      }
      if (sort_field_num > 3) {
        if (data1.sort_field4 != data2.sort_field4) {
          if (sort_method_on_field[3] == 0) {
            return (data1.sort_field4 > data2.sort_field4) ? 1 : -1;
          } else {
            return (data1.sort_field4 < data2.sort_field4) ? 1 : -1;
          }
        }
      }
      if (sort_field_num > 4) {
        if (data1.sort_field5 != data2.sort_field5) {
          if (sort_method_on_field[4] == 0) {
            return (data1.sort_field5 > data2.sort_field5) ? 1 : -1;
          } else {
            return (data1.sort_field5 < data2.sort_field5) ? 1 : -1;
          }
        }
      }

      // 3. 比较时间戳 - 使用COMPARE_SORT_FIELD宏的逻辑
      if (data1.timestamp != data2.timestamp) {
        if (sort_method_on_timestamp == 0) {
          // 降序排列：时间戳大的排在前面
          return (data1.timestamp > data2.timestamp) ? 1 : -1;
        } else {
          // 升序排列：时间戳小的排在前面
          return (data1.timestamp < data2.timestamp) ? 1 : -1;
        }
      }
    }

    // 4. 最后比较openid - 使用strncmp的逻辑
    int ret = data1.openid.compare(data2.openid);
    if (ret == 0) {
      return 0;  // COMPARE_EQ
    } else if (ret > 0) {
      return 1;  // COMPARE_GT
    } else {
      return -1;  // COMPARE_LT
    }
  }

  // 比较两个服务器的排名结果，返回是否一致
  bool compare_rank_results(const std::vector<RankData>& results1, const std::vector<RankData>& results2,
                            int score_proc_type, int sort_method_on_timestamp, int sort_method_on_score,
                            int sort_field_num, const int sort_method_on_field[5], uint32_t business_id = 0,
                            uint32_t world_id = 0, uint32_t zone_id = 0, uint32_t rank_type = 0,
                            uint32_t rank_instance_id = 0, uint32_t sub_rank_type = 0,
                            uint32_t sub_rank_instance_id = 0) {
    std::cout << "\n=== 数据校验和比较 ===" << std::endl;

    std::cout << "服务器1 (" << server1_.name << ") 返回 " << results1.size() << " 条记录" << std::endl;
    std::cout << "服务器2 (" << server2_.name << ") 返回 " << results2.size() << " 条记录" << std::endl;

    if (results1.empty() && results2.empty()) {
      std::cout << "两个服务器都没有返回数据" << std::endl;
      return true;  // 两个服务器都没有数据，认为是一致的
    }

    // 创建映射表便于比较
    std::map<std::string, RankData> map1, map2;
    for (const auto& data : results1) {
      map1[data.openid] = data;
    }
    for (const auto& data : results2) {
      map2[data.openid] = data;
    }

    // 比较结果
    std::cout << "\n--- 详细比较结果 ---" << std::endl;
    if (FLAGS_verbose) {
      std::cout << std::left << std::setw(20) << "OpenID" << std::setw(24) << "Score1/2" << std::setw(12) << "Rank1/2"
                << std::setw(40) << "SortFields1" << std::setw(40) << "SortFields2" << std::setw(15) << "ExtData1/2"
                << std::setw(10) << "状态" << std::endl;
      std::cout << std::string(161, '-') << std::endl;
    } else {
      std::cout << std::left << std::setw(20) << "OpenID" << std::setw(15) << "Server1_Score" << std::setw(15)
                << "Server2_Score" << std::setw(15) << "Server1_Rank" << std::setw(15) << "Server2_Rank"
                << std::setw(15) << "ExtDataLen1/2" << std::setw(10) << "状态" << std::endl;
      std::cout << std::string(105, '-') << std::endl;
    }

    // 获取所有唯一的openid
    std::set<std::string> all_openids;
    for (const auto& data : results1) all_openids.insert(data.openid);
    for (const auto& data : results2) all_openids.insert(data.openid);

    int match_count = 0;
    int diff_count = 0;
    int missing_count = 0;

    for (const auto& openid : all_openids) {
      auto it1 = map1.find(openid);
      auto it2 = map2.find(openid);

      std::string status;
      if (it1 != map1.end() && it2 != map2.end()) {
        // 两个服务器都有这个用户
        bool score_match = (it1->second.score == it2->second.score);
        bool rank_match = (it1->second.rank == it2->second.rank);
        bool timestamp_match = (it1->second.timestamp == it2->second.timestamp);
        // last_report_timestamp不参与比较，只是记录上报时间
        bool sort_fields_match = true;
        if (sort_field_num > 0) {
          if (it1->second.sort_field1 != it2->second.sort_field1) sort_fields_match = false;
        }
        if (sort_field_num > 1) {
          if (it1->second.sort_field2 != it2->second.sort_field2) sort_fields_match = false;
        }
        if (sort_field_num > 2) {
          if (it1->second.sort_field3 != it2->second.sort_field3) sort_fields_match = false;
        }
        if (sort_field_num > 3) {
          if (it1->second.sort_field4 != it2->second.sort_field4) sort_fields_match = false;
        }
        if (sort_field_num > 4) {
          if (it1->second.sort_field5 != it2->second.sort_field5) sort_fields_match = false;
        }
        bool ext_data_match = (it1->second.ext_data == it2->second.ext_data);

        if (score_match && rank_match && timestamp_match && sort_fields_match && ext_data_match) {
          status = "匹配";
          match_count++;
        } else {
          status = "不匹配";
          if (!score_match) status += "(分数)";
          if (!rank_match) status += "(排名)";
          if (!timestamp_match) status += "(时间戳)";
          if (!sort_fields_match) status += "(排序字段)";
          if (!ext_data_match) status += "(扩展数据)";
          diff_count++;

          // 自动上报不一致的数据，参考CompareData的逻辑
          if (FLAGS_auto_report_inconsistent && business_id > 0) {
            // 比较两个服务器的数据，决定以哪个为准
            int compare_result =
                compare_user_score_info(it1->second, it2->second, score_proc_type, sort_method_on_timestamp,
                                        sort_method_on_score, sort_field_num, sort_method_on_field);

            if (compare_result > 0) {
              // server1 > server2，需要更新server2，上报server1的数据到server2
              std::cout << "正在上报数据到server2: " << openid << " (server1数据更新)" << std::endl;
              bool report_success =
                  send_report_sub_rank_request(socket2_, server2_.name, it1->second, business_id, world_id, zone_id,
                                               rank_type, rank_instance_id, sub_rank_type, sub_rank_instance_id);
              if (report_success) {
                report_to_server2_count_.fetch_add(1);
                status += "[已上报Server2]";
              } else {
                status += "[上报Server2失败]";
              }
            } else if (compare_result < 0) {
              // server1 < server2，需要更新server1，上报server2的数据到server1
              std::cout << "正在上报数据到server1: " << openid << " (server2数据更新)" << std::endl;
              bool report_success =
                  send_report_sub_rank_request(socket1_, server1_.name, it2->second, business_id, world_id, zone_id,
                                               rank_type, rank_instance_id, sub_rank_type, sub_rank_instance_id);
              if (report_success) {
                report_to_server1_count_.fetch_add(1);
                status += "[已上报Server1]";
              } else {
                status += "[上报Server1失败]";
              }
            } else {
              // 数据相等，但其他字段不匹配（如ext_data），检查是否需要同步ext_data
              if (!ext_data_match) {
                bool report_success = false;
                if (it1->second.ext_data.size() == 0 && it2->second.ext_data.size() > 0) {
                  // 如果server1的ext_data为空，则上报server2的数据到server1
                  std::cout << "正在上报数据到server1: " << openid << " (server2数据更新)" << std::endl;
                  report_success =
                      send_report_sub_rank_request(socket1_, server1_.name, it2->second, business_id, world_id, zone_id,
                                                   rank_type, rank_instance_id, sub_rank_type, sub_rank_instance_id);
                  if (report_success) {
                    report_to_server1_count_.fetch_add(1);
                    status += "[已上报Server1]";
                  } else {
                    status += "[上报Server1失败]";
                  }
                }

                if (it2->second.ext_data.size() == 0 && it1->second.ext_data.size() > 0) {
                  // 如果server2的ext_data为空，则上报server1的数据到server2
                  std::cout << "🔄 正在上报数据到server2: " << openid << " (server1数据更新)" << std::endl;
                  report_success =
                      send_report_sub_rank_request(socket2_, server2_.name, it1->second, business_id, world_id, zone_id,
                                                   rank_type, rank_instance_id, sub_rank_type, sub_rank_instance_id);
                  if (report_success) {
                    report_to_server2_count_.fetch_add(1);
                    status += "[已上报Server2]";
                  } else {
                    status += "[上报Server2失败]";
                  }
                }
              }
            }
          }
        }

        if (FLAGS_verbose) {
          // 详细输出模式
          std::string sort_fields1 =
              "[" + std::to_string(it1->second.sort_field1) + "," + std::to_string(it1->second.sort_field2) + "," +
              std::to_string(it1->second.sort_field3) + "," + std::to_string(it1->second.sort_field4) + "," +
              std::to_string(it1->second.sort_field5) + "]";
          std::string sort_fields2 =
              "[" + std::to_string(it2->second.sort_field1) + "," + std::to_string(it2->second.sort_field2) + "," +
              std::to_string(it2->second.sort_field3) + "," + std::to_string(it2->second.sort_field4) + "," +
              std::to_string(it2->second.sort_field5) + "]";
          std::string score_info = std::to_string(it1->second.score) + "/" + std::to_string(it2->second.score);
          std::string rank_info = std::to_string(it1->second.rank) + "/" + std::to_string(it2->second.rank);
          std::string ext_data_info =
              std::to_string(it1->second.ext_data.size()) + "/" + std::to_string(it2->second.ext_data.size());

          std::cout << std::left << std::setw(20) << openid << std::setw(24) << score_info << std::setw(12) << rank_info
                    << std::setw(40) << sort_fields1 << std::setw(40) << sort_fields2 << std::setw(15) << ext_data_info
                    << std::setw(10) << status << std::endl;
        } else {
          // 简洁输出模式
          std::string ext_data_info =
              std::to_string(it1->second.ext_data.size()) + "/" + std::to_string(it2->second.ext_data.size());
          std::cout << std::left << std::setw(20) << openid << std::setw(15) << it1->second.score << std::setw(15)
                    << it2->second.score << std::setw(15) << it1->second.rank << std::setw(15) << it2->second.rank
                    << std::setw(15) << ext_data_info << std::setw(10) << status << std::endl;
        }
      } else if (it1 != map1.end()) {
        // 只有服务器1有，需要上报到server2
        status = "仅Server1";
        missing_count++;

        // 自动上报缺失的数据到server2
        if (FLAGS_auto_report_inconsistent && business_id > 0) {
          std::cout << "正在上报缺失数据到server2: " << openid << std::endl;
          bool report_success =
              send_report_sub_rank_request(socket2_, server2_.name, it1->second, business_id, world_id, zone_id,
                                           rank_type, rank_instance_id, sub_rank_type, sub_rank_instance_id);
          if (report_success) {
            report_to_server2_count_.fetch_add(1);
            status += "[已上报Server2]";
          } else {
            status += "[上报Server2失败]";
          }
        }
        if (FLAGS_verbose) {
          std::string sort_fields1 =
              "[" + std::to_string(it1->second.sort_field1) + "," + std::to_string(it1->second.sort_field2) + "," +
              std::to_string(it1->second.sort_field3) + "," + std::to_string(it1->second.sort_field4) + "," +
              std::to_string(it1->second.sort_field5) + "]";
          std::string score_info = std::to_string(it1->second.score) + "/NULL";
          std::string rank_info = std::to_string(it1->second.rank) + "/NULL";
          std::string ext_data_info = std::to_string(it1->second.ext_data.size()) + "/NULL";

          std::cout << std::left << std::setw(20) << openid << std::setw(24) << score_info << std::setw(12) << rank_info
                    << std::setw(40) << sort_fields1 << std::setw(40) << "NULL" << std::setw(15) << ext_data_info
                    << std::setw(10) << status << std::endl;
        } else {
          std::string ext_data_info = std::to_string(it1->second.ext_data.size()) + "/NULL";
          std::cout << std::left << std::setw(20) << openid << std::setw(15) << it1->second.score << std::setw(15)
                    << "NULL" << std::setw(15) << it1->second.rank << std::setw(15) << "NULL" << std::setw(15)
                    << ext_data_info << std::setw(10) << status << std::endl;
        }
      } else {
        // 只有服务器2有，需要上报到server1
        status = "仅Server2";
        missing_count++;

        // 自动上报缺失的数据到server1
        if (FLAGS_auto_report_inconsistent && business_id > 0) {
          std::cout << "正在上报缺失数据到server1: " << openid << std::endl;
          bool report_success =
              send_report_sub_rank_request(socket1_, server1_.name, it2->second, business_id, world_id, zone_id,
                                           rank_type, rank_instance_id, sub_rank_type, sub_rank_instance_id);
          if (report_success) {
            report_to_server1_count_.fetch_add(1);
            status += "[已上报Server1]";
          } else {
            status += "[上报Server1失败]";
          }
        }
        if (FLAGS_verbose) {
          std::string sort_fields2 =
              "[" + std::to_string(it2->second.sort_field1) + "," + std::to_string(it2->second.sort_field2) + "," +
              std::to_string(it2->second.sort_field3) + "," + std::to_string(it2->second.sort_field4) + "," +
              std::to_string(it2->second.sort_field5) + "]";
          std::string score_info = "NULL/" + std::to_string(it2->second.score);
          std::string rank_info = "NULL/" + std::to_string(it2->second.rank);
          std::string ext_data_info = "NULL/" + std::to_string(it2->second.ext_data.size());

          std::cout << std::left << std::setw(20) << openid << std::setw(24) << score_info << std::setw(12) << rank_info
                    << std::setw(40) << "NULL" << std::setw(40) << sort_fields2 << std::setw(15) << ext_data_info
                    << std::setw(10) << status << std::endl;
        } else {
          std::string ext_data_info = "NULL/" + std::to_string(it2->second.ext_data.size());
          std::cout << std::left << std::setw(20) << openid << std::setw(15) << "NULL" << std::setw(15)
                    << it2->second.score << std::setw(15) << "NULL" << std::setw(15) << it2->second.rank
                    << std::setw(15) << ext_data_info << std::setw(10) << status << std::endl;
        }
      }
    }

    // 输出统计信息
    std::cout << "\n--- 校验统计 ---" << std::endl;
    std::cout << "匹配记录: " << match_count << std::endl;
    std::cout << "不匹配记录: " << diff_count << std::endl;
    std::cout << "缺失记录: " << missing_count << std::endl;
    std::cout << "总记录数: " << all_openids.size() << std::endl;

    // 输出上报统计
    if (FLAGS_auto_report_inconsistent) {
      std::cout << "\n--- 上报统计 ---" << std::endl;
      std::cout << "上报到Server1数量: " << report_to_server1_count_.load() << std::endl;
      std::cout << "上报到Server2数量: " << report_to_server2_count_.load() << std::endl;
      std::cout << "总上报数量: " << (report_to_server1_count_.load() + report_to_server2_count_.load()) << std::endl;
    }

    if (diff_count == 0 && missing_count == 0) {
      std::cout << "✅ 数据校验通过：两个服务器的数据完全一致" << std::endl;
      return true;
    } else {
      std::cout << "❌ 数据校验失败：两个服务器的数据存在差异" << std::endl;
      return false;
    }
  }

 private:
  ServerInfo server1_;
  ServerInfo server2_;
  int socket1_;
  int socket2_;
  bool connected1_;
  bool connected2_;

  // 上报统计 - 使用原子操作防止竞态条件
  mutable std::atomic<int> report_to_server1_count_;
  mutable std::atomic<int> report_to_server2_count_;

  // 排行榜配置缓存
  mutable std::map<std::string, RankConfig>
      rank_config_cache_;  // key: "business_id:rank_type:rank_instance_id", value: RankConfig
};

// 示例使用函数
bool example_dual_server_usage() {
  // 解析服务器地址
  auto server1_addr = parse_server_address(FLAGS_server1);
  auto server2_addr = parse_server_address(FLAGS_server2);

  // 创建双服务器客户端，使用gflags参数
  FastCheckTopNextClient client(ServerInfo(server1_addr.first, server1_addr.second, FLAGS_server1_name),
                                ServerInfo(server2_addr.first, server2_addr.second, FLAGS_server2_name));

  std::cout << "双服务器TopNext客户端启动..." << std::endl;

  if (FLAGS_verbose) {
    std::cout << "配置参数:" << std::endl;
    std::cout << "  服务器1: " << FLAGS_server1 << " (" << FLAGS_server1_name << ")" << std::endl;
    std::cout << "  服务器2: " << FLAGS_server2 << " (" << FLAGS_server2_name << ")" << std::endl;
    std::cout << "  业务参数: business_id=" << FLAGS_business_id << ", world_id=" << FLAGS_world_id
              << ", zone_id=" << FLAGS_zone_id << std::endl;
    std::cout << "  排行榜参数: rank_type=" << FLAGS_rank_type << ", rank_instance_id=" << FLAGS_rank_instance_id
              << std::endl;
    std::cout << "  子排行榜参数: sub_rank_type=" << FLAGS_sub_rank_type
              << ", sub_rank_instance_id=" << FLAGS_sub_rank_instance_id << std::endl;
    std::cout << "  查询参数: query_from=" << FLAGS_query_from << ", query_count=" << FLAGS_query_count << std::endl;
    std::cout << "  数据源: " << (FLAGS_use_image_data ? "镜像数据" : "实时数据")
              << " (image_index=" << FLAGS_image_index << ")" << std::endl;
    std::cout << "  排行榜配置: 自动从服务器获取" << std::endl;
  }

  // 连接两个服务器
  if (!client.connect_both_servers()) {
    std::cerr << "连接服务器失败" << std::endl;
    return false;
  }

  // 预先获取排行榜配置
  int score_proc_type = 0;  // 默认为最新值模式
  int sort_method_on_timestamp = 1;
  int sort_method_on_score = 0;
  int sort_field_num = 4;
  int sort_method_on_field[5] = {0, 0, 0, 0, 0};  // 默认降序

  if (!FLAGS_use_default_config) {
    if (client.get_rank_config(FLAGS_business_id, FLAGS_rank_type, FLAGS_rank_instance_id, score_proc_type,
                               sort_method_on_timestamp, sort_method_on_score, sort_field_num, sort_method_on_field)) {
      std::cout << "获取排行榜配置成功: score_proc_type=" << score_proc_type << " ("
                << (score_proc_type == 0 ? "最新值模式" : "最大值模式")
                << "), sort_method_on_timestamp=" << sort_method_on_timestamp
                << ", sort_method_on_score=" << sort_method_on_score << ", sort_field_num=" << sort_field_num
                << std::endl;
    } else {
      std::cerr << "警告: 无法获取排行榜配置，使用默认配置" << std::endl;
      std::cout << "获取排行榜配置成功: score_proc_type=" << score_proc_type << " ("
                << (score_proc_type == 0 ? "最新值模式" : "最大值模式")
                << "), sort_method_on_timestamp=" << sort_method_on_timestamp
                << ", sort_method_on_score=" << sort_method_on_score << ", sort_field_num=" << sort_field_num
                << std::endl;
    }
  } else {
    std::cerr << "警告: 使用默认配置" << std::endl;
    std::cout << "获取排行榜配置成功: score_proc_type=" << score_proc_type << " ("
              << (score_proc_type == 0 ? "最新值模式" : "最大值模式")
              << "), sort_method_on_timestamp=" << sort_method_on_timestamp
              << ", sort_method_on_score=" << sort_method_on_score << ", sort_field_num=" << sort_field_num
              << std::endl;
  }

  // 自动上报功能将直接使用已连接的server1和server2

  // 根据参数选择校验模式
  bool success;
  if (FLAGS_check_all_sub_ranks) {
    // 获取所有子榜并逐个校验
    success = client.get_and_check_all_sub_ranks(score_proc_type, sort_method_on_timestamp, sort_method_on_score,
                                                 sort_field_num, sort_method_on_field, FLAGS_business_id,
                                                 FLAGS_world_id, FLAGS_zone_id, FLAGS_rank_type, FLAGS_rank_instance_id,
                                                 FLAGS_query_from, FLAGS_query_count);
  } else {
    // 单个子榜校验
    success = client.send_and_compare_get_top_requests(
        score_proc_type, sort_method_on_timestamp, sort_method_on_score, sort_field_num, sort_method_on_field,
        FLAGS_business_id, FLAGS_world_id, FLAGS_zone_id, FLAGS_rank_type, FLAGS_rank_instance_id, FLAGS_sub_rank_type,
        FLAGS_sub_rank_instance_id, FLAGS_query_from, FLAGS_query_count);
  }

  std::cout << "\n请求处理完成" << std::endl;

  // 关闭连接
  client.close_connections();
  std::cout << "\n连接已关闭" << std::endl;

  if (!success) {
    std::cout << "一致性校验失败" << std::endl;
    return false;
  }

  return true;
}

// 测试连接到本地测试服务器
bool test_with_local_servers() {
  std::cout << "测试连接到本地测试服务器..." << std::endl;

  // 解析服务器地址
  auto server1_addr = parse_server_address(FLAGS_server1);
  auto server2_addr = parse_server_address(FLAGS_server2);

  // 连接到我们之前创建的测试服务器，使用gflags参数
  FastCheckTopNextClient client(ServerInfo(server1_addr.first, server1_addr.second, "TestServer1"),
                                ServerInfo(server2_addr.first, server2_addr.second, "TestServer2"));

  if (FLAGS_verbose) {
    std::cout << "测试服务器配置:" << std::endl;
    std::cout << "  测试服务器1: " << FLAGS_server1 << std::endl;
    std::cout << "  测试服务器2: " << FLAGS_server2 << std::endl;
  }

  if (!client.connect_both_servers()) {
    std::cerr << "连接测试服务器失败" << std::endl;
    return false;
  }

  // 发送请求到测试服务器（虽然测试服务器不理解TopNext协议，但可以测试连接和数据传输）
  std::cout << "\n向测试服务器发送TopNext协议包..." << std::endl;
  int score_proc_type = 1;  // 测试时使用默认的最大值模式
  int sort_method_on_timestamp = 0;
  int sort_method_on_score = 0;
  int sort_field_num = 0;
  int sort_method_on_field[5] = {0, 0, 0, 0, 0};  // 默认降序
  bool success = client.send_and_compare_get_top_requests(
      score_proc_type, sort_method_on_timestamp, sort_method_on_score, sort_field_num, sort_method_on_field,
      FLAGS_business_id, FLAGS_world_id, FLAGS_zone_id, FLAGS_rank_type, FLAGS_rank_instance_id, FLAGS_sub_rank_type,
      FLAGS_sub_rank_instance_id, FLAGS_query_from, FLAGS_query_count);

  if (success) {
    std::cout << "测试完成" << std::endl;
  }

  client.close_connections();
  return success;
}

int main(int argc, char* argv[]) {
  // 设置程序描述
  google::SetUsageMessage(
      "快速榜单校验工具 - 向两个TopNext服务器发送GetTop请求并比较结果\n"
      "示例用法:\n"
      "  " +
      std::string(argv[0]) +
      " --business_id=10000 --world_id=1 --zone_id=1\n"
      "  " +
      std::string(argv[0]) +
      " --test_local --server1=127.0.0.1:8080 --server2=127.0.0.1:8081\n"
      "  " +
      std::string(argv[0]) +
      " --verbose --query_count=20 --server1=10.0.0.1:10301 --server2=10.0.0.2:10302\n"
      "  " +
      std::string(argv[0]) +
      " --use_image_data --image_index=1 --verbose\n"
      "  " +
      std::string(argv[0]) +
      " --check_all_sub_ranks --business_id=10000 --verbose\n"
      "  " +
      std::string(argv[0]) +
      " --check_all_sub_ranks --random_sample_ratio=0.1 --verbose  # 随机抽样10%的子榜\n"
      "  " +
      std::string(argv[0]) +
      " --check_all_sub_ranks --random_sample_ratio=0.05 --random_seed=12345  # 抽样5%并指定随机种子\n"
      "  " +
      std::string(argv[0]) +
      " --check_all_sub_ranks --random_sample_ratio=0.1 --sample_sub_rank_types=1001,1002  # 只抽样指定类型的子榜\n"
      "  " +
      std::string(argv[0]) + " --auto_report_inconsistent --business_id=10000 --verbose  # 自动上报不一致的数据");

  // 解析命令行参数
  google::ParseCommandLineFlags(&argc, &argv, true);

  if (FLAGS_only_show_summary) {
    std::cout.setstate(std::ios_base::failbit);
  }

  bool success = false;
  try {
    if (FLAGS_test_local) {
      success = test_with_local_servers();
    } else {
      success = example_dual_server_usage();
    }
  } catch (const std::exception& e) {
    std::cerr << "异常: " << e.what() << std::endl;
    return 1;
  }

  // 清理gflags
  google::ShutDownCommandLineFlags();

  if (!success) {
    return 1;
  }

  return 0;
}