# TopNext 榜单数据一致性快速检测工具

## 概述

`fast_check` 是一个用于检测两个 TopNext 服务器之间榜单数据一致性的工具。它可以同时向两个服务器发送 GetTop 请求，并比较返回的排名数据，帮助验证服务器间数据的一致性。

## 功能特性

- ✅ **双服务器并行检测**: 同时连接两个 TopNext 服务器
- ✅ **完整的 TDR 协议支持**: 使用真正的 TopNext TDR 协议进行通信
- ✅ **灵活的参数配置**: 支持通过命令行参数配置所有请求参数
- ✅ **数据源支持**: 支持实时数据和镜像数据两种数据源
- ✅ **详细的数据比较**: 提供逐条记录的数据对比和统计信息，包括分数、排名、排序字段和扩展数据
- ✅ **详细日志输出**: 可选的详细输出模式用于调试
- ✅ **自动分页获取**: 自动分批获取完整榜单数据，突破单次100条限制
- ✅ **子榜批量校验**: 支持获取所有子榜并逐个进行数据一致性校验
- ✅ **随机抽样校验**: 支持随机抽样部分子榜进行校验，提高大规模检测效率
- ✅ **自动数据修复**: 支持自动上报不一致的数据到服务器进行修复，参考TopNext Check的数据比较逻辑

## 编译要求

- CMake 3.14+
- C++11 编译器
- 依赖库：
  - gflags (命令行参数解析)
  - asio (网络库，通过 FetchContent 自动获取)
  - TopNext TDR 协议库

## 使用方法

### 基本用法

```bash
# 使用默认参数连接两个服务器
./fast_check --server1=10.0.0.1:10301 --server2=10.0.0.2:10302

# 指定业务参数
./fast_check \
  --server1=10.0.0.1:10301 \
  --server2=10.0.0.2:10302 \
  --business_id=10249 \
  --world_id=62 \
  --zone_id=1 \
  --verbose
```

### 使用镜像数据源

```bash
# 使用镜像数据进行检测
./fast_check \
  --server1=10.0.0.1:10301 \
  --server2=10.0.0.2:10302 \
  --use_image_data \
  --image_index=1 \
  --verbose
```

### 子榜批量校验

```bash
# 获取所有子榜并逐个校验
./fast_check \
  --server1=10.0.0.1:10301 \
  --server2=10.0.0.2:10302 \
  --check_all_sub_ranks \
  --business_id=10249 \
  --world_id=62 \
  --verbose
```

### 随机抽样校验

```bash
# 随机抽样10%的子榜进行校验
./fast_check \
  --server1=10.0.0.1:10301 \
  --server2=10.0.0.2:10302 \
  --check_all_sub_ranks \
  --random_sample_ratio=0.1 \
  --business_id=10249 \
  --verbose

# 抽样5%并指定随机种子（确保结果可重现）
./fast_check \
  --server1=10.0.0.1:10301 \
  --server2=10.0.0.2:10302 \
  --check_all_sub_ranks \
  --random_sample_ratio=0.05 \
  --random_seed=12345 \
  --verbose
```

### 自动数据修复

```bash
# 启用自动上报不一致的数据（使用server1作为上报服务器）
./fast_check \
  --server1=10.0.0.1:10301 \
  --server2=10.0.0.2:10302 \
  --auto_report_inconsistent \
  --business_id=10249 \
  --verbose

# 指定专门的上报服务器
./fast_check \
  --server1=10.0.0.1:10301 \
  --server2=10.0.0.2:10302 \
  --auto_report_inconsistent \
  --report_server=10.0.0.3:10301 \
  --business_id=10249 \
  --verbose

# 结合子榜批量校验和自动修复
./fast_check \
  --server1=10.0.0.1:10301 \
  --server2=10.0.0.2:10302 \
  --check_all_sub_ranks \
  --auto_report_inconsistent \
  --random_sample_ratio=0.1 \
  --business_id=10249 \
  --verbose
```

### 本地测试模式

```bash
# 连接到本地测试服务器
./fast_check \
  --test_local \
  --server1=127.0.0.1:8080 \
  --server2=127.0.0.1:8081
```

## 命令行参数

### 服务器配置

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--server1` | `127.0.0.1:10301` | 第一个服务器地址 (格式: IP:port)，topnext_app的tcp监听地址，跟check使用的地址是同一个 |
| `--server1_name` | `TopNextServer1` | 第一个服务器的名称 |
| `--server2` | `127.0.0.1:10302` | 第二个服务器地址 (格式: IP:port)，topnext_app的tcp监听地址，跟check使用的地址是同一个 |
| `--server2_name` | `TopNextServer2` | 第二个服务器的名称 |

### 业务参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--business_id` | `10000` | 业务ID |
| `--world_id` | `1` | 世界ID |
| `--zone_id` | `1` | 区域ID |
| `--rank_type` | `1` | 排行榜类型 |
| `--rank_instance_id` | `1` | 排行榜实例ID |
| `--sub_rank_type` | `1` | 子排行榜类型 |
| `--sub_rank_instance_id` | `1` | 子排行榜实例ID |

### 查询参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--query_from` | `1` | 查询起始位置 |
| `--query_count` | `100` | 单次查询数量 (工具会自动分页获取所有数据) |
| `--image_index` | `0` | 镜像索引 (当使用镜像数据源时) |

### 控制参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--use_image_data` | `false` | 是否使用镜像数据源 (false=实时数据, true=镜像数据) |
| `--test_local` | `false` | 是否连接到本地测试服务器 |
| `--verbose` | `false` | 是否显示详细输出 |
| `--check_all_sub_ranks` | `false` | 是否获取所有子榜并逐个校验 |
| `--random_sample_ratio` | `0.0` | 随机抽样校验比例 (0.0-1.0, 0表示不随机抽样, 0.1表示抽样10%) |
| `--random_seed` | `0` | 随机数种子 (0表示使用当前时间作为种子) |
| `--sample_sub_rank_types` | `""` | 指定抽样的子榜类型列表 (逗号分隔, 如: 1001,1002,1003, 空表示所有类型) |
| `--auto_report_inconsistent` | `false` | 是否自动上报不一致的数据到服务器进行修复 |
| `--report_server` | `""` | 数据上报服务器地址 (格式: IP:port, 空表示使用server1) |
| `--only_show_summary` | `false` | 是否只显示最后的统计信息 |

## 分页获取机制

工具会自动处理大型榜单的分页获取：

1. **自动检测榜单大小**: 首次请求会返回榜单的总记录数 (`total_count`)
2. **分批获取数据**: 如果总记录数超过单次查询限制（100条），工具会自动分批获取
3. **完整数据校验**: 确保获取到所有榜单数据后再进行两个服务器间的数据比较
4. **进度显示**: 显示每批次的获取进度和累计数据量

### 分页获取示例输出

```
开始分页获取榜单数据，每批次最多 100 条记录
第 1 批次: 从位置 1 开始获取 100 条记录
榜单总记录数: 350
本批次获取到 100 条记录，累计 100 条
第 2 批次: 从位置 101 开始获取 100 条记录
本批次获取到 100 条记录，累计 200 条
第 3 批次: 从位置 201 开始获取 100 条记录
本批次获取到 100 条记录，累计 300 条
第 4 批次: 从位置 301 开始获取 50 条记录
本批次获取到 50 条记录，累计 350 条
已获取完所有数据，总计 350 条记录
```

## 随机抽样校验功能

当需要检测大量子榜时，全量校验可能耗时较长。工具提供了随机抽样功能，可以从所有子榜中随机选择一定比例进行校验，在保证一定覆盖率的同时大幅提高检测效率。

### 抽样策略

1. **比例控制**: 通过 `--random_sample_ratio` 参数控制抽样比例（0.0-1.0）
2. **类型过滤**: 通过 `--sample_sub_rank_types` 参数指定只抽样特定类型的子榜
3. **最小保证**: 即使抽样比例很小，也会至少抽取1个子榜进行校验
4. **随机种子**: 支持指定随机种子确保结果可重现
5. **均匀分布**: 使用标准随机算法确保抽样的均匀性

### 抽样参数说明

| 参数 | 取值范围 | 说明 | 示例 |
|------|----------|------|------|
| `--random_sample_ratio` | 0.0-1.0 | 抽样比例 | 0.1 表示抽样10% |
| `--random_seed` | 0或正整数 | 随机种子 | 0表示使用当前时间，12345表示固定种子 |
| `--sample_sub_rank_types` | 逗号分隔的数字 | 指定子榜类型 | 1001,1002,1003 表示只抽样这三种类型 |

### 使用示例

```bash
# 随机抽样10%的子榜进行校验
./fast_check \
  --server1=10.0.0.1:10301 \
  --server2=10.0.0.2:10302 \
  --check_all_sub_ranks \
  --random_sample_ratio=0.1 \
  --business_id=10249 \
  --verbose

# 抽样5%并指定随机种子（确保结果可重现）
./fast_check \
  --server1=10.0.0.1:10301 \
  --server2=10.0.0.2:10302 \
  --check_all_sub_ranks \
  --random_sample_ratio=0.05 \
  --random_seed=12345 \
  --verbose

# 全量校验（不使用随机抽样）
./fast_check \
  --server1=10.0.0.1:10301 \
  --server2=10.0.0.2:10302 \
  --check_all_sub_ranks \
  --business_id=10249 \
  --verbose

# 只抽样指定类型的子榜（10%抽样比例）
./fast_check \
  --server1=10.0.0.1:10301 \
  --server2=10.0.0.2:10302 \
  --check_all_sub_ranks \
  --random_sample_ratio=0.1 \
  --sample_sub_rank_types=1001,1002,1003 \
  --business_id=10249 \
  --verbose

# 全量校验指定类型的子榜（不随机抽样）
./fast_check \
  --server1=10.0.0.1:10301 \
  --server2=10.0.0.2:10302 \
  --check_all_sub_ranks \
  --sample_sub_rank_types=1001,1005,1010 \
  --business_id=10249 \
  --verbose
```

### 抽样输出示例

```
获取到 3134 个子榜
根据指定类型过滤后剩余 856 个子榜 (指定类型: 1001, 1002, 1003)
随机抽样模式: 从 856 个子榜中抽取 85 个进行校验 (抽样比例: 10%)
使用随机数种子: 1640995200123
开始校验 85 个子榜...

--- 校验子榜 [type=1001, instance_id=10000001] ---
✅ 子榜校验通过

--- 校验子榜 [type=1005, instance_id=10000005] ---
✅ 子榜校验通过

...

=== 子榜校验总结 ===
总共校验子榜数: 313
校验通过: 310
校验失败: 3
⚠️  有 3 个子榜存在数据不一致
```

### 适用场景

1. **快速健康检查**: 使用较小的抽样比例（如5%-10%）进行快速检测
2. **定期巡检**: 使用中等抽样比例（如20%-30%）进行定期数据一致性检查
3. **问题排查**: 发现问题后使用全量校验进行详细分析
4. **性能测试**: 在不同抽样比例下测试工具性能
5. **特定类型检测**: 只检测特定业务类型的子榜，提高检测针对性
6. **分类验证**: 按子榜类型分组进行验证，便于问题定位

### 抽样效果对比

| 子榜总数 | 抽样比例 | 抽样数量 | 预估时间节省 |
|----------|----------|----------|--------------|
| 3000 | 10% | 300 | 90% |
| 5000 | 5% | 250 | 95% |
| 1000 | 20% | 200 | 80% |
| 10000 | 1% | 100 | 99% |

## 自动数据修复机制

工具支持自动上报不一致的数据到服务器进行修复，参考了TopNext Check模块的数据比较和修复逻辑。

### 数据比较策略

参考`CompareUserScoreInfo`函数的逻辑，工具会按以下优先级比较两个服务器的数据：

1. **分数比较**:
   - **最新值模式** (score_proc_type=0): 只比较时间戳，不比较分数大小
   - **最大值模式** (score_proc_type=1): 按以下顺序进行比较：
     - **分数比较**: 根据`sort_method_on_score`配置决定排序方向
       - `sort_method_on_score=0`: 降序排列（分数大的排在前面）
       - `sort_method_on_score=1`: 升序排列（分数小的排在前面）
     - **扩展排序字段**: 依次比较sort_field1到sort_field5，每个字段独立配置排序方向
       - `sort_method_on_field[0-4]=0`: 对应字段降序排列（值大的排在前面）
       - `sort_method_on_field[0-4]=1`: 对应字段升序排列（值小的排在前面）
     - **时间戳比较**: 根据`sort_method_on_timestamp`配置决定排序方向
       - `sort_method_on_timestamp=0`: 降序排列（时间戳大的排在前面）
       - `sort_method_on_timestamp=1`: 升序排列（时间戳小的排在前面）
2. **用户ID**: 最后比较openid（字符串比较）

注意：

- `ext_data`字段不参与排序比较，但仍会在数据一致性校验中检查其内容是否一致
- `last_report_timestamp`字段不参与排序比较，也不参与数据一致性比较，仅用于记录
- 比较逻辑完全遵循TopNext Check模块的`CompareUserScoreInfo`函数实现
- 工具会自动从服务器获取完整的排序配置，包括所有字段的排序方向

### 修复策略

根据比较结果，工具会采用以下修复策略：

- **数据不一致**:
  - 如果server1数据 > server2数据，标记需要更新server2
  - 如果server1数据 < server2数据，自动上报server2的数据到server1
  - 如果核心数据相等但其他字段不匹配，使用server1的数据

- **ext_data校准**:
  - 如果一个服务器的ext_data为空，另一个不为空，使用非空的ext_data进行校准
  - 如果两个服务器的ext_data都不为空但内容不同，使用server1的数据
  - 校准操作会在状态中显示为"[已校准ServerX]"

- **数据缺失**:
  - 如果只有server1有数据，标记需要上报到server2
  - 如果只有server2有数据，自动上报到server1

### 上报服务器配置

- **默认模式**: 使用`--server1`作为上报服务器
- **指定模式**: 通过`--report_server`参数指定专门的上报服务器
- **连接管理**: 工具会自动管理上报服务器的连接，失败时会禁用自动上报功能

### 上报状态标识

在输出结果中，会显示以下状态标识：

- `[已上报]`: 数据已成功上报到服务器
- `[上报失败]`: 数据上报失败
- `[已上报Server1]`: 数据已上报到server1
- `[上报Server1失败]`: 向server1上报失败
- `[已上报Server2]`: 数据已上报到server2
- `[上报Server2失败]`: 向server2上报失败
- `[已校准Server1]`: ext_data已成功校准到server1
- `[已校准Server2]`: ext_data已成功校准到server2
- `[校准失败]`: ext_data校准失败

## 数据校验功能

工具会对以下字段进行完整校验：

1. **基础字段**：
   - `openid`: 用户唯一标识
   - `score`: 用户分数
   - `rank`: 用户排名

2. **时间戳字段**：
   - `timestamp`: 用户数据时间戳（用于最新值模式比较）
   - `last_report_timestamp`: 最后一次上报时间戳（仅用于记录，不参与一致性比较）

3. **扩展排序字段**：
   - `sort_field1` ~ `sort_field5`: 五个扩展排序字段

4. **扩展数据**：
   - `ext_data`: 用户扩展数据（二进制数据）

### 校验模式

- **简洁模式** (默认): 显示基础字段比较结果，包括分数、排名、时间戳和扩展数据长度
- **详细模式** (`--verbose`): 显示所有字段的详细比较信息，包括完整的排序字段、时间戳和扩展数据长度

注意：`last_report_timestamp`字段会在输出中显示，但不参与数据一致性比较。

## 输出说明

### 正常输出示例

```
双服务器TopNext客户端启动...
成功连接到服务器 TopNextServer1 (10.0.0.1:10301)
成功连接到服务器 TopNextServer2 (10.0.0.2:10302)
成功连接到两个服务器

=== 向两个服务器发送GetTop请求 ===
请求参数: business_id=10249, world_id=62, zone_id=1, rank_type=100, rank_instance_id=10151, sub_rank_type=1000, sub_rank_instance_id=10000000, query_from=1, query_count=100

--- 向 TopNextServer1 发送请求 ---
开始分页获取榜单数据，每批次最多 100 条记录
第 1 批次: 从位置 1 开始获取 100 条记录
成功构建GetTopSubRankReq请求包，大小: 132 字节
数据源类型: 实时数据(REAL), 镜像索引: 0
榜单总记录数: 250
本批次获取到 100 条记录，累计 100 条
第 2 批次: 从位置 101 开始获取 100 条记录
本批次获取到 100 条记录，累计 200 条
第 3 批次: 从位置 201 开始获取 50 条记录
本批次获取到 50 条记录，累计 250 条
已获取完所有数据，总计 250 条记录

--- 向 TopNextServer2 发送请求 ---
开始分页获取榜单数据，每批次最多 100 条记录
第 1 批次: 从位置 1 开始获取 100 条记录
榜单总记录数: 250
本批次获取到 100 条记录，累计 100 条
第 2 批次: 从位置 101 开始获取 100 条记录
本批次获取到 100 条记录，累计 200 条
第 3 批次: 从位置 201 开始获取 50 条记录
本批次获取到 50 条记录，累计 250 条
已获取完所有数据，总计 250 条记录

=== 数据获取完成 ===
服务器1总记录数: 250, 实际获取: 250
服务器2总记录数: 250, 实际获取: 250

=== 数据校验和比较 ===
服务器1 (TopNextServer1) 返回 250 条记录
服务器2 (TopNextServer2) 返回 250 条记录

--- 详细比较结果 ---
OpenID              Server1_Score   Server2_Score   Server1_Rank    Server2_Rank    ExtDataLen1/2   状态
---------------------------------------------------------------------------------------------------------
user001             1000            1000            1               1               24/24          匹配
user002             950             950             2               2               18/18          匹配
user003             900             900             3               3               12/8           不匹配(扩展数据)

--- 校验统计 ---
匹配记录: 250
不匹配记录: 0
缺失记录: 0
总记录数: 250
✅ 数据校验通过：两个服务器的数据完全一致
```

### 详细模式输出示例

使用 `--verbose` 参数时，会显示更详细的比较信息：

```
--- 详细比较结果 ---
OpenID              Score1/2                Rank1/2      SortFields1                             SortFields2                             ExtData1/2      状态
-----------------------------------------------------------------------------------------------------------------------------------------------------------------
user001             1000/1000               1/1          [100,200,300,400,500]                  [100,200,300,400,500]                  24/24          匹配
user002             950/950                 2/2          [95,190,285,380,475]                   [95,190,285,380,475]                   18/18          匹配
user003             900/900                 3/3          [90,180,270,360,450]                   [90,180,270,360,451]                   12/12          不匹配(排序字段)
user004             850/850                 4/4          [85,170,255,340,425]                   [85,170,255,340,425]                   0/8            不匹配(扩展数据)
```

### 数据不一致输出示例

```
--- 校验统计 ---
匹配记录: 8
不匹配记录: 2
缺失记录: 1
总记录数: 11
❌ 数据校验失败：两个服务器的数据存在差异
```

不匹配状态说明：

- `不匹配(分数)`: 分数字段不一致
- `不匹配(排名)`: 排名字段不一致  
- `不匹配(时间戳)`: timestamp字段不一致
- `不匹配(排序字段)`: sort_field1~5 中有字段不一致
- `不匹配(扩展数据)`: ext_data 内容不一致
- 可能同时显示多个不匹配类型

## 错误处理

工具会处理以下常见错误情况：

1. **连接失败**: 无法连接到指定的服务器
2. **协议错误**: TDR 协议序列化/反序列化失败
3. **参数错误**: 无效的服务器地址格式或端口号
4. **网络错误**: 发送/接收数据失败

## 性能特性

- **批量处理**: 每次最多获取100条记录，避免单次请求过大
- **自动延迟**: 批次间有10ms延迟，避免对服务器造成压力
- **内存优化**: 逐批累积数据，避免一次性加载大量数据
- **进度显示**: 实时显示获取进度，便于监控大型榜单处理

## 使用场景

### 1. 生产环境数据一致性检查

```bash
./fast_check \
  --server1=prod-server1:10301 \
  --server2=prod-server2:10301 \
  --business_id=10249 \
  --world_id=62 \
  --query_count=50 \
  --verbose
```

### 2. 镜像数据验证

```bash
./fast_check \
  --server1=real-server:10301 \
  --server2=image-server:10301 \
  --use_image_data \
  --image_index=1 \
  --business_id=10249
```

### 3. 随机抽样快速检测

```bash
# 快速健康检查 - 抽样5%的子榜
./fast_check \
  --server1=prod-server1:10301 \
  --server2=prod-server2:10301 \
  --check_all_sub_ranks \
  --random_sample_ratio=0.05 \
  --business_id=10249 \
  --verbose

# 特定类型子榜检测 - 只检测核心业务类型
./fast_check \
  --server1=prod-server1:10301 \
  --server2=prod-server2:10301 \
  --check_all_sub_ranks \
  --sample_sub_rank_types=1001,1002,1005 \
  --random_sample_ratio=0.2 \
  --business_id=10249 \
  --verbose
```

### 4. 批量检测脚本

```bash
#!/bin/bash
# 检测多个业务的数据一致性

SERVERS="--server1=10.0.0.1:10301 --server2=10.0.0.2:10301"

for business_id in 10249 10250 10251; do
  echo "检测业务 $business_id"
  ./fast_check $SERVERS --business_id=$business_id --query_count=20
  echo "---"
done
```

### 5. 分层检测策略

```bash
#!/bin/bash
# 先进行快速抽样检测，发现问题后进行全量检测

SERVERS="--server1=10.0.0.1:10301 --server2=10.0.0.2:10301"
BUSINESS_ID=10249

echo "第一阶段：快速抽样检测（10%）"
if ./fast_check $SERVERS --check_all_sub_ranks --random_sample_ratio=0.1 --business_id=$BUSINESS_ID; then
  echo "✅ 抽样检测通过，数据一致性良好"
else
  echo "⚠️ 抽样检测发现问题，开始全量检测"
  ./fast_check $SERVERS --check_all_sub_ranks --business_id=$BUSINESS_ID --verbose
fi
```

### 6. ext_data校准示例

```bash
# 启用自动修复功能，包括ext_data校准
./fast_check \
  --server1=prod-server1:10301 \
  --server2=prod-server2:10301 \
  --auto_report_inconsistent \
  --business_id=10249 \
  --verbose

# 输出示例：
# 🔄 正在校准ext_data到server2: user001 (使用server1的非空ext_data)
# ✅ 数据上报成功: user001
# 状态: 不匹配(扩展数据)[已校准Server2]
```

## 故障排除

### 1. 编译错误

- 确保 CMake 版本 >= 3.14
- 检查 gflags 库是否正确安装
- 确保 TDR 协议文件路径正确

### 2. 连接失败

- 检查服务器地址和端口是否正确
- 确认网络连通性
- 验证服务器是否正在运行

### 3. 协议错误

- 确认服务器支持 TopNext TDR 协议
- 检查业务参数是否正确
- 使用 `--verbose` 参数查看详细错误信息

## 开发说明

### 代码结构

- `fast_check.cpp`: 主程序文件
- `CMakeLists.txt`: 构建配置
- `../protocol/`: TopNext TDR 协议定义

### 扩展功能

可以通过修改代码添加以下功能：

1. 支持更多的协议命令
2. 添加性能测试功能
3. 支持配置文件
4. 添加结果导出功能
