#pragma once

#include <asio.hpp>
#include <atomic>
#include <cstdarg>
#include <functional>
#include <memory>
#include <string>
#include <vector>

#include "asio_network_client.h"
#include "tloghelp/tlogload.h"
#include "topnext_protocol_handler.h"

// 应用检查器类 - 协调网络客户端和协议处理器
class AppChecker {
 public:
  AppChecker(asio::io_context& io_context, TLOGCATEGORYINST* logger);

  // 执行完整的检查流程：先校验数据一致性，然后发送Report请求
  void run_check(const ServerInfo& server1, const ServerInfo& server2, const std::string& openid, int64_t score,
                 std::function<void(bool)> callback);

 private:
  // 连接到两个服务器
  void connect_both_servers(const ServerInfo& server1, const ServerInfo& server2,
                            std::function<void(bool, int, int)> callback);

  // 发送请求并比较结果（分页版本）
  void send_and_compare_requests_with_pagination(int conn1, int conn2, const std::string& server1_name,
                                                 const std::string& server2_name, std::function<void(bool)> callback);

  // 向单个服务器发送分页请求
  void send_paginated_requests_to_server(int connection_id, const std::string& server_name,
                                         std::function<void(bool, const std::vector<RankData>&)> callback);

  // 向单个服务器发送单次请求
  void send_single_request_to_server(int connection_id, const std::vector<char>& request_buffer,
                                     const std::string& server_name,
                                     std::function<void(bool, const std::vector<RankData>&, uint32_t)> callback);

  // 向单个服务器发送report请求
  void send_report_to_server(int connection_id, const std::string& openid, int64_t score,
                             const std::string& server_name, std::function<void(bool, int32_t)> callback);

  // 处理结果
  void handle_results(const std::vector<RankData>& results1, const std::vector<RankData>& results2, bool both_success,
                      std::function<void(bool)> callback);

  // 使用现有连接发送report请求到两个服务器
  void send_report_requests(int conn1, int conn2, const std::string& server1_name, const std::string& server2_name,
                            const std::string& openid, int64_t score, std::function<void(bool)> callback);

  // 日志辅助函数
  void LogInfo(const char* format, ...);
  void LogError(const char* format, ...);

 private:
  AsioNetworkClient network_client_;
  TopNextProtocolHandler protocol_handler_;
  std::atomic<bool> is_running_;
  TLOGCATEGORYINST* logger_;
};
