#include "topnext_protocol_handler.h"
#include <gflags/gflags.h>

DECLARE_uint64(business_id);
DECLARE_uint64(world_id);
DECLARE_uint64(zone_id);
DECLARE_uint64(rank_type);
DECLARE_uint64(rank_instance_id);
DECLARE_uint64(sub_rank_type);
DECLARE_uint64(sub_rank_instance_id);
DECLARE_bool(check_image);

TopNextProtocolHandler::TopNextProtocolHandler(TLOGCATEGORYINST* logger) : logger_(logger) {}

bool TopNextProtocolHandler::build_get_top_request(std::vector<char>& buffer) {
  // 默认从第1条开始，查询100条记录
  return build_get_top_request(buffer, 1, 100);
}

bool TopNextProtocolHandler::build_get_top_request(std::vector<char>& buffer, uint64_t query_from,
                                                   uint64_t query_count) {
  TopNextMsg msg;

  // 构建消息头
  msg.head.construct();
  msg.head.magic = TOPNEXT_MAGIC;
  msg.head.version = 1;
  msg.head.command = CMD_GET_TOP_SUB_RANK_REQ;
  msg.head.businessID = FLAGS_business_id;
  msg.head.headLen = sizeof(TopNextHead);
  msg.head.bodyLen = 0;  // 稍后设置

  // 构建消息体
  msg.body.construct(CMD_GET_TOP_SUB_RANK_REQ);
  auto& get_top_req = msg.body.get_top_sub_rank_req;

  // 设置client_info
  get_top_req.client_info.construct();
  get_top_req.client_info.from = REQ_CLIENT_FROM_INNER;

  get_top_req.data_source = FLAGS_check_image ? REQ_DATA_SOURCE_IMAGE : REQ_DATA_SOURCE_REAL;

  // 设置rank_info
  get_top_req.rank_info.construct();
  get_top_req.rank_info.business_id = FLAGS_business_id;
  get_top_req.rank_info.world_id = FLAGS_world_id;
  get_top_req.rank_info.zone_id = FLAGS_zone_id;
  get_top_req.rank_info.type = FLAGS_rank_type;
  get_top_req.rank_info.instance_id = FLAGS_rank_instance_id;

  // 设置sub_rank_info
  get_top_req.sub_rank_info.type = FLAGS_sub_rank_type;
  get_top_req.sub_rank_info.instance_id = FLAGS_sub_rank_instance_id;

  // 设置查询参数
  get_top_req.query_from = query_from;
  get_top_req.query_count = query_count;

  // 序列化消息
  buffer.resize(1024);  // 预分配足够的空间
  size_t used_size;
  TdrError::ErrorType ret = msg.pack(buffer.data(), buffer.size(), &used_size);
  if (ret != TdrError::TDR_NO_ERROR) {
    LogError("Failed to serialize GetTopSubRankReq: %d", ret);
    return false;
  }

  buffer.resize(used_size);  // 调整到实际大小

  LogInfo("Successfully built GetTopSubRankReq request packet, size: %zu bytes (query_from=%lu, query_count=%lu)",
          used_size, query_from, query_count);
  return true;
}

bool TopNextProtocolHandler::parse_get_top_response(const std::vector<char>& buffer, std::vector<RankData>& results) {
  uint32_t total_count = 0;
  return parse_get_top_response(buffer, results, total_count);
}

bool TopNextProtocolHandler::parse_get_top_response(const std::vector<char>& buffer, std::vector<RankData>& results,
                                                    uint32_t& total_count) {
  if (buffer.size() < sizeof(TopNextHead)) {
    LogError("GetTop response data too small");
    return false;
  }

  // 尝试解析TopNext消息
  TopNextMsg response_msg;
  TdrError::ErrorType ret = response_msg.unpack(buffer.data(), buffer.size());
  if (ret != TdrError::TDR_NO_ERROR) {
    LogError("Failed to parse GetTop response: %d", ret);
    return false;
  }

  if (response_msg.head.command == CMD_GET_TOP_SUB_RANK_RSP) {
    const auto& rsp = response_msg.body.get_top_sub_rank_rsp;

    // 设置总数量
    total_count = rsp.total_count;

    LogInfo("GetTop response details: return_code=%d, return_message=%s, record_count=%u, total_count=%u",
            rsp.ret_info.ret, rsp.ret_info.msg, rsp.op_result.count, total_count);

    if (rsp.ret_info.ret != 0 && rsp.ret_info.ret != 115003) {
      // 0表示成功，115003表示数据不存在/榜单为空，这两种情况都应该继续处理
      LogError("Server returned error: %d - %s", rsp.ret_info.ret, rsp.ret_info.msg);
      return false;
    }

    // 提取排名数据
    results.clear();
    for (uint32_t i = 0; i < rsp.op_result.count; ++i) {
      const auto& op_result = rsp.op_result.op_result[i];
      if (op_result.result != 0) {
        LogInfo("Skipping failed record %u: %d", i, op_result.result);
        continue;
      }

      const auto& rank_data = op_result.user_rank_info;
      RankData data;

      data.openid = rank_data.user_info.openid;
      data.score = rank_data.user_info.score;
      data.rank = rank_data.rank_no;
      data.timestamp = rank_data.user_info.timestamp;
      data.last_report_timestamp = rank_data.user_info.last_report_timestamp;

      data.sort_field1 = rank_data.user_info.sort_field1;
      data.sort_field2 = rank_data.user_info.sort_field2;
      data.sort_field3 = rank_data.user_info.sort_field3;
      data.sort_field4 = rank_data.user_info.sort_field4;
      data.sort_field5 = rank_data.user_info.sort_field5;

      data.ext_field1 = rank_data.user_info.ext_field1;
      data.ext_field2 = rank_data.user_info.ext_field2;
      data.ext_field3 = rank_data.user_info.ext_field3;

      data.reduce_field1 = rank_data.user_info.reduce_field1;
      data.reduce_field2 = rank_data.user_info.reduce_field2;
      data.reduce_field3 = rank_data.user_info.reduce_field3;

      // 复制扩展数据
      if (rank_data.user_info.ext_data.data_len > 0) {
        data.ext_data.resize(rank_data.user_info.ext_data.data_len);
        memcpy(data.ext_data.data(), rank_data.user_info.ext_data.data_info, rank_data.user_info.ext_data.data_len);
      }

      results.push_back(data);
    }

    return true;
  } else {
    LogError("Unknown GetTop response command: %u", response_msg.head.command);
    return false;
  }
}

bool TopNextProtocolHandler::compare_rank_results(const std::vector<RankData>& results1,
                                                  const std::vector<RankData>& results2) {
  LogInfo("=== Starting data consistency comparison ===");

  if (results1.size() != results2.size()) {
    LogError("Record count inconsistent: Server1=%zu, Server2=%zu", results1.size(), results2.size());
    return false;
  }

  if (results1.empty()) {
    LogInfo("Both servers returned empty results, data is consistent");
    return true;
  }

  bool all_consistent = true;
  size_t inconsistent_count = 0;

  for (size_t i = 0; i < results1.size(); ++i) {
    const auto& data1 = results1[i];
    const auto& data2 = results2[i];

    std::vector<std::string> differences;
    bool record_consistent = compare_single_record(data1, data2, i, differences);

    if (!record_consistent) {
      inconsistent_count++;
      all_consistent = false;

      LogError("Record %zu inconsistent: OpenID=%s", (i + 1), data1.openid.c_str());
      for (const auto& diff : differences) {
        LogError("  %s", diff.c_str());
      }
    }
  }

  // 输出总结
  LogInfo("=== Data consistency check results ===");
  LogInfo("Total records: %zu, Consistent records: %zu, Inconsistent records: %zu", results1.size(),
          (results1.size() - inconsistent_count), inconsistent_count);

  if (all_consistent) {
    LogInfo("All data is completely consistent!");
  } else {
    LogError("Found %zu inconsistent records", inconsistent_count);
  }

  return all_consistent;
}

void TopNextProtocolHandler::display_rank_statistics(const std::vector<RankData>& results,
                                                     const std::string& server_name) {
  LogInfo("=== %s ranking data statistics ===", server_name.c_str());
  LogInfo("%s total records: %zu", server_name.c_str(), results.size());

  if (results.empty()) {
    LogInfo("%s no ranking data", server_name.c_str());
    return;
  }

  // 统计分数范围
  int64_t min_score = results[0].score;
  int64_t max_score = results[0].score;
  int32_t min_rank = results[0].rank;
  int32_t max_rank = results[0].rank;

  for (const auto& data : results) {
    min_score = std::min(min_score, data.score);
    max_score = std::max(max_score, data.score);
    min_rank = std::min(min_rank, data.rank);
    max_rank = std::max(max_rank, data.rank);
  }

  LogInfo("%s score range: %ld - %ld, rank range: %d - %d", server_name.c_str(), min_score, max_score, min_rank,
          max_rank);
  for (size_t i = 0; i < results.size(); ++i) {
    const auto& data = results[i];
    LogInfo("%s rank %zu: openid=%s, score=%ld, rank=%d, timestamp=%lu, last_report_timestamp=%lu, ext_data_len=%zu",
            server_name.c_str(), (i + 1), data.openid.c_str(), data.score, data.rank, data.timestamp,
            data.last_report_timestamp, data.ext_data.size());
  }
}

bool TopNextProtocolHandler::compare_single_record(const RankData& data1, const RankData& data2, size_t index,
                                                   std::vector<std::string>& differences) {
  bool record_consistent = true;
  differences.clear();

  // 比较各个字段
  if (data1.openid != data2.openid) {
    differences.push_back("openid: '" + data1.openid + "' vs '" + data2.openid + "'");
    record_consistent = false;
  }

  if (data1.score != data2.score) {
    differences.push_back("score: " + std::to_string(data1.score) + " vs " + std::to_string(data2.score));
    record_consistent = false;
  }

  if (data1.rank != data2.rank) {
    differences.push_back("rank: " + std::to_string(data1.rank) + " vs " + std::to_string(data2.rank));
    record_consistent = false;
  }

  if (data1.timestamp != data2.timestamp) {
    differences.push_back("timestamp: " + std::to_string(data1.timestamp) + " vs " + std::to_string(data2.timestamp));
    record_consistent = false;
  }

  // 比较扩展字段
  if (data1.sort_field1 != data2.sort_field1 || data1.sort_field2 != data2.sort_field2 ||
      data1.sort_field3 != data2.sort_field3 || data1.sort_field4 != data2.sort_field4 ||
      data1.sort_field5 != data2.sort_field5) {
    differences.push_back("sort_fields differ");
    record_consistent = false;
  }

  if (data1.ext_field1 != data2.ext_field1 || data1.ext_field2 != data2.ext_field2 ||
      data1.ext_field3 != data2.ext_field3) {
    differences.push_back("ext_fields differ");
    record_consistent = false;
  }

  if (data1.reduce_field1 != data2.reduce_field1 || data1.reduce_field2 != data2.reduce_field2 ||
      data1.reduce_field3 != data2.reduce_field3) {
    differences.push_back("reduce_fields differ");
    record_consistent = false;
  }

  if (data1.ext_data != data2.ext_data) {
    differences.push_back("ext_data differ (size: " + std::to_string(data1.ext_data.size()) + " vs " +
                          std::to_string(data2.ext_data.size()) + ")");
    record_consistent = false;
  }

  return record_consistent;
}

// 日志辅助函数实现
void TopNextProtocolHandler::LogInfo(const char* format, ...) {
  if (!logger_) return;

  va_list args;
  va_start(args, format);
  char buffer[1024];
  vsnprintf(buffer, sizeof(buffer), format, args);
  va_end(args);

  tlog_info(logger_, 0, 0, "%s", buffer);
}

bool TopNextProtocolHandler::build_report_request(std::vector<char>& buffer, const std::string& openid, int64_t score) {
  LogInfo("Building Report request: openid=%s, score=%ld", openid.c_str(), score);

  try {
    // 创建Report请求消息
    topnext_proto_tdr::TopNextMsg msg;
    msg.head.construct();
    msg.head.magic = TOPNEXT_MAGIC;
    msg.head.version = 1;
    msg.head.command = CMD_REPORT_SUB_RANK_REQ;
    msg.head.businessID = FLAGS_business_id;
    msg.head.headLen = sizeof(TopNextHead);
    msg.head.bodyLen = 0;  // 稍后设置

    // 设置消息头
    msg.head.magic = topnext_proto_tdr::TOPNEXT_MAGIC;
    msg.head.version = 1;
    msg.head.command = topnext_proto_tdr::CMD_REPORT_SUB_RANK_REQ;
    msg.head.businessID = FLAGS_business_id;

    // 设置Report请求体
    msg.body.report_sub_rank_req.client_info.from = topnext_proto_tdr::REQ_CLIENT_FROM_INNER;
    msg.body.report_sub_rank_req.data_source = topnext_proto_tdr::REQ_DATA_SOURCE_REAL;

    // 设置榜单信息
    msg.body.report_sub_rank_req.rank_info.business_id = FLAGS_business_id;
    msg.body.report_sub_rank_req.rank_info.world_id = FLAGS_world_id;
    msg.body.report_sub_rank_req.rank_info.zone_id = FLAGS_zone_id;
    msg.body.report_sub_rank_req.rank_info.type = FLAGS_rank_type;
    msg.body.report_sub_rank_req.rank_info.instance_id = FLAGS_rank_instance_id;

    // 设置用户信息
    strncpy(msg.body.report_sub_rank_req.user_info.openid, openid.c_str(),
            sizeof(msg.body.report_sub_rank_req.user_info.openid) - 1);
    msg.body.report_sub_rank_req.user_info.score = score;
    msg.body.report_sub_rank_req.user_info.timestamp = time(nullptr);
    msg.body.report_sub_rank_req.user_info.last_report_timestamp = time(nullptr);

    // 设置子榜信息
    msg.body.report_sub_rank_req.sub_rank_info.count = 1;
    msg.body.report_sub_rank_req.sub_rank_info.sub_rank_info[0].type = FLAGS_sub_rank_type;
    msg.body.report_sub_rank_req.sub_rank_info.sub_rank_info[0].instance_id = FLAGS_sub_rank_instance_id;

    // 序列化消息
    buffer.resize(1024);  // 预分配足够的空间
    size_t used_size = 0;
    if (msg.pack(buffer.data(), buffer.size(), &used_size) != tsf4g_tdr::TdrError::TDR_NO_ERROR) {
      LogError("Failed to serialize Report request");
      return false;
    }
    buffer.resize(used_size);

    LogInfo("Report request built successfully, message size: %zu", buffer.size());
    return true;

  } catch (const std::exception& e) {
    LogError("Failed to build Report request: %s", e.what());
    return false;
  }
}

bool TopNextProtocolHandler::parse_report_response(const std::vector<char>& buffer, int32_t& result_code) {
  LogInfo("Parsing Report response, data size: %zu", buffer.size());

  if (buffer.size() < sizeof(TopNextHead)) {
    LogError("Report response data too small");
    return false;
  }

  // 尝试解析TopNext消息
  TopNextMsg response_msg;
  TdrError::ErrorType ret = response_msg.unpack(buffer.data(), buffer.size());
  if (ret != TdrError::TDR_NO_ERROR) {
    LogError("Failed to parse Report response: %d", ret);
    return false;
  }

  LogInfo("Report response command: %u", response_msg.head.command);

  if (response_msg.head.command == CMD_REPORT_SUB_RANK_RSP) {
    const auto& rsp = response_msg.body.report_sub_rank_rsp;
    result_code = rsp.ret_info.ret;

    LogInfo("Report response parsed successfully, result code: %d, message: %s", result_code, rsp.ret_info.msg);

    // Report请求的结果码含义：
    // 0: 成功
    // 其他值: 各种错误码
    if (result_code != 0) {
      LogInfo("Report request returned non-zero result code: %d - %s", result_code, rsp.ret_info.msg);
    }

    return true;
  } else {
    LogError("Unknown Report response command: %u", response_msg.head.command);
    return false;
  }
}

void TopNextProtocolHandler::LogError(const char* format, ...) {
  va_list args;
  va_start(args, format);
  char buffer[1024];
  vsnprintf(buffer, sizeof(buffer), format, args);
  va_end(args);

  fprintf(stderr, "[ERROR] %s\n", buffer);
  if (logger_) {
    tlog_error(logger_, 0, 0, "%s", buffer);
  }
}
