#pragma once

#include <asio.hpp>
#include <cstdarg>
#include <functional>
#include <map>
#include <memory>
#include <vector>

#include <arpa/inet.h>
#include <cstring>

#include "protocol/topnext_protocol_tdr.h"
#include "tloghelp/tlogload.h"

using asio::ip::tcp;
using namespace topnext_proto_tdr;

// 服务器连接信息结构体
struct ServerInfo {
  std::string host;
  int port;
  std::string name;

  ServerInfo(const std::string& h, int p, const std::string& n) : host(h), port(p), name(n) {}
};

// 基于 ASIO 的网络客户端类 - 专门负责网络连接和数据传输
class AsioNetworkClient {
 public:
  AsioNetworkClient(asio::io_context& io_context, TLOGCATEGORYINST* logger);
  ~AsioNetworkClient();

  // 连接到服务器
  void connect_to_server(const ServerInfo& server, std::function<void(bool, int)> callback);

  // 发送数据
  void send_data(int connection_id, const std::vector<char>& data, std::function<void(bool)> callback);

  // 接收数据
  void receive_data(int connection_id, std::function<void(bool, const std::vector<char>&)> callback);

  // 关闭指定连接
  void close_connection(int connection_id);

  // 关闭所有连接
  void close_all_connections();

 private:
  // 日志辅助函数
  void LogInfo(const char* format, ...);
  void LogError(const char* format, ...);

  asio::io_context& io_context_;
  std::map<int, std::shared_ptr<tcp::socket>> connections_;
  int next_connection_id_ = 1;
  TLOGCATEGORYINST* logger_;

  // 获取包长度的辅助函数
  int get_packet_length(const void* data, unsigned int data_len);
};
