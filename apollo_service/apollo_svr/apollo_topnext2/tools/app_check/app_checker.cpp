#include "app_checker.h"
#include <fcntl.h>
#include <gflags/gflags.h>
#include <sys/stat.h>
#include <unistd.h>

DECLARE_uint64(business_id);
DECLARE_uint64(world_id);
DECLARE_uint64(zone_id);
DECLARE_uint64(rank_type);
DECLARE_uint64(rank_instance_id);
DECLARE_uint64(sub_rank_type);
DECLARE_uint64(sub_rank_instance_id);

AppChecker::AppChecker(asio::io_context& io_context, TLOGCATEGORYINST* logger)
    : network_client_(io_context, logger), protocol_handler_(logger), logger_(logger) {}

void AppChecker::run_check(const ServerInfo& server1, const ServerInfo& server2, const std::string& openid,
                           int64_t score, std::function<void(bool)> callback) {
  LogInfo("=== TopNext App Check Tool (Based on ASIO) ===");
  LogInfo("Connecting to server1: %s:%d (%s)", server1.host.c_str(), server1.port, server1.name.c_str());
  LogInfo("Connecting to server2: %s:%d (%s)", server2.host.c_str(), server2.port, server2.name.c_str());
  LogInfo("=== Starting serial processing: data consistency check first, then send Report requests ===");
  LogInfo("Report parameters: openid=%s, score=%ld", openid.c_str(), score);

  // 连接到两个服务器
  connect_both_servers(
      server1, server2, [this, server1, server2, openid, score, callback](bool success, int conn1, int conn2) {
        if (success) {
          LogInfo("Successfully connected to both servers");

          // 第一步：执行数据一致性检查
          send_and_compare_requests_with_pagination(
              conn1, conn2, server1.name, server2.name,
              [this, conn1, conn2, server1, server2, openid, score, callback](bool check_result) {
                LogInfo("=== Data consistency check completed, result: %s ===",
                        check_result ? "consistent" : "inconsistent");

                if (!check_result) {
                  LogError("Data consistency check failed, skipping Report request sending");
                  callback(false);
                  return;
                }

                LogInfo("Data consistency check passed, starting to send Report requests");

                // 第二步：直接使用现有连接发送Report请求
                send_report_requests(
                    conn1, conn2, server1.name, server2.name, openid, score, [this, callback](bool report_result) {
                      LogInfo("=== Report requests completed, result: %s ===", report_result ? "success" : "failed");
                      callback(report_result);
                    });
              });
        } else {
          LogError("Failed to connect to servers");
          callback(false);
        }
      });
}

void AppChecker::connect_both_servers(const ServerInfo& server1, const ServerInfo& server2,
                                      std::function<void(bool, int, int)> callback) {
  LogInfo("Starting to connect to both servers...");

  auto connect_count = std::make_shared<std::atomic<int>>(0);
  auto success_count = std::make_shared<std::atomic<int>>(0);
  auto connection1 = std::make_shared<int>(-1);
  auto connection2 = std::make_shared<int>(-1);

  // 连接服务器1
  network_client_.connect_to_server(server1, [this, connect_count, success_count, connection1, connection2, server1,
                                              callback](bool success, int conn_id) {
    if (success) {
      *connection1 = conn_id;
      success_count->fetch_add(1);
      LogInfo("Successfully connected to server1: %s", server1.name.c_str());
    } else {
      LogError("Failed to connect to server1: %s", server1.name.c_str());
    }

    if (connect_count->fetch_add(1) == 1) {
      // 两个连接都完成了
      bool both_success = (success_count->load() == 2);
      if (both_success) {
        LogInfo("Successfully connected to both servers");
      } else {
        LogError("Failed to connect to servers, successful connections: %d/2", success_count->load());
      }
      callback(both_success, *connection1, *connection2);
    }
  });

  // 连接服务器2
  network_client_.connect_to_server(server2, [this, connect_count, success_count, connection1, connection2, server2,
                                              callback](bool success, int conn_id) {
    if (success) {
      *connection2 = conn_id;
      success_count->fetch_add(1);
      LogInfo("Successfully connected to server2: %s", server2.name.c_str());
    } else {
      LogError("Failed to connect to server2: %s", server2.name.c_str());
    }

    if (connect_count->fetch_add(1) == 1) {
      // 两个连接都完成了
      bool both_success = (success_count->load() == 2);
      if (both_success) {
        LogInfo("Successfully connected to both servers");
      } else {
        LogError("Failed to connect to servers, successful connections: %d/2", success_count->load());
      }
      callback(both_success, *connection1, *connection2);
    }
  });
}

void AppChecker::send_and_compare_requests_with_pagination(int conn1, int conn2, const std::string& server1_name,
                                                           const std::string& server2_name,
                                                           std::function<void(bool)> callback) {
  LogInfo("=== Sending GetTop paginated requests to both servers ===");
  LogInfo(
      "Request parameters: business_id=%lu, world_id=%lu, zone_id=%lu, rank_type=%lu, rank_instance_id=%lu, "
      "sub_rank_type=%lu, "
      "sub_rank_instance_id=%lu",
      FLAGS_business_id, FLAGS_world_id, FLAGS_zone_id, FLAGS_rank_type, FLAGS_rank_instance_id, FLAGS_sub_rank_type,
      FLAGS_sub_rank_instance_id);
  LogInfo("Pagination strategy: query 100 records each time until all data is retrieved");

  LogInfo(
      "Starting paginated requests - business_id=%lu, world_id=%lu, zone_id=%lu, rank_type=%lu, rank_instance_id=%lu, "
      "sub_rank_type=%lu, sub_rank_instance_id=%lu",
      FLAGS_business_id, FLAGS_world_id, FLAGS_zone_id, FLAGS_rank_type, FLAGS_rank_instance_id, FLAGS_sub_rank_type,
      FLAGS_sub_rank_instance_id);

  auto request_count = std::make_shared<std::atomic<int>>(0);
  auto success_count = std::make_shared<std::atomic<int>>(0);
  auto results1 = std::make_shared<std::vector<RankData>>();
  auto results2 = std::make_shared<std::vector<RankData>>();

  // 向服务器1发送分页请求
  LogInfo("--- Sending paginated requests to %s ---", server1_name.c_str());
  send_paginated_requests_to_server(conn1, server1_name,
                                    [this, request_count, success_count, results1, results2, callback](
                                        bool success, const std::vector<RankData>& results) {
                                      if (success) {
                                        *results1 = results;
                                        success_count->fetch_add(1);
                                        LogInfo("Server1 returned total %zu records", results.size());
                                      } else {
                                        LogError("Server1 paginated requests failed");
                                      }

                                      if (request_count->fetch_add(1) == 1) {
                                        // 两个请求都完成了
                                        handle_results(*results1, *results2, success_count->load() == 2, callback);
                                      }
                                    });

  // 向服务器2发送分页请求
  LogInfo("--- Sending paginated requests to %s ---", server2_name.c_str());
  send_paginated_requests_to_server(conn2, server2_name,
                                    [this, request_count, success_count, results1, results2, callback](
                                        bool success, const std::vector<RankData>& results) {
                                      if (success) {
                                        *results2 = results;
                                        success_count->fetch_add(1);
                                        LogInfo("Server2 returned total %zu records", results.size());
                                      } else {
                                        LogError("Server2 paginated requests failed");
                                      }

                                      if (request_count->fetch_add(1) == 1) {
                                        // 两个请求都完成了
                                        handle_results(*results1, *results2, success_count->load() == 2, callback);
                                      }
                                    });
}

// 向单个服务器发送分页请求，获取所有数据
void AppChecker::send_paginated_requests_to_server(int connection_id, const std::string& server_name,
                                                   std::function<void(bool, const std::vector<RankData>&)> callback) {
  auto all_results = std::make_shared<std::vector<RankData>>();
  auto current_from = std::make_shared<uint64_t>(1);  // 从第1条开始
  auto total_count = std::make_shared<uint32_t>(0);   // 总记录数
  const uint64_t batch_size = 100;                    // 每次查询100条

  // 创建一个递归函数来处理分页
  auto fetch_function = std::make_shared<std::function<void()>>();
  *fetch_function = [this, connection_id, server_name, all_results, current_from, total_count, callback,
                     fetch_function]() {
    // 构建当前批次的请求包
    std::vector<char> request_buffer;
    if (!protocol_handler_.build_get_top_request(request_buffer, *current_from, batch_size)) {
      LogError("Failed to build GetTop request packet for %s", server_name.c_str());
      callback(false, {});
      return;
    }

    LogInfo("Requesting records %lu to %lu from %s", *current_from, (*current_from + batch_size - 1),
            server_name.c_str());

    // 发送请求
    send_single_request_to_server(
        connection_id, request_buffer, server_name,
        [this, server_name, all_results, current_from, total_count, callback, fetch_function](
            bool success, const std::vector<RankData>& batch_results, uint32_t batch_total_count) {
          if (!success) {
            LogError("Paginated request failed for %s (from=%lu)", server_name.c_str(), *current_from);
            callback(false, {});
            return;
          }

          // 第一次请求时记录总数
          if (*total_count == 0) {
            *total_count = batch_total_count;
          }

          // 将当前批次的结果添加到总结果中
          all_results->insert(all_results->end(), batch_results.begin(), batch_results.end());

          LogInfo("Retrieved %zu records from %s, accumulated %zu records, total %u", batch_results.size(),
                  server_name.c_str(), all_results->size(), *total_count);

          // 检查是否还有更多数据
          if (batch_results.size() < 100 || all_results->size() >= *total_count) {
            // 没有更多数据了，返回所有结果
            LogInfo("Completed paginated query from %s, total retrieved %zu records", server_name.c_str(),
                    all_results->size());
            callback(true, *all_results);
          } else {
            // 还有更多数据，继续下一批次
            *current_from += batch_results.size();  // 使用实际获取的记录数
            (*fetch_function)();                    // 递归调用
          }
        });
  };

  // 开始第一批次的查询
  (*fetch_function)();
}

// 向单个服务器发送单次请求
void AppChecker::send_single_request_to_server(
    int connection_id, const std::vector<char>& request_buffer, const std::string& server_name,
    std::function<void(bool, const std::vector<RankData>&, uint32_t)> callback) {
  network_client_.send_data(connection_id, request_buffer, [this, connection_id, server_name, callback](bool success) {
    if (!success) {
      LogError("Failed to send request to %s", server_name.c_str());
      callback(false, {}, 0);
      return;
    }

    LogInfo("Successfully sent request to %s", server_name.c_str());

    // 接收响应
    network_client_.receive_data(
        connection_id, [this, server_name, callback](bool success, const std::vector<char>& response_buffer) {
          if (!success) {
            LogError("Failed to receive response from %s", server_name.c_str());
            callback(false, {}, 0);
            return;
          }

          LogInfo("Successfully received response from %s: %zu bytes", server_name.c_str(), response_buffer.size());

          // 解析响应
          std::vector<RankData> results;
          uint32_t total_count = 0;
          bool parse_success = protocol_handler_.parse_get_top_response(response_buffer, results, total_count);
          if (parse_success) {
            protocol_handler_.display_rank_statistics(results, server_name);
          }
          callback(parse_success, results, total_count);
        });
  });
}

void AppChecker::handle_results(const std::vector<RankData>& results1, const std::vector<RankData>& results2,
                                bool both_success, std::function<void(bool)> callback) {
  if (!both_success) {
    LogError("Request failed, cannot compare data");
    callback(false);
    return;
  }

  LogInfo("=== Data retrieval completed ===");
  LogInfo("Server1 returned records: %zu", results1.size());
  LogInfo("Server2 returned records: %zu", results2.size());

  LogInfo("Data retrieval completed - Server1: %zu records, Server2: %zu records", results1.size(), results2.size());

  // 比较两个服务器的结果
  bool data_consistent = protocol_handler_.compare_rank_results(results1, results2);

  if (data_consistent) {
    LogInfo("Data comparison completed - Results are CONSISTENT");
  } else {
    LogError("Data comparison completed - Results are INCONSISTENT");
  }

  callback(data_consistent);
}

// 日志辅助函数实现
void AppChecker::LogInfo(const char* format, ...) {
  if (!logger_) return;

  va_list args;
  va_start(args, format);
  char buffer[1024];
  vsnprintf(buffer, sizeof(buffer), format, args);
  va_end(args);

  tlog_info(logger_, 0, 0, "%s", buffer);
}

void AppChecker::send_report_requests(int conn1, int conn2, const std::string& server1_name,
                                      const std::string& server2_name, const std::string& openid, int64_t score,
                                      std::function<void(bool)> callback) {
  LogInfo("=== Sending Report requests to both servers (using existing connections) ===");
  LogInfo("Report parameters: openid=%s, score=%ld", openid.c_str(), score);

  auto success_count = std::make_shared<std::atomic<int>>(0);
  auto result1 = std::make_shared<int32_t>(0);
  auto result2 = std::make_shared<int32_t>(0);

  // 向服务器1发送report请求
  LogInfo("--- Sending Report request to %s ---", server1_name.c_str());
  send_report_to_server(
      conn1, openid, score, server1_name,
      [this, success_count, result1, result2, server1_name, callback](bool success, int32_t result_code) {
        if (success) {
          success_count->fetch_add(1);
          *result1 = result_code;
          LogInfo("Server1 Report request succeeded, result code: %d", result_code);
        } else {
          LogError("Server1 Report request failed");
        }

        // 检查是否两个请求都完成
        if (success_count->load() == 2) {
          LogInfo("=== Report requests completed ===");
          LogInfo("Server1 result code: %d", *result1);
          LogInfo("Server2 result code: %d", *result2);
          callback(true);
        }
      });

  // 向服务器2发送report请求
  LogInfo("--- Sending Report request to %s ---", server2_name.c_str());
  send_report_to_server(
      conn2, openid, score, server2_name,
      [this, success_count, result1, result2, server2_name, callback](bool success, int32_t result_code) {
        if (success) {
          success_count->fetch_add(1);
          *result2 = result_code;
          LogInfo("Server2 Report request succeeded, result code: %d", result_code);
        } else {
          LogError("Server2 Report request failed");
        }

        // 检查是否两个请求都完成
        if (success_count->load() == 2) {
          LogInfo("=== Report requests completed ===");
          LogInfo("Server1 result code: %d", *result1);
          LogInfo("Server2 result code: %d", *result2);
          callback(true);
        }
      });
}

void AppChecker::send_report_to_server(int connection_id, const std::string& openid, int64_t score,
                                       const std::string& server_name, std::function<void(bool, int32_t)> callback) {
  // 构建report请求包
  std::vector<char> request_buffer;
  if (!protocol_handler_.build_report_request(request_buffer, openid, score)) {
    LogError("Failed to build Report request packet for %s", server_name.c_str());
    callback(false, -1);
    return;
  }

  LogInfo("Sending Report request to %s: openid=%s, score=%ld", server_name.c_str(), openid.c_str(), score);

  // 发送请求
  network_client_.send_data(connection_id, request_buffer, [this, connection_id, server_name, callback](bool success) {
    if (!success) {
      LogError("Failed to send Report request to %s", server_name.c_str());
      callback(false, -1);
      return;
    }

    LogInfo("Successfully sent Report request to %s", server_name.c_str());

    // 接收响应
    network_client_.receive_data(connection_id, [this, server_name, callback](
                                                    bool success, const std::vector<char>& response_buffer) {
      if (!success) {
        LogError("Failed to receive Report response from %s", server_name.c_str());
        callback(false, -1);
        return;
      }

      LogInfo("Successfully received Report response from %s: %zu bytes", server_name.c_str(), response_buffer.size());

      // 解析响应
      int32_t result_code = 0;
      if (!protocol_handler_.parse_report_response(response_buffer, result_code)) {
        LogError("Failed to parse Report response from %s", server_name.c_str());
        callback(false, -1);
        return;
      }

      LogInfo("Report response parsed successfully from %s, result code: %d", server_name.c_str(), result_code);
      callback(true, result_code);
    });
  });
}

void AppChecker::LogError(const char* format, ...) {
  va_list args;
  va_start(args, format);
  char buffer[1024];
  vsnprintf(buffer, sizeof(buffer), format, args);
  va_end(args);

  fprintf(stderr, "[ERROR] %s\n", buffer);
  if (logger_) {
    tlog_error(logger_, 0, 0, "%s", buffer);
  }
}
