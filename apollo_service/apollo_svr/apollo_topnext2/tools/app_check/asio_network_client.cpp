#include "asio_network_client.h"
#include <gflags/gflags.h>

AsioNetworkClient::AsioNetworkClient(asio::io_context& io_context, TLOGCATEGORYINST* logger)
    : io_context_(io_context), logger_(logger) {}

AsioNetworkClient::~AsioNetworkClient() { close_all_connections(); }

void AsioNetworkClient::connect_to_server(const ServerInfo& server, std::function<void(bool, int)> callback) {
  auto socket = std::make_shared<tcp::socket>(io_context_);
  auto resolver = std::make_shared<tcp::resolver>(io_context_);

  resolver->async_resolve(
      server.host, std::to_string(server.port),
      [this, server, socket, resolver, callback](const std::error_code& error, tcp::resolver::results_type results) {
        if (error) {
          LogError("Failed to resolve hostname %s: %s", server.name.c_str(), error.message().c_str());
          callback(false, -1);
          return;
        }

        asio::async_connect(
            *socket, results, [this, server, socket, callback](const std::error_code& error, const tcp::endpoint&) {
              if (error) {
                LogError("Failed to connect to server %s: %s", server.name.c_str(), error.message().c_str());
                callback(false, -1);
              } else {
                int connection_id = next_connection_id_++;
                connections_[connection_id] = socket;

                LogInfo("Successfully connected to server %s (%s:%d), connection ID: %d", server.name.c_str(),
                        server.host.c_str(), server.port, connection_id);

                callback(true, connection_id);
              }
            });
      });
}

void AsioNetworkClient::send_data(int connection_id, const std::vector<char>& data,
                                  std::function<void(bool)> callback) {
  auto it = connections_.find(connection_id);
  if (it == connections_.end()) {
    LogError("Connection ID %d does not exist", connection_id);
    callback(false);
    return;
  }

  auto socket = it->second;
  auto buffer = std::make_shared<std::vector<char>>(data);

  asio::async_write(
      *socket, asio::buffer(*buffer),
      [this, connection_id, callback, buffer](const std::error_code& error, std::size_t bytes_transferred) {
        if (error) {
          LogError("Failed to send data, connection ID %d: %s", connection_id, error.message().c_str());
          callback(false);
          return;
        }

        LogInfo("Successfully sent %zu bytes to connection ID %d", bytes_transferred, connection_id);

        callback(true);
      });
}

void AsioNetworkClient::receive_data(int connection_id, std::function<void(bool, const std::vector<char>&)> callback) {
  auto it = connections_.find(connection_id);
  if (it == connections_.end()) {
    LogError("Connection ID %d does not exist", connection_id);
    callback(false, {});
    return;
  }

  auto socket = it->second;
  auto header_buffer = std::make_shared<std::vector<char>>(sizeof(TopNextHead));

  // 先接收头部
  asio::async_read(
      *socket, asio::buffer(*header_buffer),
      [this, connection_id, callback, socket, header_buffer](const std::error_code& error,
                                                             std::size_t bytes_transferred) {
        if (error) {
          LogError("Failed to receive response header, connection ID %d: %s", connection_id, error.message().c_str());
          callback(false, {});
          return;
        }

        // 解析包长度
        int packet_length = get_packet_length(header_buffer->data(), header_buffer->size());
        if (packet_length <= 0) {
          LogError("Unable to parse packet length, connection ID %d", connection_id);
          callback(false, {});
          return;
        }

        auto response_buffer = std::make_shared<std::vector<char>>(packet_length);
        std::copy(header_buffer->begin(), header_buffer->end(), response_buffer->begin());

        // 接收剩余的消息体
        size_t remaining = packet_length - header_buffer->size();
        if (remaining > 0) {
          asio::async_read(*socket, asio::buffer(response_buffer->data() + header_buffer->size(), remaining),
                           [this, connection_id, callback, response_buffer](const std::error_code& error,
                                                                            std::size_t bytes_transferred) {
                             if (error) {
                               LogError("Failed to receive response body, connection ID %d: %s", connection_id,
                                        error.message().c_str());
                               callback(false, {});
                               return;
                             }

                             LogInfo("Successfully received complete message, connection ID %d: %zu bytes",
                                     connection_id, response_buffer->size());

                             callback(true, *response_buffer);
                           });
        } else {
          LogInfo("Successfully received complete message, connection ID %d: %zu bytes", connection_id,
                  response_buffer->size());

          callback(true, *response_buffer);
        }
      });
}

void AsioNetworkClient::close_connection(int connection_id) {
  auto it = connections_.find(connection_id);
  if (it != connections_.end()) {
    if (it->second->is_open()) {
      it->second->close();
    }
    connections_.erase(it);
  }
}

void AsioNetworkClient::close_all_connections() {
  for (auto& pair : connections_) {
    if (pair.second->is_open()) {
      pair.second->close();
    }
  }
  connections_.clear();
}

int AsioNetworkClient::get_packet_length(const void* data, unsigned int data_len) {
  if (nullptr == data || 0 == data_len) {
    return -1;
  }

  // 检查是否有足够的数据来读取头部长度和体长度字段
  size_t body_offset = offsetof(TopNextHead, bodyLen) + sizeof(uint32_t);
  if (data_len >= body_offset) {
    // 安全地读取头部长度和体长度，避免未对齐访问
    uint32_t head_len, body_len;
    memcpy(&head_len, (const unsigned char*)data + offsetof(TopNextHead, headLen), sizeof(uint32_t));
    memcpy(&body_len, (const unsigned char*)data + offsetof(TopNextHead, bodyLen), sizeof(uint32_t));

    head_len = ntohl(head_len);
    body_len = ntohl(body_len);

    // 添加合理性检查，防止恶意数据
    const uint32_t MAX_REASONABLE_SIZE = 10 * 1024 * 1024;  // 10MB
    if (head_len > MAX_REASONABLE_SIZE || body_len > MAX_REASONABLE_SIZE) {
      LogError("Warning: abnormal packet length head_len=%u, body_len=%u", head_len, body_len);
      return -1;
    }

    LogInfo("Parsed packet length: head_len=%u, body_len=%u, total=%u", head_len, body_len, (head_len + body_len));

    return static_cast<int>(head_len + body_len);
  } else {
    return 0;  // 需要更多数据
  }
}

// 日志辅助函数实现
void AsioNetworkClient::LogInfo(const char* format, ...) {
  if (!logger_) return;

  va_list args;
  va_start(args, format);
  char buffer[1024];
  vsnprintf(buffer, sizeof(buffer), format, args);
  va_end(args);

  tlog_info(logger_, 0, 0, "%s", buffer);
}

void AsioNetworkClient::LogError(const char* format, ...) {
  va_list args;
  va_start(args, format);
  char buffer[1024];
  vsnprintf(buffer, sizeof(buffer), format, args);
  va_end(args);

  fprintf(stderr, "[ERROR] %s\n", buffer);
  if (logger_) {
    tlog_error(logger_, 0, 0, "%s", buffer);
  }
}
