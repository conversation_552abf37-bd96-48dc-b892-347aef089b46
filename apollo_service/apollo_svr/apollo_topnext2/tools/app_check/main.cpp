#include <fcntl.h>
#include <sys/stat.h>
#include <unistd.h>
#include <string>
#include <utility>

#include <gflags/gflags.h>

#include "app_checker.h"
#include "tloghelp/tlogload.h"

// 定义命令行参数
DEFINE_string(server1, "127.0.0.1:10301", "First server address (format: IP:port)");
DEFINE_string(server1_name, "TopNextServer1", "First server name");

DEFINE_string(server2, "127.0.0.1:10302", "Second server address (format: IP:port)");
DEFINE_string(server2_name, "TopNextServer2", "Second server name");

// 排行榜相关参数
// FIXME: 正式发布的时候 business_id 固定 10000，避免误操作
DEFINE_uint64(business_id, 10000, "Business ID");
DEFINE_uint64(world_id, 1, "World ID");
DEFINE_uint64(zone_id, 1, "Zone ID");
DEFINE_uint64(rank_type, 1, "Rank type");
DEFINE_uint64(rank_instance_id, 1, "Rank instance ID");
DEFINE_uint64(sub_rank_type, 1, "Sub rank type");
DEFINE_uint64(sub_rank_instance_id, 1, "Sub rank instance ID");
DEFINE_bool(check_image, false, "Whether to check image data (false=check real-time data, true=check image data)");

// Report相关参数
DEFINE_string(openid, "test_user_001", "User OpenID");
DEFINE_int64(score, 1000, "User score");

// 日志相关常量
namespace {
constexpr int kLogFileSizeLimit = 10485760;  // 10MB
constexpr int kLogMaxRotate = 2;
constexpr const char* kLogFileName = "./log/app_check_tool.log";
constexpr const char* kLogConfigFile = "./app_check_log.xml";
}  // namespace

// 生成日志配置内容
std::string GenerateLogConfigContent() {
  char config[4096];
  snprintf(config, sizeof(config),
           "<?xml version=\"1.0\" encoding=\"GBK\" standalone=\"yes\" ?>\n"
           "<TLOGConf version=\"2\">\n"
           "  <Magic>1548</Magic>\n"
           "  <PriorityHigh>NULL</PriorityHigh>\n"
           "  <PriorityLow>DEBUG</PriorityLow>\n"
           "  <SuppressError>1</SuppressError>\n"
           "  <Count>3</Count>\n"
           "  <CategoryList type=\"TLOGCategory\">\n"
           "    <Name>text</Name>\n"
           "    <PriorityHigh>INFO</PriorityHigh>\n"
           "    <PriorityLow>TRACE</PriorityLow>\n"
           "    <Device type=\"TLOGDevAny\">\n"
           "      <Type>FILE</Type>\n"
           "      <Device type=\"TLOGDevSelector\">\n"
           "        <File type=\"TLOGDevFile\">\n"
           "          <Pattern>%s</Pattern>\n"
           "          <SizeLimit>%d</SizeLimit>\n"
           "          <MaxRotate>%d</MaxRotate>\n"
           "        </File>\n"
           "      </Device>\n"
           "    </Device>\n"
           "  </CategoryList>\n"
           "</TLOGConf>\n",
           kLogFileName, kLogFileSizeLimit, kLogMaxRotate);

  return std::string(config);
}

// 生成日志配置文件
int GenerateLogConfig() {
  // 检查配置文件是否已存在
  if (access(kLogConfigFile, F_OK) == 0) {
    return 0;
  }

  // 创建日志目录
  if (access("./log", F_OK) != 0) {
    if (mkdir("./log", 0755) != 0) {
      fprintf(stderr, "Failed to create log directory\n");
      return -1;
    }
  }

  // 生成日志配置
  std::string log_config = GenerateLogConfigContent();

  // 写入配置文件
  int fd = open(kLogConfigFile, O_CREAT | O_RDWR | O_TRUNC, 0666);
  if (fd < 0) {
    fprintf(stderr, "Failed to create log config file: %s\n", kLogConfigFile);
    return -1;
  }

  ssize_t written = write(fd, log_config.c_str(), log_config.length());
  close(fd);

  if (written != static_cast<ssize_t>(log_config.length())) {
    fprintf(stderr, "Failed to write log config\n");
    return -1;
  }

  return 0;
}

// 初始化日志系统
TLOGCATEGORYINST* InitializeLogger() {
  // 生成日志配置
  if (GenerateLogConfig() != 0) {
    return nullptr;
  }

  // 初始化日志上下文
  LPTLOGCTX log_ctx = tlog_init_from_file(kLogConfigFile);
  if (!log_ctx) {
    fprintf(stderr, "Failed to initialize log from file: %s\n", kLogConfigFile);
    return nullptr;
  }

  // 获取日志类别
  TLOGCATEGORYINST* logger = tlog_get_category(log_ctx, "text");
  if (!logger) {
    fprintf(stderr, "Failed to get log category\n");
    return nullptr;
  }

  return logger;
}

// 解析IP:port格式的服务器地址
std::pair<std::string, int> parse_server_address(const std::string& address) {
  size_t colon_pos = address.find(':');
  if (colon_pos == std::string::npos) {
    fprintf(stderr, "Invalid server address format: %s (should be IP:port)\n", address.c_str());
    return std::make_pair("127.0.0.1", 10301);
  }

  std::string host = address.substr(0, colon_pos);
  std::string port_str = address.substr(colon_pos + 1);

  int port = 10301;
  try {
    port = std::stoi(port_str);
    if (port <= 0 || port > 65535) {
      fprintf(stderr, "Invalid port number: %d (should be between 1-65535)\n", port);
      return std::make_pair(host, 10301);
    }
  } catch (const std::exception& e) {
    fprintf(stderr, "Invalid port number format: %s - %s\n", port_str.c_str(), e.what());
    return std::make_pair(host, 10301);
  }

  return std::make_pair(host, port);
}

int main(int argc, char* argv[]) {
  // Parse command line arguments
  google::ParseCommandLineFlags(&argc, &argv, true);

  // Initialize logging system
  TLOGCATEGORYINST* logger = InitializeLogger();
  if (!logger) {
    fprintf(stderr, "Failed to initialize logger\n");
    return -1;
  }

  // 解析服务器地址
  auto server1_addr = parse_server_address(FLAGS_server1);
  auto server2_addr = parse_server_address(FLAGS_server2);

  ServerInfo server1(server1_addr.first, server1_addr.second, FLAGS_server1_name);
  ServerInfo server2(server2_addr.first, server2_addr.second, FLAGS_server2_name);

  try {
    asio::io_context io_context;
    AppChecker checker(io_context, logger);

    bool final_result = false;
    bool operation_completed = false;

    // 执行完整的检查流程：先校验数据一致性，然后发送Report请求
    checker.run_check(server1, server2, FLAGS_openid, FLAGS_score, [&](bool success) {
      final_result = success;
      operation_completed = true;
      io_context.stop();
    });

    // 运行事件循环
    io_context.run();

    if (operation_completed) {
      if (final_result) {
        printf("\nComplete process succeeded: data validation passed and Report requests succeeded\n");
        return 0;
      } else {
        printf("\nProcess failed: data validation failed or Report requests failed\n");
        return 1;
      }
    } else {
      fprintf(stderr, "\nOperation not completed\n");
      return 1;
    }

  } catch (const std::exception& e) {
    fprintf(stderr, "Exception: %s\n", e.what());
    return 1;
  }

  return 0;
}
