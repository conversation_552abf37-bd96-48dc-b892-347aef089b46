# TopNext App Check Tool (基于 ASIO)

## 概述

`app_check` 是一个基于 ASIO 异步网络库实现的 TopNext 服务器功能检查工具。它可以同时连接两个 TopNext 服务器，发送 GetTop 请求，并对返回的排行榜数据进行一致性检查。

## 功能特性

- ✅ **异步网络通信**: 基于 ASIO 库实现高效的异步网络通信
- ✅ **双服务器并行检测**: 同时连接两个 TopNext 服务器
- ✅ **完整的 TDR 协议支持**: 使用真正的 TopNext TDR 协议进行通信
- ✅ **灵活的参数配置**: 支持通过命令行参数配置所有请求参数
- ✅ **数据源支持**: 支持实时数据和镜像数据两种数据源
- ✅ **详细的数据比较**: 提供逐条记录的数据对比和统计信息
- ✅ **详细日志输出**: 可选的详细输出模式用于调试
- ✅ **Report请求支持**: 支持向服务器发送Report请求
- ✅ **串行处理**: 自动先校验数据一致性，校验通过后发送Report请求

## 编译

```bash
# 在项目根目录下
mkdir build && cd build
cmake ..
make app_check
```

## 使用方法

### 基本用法

```bash
./app_check \
  --server1="127.0.0.1:10301" \
  --server1_name="TopNextServer1" \
  --server2="127.0.0.1:10302" \
  --server2_name="TopNextServer2" \
  --business_id=10000 \
  --world_id=1 \
  --zone_id=1 \
  --rank_type=1 \
  --rank_instance_id=1 \
  --sub_rank_type=1 \
  --sub_rank_instance_id=1 \
  --openid="test_user_001" \
  --score=1000
```

工具会自动执行以下流程：
1. 连接到两个服务器
2. 发送GetTop请求进行数据一致性校验
3. 如果校验通过，直接使用现有连接发送Report请求到两个服务器
4. 输出最终结果

**优化特性**：
- 复用连接：数据校验和Report请求使用同一个TCP连接，避免重复连接开销
- 串行处理：确保数据一致性后才发送Report请求，保证数据可靠性

### 命令行参数

| 参数                     | 默认值            | 说明                             |
| ------------------------ | ----------------- | -------------------------------- |
| `--server1`              | "127.0.0.1:10301" | 第一个服务器地址 (格式: IP:port) |
| `--server1_name`         | "TopNextServer1"  | 第一个服务器的名称               |
| `--server2`              | "127.0.0.1:10302" | 第二个服务器地址 (格式: IP:port) |
| `--server2_name`         | "TopNextServer2"  | 第二个服务器的名称               |
| `--business_id`          | 10000             | 业务ID                           |
| `--world_id`             | 1                 | 世界ID                           |
| `--zone_id`              | 1                 | 区域ID                           |
| `--rank_type`            | 1                 | 排行榜类型                       |
| `--rank_instance_id`     | 1                 | 排行榜实例ID                     |
| `--sub_rank_type`        | 1                 | 子排行榜类型                     |
| `--sub_rank_instance_id` | 1                 | 子排行榜实例ID                   |
| `--check_image`          | false             | 是否检查镜像数据                 |
| `--openid`               | "test_user_001"   | 用户OpenID                       |
| `--score`                | 1000              | 用户分数                         |

### 使用示例

#### 完整流程（数据校验 + Report请求）
```bash
./app_check \
  --server1="192.168.1.100:10301" \
  --server2="192.168.1.101:10301" \
  --business_id=10000 \
  --rank_type=1 \
  --sub_rank_type=1001 \
  --openid="test_user_001" \
  --score=1000
```

#### 检查镜像数据
```bash
./app_check \
  --server1="192.168.1.100:10301" \
  --server2="192.168.1.101:10301" \
  --check_image=true \
  --openid="test_user_001" \
  --score=1000
```

## 输出说明

### 成功输出示例
```
=== TopNext App Check Tool (基于 ASIO) ===
连接服务器1: 127.0.0.1:10301 (TopNextServer1)
连接服务器2: 127.0.0.1:10302 (TopNextServer2)
开始连接到两个服务器...
成功连接到服务器1: TopNextServer1
成功连接到服务器2: TopNextServer2
成功连接到两个服务器

=== 向两个服务器发送GetTop请求 ===
请求参数: business_id=10000, world_id=1, zone_id=1, rank_type=1, rank_instance_id=1, sub_rank_type=1, sub_rank_instance_id=1, query_from=1, query_count=100

--- 向 TopNextServer1 发送请求 ---
--- 向 TopNextServer2 发送请求 ---
服务器1返回 10 条记录
服务器2返回 10 条记录

=== 数据获取完成 ===
服务器1返回记录数: 10
服务器2返回记录数: 10

=== 开始数据一致性比较 ===

=== 数据一致性检查结果 ===
总记录数: 10
一致记录数: 10
不一致记录数: 0
🎉 所有数据完全一致！

✅ 检查完成：两个服务器的数据一致
```

### 错误输出示例
```
❌ 记录 3 不一致:
  OpenID: user123
  score: 1000 vs 1001
  rank: 3 vs 2

⚠️  发现 1 条不一致的记录

❌ 检查完成：发现数据不一致
```

## 返回值

- `0`: 检查成功，数据一致
- `1`: 检查失败或发现数据不一致

## 技术实现

- **网络库**: ASIO (异步网络通信)
- **协议**: TopNext TDR 协议
- **编程语言**: C++11
- **构建系统**: CMake
- **命令行解析**: gflags

## 代码架构

项目采用模块化设计，将功能拆分为独立的组件：

### 核心组件

1. **AsioNetworkClient** (`asio_network_client.h/cpp`)
   - 负责异步网络连接和数据传输
   - 管理多个 TCP 连接
   - 提供统一的网络接口

2. **TopNextProtocolHandler** (`topnext_protocol_handler.h/cpp`)
   - 负责 TopNext 协议的构建和解析
   - 处理 GetTop 请求和响应
   - 提供数据比较和统计功能

3. **AppChecker** (`app_checker.h/cpp`)
   - 协调网络客户端和协议处理器
   - 实现完整的检查流程
   - 管理异步操作的生命周期

4. **Main** (`main.cpp`)
   - 程序入口点
   - 命令行参数解析
   - 工具函数

### 设计优势

- ✅ **职责分离**: 网络层和协议层完全分离
- ✅ **易于扩展**: 可以轻松添加新的协议类型
- ✅ **可重用性**: 各组件可以独立使用
- ✅ **可测试性**: 每个组件都可以单独测试
- ✅ **可维护性**: 代码结构清晰，易于理解和修改

## 注意事项

1. 确保两个服务器都可以正常访问
2. 确保业务ID、榜单类型等参数配置正确
3. 使用 `--verbose` 参数可以获得更详细的调试信息
4. 工具会自动处理网络连接和协议解析
5. 支持 IPv4 地址和域名解析

## 故障排除

### 连接失败
- 检查服务器地址和端口是否正确
- 确认服务器是否正在运行
- 检查网络连通性

### 协议错误
- 确认服务器支持的协议版本
- 检查业务ID等参数是否正确
- 查看详细日志输出（使用 --verbose）

### 数据不一致
- 检查两个服务器的数据同步状态
- 确认查询参数是否一致
- 检查服务器配置是否相同
