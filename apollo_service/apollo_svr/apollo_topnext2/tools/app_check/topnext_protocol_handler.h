#pragma once

#include <cstdarg>
#include <cstring>
#include <string>
#include <vector>

#include "protocol/apollo_service_base_header_topnext_proto.h"
#include "protocol/proxy_protocol.h"
#include "protocol/topnext_protocol_tdr.h"
#include "tloghelp/tlogload.h"

using namespace topnext_proto_tdr;

// 排名数据结构
struct RankData {
  std::string openid;
  int64_t score;
  int32_t rank;
  uint64_t timestamp;
  uint64_t last_report_timestamp;

  // 扩展排序字段
  uint32_t sort_field1;
  uint32_t sort_field2;
  uint32_t sort_field3;
  uint32_t sort_field4;
  uint32_t sort_field5;

  uint64_t ext_field1;
  uint64_t ext_field2;
  uint64_t ext_field3;

  uint32_t reduce_field1;
  uint32_t reduce_field2;
  uint32_t reduce_field3;

  std::vector<uint8_t> ext_data;

  RankData()
      : score(0),
        rank(0),
        timestamp(0),
        last_report_timestamp(0),
        sort_field1(0),
        sort_field2(0),
        sort_field3(0),
        sort_field4(0),
        sort_field5(0),
        ext_field1(0),
        ext_field2(0),
        ext_field3(0),
        reduce_field1(0),
        reduce_field2(0),
        reduce_field3(0) {}
};

// TopNext 协议处理类 - 专门负责协议构建、解析和业务逻辑
class TopNextProtocolHandler {
 public:
  TopNextProtocolHandler(TLOGCATEGORYINST* logger);
  TopNextProtocolHandler() = default;

  // 构建GetTop请求包
  bool build_get_top_request(std::vector<char>& buffer);

  // 构建GetTop请求包（指定查询参数）
  bool build_get_top_request(std::vector<char>& buffer, uint64_t query_from, uint64_t query_count);

  // 构建Report请求包
  bool build_report_request(std::vector<char>& buffer, const std::string& openid, int64_t score);

  // 解析GetTop响应
  bool parse_get_top_response(const std::vector<char>& buffer, std::vector<RankData>& results);

  // 解析Report响应
  bool parse_report_response(const std::vector<char>& buffer, int32_t& result_code);

  // 解析GetTop响应（带总数信息）
  bool parse_get_top_response(const std::vector<char>& buffer, std::vector<RankData>& results, uint32_t& total_count);

  // 比较排名结果
  bool compare_rank_results(const std::vector<RankData>& results1, const std::vector<RankData>& results2);

  // 显示排名数据统计信息
  void display_rank_statistics(const std::vector<RankData>& results, const std::string& server_name);

 private:
  // 比较单条记录
  bool compare_single_record(const RankData& data1, const RankData& data2, size_t index,
                             std::vector<std::string>& differences);

  // 日志辅助函数
  void LogInfo(const char* format, ...);
  void LogError(const char* format, ...);

  TLOGCATEGORYINST* logger_;
};
