# TopNext Dump 单元测试说明文档

本文档详细说明了 TopNext Dump 项目中两个主要测试文件的所有测试用例。

## 文件概述

- `test_flowcontrol_direct_file_writer.cpp` - FlowControlDirectFileWriter 类的单元测试
- `test_topnext_dump_app.cpp` - TopnextDumpApp 类的单元测试

## test_flowcontrol_direct_file_writer.cpp 测试用例

### 基础功能测试

| 测试用例名称       | 测试目的               | 测试内容                       | 期望结果                   |
| ------------------ | ---------------------- | ------------------------------ | -------------------------- |
| `SimpleWrite`      | 测试正常的文件写入流程 | 创建大量内容，通过流控写入文件 | 文件内容与原始内容完全一致 |
| `InitWithZeroIops` | 测试无效IOPS参数       | 使用0作为IOPS参数调用Init      | 返回-1表示初始化失败       |
| `Reload`           | 测试配置重载功能       | 测试正常重载和无效参数重载     | 正常参数成功，无效参数失败 |

### 异常场景测试

| 测试用例名称           | 测试目的                   | 测试内容                      | 期望结果                   |
| ---------------------- | -------------------------- | ----------------------------- | -------------------------- |
| `BeginWithInvalidPath` | 测试无效文件路径处理       | 尝试在不存在的目录中创建文件  | Begin方法返回-1            |
| `WritePermissionTest`  | 测试文件权限问题           | 尝试写入只读目录              | Begin方法返回-1            |
| `UpdateWithoutBegin`   | 测试未初始化状态调用Update | 直接调用Update而不先调用Begin | 不应崩溃，可能返回错误状态 |
| `BeginWithoutInit`     | 测试未Init状态调用Begin    | 在没有Init的情况下调用Begin   | 测试边界行为               |

### 内容大小边界测试

| 测试用例名称             | 测试目的       | 测试内容                        | 期望结果                    |
| ------------------------ | -------------- | ------------------------------- | --------------------------- |
| `BeginWithEmptyContent`  | 测试空内容处理 | 写入空字符串                    | 成功完成写入                |
| `BeginWithSmallContent`  | 测试小内容写入 | 写入小于8K对齐的内容            | 内容正确写入                |
| `ExactAlignmentBoundary` | 测试8K边界对齐 | 写入精确8K大小的内容            | 内容正确写入                |
| `LargeFileWrite`         | 测试大文件写入 | 写入约100KB内容，触发多次Update | 多次SYNC_DATA状态，最终完成 |
| `TurnBufferIoPath`       | 测试缓冲IO转换 | 写入9K内容触发TurnBufferIo调用  | 成功写入完整内容            |

### 状态和流控测试

| 测试用例名称            | 测试目的         | 测试内容               | 期望结果               |
| ----------------------- | ---------------- | ---------------------- | ---------------------- |
| `UpdateIdleStatus`      | 测试IDLE状态触发 | 连续快速调用Update     | 第二次调用返回IDLE状态 |
| `DifferentIopsSettings` | 测试不同IOPS设置 | 使用10MB/s的高IOPS设置 | 写入成功完成           |
| `MinimalIopsValue`      | 测试最小IOPS值   | 使用最小有效IOPS值1    | 写入成功完成           |
| `VeryHighIopsValue`     | 测试极大IOPS值   | 使用1GB/s的极大IOPS    | 写入成功完成           |
| `TimeBackward`          | 测试时间回退处理 | 模拟时间回退情况       | 正常处理不崩溃         |

### 资源管理测试

| 测试用例名称            | 测试目的              | 测试内容               | 期望结果         |
| ----------------------- | --------------------- | ---------------------- | ---------------- |
| `MultipleEndCalls`      | 测试多次End调用安全性 | 连续调用三次End        | 不崩溃，安全处理 |
| `ConstructorDestructor` | 测试构造析构函数      | 测试默认构造和资源清理 | 正确清理资源     |
| `BeginThenImmediateEnd` | 测试立即结束边界情况  | Begin后立即调用End     | 不崩溃           |
| `UpdateWriteError`      | 测试写入错误处理      | 测试正常路径的错误处理 | 正常完成写入     |

## test_topnext_dump_app.cpp 测试用例

### 基础工具函数测试

| 测试用例名称                | 测试目的           | 测试内容                        | 期望结果             |
| --------------------------- | ------------------ | ------------------------------- | -------------------- |
| `IsLeapYear`                | 测试闰年判断逻辑   | 测试2000,1900,2004,2001年       | 正确识别闰年和平年   |
| `IsLeapYearExtended`        | 测试闰年边界情况   | 测试400整除、100整除、4整除规则 | 正确处理所有闰年规则 |
| `GetDays`                   | 测试日期计算       | 计算公元1年起的天数             | 正确计算天数差值     |
| `GetDaysBoundaryConditions` | 测试日期计算边界   | 测试闰年2月29日、月末月初等     | 正确处理各种边界情况 |
| `GetCurrentDate`            | 测试当前日期格式化 | 格式化tm结构为YYYY-MM-DD        | 正确格式化日期字符串 |
| `GetDate`                   | 测试历史日期计算   | 计算N天前的日期                 | 正确计算并格式化日期 |
| `IsTomorrow`                | 测试日期变更检测   | 检测是否到了第二天              | 正确检测日期变更     |

### 文件操作测试

| 测试用例名称                               | 测试目的                 | 测试内容                         | 期望结果                   |
| ------------------------------------------ | ------------------------ | -------------------------------- | -------------------------- |
| `RemoveDir`                                | 测试目录删除             | 删除包含文件的目录               | 成功删除目录和文件         |
| `SafeOverwriteDirectory`                   | 测试安全目录覆盖基础功能 | 将源目录移动到目标位置           | 成功移动，源目录消失       |
| `SafeOverwriteDirectoryEdgeCases`          | 测试目录覆盖边界情况     | 源目录不存在、空目录等           | 正确处理各种边界情况       |
| `SafeOverwriteDirectoryToNewTarget`        | 测试目标目录不存在覆盖   | 目标目录不存在时的正常覆盖       | 成功移动到新目标位置       |
| `SafeOverwriteDirectoryWithExistingTarget` | 测试目标目录已存在覆盖   | 目标目录存在时的处理（会失败）   | 由于rename()限制返回错误   |
| `SafeOverwriteDirectoryDeepNesting`        | 测试深层嵌套目录覆盖     | 目标存在时的深层结构处理         | 由于rename()限制返回错误   |
| `SafeOverwriteDirectorySpecialCases`       | 测试特殊情况处理         | 空子目录、目标为空目录等特殊情况 | 正确处理各种特殊情况       |
| `RemoveMatchingFiles`                      | 测试匹配文件删除         | 删除目标目录中与源目录匹配的文件 | 删除匹配文件，保留独有文件 |
| `RemoveMatchingFilesEdgeCases`             | 测试文件删除边界情况     | 目标目录不存在、子目录处理等     | 正确处理错误和子目录       |

### 数据转换测试

| 测试用例名称                            | 测试目的                 | 测试内容                     | 期望结果                 |
| --------------------------------------- | ------------------------ | ---------------------------- | ------------------------ |
| `CastUserInfoNet2Svr`                   | 测试网络到服务器数据转换 | 转换UserInfo结构体           | 所有字段正确转换         |
| `CastUserInfoNet2SvrWithZeroExtData`    | 测试零扩展数据转换       | ext_data_len为0的情况        | ext_data_len保持为0      |
| `CastUserInfoNet2SvrBoundaryConditions` | 测试数据转换边界情况     | 最大值、最大长度数据         | 正确处理边界值           |
| `CastUserInfoNet2SvrOpenidBoundary`     | 测试openid字符串边界     | 正常、空、接近最大长度openid | 正确处理各种长度的openid |
| `CastUserInfo2String`                   | 测试结构体到字符串转换   | 将UserInfo转换为字符串       | 正确序列化数据           |
| `CastUserInfo2StringBoundaryConditions` | 测试字符串转换边界情况   | 零长度和最大长度扩展数据     | 正确处理各种长度         |

### 备份和文件管理测试

| 测试用例名称        | 测试目的         | 测试内容                   | 期望结果                 |
| ------------------- | ---------------- | -------------------------- | ------------------------ |
| `OverwriteDumpFile` | 测试dump文件覆盖 | 测试时间间隔控制的文件覆盖 | 根据时间间隔正确执行覆盖 |
| `BackupDumpFile`    | 测试dump文件备份 | 测试日常备份功能           | 正确执行备份流程         |

### 数据结构比较测试

| 测试用例名称                | 测试目的                | 测试内容               | 期望结果           |
| --------------------------- | ----------------------- | ---------------------- | ------------------ |
| `DumpRankKeyComparison`     | 测试DumpRankKey比较     | 测试==操作符的各个字段 | 正确比较所有字段   |
| `RankKeyComparison`         | 测试RankKey比较         | 测试<操作符的排序逻辑  | 正确实现字典序比较 |
| `TopnextRankInfoComparison` | 测试TopnextRankInfo比较 | 测试==和<操作符        | 正确比较复合结构   |

## 测试覆盖率目标

### FlowControlDirectFileWriter 覆盖率目标

- **行覆盖率**: 100% - 覆盖所有代码行
- **分支覆盖率**: 100% - 覆盖所有条件分支
- **函数覆盖率**: 100% - 覆盖所有公有和私有方法

### TopnextDumpApp 覆盖率目标

- **工具函数覆盖率**: 100% - 覆盖所有日期、文件、数据转换函数
- **边界条件覆盖率**: 95% - 覆盖主要边界情况
- **错误处理覆盖率**: 90% - 覆盖主要错误处理路径
