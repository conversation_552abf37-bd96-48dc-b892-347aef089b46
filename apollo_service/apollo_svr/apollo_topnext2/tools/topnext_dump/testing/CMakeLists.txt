set(CMAKE_CXX_STANDARD 11)

include_directories(
  ../src
  ${PROJECT_SOURCE_DIR}/apollo_service/apollo_svr/apollo_topnext2/proxy_src
  ${CMAKE_SOURCE_DIR}/secure
  ${CMAKE_SOURCE_DIR}/secure/tdr_files
  ${PROJECT_SOURCE_DIR}/apollo_service/apollo_svr/apollo_topnext2/proxy_src/source/
)

set(TEST_LINK_LIBS
   topnext_dump_test_lib
    swift
    pebble
    pebble_rpc_s
    crypto
    gtest
    gmock
    tsf4g
    tdr_comm
    pal
    trapidxml
    linenoise
    ncurses
    pthread
    dl
    c
    rt
    z
    anl
    ${LIB_GCOV})

add_executable(test_flowcontrol_direct_file_writer test_flowcontrol_direct_file_writer.cpp ../src/flowcontrol_direct_file_writer.cpp)
target_link_libraries(test_flowcontrol_direct_file_writer ${TEST_LINK_LIBS})
add_test(NAME test_flowcontrol_direct_file_writer COMMAND test_flowcontrol_direct_file_writer --gtest_output=xml:test_flowcontrol_direct_file_writer.xml)

add_executable(test_topnext_dump_app test_topnext_dump_app.cpp ../src/topnext_dump_app.cpp)
target_link_libraries(test_topnext_dump_app ${TEST_LINK_LIBS})
add_test(NAME test_topnext_dump_app COMMAND test_topnext_dump_app --gtest_output=xml:test_topnext_dump_app.xml)

 