#include <fcntl.h>
#include <gtest/gtest.h>
#include <sys/stat.h>
#include <unistd.h>
#include <fstream>
#include <string>
#include "../src/flowcontrol_direct_file_writer.h"
#include "tloghelp/tlogload.h"

namespace topnext_dump {
TLOGCATEGORYINST* g_dump_logcat_ = nullptr;
}

// 生成测试用的日志配置内容
std::string GenerateTestLogConfigContent() {
  const char* config =
      "<?xml version=\"1.0\" encoding=\"GBK\" standalone=\"yes\" ?>\n"
      "<TLOGConf version=\"2\">\n"
      "  <Magic>1548</Magic>\n"
      "  <PriorityHigh>NULL</PriorityHigh>\n"
      "  <PriorityLow>DEBUG</PriorityLow>\n"
      "  <SuppressError>1</SuppressError>\n"
      "  <Count>1</Count>\n"
      "  <CategoryList type=\"TLOGCategory\">\n"
      "    <Name>test</Name>\n"
      "    <PriorityHigh>INFO</PriorityHigh>\n"
      "    <PriorityLow>TRACE</PriorityLow>\n"
      "    <Device type=\"TLOGDevAny\">\n"
      "      <Type>NO</Type>\n"
      "    </Device>\n"
      "  </CategoryList>\n"
      "</TLOGConf>\n";
  return std::string(config);
}

// 初始化测试日志系统
void InitializeTestLogger() {
  const char* kTestLogConfigFile = "./test_log.xml";

  // 生成日志配置
  std::string log_config = GenerateTestLogConfigContent();

  // 写入配置文件
  int fd = open(kTestLogConfigFile, O_CREAT | O_RDWR | O_TRUNC, 0666);
  if (fd >= 0) {
    write(fd, log_config.c_str(), log_config.length());
    close(fd);

    // 初始化日志上下文
    LPTLOGCTX log_ctx = tlog_init_from_file(kTestLogConfigFile);
    if (log_ctx != nullptr) {
      topnext_dump::g_dump_logcat_ = tlog_get_category(log_ctx, "test");
    }

    // 清理配置文件
    unlink(kTestLogConfigFile);
  }
}

class FlowControlDirectFileWriterTest : public ::testing::Test {
 protected:
  void SetUp() override {
    mkdir("test_output", 0777);
    file_path_ = "test_output/test_file.txt";
    if (topnext_dump::g_dump_logcat_ == nullptr) {
      InitializeTestLogger();
    }
  }

  void TearDown() override {
    remove(file_path_.c_str());
    rmdir("test_output");
  }

  std::string ReadFileContent(const std::string& path) {
    std::ifstream file(path);
    if (!file) {
      return "";
    }
    file.seekg(0, std::ios::end);
    size_t size = file.tellg();
    std::string buffer(size, ' ');
    file.seekg(0);
    file.read(&buffer[0], size);
    return buffer;
  }

  std::string file_path_;
};

TEST_F(FlowControlDirectFileWriterTest, SimpleWrite) {
  topnext_dump::FlowControlDirectFileWriter writer;

  ASSERT_EQ(0, writer.Init(1));

  std::string content;
  for (int i = 0; i < 2048; ++i) {
    content += "This is a test string. ";
  }

  ASSERT_EQ(0, writer.Begin(file_path_, content));

  topnext_dump::WRITER_STATUS status;
  int loop_count = 0;
  const int MAX_LOOPS = 5000;
  do {
    status = writer.Update();
    ASSERT_NE(status, topnext_dump::WRITER_ERROR);
    usleep(1000 * 10);  // sleep 10ms
    loop_count++;
  } while (status != topnext_dump::WRITER_DONE && loop_count < MAX_LOOPS);

  ASSERT_EQ(topnext_dump::WRITER_DONE, status);

  writer.End();

  std::string written_content = ReadFileContent(file_path_);
  ASSERT_EQ(content.size(), written_content.size());
  ASSERT_EQ(content, written_content);
}

TEST_F(FlowControlDirectFileWriterTest, InitWithZeroIops) {
  topnext_dump::FlowControlDirectFileWriter writer;
  ASSERT_EQ(-1, writer.Init(0));
}

TEST_F(FlowControlDirectFileWriterTest, Reload) {
  topnext_dump::FlowControlDirectFileWriter writer;
  ASSERT_EQ(0, writer.Init(1));
  ASSERT_EQ(0, writer.Reload(2));
  ASSERT_EQ(-1, writer.Reload(0));
}

// 测试Begin方法 - 无效文件路径
TEST_F(FlowControlDirectFileWriterTest, BeginWithInvalidPath) {
  topnext_dump::FlowControlDirectFileWriter writer;
  ASSERT_EQ(0, writer.Init(1));

  // 尝试在不存在的目录中创建文件
  std::string invalid_path = "/nonexistent_directory/test_file.txt";
  std::string content = "test content";

  ASSERT_EQ(-1, writer.Begin(invalid_path, content));
}

// 测试Begin方法 - 空内容
TEST_F(FlowControlDirectFileWriterTest, BeginWithEmptyContent) {
  topnext_dump::FlowControlDirectFileWriter writer;
  ASSERT_EQ(0, writer.Init(1));

  std::string empty_content = "";
  ASSERT_EQ(0, writer.Begin(file_path_, empty_content));

  topnext_dump::WRITER_STATUS status;
  int loop_count = 0;
  const int MAX_LOOPS = 100;
  do {
    status = writer.Update();
    ASSERT_NE(status, topnext_dump::WRITER_ERROR);
    usleep(1000 * 10);
    loop_count++;
  } while (status != topnext_dump::WRITER_DONE && loop_count < MAX_LOOPS);

  ASSERT_EQ(topnext_dump::WRITER_DONE, status);
  writer.End();
}

// 测试Begin方法 - 小内容（小于8K对齐）
TEST_F(FlowControlDirectFileWriterTest, BeginWithSmallContent) {
  topnext_dump::FlowControlDirectFileWriter writer;
  ASSERT_EQ(0, writer.Init(1));

  std::string small_content = "small test content";
  ASSERT_EQ(0, writer.Begin(file_path_, small_content));

  topnext_dump::WRITER_STATUS status;
  int loop_count = 0;
  const int MAX_LOOPS = 100;
  do {
    status = writer.Update();
    ASSERT_NE(status, topnext_dump::WRITER_ERROR);
    usleep(1000 * 10);
    loop_count++;
  } while (status != topnext_dump::WRITER_DONE && loop_count < MAX_LOOPS);

  ASSERT_EQ(topnext_dump::WRITER_DONE, status);
  writer.End();

  std::string written_content = ReadFileContent(file_path_);
  ASSERT_EQ(small_content, written_content);
}

// 测试Update方法 - IDLE状态
TEST_F(FlowControlDirectFileWriterTest, UpdateIdleStatus) {
  topnext_dump::FlowControlDirectFileWriter writer;
  ASSERT_EQ(0, writer.Init(1));

  std::string content = "test content for idle status";
  ASSERT_EQ(0, writer.Begin(file_path_, content));

  // 连续快速调用Update，应该返回IDLE状态
  topnext_dump::WRITER_STATUS status1 = writer.Update();
  topnext_dump::WRITER_STATUS status2 = writer.Update();

  // 第一次可能是SYNC_DATA，第二次应该是IDLE（因为时间间隔太短）
  ASSERT_TRUE(status2 == topnext_dump::WRITER_IDLE || status2 == topnext_dump::WRITER_SYNC_DATA);

  writer.End();
}

// 测试不同IOPS设置下的写入行为
TEST_F(FlowControlDirectFileWriterTest, DifferentIopsSettings) {
  topnext_dump::FlowControlDirectFileWriter writer;

  // 测试高IOPS设置
  ASSERT_EQ(0, writer.Init(10));  // 10 MB/s

  std::string content;
  for (int i = 0; i < 1000; ++i) {
    content += "This is test content for high IOPS. ";
  }

  ASSERT_EQ(0, writer.Begin(file_path_, content));

  topnext_dump::WRITER_STATUS status;
  int loop_count = 0;
  const int MAX_LOOPS = 1000;
  do {
    status = writer.Update();
    ASSERT_NE(status, topnext_dump::WRITER_ERROR);
    usleep(1000 * 10);
    loop_count++;
  } while (status != topnext_dump::WRITER_DONE && loop_count < MAX_LOOPS);

  ASSERT_EQ(topnext_dump::WRITER_DONE, status);
  writer.End();

  std::string written_content = ReadFileContent(file_path_);
  ASSERT_EQ(content, written_content);
}

// 测试End方法的多次调用
TEST_F(FlowControlDirectFileWriterTest, MultipleEndCalls) {
  topnext_dump::FlowControlDirectFileWriter writer;
  ASSERT_EQ(0, writer.Init(1));

  std::string content = "test content";
  ASSERT_EQ(0, writer.Begin(file_path_, content));

  // 第一次调用End
  writer.End();

  // 第二次调用End应该是安全的
  writer.End();

  // 第三次调用End也应该是安全的
  writer.End();
}

// 测试在没有Begin的情况下调用Update
TEST_F(FlowControlDirectFileWriterTest, UpdateWithoutBegin) {
  topnext_dump::FlowControlDirectFileWriter writer;
  ASSERT_EQ(0, writer.Init(1));

  // 直接调用Update而不先调用Begin
  // 这可能导致未定义行为，但不应该崩溃
  topnext_dump::WRITER_STATUS status = writer.Update();
  // 由于fd_为-1，可能返回错误状态
}

// 测试在没有Init的情况下调用Begin
TEST_F(FlowControlDirectFileWriterTest, BeginWithoutInit) {
  topnext_dump::FlowControlDirectFileWriter writer;

  std::string content = "test content";
  // 在没有Init的情况下调用Begin
  // max_iops_magebytes_为0，可能影响Update的行为
  int result = writer.Begin(file_path_, content);
  // Begin本身可能成功，但Update会有问题

  if (result == 0) {
    writer.End();
  }
}

// 测试大文件写入（触发多次Update调用）
TEST_F(FlowControlDirectFileWriterTest, LargeFileWrite) {
  topnext_dump::FlowControlDirectFileWriter writer;
  ASSERT_EQ(0, writer.Init(1));  // 低IOPS，确保需要多次Update

  std::string content;
  // 创建大约100KB的内容
  for (int i = 0; i < 5000; ++i) {
    content += "This is a large test content string. ";
  }

  ASSERT_EQ(0, writer.Begin(file_path_, content));

  topnext_dump::WRITER_STATUS status;
  int loop_count = 0;
  const int MAX_LOOPS = 10000;
  int sync_data_count = 0;
  int idle_count = 0;

  do {
    status = writer.Update();
    ASSERT_NE(status, topnext_dump::WRITER_ERROR);

    if (status == topnext_dump::WRITER_SYNC_DATA) {
      sync_data_count++;
    } else if (status == topnext_dump::WRITER_IDLE) {
      idle_count++;
    }

    usleep(1000 * 10);
    loop_count++;
  } while (status != topnext_dump::WRITER_DONE && loop_count < MAX_LOOPS);

  ASSERT_EQ(topnext_dump::WRITER_DONE, status);
  ASSERT_GT(sync_data_count, 1);  // 应该有多次数据同步

  writer.End();

  std::string written_content = ReadFileContent(file_path_);
  ASSERT_EQ(content.size(), written_content.size());
  ASSERT_EQ(content, written_content);
}

// 测试精确的8K边界内容
TEST_F(FlowControlDirectFileWriterTest, ExactAlignmentBoundary) {
  topnext_dump::FlowControlDirectFileWriter writer;
  ASSERT_EQ(0, writer.Init(1));

  // 创建精确8K大小的内容
  std::string content(8192, 'A');

  ASSERT_EQ(0, writer.Begin(file_path_, content));

  topnext_dump::WRITER_STATUS status;
  int loop_count = 0;
  const int MAX_LOOPS = 1000;
  do {
    status = writer.Update();
    ASSERT_NE(status, topnext_dump::WRITER_ERROR);
    usleep(1000 * 10);
    loop_count++;
  } while (status != topnext_dump::WRITER_DONE && loop_count < MAX_LOOPS);

  ASSERT_EQ(topnext_dump::WRITER_DONE, status);
  writer.End();

  std::string written_content = ReadFileContent(file_path_);
  ASSERT_EQ(content, written_content);
}

// 测试写入权限问题（如果可能的话）
TEST_F(FlowControlDirectFileWriterTest, WritePermissionTest) {
  topnext_dump::FlowControlDirectFileWriter writer;
  ASSERT_EQ(0, writer.Init(1));

  // 尝试写入到只读目录（如果存在的话）
  std::string readonly_path = "/proc/test_file.txt";  // /proc通常是只读的
  std::string content = "test content";

  int result = writer.Begin(readonly_path, content);
  // 期望失败
  ASSERT_EQ(-1, result);
}

// 测试TurnBufferIo路径 - 通过创建需要buffer IO的场景
TEST_F(FlowControlDirectFileWriterTest, TurnBufferIoPath) {
  topnext_dump::FlowControlDirectFileWriter writer;
  ASSERT_EQ(0, writer.Init(1));

  // 创建一个内容，其大小不是8K的整数倍，但大于一次写入的大小
  // 这样会触发TurnBufferIo的调用
  std::string content;
  // 创建大约9K的内容（8192 + 1000）
  content.assign(8192, 'A');
  content.append(1000, 'B');

  ASSERT_EQ(0, writer.Begin(file_path_, content));

  topnext_dump::WRITER_STATUS status;
  int loop_count = 0;
  const int MAX_LOOPS = 1000;
  bool turned_buffer_io = false;

  do {
    status = writer.Update();
    ASSERT_NE(status, topnext_dump::WRITER_ERROR);

    // 当写入到最后一部分时，应该会调用TurnBufferIo
    if (status == topnext_dump::WRITER_SYNC_DATA) {
      turned_buffer_io = true;
    }

    usleep(1000 * 10);
    loop_count++;
  } while (status != topnext_dump::WRITER_DONE && loop_count < MAX_LOOPS);

  ASSERT_EQ(topnext_dump::WRITER_DONE, status);
  writer.End();

  std::string written_content = ReadFileContent(file_path_);
  ASSERT_EQ(content, written_content);
}

// 测试极小IOPS值的边界情况
TEST_F(FlowControlDirectFileWriterTest, MinimalIopsValue) {
  topnext_dump::FlowControlDirectFileWriter writer;
  ASSERT_EQ(0, writer.Init(1));  // 最小有效IOPS值

  std::string content = "minimal iops test content";
  ASSERT_EQ(0, writer.Begin(file_path_, content));

  topnext_dump::WRITER_STATUS status;
  int loop_count = 0;
  const int MAX_LOOPS = 1000;

  do {
    status = writer.Update();
    ASSERT_NE(status, topnext_dump::WRITER_ERROR);
    usleep(1000 * 10);
    loop_count++;
  } while (status != topnext_dump::WRITER_DONE && loop_count < MAX_LOOPS);

  ASSERT_EQ(topnext_dump::WRITER_DONE, status);
  writer.End();
}

// 测试构造函数和析构函数
TEST_F(FlowControlDirectFileWriterTest, ConstructorDestructor) {
  // 测试默认构造
  topnext_dump::FlowControlDirectFileWriter* writer = new topnext_dump::FlowControlDirectFileWriter();

  // 在没有任何操作的情况下销毁
  delete writer;

  // 测试在Begin后但没有End的情况下销毁
  writer = new topnext_dump::FlowControlDirectFileWriter();
  writer->Init(1);
  writer->Begin(file_path_, "test content");
  delete writer;  // 析构函数应该清理资源
}

// 测试Update方法中write返回错误的情况
// 这个测试比较难模拟，因为需要让write系统调用失败
TEST_F(FlowControlDirectFileWriterTest, UpdateWriteError) {
  topnext_dump::FlowControlDirectFileWriter writer;
  ASSERT_EQ(0, writer.Init(1));

  std::string content = "test content for write error";
  ASSERT_EQ(0, writer.Begin(file_path_, content));

  // 正常情况下很难触发write错误，但我们可以测试正常路径
  topnext_dump::WRITER_STATUS status;
  int loop_count = 0;
  const int MAX_LOOPS = 100;

  do {
    status = writer.Update();
    // 正常情况下不应该有错误
    ASSERT_NE(status, topnext_dump::WRITER_ERROR);
    usleep(1000 * 10);
    loop_count++;
  } while (status != topnext_dump::WRITER_DONE && loop_count < MAX_LOOPS);

  writer.End();
}

// 测试Begin后立即调用End
TEST_F(FlowControlDirectFileWriterTest, BeginThenImmediateEnd) {
  topnext_dump::FlowControlDirectFileWriter writer;
  ASSERT_EQ(0, writer.Init(1));

  std::string content = "test content";
  ASSERT_EQ(0, writer.Begin(file_path_, content));

  // 立即调用End，不调用Update
  writer.End();

  // 文件可能存在但内容可能不完整
  // 这是一个边界情况测试
}

// 测试非常大的IOPS值
TEST_F(FlowControlDirectFileWriterTest, VeryHighIopsValue) {
  topnext_dump::FlowControlDirectFileWriter writer;
  ASSERT_EQ(0, writer.Init(1000));  // 1GB/s

  std::string content;
  for (int i = 0; i < 1000; ++i) {
    content += "High IOPS test content. ";
  }

  ASSERT_EQ(0, writer.Begin(file_path_, content));

  topnext_dump::WRITER_STATUS status;
  int loop_count = 0;
  const int MAX_LOOPS = 100;

  do {
    status = writer.Update();
    ASSERT_NE(status, topnext_dump::WRITER_ERROR);
    usleep(1000 * 10);
    loop_count++;
  } while (status != topnext_dump::WRITER_DONE && loop_count < MAX_LOOPS);

  ASSERT_EQ(topnext_dump::WRITER_DONE, status);
  writer.End();

  std::string written_content = ReadFileContent(file_path_);
  ASSERT_EQ(content, written_content);
}

// 测试时间回退的情况
TEST_F(FlowControlDirectFileWriterTest, TimeBackward) {
  topnext_dump::FlowControlDirectFileWriter writer;
  ASSERT_EQ(0, writer.Init(1));

  std::string content = "test content for time backward";
  ASSERT_EQ(0, writer.Begin(file_path_, content));

  // 正常调用Update
  topnext_dump::WRITER_STATUS status = writer.Update();

  // 再次调用Update，时间差可能为负或很小
  status = writer.Update();

  // 应该能正常处理
  ASSERT_TRUE(status == topnext_dump::WRITER_IDLE || status == topnext_dump::WRITER_SYNC_DATA ||
              status == topnext_dump::WRITER_DONE);

  writer.End();
}

int main(int argc, char** argv) {
  ::testing::InitGoogleTest(&argc, argv);
  return RUN_ALL_TESTS();
}