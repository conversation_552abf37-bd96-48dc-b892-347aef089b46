cmake_minimum_required(VERSION 3.5)

set(CMAKE_CXX_STANDARD 11)

link_directories(
  ${CMAKE_CURRENT_SOURCE_DIR}/../../deps/pebble_c11/lib/pebble
  ${CMAKE_CURRENT_SOURCE_DIR}/../../deps/pebble_c11/lib/data_api
  ${CMAKE_CURRENT_SOURCE_DIR}/../../deps/pebble_c11/lib/pebble_module
  ${CMAKE_CURRENT_SOURCE_DIR}/../../deps/pebble_c11/lib/pipe
  ${CMAKE_CURRENT_SOURCE_DIR}/../../deps/pebble_c11/lib/thirdparty)

set(GEN_SRC
    ${CMAKE_CURRENT_LIST_DIR}/protocol/topnext_dump_conf_desc_v2.cpp
    ${CMAKE_CURRENT_LIST_DIR}/protocol/topnext_dump_conf_desc_v2.h
    ${CMAKE_CURRENT_LIST_DIR}/protocol/proxy_protocol.h
    ${CMAKE_CURRENT_LIST_DIR}/protocol/proxy_protocol.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/protocol/apollo_service_base_header_topnext.h
    ${CMAKE_CURRENT_SOURCE_DIR}/protocol/apollo_service_base_header_topnext.cpp
    ${CMAKE_CURRENT_LIST_DIR}/protocol/topnext_protocol_tdr.cpp
    ${CMAKE_CURRENT_LIST_DIR}/protocol/topnext_protocol_tdr.h
    ${CMAKE_CURRENT_LIST_DIR}/cfg/topnext_dump_conf_desc_v2.tdr)

add_custom_command(
  OUTPUT ${GEN_SRC}
  COMMAND mkdir -p ${CMAKE_CURRENT_SOURCE_DIR}/protocol
  COMMAND
    cp -rf
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../apollo_xml/apollo_service_base_header.xml
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../apollo_xml/apollo_topnext2/apollo_service_base_header_topnext_proto.xml
  COMMAND
    cp -rf
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../apollo_xml/apollo_topnext2/topnext_protocol.xml
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../apollo_xml/apollo_topnext2/topnext_protocol_tdr.xml
  COMMAND
    ${TDR} --xml2h
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../apollo_xml/apollo_topnext2/proxy_protocol.xml
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../apollo_xml/apollo_topnext2/apollo_service_base_header_topnext.xml
    --out_path=${CMAKE_CURRENT_SOURCE_DIR}/protocol/
  COMMAND
    ${TDR} --xml2cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../apollo_xml/apollo_topnext2/proxy_protocol.xml
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../apollo_xml/apollo_topnext2/apollo_service_base_header_topnext.xml
    --no_comm_files --out_path=${CMAKE_CURRENT_SOURCE_DIR}/protocol/
  COMMAND
    ${TDR} --xml2h
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../apollo_xml/apollo_topnext2/topnext_protocol_tdr.xml
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../apollo_xml/apollo_topnext2/apollo_service_base_header_topnext_proto.xml
    --out_path=${CMAKE_CURRENT_SOURCE_DIR}/protocol/
  COMMAND
    ${TDR} --xml2cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../apollo_xml/apollo_topnext2/topnext_protocol_tdr.xml
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../apollo_xml/apollo_topnext2/apollo_service_base_header_topnext_proto.xml
    --no_comm_files -p --out_path=${CMAKE_CURRENT_SOURCE_DIR}/protocol/
  COMMAND
    ${TDR} --xml2h
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../apollo_xml/apollo_topnext2/topnext_dump_conf_desc_v2.xml
    --out_path=${CMAKE_CURRENT_SOURCE_DIR}/protocol --no_comm_files
  COMMAND
    ${TDR} --xml2c
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../apollo_xml/apollo_topnext2/topnext_dump_conf_desc_v2.xml
    -o ${CMAKE_CURRENT_SOURCE_DIR}/protocol/topnext_dump_conf_desc_v2.cpp
  COMMAND
    ${TDR} --xml2dr
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../apollo_xml/apollo_topnext2/topnext_dump_conf_desc_v2.xml
    -o
    ${CMAKE_CURRENT_SOURCE_DIR}/cfg/topnext_dump_conf_desc_v2.tdr
  COMMAND rm -rf
          ${CMAKE_CURRENT_SOURCE_DIR}/protocol/topnext_proto_tdr_metalib.h
  COMMAND rm -rf ${CMAKE_CURRENT_SOURCE_DIR}/protocol/topnext_dump_cfg_metalib.h
  WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
  COMMENT "generate protocol files"
  VERBATIM)

# topnext_dump
file(GLOB DUMP_SRCS_2 CMAKE_CONFIGURE_DEPENDS
     ${CMAKE_CURRENT_LIST_DIR}/src/*.cpp)
add_executable(topnext_dump ${DUMP_SRCS_2} ${GEN_SRC})
add_dependencies(topnext_dump topnext_tools_proto_gen)
target_include_directories(
  topnext_dump
  PUBLIC ${CMAKE_CURRENT_LIST_DIR}
         ${CMAKE_CURRENT_LIST_DIR}/protocol
         ${CMAKE_CURRENT_LIST_DIR}/../../../../apollo_api/apollo_common
         ${CMAKE_CURRENT_LIST_DIR}/../../server/idl
         ${CMAKE_CURRENT_LIST_DIR}/../../server
         ${CMAKE_CURRENT_LIST_DIR}/../../server/tdr_cpp_files
         ${CMAKE_CURRENT_LIST_DIR}/../../common
         ${CMAKE_CURRENT_LIST_DIR}/../../server_pub)
target_link_libraries(
  topnext_dump
  swift
  tsf4g
  pal
  ncurses
  tdr_comm
  tconnapi
  server_pub
  crypto
  trapidxml
  linenoise
  tbuspp_swift
  tbuspp_proto_swift
  protobuf_tbuspp_swift
  pthread
  dl
  c
  rt
  anl
  z
  ${LIB_GCOV})

add_library(topnext_dump_test_lib 
  ${CMAKE_CURRENT_LIST_DIR}/src/flowcontrol_direct_file_writer.cpp
  ${CMAKE_CURRENT_LIST_DIR}/src/topnext_dump_app.cpp
  ${GEN_SRC})
add_dependencies(topnext_dump_test_lib topnext_tools_proto_gen)
target_include_directories(
  topnext_dump_test_lib
  PUBLIC ${CMAKE_CURRENT_LIST_DIR}
         ${CMAKE_CURRENT_LIST_DIR}/protocol
         ${CMAKE_CURRENT_LIST_DIR}/../../../../apollo_api/apollo_common
         ${CMAKE_CURRENT_LIST_DIR}/../../server/idl
         ${CMAKE_CURRENT_LIST_DIR}/../../server
         ${CMAKE_CURRENT_LIST_DIR}/../../server/tdr_cpp_files
         ${CMAKE_CURRENT_LIST_DIR}/../../common
         ${CMAKE_CURRENT_LIST_DIR}/../../../server_pub)
target_link_libraries(
  topnext_dump_test_lib
  swift
  tsf4g
  pal
  ncurses
  tdr_comm
  tconnapi
  server_pub
  crypto
  trapidxml
  linenoise
  tbuspp_swift
  tbuspp_proto_swift
  protobuf_tbuspp_swift
  pthread
  dl
  c
  rt
  anl
  z
  ${LIB_GCOV})

if(WITH_UNITTEST)
  add_subdirectory(testing)
endif()