#include <unistd.h>
#include <ctime>
#include <iostream>
#include <vector>
#include "../src/topnext_image_coordinator.h"
#include "../src/topnext_rank_instance.h"

using namespace topnext_app;

// 模拟的 TopNextRankInstance 类用于测试
class MockTopNextRankInstance : public TopNextRankInstance {
 public:
  MockTopNextRankInstance(const std::string& name) : name_(name), image_status_set_count_(0) {}

  int SetSubRankImageStatusForType(uint32_t sub_type, IMAGE_STATUS status) override {
    std::cout << "[" << name_ << "] SetSubRankImageStatusForType called: sub_type=" << sub_type << ", status=" << status
              << std::endl;
    ++image_status_set_count_;
    return 1;  // 模拟设置了1个子榜的状态
  }

  void AddSubRankToImageQueue(uint32_t sub_type) override {
    std::cout << "[" << name_ << "] AddSubRankToImageQueue called: sub_type=" << sub_type << std::endl;
  }

  int GetImageStatusSetCount() const { return image_status_set_count_; }
  const std::string& GetName() const { return name_; }

 private:
  std::string name_;
  int image_status_set_count_;
};

void TestBasicFunctionality() {
  std::cout << "\n=== Testing Basic Functionality ===" << std::endl;

  // 初始化协调器
  int ret = TopNextImageCoordinator::Instance().Init();
  if (ret != 0) {
    std::cout << "Failed to init coordinator, ret=" << ret << std::endl;
    return;
  }
  std::cout << "Coordinator initialized successfully" << std::endl;

  // 创建模拟实例
  MockTopNextRankInstance instance1("Instance1");
  MockTopNextRankInstance instance2("Instance2");
  MockTopNextRankInstance instance3("Instance3");

  // 注册子榜类型
  uint32_t business_id = 100;
  uint32_t type = 200;
  uint32_t sub_type = 1001;
  std::string cron = "*/1 * * * *";  // 每分钟触发一次

  ret = TopNextImageCoordinator::Instance().RegisterSubType(business_id, type, sub_type, cron, &instance1);
  if (ret != 0) {
    std::cout << "Failed to register instance1, ret=" << ret << std::endl;
    return;
  }

  ret = TopNextImageCoordinator::Instance().RegisterSubType(business_id, type, sub_type, cron, &instance2);
  if (ret != 0) {
    std::cout << "Failed to register instance2, ret=" << ret << std::endl;
    return;
  }

  ret = TopNextImageCoordinator::Instance().RegisterSubType(business_id, type, sub_type, cron, &instance3);
  if (ret != 0) {
    std::cout << "Failed to register instance3, ret=" << ret << std::endl;
    return;
  }

  std::cout << "All instances registered successfully" << std::endl;
  std::cout << "Registered sub types: " << TopNextImageCoordinator::Instance().GetRegisteredSubTypeCount() << std::endl;
  std::cout << "Registered instances: " << TopNextImageCoordinator::Instance().GetRegisteredInstanceCount()
            << std::endl;

  // 模拟定时器触发（需要等待到下一分钟的开始）
  std::cout << "\nWaiting for timer trigger..." << std::endl;
  time_t now = time(NULL);
  time_t next_minute = (now / 60 + 1) * 60;

  while (time(NULL) < next_minute) {
    TopNextImageCoordinator::Instance().Update();
    usleep(100000);  // 100ms
  }

  // 继续更新一段时间以确保定时器被触发
  for (int i = 0; i < 20; ++i) {
    TopNextImageCoordinator::Instance().Update();
    usleep(100000);  // 100ms
  }

  // 检查结果
  std::cout << "\n=== Results ===" << std::endl;
  std::cout << "Instance1 image status set count: " << instance1.GetImageStatusSetCount() << std::endl;
  std::cout << "Instance2 image status set count: " << instance2.GetImageStatusSetCount() << std::endl;
  std::cout << "Instance3 image status set count: " << instance3.GetImageStatusSetCount() << std::endl;

  if (instance1.GetImageStatusSetCount() > 0 && instance2.GetImageStatusSetCount() > 0 &&
      instance3.GetImageStatusSetCount() > 0) {
    std::cout << "SUCCESS: All instances received image status updates!" << std::endl;
  } else {
    std::cout << "FAILED: Not all instances received updates" << std::endl;
  }

  // 清理
  TopNextImageCoordinator::Instance().Reset();
  std::cout << "Coordinator reset" << std::endl;
}

void TestUnregister() {
  std::cout << "\n=== Testing Unregister ===" << std::endl;

  TopNextImageCoordinator::Instance().Init();

  MockTopNextRankInstance instance1("Instance1");
  MockTopNextRankInstance instance2("Instance2");

  uint32_t business_id = 100;
  uint32_t type = 200;
  uint32_t sub_type = 1002;
  std::string cron = "*/1 * * * *";

  TopNextImageCoordinator::Instance().RegisterSubType(business_id, type, sub_type, cron, &instance1);
  TopNextImageCoordinator::Instance().RegisterSubType(business_id, type, sub_type, cron, &instance2);

  std::cout << "Before unregister - instances: " << TopNextImageCoordinator::Instance().GetRegisteredInstanceCount()
            << std::endl;

  TopNextImageCoordinator::Instance().UnregisterInstance(&instance1);

  std::cout << "After unregister - instances: " << TopNextImageCoordinator::Instance().GetRegisteredInstanceCount()
            << std::endl;

  TopNextImageCoordinator::Instance().Reset();
}

void TestMultipleSubTypes() {
  std::cout << "\n=== Testing Multiple Sub Types ===" << std::endl;

  TopNextImageCoordinator::Instance().Init();

  MockTopNextRankInstance instance1("Instance1");

  // 注册多个子榜类型
  uint32_t business_id = 100;
  uint32_t type = 200;
  TopNextImageCoordinator::Instance().RegisterSubType(business_id, type, 2001, "*/1 * * * *", &instance1);
  TopNextImageCoordinator::Instance().RegisterSubType(business_id, type, 2002, "*/2 * * * *", &instance1);
  TopNextImageCoordinator::Instance().RegisterSubType(business_id, type, 2003, "*/1 * * * *", &instance1);

  std::cout << "Registered sub types: " << TopNextImageCoordinator::Instance().GetRegisteredSubTypeCount() << std::endl;
  std::cout << "Registered instances: " << TopNextImageCoordinator::Instance().GetRegisteredInstanceCount()
            << std::endl;

  TopNextImageCoordinator::Instance().Reset();
}

int main() {
  std::cout << "TopNext Image Coordinator Test" << std::endl;
  std::cout << "==============================" << std::endl;

  TestBasicFunctionality();
  TestUnregister();
  TestMultipleSubTypes();

  std::cout << "\nAll tests completed!" << std::endl;
  return 0;
}
