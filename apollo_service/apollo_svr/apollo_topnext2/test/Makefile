# Makefile for TopNext Image Coordinator Test

CXX = g++
CXXFLAGS = -std=c++11 -Wall -Wextra -g -O0
INCLUDES = -I../src -I../../apollo_topnext/src -I../../../common/include

# 源文件
COORDINATOR_SRCS = ../src/topnext_image_coordinator.cpp
CRONTAB_SRCS = ../src/crontab_timer.cpp
TEST_SRCS = test_image_coordinator.cpp

# 目标文件
COORDINATOR_OBJS = $(COORDINATOR_SRCS:.cpp=.o)
CRONTAB_OBJS = $(CRONTAB_SRCS:.cpp=.o)
TEST_OBJS = $(TEST_SRCS:.cpp=.o)

# 可执行文件
TARGET = test_image_coordinator

# 默认目标
all: $(TARGET)

# 编译可执行文件
$(TARGET): $(COORDINATOR_OBJS) $(CRONTAB_OBJS) $(TEST_OBJS)
	$(CXX) $(CXXFLAGS) -o $@ $^ -lpthread

# 编译目标文件
%.o: %.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# 清理
clean:
	rm -f $(COORDINATOR_OBJS) $(CRONTAB_OBJS) $(TEST_OBJS) $(TARGET)

# 运行测试
test: $(TARGET)
	./$(TARGET)

.PHONY: all clean test
