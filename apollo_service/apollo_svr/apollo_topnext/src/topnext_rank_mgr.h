#ifndef TOPNEXT_RANK_MGR_H_
#define TOPNEXT_RANK_MGR_H_

#include <sstream>

#include "shtable_util.h"
#include "tlog/tlog.h"
#include "topnext_conf_desc.h"
#include "topnext_protocol.h"
#include "topnext_rank_instance.h"

namespace topnext_app {

typedef struct {
  RankKey rank_key;
  TopNextRankInstance* rank_instance;
} RankInstance;

// extern std::string g_client_addr;

class TopNextServer;

// 榜单管理
class TopNextRankMgr {
 public:
  TopNextRankMgr();
  ~TopNextRankMgr() {}

 public:
  int Init(TopNextServer* server, TOPNEXTCFG* cfg);
  int InitDataDir();
  int Reload(TOPNEXTCFG* cfg);

 public:
  template <class RequestType, class ResponseType>
  int HandleClientRequest(const RequestType& req, ResponseType& rsp);
  int HandleClientRequest(
      const topnext_proto::GetOneUserMultiRankMultiSubRankReq& req,
      topnext_proto::GetOneUserMultiRankMultiSubRankRsp& rsp);

  int HandleClientRequest(const topnext_proto::InnerGetRankListReq& req,
                          topnext_proto::InnerGetRankListRsp& rsp);
  // 定时触发
  void OnTickTimer();
  int OnIdle();

 public:
  int AddRankInstance(TOPNEXTCFG* cfg, const RANKCFG& rank_cfg,
                      const TYPECFG& type_cfg);
  int DeleteRankInstance(const RankKey& rank_key);
  TopNextRankInstance* GetRankInstance(const RankKey& rank_key);
  TopNextRankInstance* GetRankInstance(
      const topnext_proto::RankInfo& rank_info);

 private:
  // 默认榜单:共享内存管理
  shm_manager::ShmManager main_rank_shm_manager_;
  // 子榜单
  shtable_util::ShtableUtil<RankInstance> rank_instance_ht_;
  TopNextServer* server_;
  // tick函数当前处理的榜单
  int current_tick_pos_;
};

template <class RequestType, class ResponseType>
int TopNextRankMgr::HandleClientRequest(const RequestType& req,
                                        ResponseType& rsp) {
  g_client_addr = req.client_info.client_addr;
  if (Loggable(TLOG_PRIORITY_DEBUG)) {
    std::ostringstream ss;
    ss << req;
    TOPNEXT_LOG_DEBUG("rpc request: %s", ss.str().c_str());
  }
  TopNextRankInstance* rank_instance = GetRankInstance(req.rank_info);
  if (NULL == rank_instance) {
    TOPNEXT_LOG_ERROR(
        "can not find rank instance,"
        "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u",
        req.rank_info.business_id, req.rank_info.world_id,
        req.rank_info.zone_id, req.rank_info.type, req.rank_info.instance_id);
    rsp.ret_info.ret = APOLLO_TOPNEXT_SVR_RANK_NOT_EXISTS;
    rsp.ret_info.msg = "rank instance not found";
    return -1;
  }
  int ret = rank_instance->HandleClientRequest(req, rsp);
  if (Loggable(TLOG_PRIORITY_DEBUG)) {
    std::ostringstream ss;
    ss << rsp;
    TOPNEXT_LOG_DEBUG("rpc response, ret:%d, rsp: %s", ret, ss.str().c_str());
  }
  if (0 == ret) {
    rsp.ret_info.ret = 0;
    rsp.ret_info.msg = "succ";
  }
  return ret;
};

}  // namespace topnext_app

#endif
