#include "topnext_rank_mgr.h"

#include <algorithm>
#include <sstream>

#include "pal/tos.h"
#include "topnext_server.h"

namespace topnext_app {

extern LPTLOGCATEGORYINST g_tlogcat;

class RankInstanceHasher : public shtable_util::IHashFunctor<RankInstance> {
 public:
  inline unsigned int GetCode(const RankInstance& node) const {
    return node.rank_key.business_id ^ node.rank_key.world_id ^
           node.rank_key.zone_id ^ node.rank_key.type ^
           node.rank_key.instance_id;
  }

  inline int Compare(const RankInstance& left,
                     const RankInstance& right) const {
    return (left.rank_key.zone_id == right.rank_key.zone_id &&
            left.rank_key.type == right.rank_key.type &&
            left.rank_key.world_id == right.rank_key.world_id &&
            left.rank_key.instance_id == right.rank_key.instance_id &&
            left.rank_key.business_id == right.rank_key.business_id)
               ? 0
               : -1;
  }
};

static RankInstanceHasher g_rank_instance_hasher;

TopNextRankMgr::TopNextRankMgr() {
  server_ = NULL;
  current_tick_pos_ = -1;
}

int TopNextRankMgr::Init(TopNextServer* server, TOPNEXTCFG* cfg) {
  server_ = server;
  int ret = 0;
  ret = InitDataDir();
  if (0 != ret) {
    TOPNEXT_LOG_ERROR("init main rank shm manager fail, ret:%d", ret);
    return -1;
  }
  // 创建榜单映射表
  ret = rank_instance_ht_.Create(TOPNEXT_MAX_RANK_NUM * 2, TOPNEXT_MAX_RANK_NUM,
                                 &g_rank_instance_hasher);
  if (0 != ret) {
    TOPNEXT_LOG_ERROR("create rank instance hashtable failed, ret:%d", ret);
    return -1;
  }
  // 加载所有配置的榜单
  for (uint32_t i = 0; i < cfg->dwRank_cfg_num; ++i) {
    const RANKCFG& rank_cfg = cfg->astRank_cfg[i];
    TypeKey type_key;
    type_key.business_id = rank_cfg.dwBusiness_id;
    type_key.type = rank_cfg.dwType;
    TYPECFG type_cfg;
    ret = GetTypeCfg(cfg, type_key, type_cfg);
    if (0 != ret) {
      TOPNEXT_LOG_INFO(
          "get type cfg fail,"
          "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u",
          rank_cfg.dwBusiness_id, rank_cfg.dwWorld_id, rank_cfg.dwZone_id,
          rank_cfg.dwType, rank_cfg.dwInstance_id);
      return -1;
    }
    ret = AddRankInstance(cfg, rank_cfg, type_cfg);
    if (0 != ret) {
      TOPNEXT_LOG_ERROR(
          "init rank instance fail,"
          "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u, "
          "ret:%d",
          rank_cfg.dwBusiness_id, rank_cfg.dwWorld_id, rank_cfg.dwZone_id, rank_cfg.dwType, rank_cfg.dwInstance_id,
          ret);
      return -1;
    }
    TOPNEXT_LOG_INFO(
        "init rank instance succ,"
        "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u",
        rank_cfg.dwBusiness_id, rank_cfg.dwWorld_id, rank_cfg.dwZone_id,
        rank_cfg.dwType, rank_cfg.dwInstance_id);
  }
  return 0;
};

int TopNextRankMgr::Reload(TOPNEXTCFG* cfg) {
  int ret = 0;
  for (uint32_t i = 0; i < cfg->dwRank_cfg_num; ++i) {
    RankKey rank_key;
    const RANKCFG& rank_cfg = cfg->astRank_cfg[i];
    CastRankCfg2RankKey(rank_cfg, rank_key);
    TypeKey type_key;
    type_key.business_id = rank_cfg.dwBusiness_id;
    type_key.type = rank_cfg.dwType;
    TYPECFG type_cfg;
    ret = GetTypeCfg(cfg, type_key, type_cfg);
    if (0 != ret) {
      TOPNEXT_LOG_INFO(
          "get type cfg fail,"
          "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u",
          rank_cfg.dwBusiness_id, rank_cfg.dwWorld_id, rank_cfg.dwZone_id,
          rank_cfg.dwType, rank_cfg.dwInstance_id);
      return -1;
    }
    TopNextRankInstance* rank_instance = GetRankInstance(rank_key);
    if (NULL == rank_instance) {
      // 新增的榜单
      ret = AddRankInstance(cfg, rank_cfg, type_cfg);
      if (0 != ret) {
        TOPNEXT_LOG_ERROR(
            "init rank instance fail,"
            "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u, "
            "ret:%d",
            rank_cfg.dwBusiness_id, rank_cfg.dwWorld_id, rank_cfg.dwZone_id,
            rank_cfg.dwType, rank_cfg.dwInstance_id, ret);
        return -1;
      }
      TOPNEXT_LOG_INFO(
          "init rank instance succ,"
          "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u",
          rank_cfg.dwBusiness_id, rank_cfg.dwWorld_id, rank_cfg.dwZone_id,
          rank_cfg.dwType, rank_cfg.dwInstance_id);
    } else {
      // 完全一样的榜单
      if (rank_instance->rank_key_.id == rank_cfg.dwId) {
        // 重载当前榜单
        ret = rank_instance->Reload(type_cfg);
        if (0 != ret) {
          TOPNEXT_LOG_ERROR("reload rank fail, ret:%d", ret);
          return -1;
        }
      } else {
        // 内部ID变化了，需要重建
        // 需要删除删除之前的榜单再添加新榜单
        ret = DeleteRankInstance(rank_key);
        if (0 != ret) {
          TOPNEXT_LOG_ERROR(
              "delete rank instance fail,"
              "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u, "
              "id:%u",
              rank_cfg.dwBusiness_id, rank_cfg.dwWorld_id, rank_cfg.dwZone_id,
              rank_cfg.dwType, rank_cfg.dwInstance_id, rank_cfg.dwId);
          return -1;
        }
        TOPNEXT_LOG_INFO(
            "delete rank instance succ,"
            "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u, "
            "id:%u",
            rank_cfg.dwBusiness_id, rank_cfg.dwWorld_id, rank_cfg.dwZone_id,
            rank_cfg.dwType, rank_cfg.dwInstance_id, rank_cfg.dwId);
        ret = AddRankInstance(cfg, rank_cfg, type_cfg);
        if (0 != ret) {
          TOPNEXT_LOG_INFO(
              "add rank instance succ,"
              "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u, "
              "id:%u",
              rank_cfg.dwBusiness_id, rank_cfg.dwWorld_id, rank_cfg.dwZone_id,
              rank_cfg.dwType, rank_cfg.dwInstance_id, rank_cfg.dwId);
          return -1;
        }
        TOPNEXT_LOG_INFO(
            "add rank instance succ,"
            "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u, "
            "id:%u",
            rank_cfg.dwBusiness_id, rank_cfg.dwWorld_id, rank_cfg.dwZone_id,
            rank_cfg.dwType, rank_cfg.dwInstance_id, rank_cfg.dwId);
      }
    }
  }
  // 删除不再使用的榜单,为安全起见，目前不删除共享内存等
  int pos = rank_instance_ht_.head();
  while (pos >= 0) {
    RankInstance* rank_instance = rank_instance_ht_.GetData(pos);
    bool found = false;
    for (uint32_t i = 0; i < cfg->dwRank_cfg_num; ++i) {
      RankKey rank_key;
      const RANKCFG& rank_cfg = cfg->astRank_cfg[i];
      CastRankCfg2RankKey(rank_cfg, rank_key);
      if (rank_instance->rank_key.business_id == rank_key.business_id &&
          rank_instance->rank_key.world_id == rank_key.world_id &&
          rank_instance->rank_key.zone_id == rank_key.zone_id &&
          rank_instance->rank_key.type == rank_key.type &&
          rank_instance->rank_key.instance_id == rank_key.instance_id) {
        found = true;
        break;
      }
    }
    if (!found) {
      pos = rank_instance_ht_.next(pos);
      const RankKey& rank_key = rank_instance->rank_key;
      ret = DeleteRankInstance(rank_key);
      if (0 != ret) {
        TOPNEXT_LOG_ERROR(
            "delete rank instance fail,"
            "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u, "
            "id:%u",
            rank_key.business_id, rank_key.world_id, rank_key.zone_id,
            rank_key.type, rank_key.instance_id, rank_key.id);
        return -1;
      }
      TOPNEXT_LOG_INFO(
          "delete rank instance succ,"
          "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u, id:%u",
          rank_key.business_id, rank_key.world_id, rank_key.zone_id,
          rank_key.type, rank_key.instance_id, rank_key.id);
    } else {
      pos = rank_instance_ht_.next(pos);
    }
  }
  return 0;
};

int TopNextRankMgr::InitDataDir() {
  std::string data_dir = server_->DataDir();
  if ("" == data_dir) {
    TOPNEXT_LOG_ERROR("init main rank shm manager fail, data dir is empty");
    return -1;
  }
  int ret = tos_mkdir_fast(const_cast<char*>(data_dir.c_str()));
  if (0 != ret) {
    TOPNEXT_LOG_ERROR("fail to create directory[%s] for data file",
                      data_dir.c_str());
    return -1;
  }
  return 0;
};

static void AddSubRankOpResult(
    TOPNEXT_SVR_ERR code, const topnext_proto::RankInfo& rank_info,
    const topnext_proto::SubRankInfo& sub_rank_info,
    std::vector<topnext_proto::SubRankOpResult>& op_result) {
  topnext_proto::SubRankOpResult result;
  result.result = (int32_t)code;
  result.rank_info = rank_info;
  result.sub_rank_info = sub_rank_info;
  op_result.push_back(result);
};

int TopNextRankMgr::HandleClientRequest(
    const topnext_proto::InnerGetRankListReq& req,
    topnext_proto::InnerGetRankListRsp& rsp) {
  if (Loggable(TLOG_PRIORITY_DEBUG)) {
    std::ostringstream ss;
    ss << req;
    TOPNEXT_LOG_DEBUG("rpc request: %s", ss.str().c_str());
  }
  int pos = rank_instance_ht_.head();
  while (pos >= 0) {
    RankInstance* rank_instance = rank_instance_ht_.GetData(pos);
    if (NULL != rank_instance && NULL != rank_instance->rank_instance) {
      topnext_proto::RankInfo rank_info;
      CastRankKey2RankInfo(rank_instance->rank_key, rank_info);
      rsp.rank_info.push_back(rank_info);
    }
    pos = rank_instance_ht_.next(pos);
  }
  rsp.ret_info.ret = 0;
  rsp.ret_info.msg = "succ";
  if (Loggable(TLOG_PRIORITY_DEBUG)) {
    std::ostringstream ss;
    ss << rsp;
    TOPNEXT_LOG_DEBUG("rpc response, rsp: %s", ss.str().c_str());
  }
  return 0;
};

int TopNextRankMgr::HandleClientRequest(
    const topnext_proto::GetOneUserMultiRankMultiSubRankReq& req,
    topnext_proto::GetOneUserMultiRankMultiSubRankRsp& rsp) {
  g_client_addr = req.client_info.client_addr;
  if (Loggable(TLOG_PRIORITY_DEBUG)) {
    std::ostringstream ss;
    ss << req;
    TOPNEXT_LOG_DEBUG("rpc request: %s", ss.str().c_str());
  }
  std::vector<topnext_proto::RankInfo>::const_iterator it_rank;
  std::vector<topnext_proto::SubRankInfo>::const_iterator it_sub_rank;
  for (it_rank = req.rank_info.begin(); it_rank != req.rank_info.end();
       ++it_rank) {
    RankKey rank_key = IndexableConvertType(*it_rank);
    TopNextRankInstance* rank_instance = GetRankInstance(rank_key);
    if (NULL == rank_instance) {
      // fill all sub_rank result with RANK_NOT_EXISTS
      for (it_sub_rank = req.sub_rank_info.begin();
           it_sub_rank != req.sub_rank_info.end(); ++it_sub_rank) {
        AddSubRankOpResult(APOLLO_TOPNEXT_SVR_RANK_NOT_EXISTS, *it_rank,
                           *it_sub_rank, rsp.op_result);
      }
      TOPNEXT_LOG_ERROR(
          "can not find rank instance,"
          "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u",
          it_rank->business_id, it_rank->world_id, it_rank->zone_id,
          it_rank->type, it_rank->instance_id);
    } else {
      int ret = rank_instance->HandleClientRequest(req, rsp);
      // 失败，理论上不会进入这个分支
      if (0 != ret) {
        for (it_sub_rank = req.sub_rank_info.begin();
             it_sub_rank != req.sub_rank_info.end(); ++it_sub_rank) {
          AddSubRankOpResult(APOLLO_TOPNEXT_SVR_INTERNAL_ERROR, *it_rank,
                             *it_sub_rank, rsp.op_result);
        }
        TOPNEXT_LOG_ERROR(
            "rank instance internal error,"
            "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u, "
            "ret:%d",
            it_rank->business_id, it_rank->world_id, it_rank->zone_id,
            it_rank->type, it_rank->instance_id, ret);
      }
    }
  }
  rsp.ret_info.ret = 0;
  rsp.ret_info.msg = "succ";
  if (Loggable(TLOG_PRIORITY_DEBUG)) {
    std::ostringstream ss;
    ss << rsp;
    TOPNEXT_LOG_DEBUG("rpc response, rsp: %s", ss.str().c_str());
  }
  return 0;
};
int TopNextRankMgr::AddRankInstance(TOPNEXTCFG* cfg, const RANKCFG& rank_cfg,
                                    const TYPECFG& type_cfg) {
  TopNextRankInstance* instance = new TopNextRankInstance();
  // 这里主要是希望默认榜单共享统一的共享内存记录文件
  int ret = instance->Init(server_, rank_cfg, type_cfg);
  if (0 != ret) {
    TOPNEXT_LOG_ERROR(
        "init rank instance fail, "
        "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u, ret:%d",
        rank_cfg.dwBusiness_id, rank_cfg.dwWorld_id, rank_cfg.dwZone_id,
        rank_cfg.dwType, rank_cfg.dwInstance_id, ret);
    delete instance;
    return -1;
  }
  RankInstance rank_instance;
  CastRankCfg2RankKey(rank_cfg, rank_instance.rank_key);
  rank_instance.rank_instance = instance;
  // 添加到榜单hashtable
  int pos = rank_instance_ht_.Insert(rank_instance, NULL);
  if (pos < 0) {
    TOPNEXT_LOG_ERROR(
        "insert rank instance ht fail,"
        "busi_id:%u, world_id:%u, zone_id:%u, type:%u, instance_id:%u, pos:%d",
        rank_cfg.dwBusiness_id, rank_cfg.dwWorld_id, rank_cfg.dwZone_id,
        rank_cfg.dwType, rank_cfg.dwInstance_id, pos);
    return -1;
  }
  if (NULL != server_->RankCumu()) {
    server_->RankCumu()->Insert(rank_instance.rank_key,
                                IndexableConstructType(rank_instance.rank_key));
    server_->Cumu()->dwRank_cumu_info_num++;
  }
  return 0;
};

int TopNextRankMgr::DeleteRankInstance(const RankKey& rank_key) {
  RankInstance* p_rank_instance = NULL;
  RankInstance rank_instance;
  rank_instance.rank_key = rank_key;
  rank_instance.rank_instance = NULL;
  int pos = rank_instance_ht_.Find(rank_instance, &p_rank_instance);
  if (pos < 0) {
    return 0;
  }
  if (NULL == p_rank_instance) {
    return -1;
  }
  if (NULL == p_rank_instance->rank_instance) {
    return -2;
  }
  p_rank_instance->rank_instance->Fini();
  delete p_rank_instance->rank_instance;
  if (NULL != server_->RankCumu()) {
    server_->RankCumu()->Remove(rank_key);
    server_->Cumu()->dwRank_cumu_info_num--;
  }
  return rank_instance_ht_.Remove(rank_instance);
};

TopNextRankInstance* TopNextRankMgr::GetRankInstance(
    const topnext_proto::RankInfo& rank_info) {
  RankKey rank_key = IndexableConvertType(rank_info);
  return GetRankInstance(rank_key);
};

TopNextRankInstance* TopNextRankMgr::GetRankInstance(const RankKey& rank_key) {
  RankInstance* p_rank_instance = NULL;
  RankInstance rank_instance;
  rank_instance.rank_key = rank_key;
  rank_instance.rank_instance = NULL;
  int pos = rank_instance_ht_.Find(rank_instance, &p_rank_instance);
  if (pos < 0) {
    return NULL;
  }
  if (NULL == p_rank_instance) {
    return NULL;
  }
  return p_rank_instance->rank_instance;
};

void TopNextRankMgr::OnTickTimer() {
  if (current_tick_pos_ < 0) {
    current_tick_pos_ = rank_instance_ht_.head();
  }
  if (current_tick_pos_ < 0) {
    return;
  }
  const RankInstance* rank_instance =
      rank_instance_ht_.GetData(current_tick_pos_);
  if (NULL == rank_instance || NULL == rank_instance->rank_instance) {
    // 重置
    current_tick_pos_ = rank_instance_ht_.head();
    return;
  }
  // 先调用tick定时器
  rank_instance->rank_instance->OnTickTimer();
  if (!rank_instance->rank_instance->HasDoneGenerateImageAndReduce()) {
    return;
  }
  // 继续下一个
  current_tick_pos_ = rank_instance_ht_.next(current_tick_pos_);
};

int TopNextRankMgr::OnIdle() {
  const RankInstance* rank_instance =
      rank_instance_ht_.GetData(current_tick_pos_);
  if (NULL == rank_instance || NULL == rank_instance->rank_instance) {
    return 0;
  }
  if (!rank_instance->rank_instance->HasDoneGenerateImageAndReduce()) {
    return rank_instance->rank_instance->OnIdle();
  }
  return 0;
};

};  // namespace topnext_app
