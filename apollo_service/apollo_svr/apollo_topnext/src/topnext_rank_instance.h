#ifndef TOPNEXT_RANK_INSTANCE_H_
#define TOPNEXT_RANK_INSTANCE_H_

#include "crontab_timer.h"
#include "shm_manager.h"
#include "shtable_util.h"
#include "tlog/tlog.h"
#include "topnext_conf_desc.h"
#include "topnext_protocol.h"
#include "topnext_sub_rank_instance.h"
#include "topnext_sub_rank_shm_group_manager.h"

#include <tr1/unordered_map>
#include <vector>

namespace topnext_app {

class TopNextServer;

class TopNextRankInstance {
 public:
  TopNextRankInstance() {
    server_ = NULL;
    is_sub_rank_shm_manager_existed_ = false;
    memset(&type_cfg_, 0, sizeof(type_cfg_));
    memset(&type_key_, 0, sizeof(type_key_));
    need_pull_reduce_args_ = false;
    last_pull_reduce_ = 0;
  }

  ~TopNextRankInstance() {}

  int Init(TopNextServer* server, const RANKCFG& rank_cfg, const TYPECFG& type_cfg);
  int Reload(const TYPECFG& type_cfg);
  int Fini();

  int InitSubRankInstance();
  int InitImageCrontabTimer(const TYPECFG& type_cfg);
  int InitReduceCrontabTimer(const TYPECFG& type_cfg);
  int InitResizeOnSubRank();
  int AddToSubRankInstanceMap(const SubRankKey& sub_rank_key, bool is_image, TopNextSubRankInstance* sub_instance);
  int AddToExtraSubRankInstanceMap(const SubRankKey& sub_rank_key, TopNextSubRankInstance* sub_instance, int32_t index);
  int InitSubRankShmGroupMgr(const SUBTYPECFG& sub_type_cfg);

  int RecoverSubRankFromDumpFile();
  int RecoverSubRankFromDumpFile(const std::string& file_path);

  int ReadUserInfoFromDumpBuff(const char* buff, uint32_t remain_buff_len, topnext_app::UserInfo& user_info,
                               uint32_t& used_len);

  int CreateSubInstanceIncludeImage(const SubRankKey& sub_rank_key, bool force_image = false);
  int CreateSubInstanceIncludeImage(const topnext_proto::SubRankInfo& sub_rank_info, bool force_image = false) {
    SubRankKey sub_rank_key;
    sub_rank_key.type = sub_rank_info.type;
    sub_rank_key.instance_id = sub_rank_info.instance_id;
    return CreateSubInstanceIncludeImage(sub_rank_key, force_image);
  };
  int CreateExtraImage(const SubRankKey& sub_rank_key, uint32_t index);
  int CreateExtraImage(const topnext_proto::SubRankInfo& sub_rank_info, uint32_t index) {
    SubRankKey sub_rank_key;
    sub_rank_key.type = sub_rank_info.type;
    sub_rank_key.instance_id = sub_rank_info.instance_id;
    return CreateExtraImage(sub_rank_key, index);
  };

  int RemoveSubInstance(const SubRankKey& sub_rank_key, bool is_image);
  int CreateSubInstance(const SubRankKey& sub_rank_key, bool is_image, bool check_capacity = true);
  int CreateExtraSubInstance(const SubRankKey& sub_rank_key, int32_t index);

  TopNextSubRankInstance* FindSubInstance(const SubRankKey& sub_rank_key, bool is_image);
  TopNextSubRankInstance* FindSubInstance(const topnext_proto::SubRankInfo& sub_rank_info, bool is_image) {
    SubRankKey sub_rank_key;
    sub_rank_key.type = sub_rank_info.type;
    sub_rank_key.instance_id = sub_rank_info.instance_id;
    return FindSubInstance(sub_rank_key, is_image);
  };

  // 获取子榜所有extra镜像
  std::vector<TopNextSubRankInstance*> FindExtraSubInstanceImages(const SubRankKey& sub_rank_key);
  std::vector<TopNextSubRankInstance*> FindExtraSubInstanceImages(const topnext_proto::SubRankInfo& sub_rank_info);

  // 获取extra镜像榜单的第index个子榜实例
  TopNextSubRankInstance* FindExtraSubInstanceImage(const SubRankKey& sub_rank_key, uint32_t index);
  TopNextSubRankInstance* FindExtraSubInstanceImage(const topnext_proto::SubRankInfo& sub_rank_info, uint32_t index);

  // 处理协议
  int HandleClientRequest(const topnext_proto::GetSubRankListSubRankReq& req,
                          topnext_proto::GetSubRankListSubRankRsp& rsp);
  int HandleClientRequest(const topnext_proto::ReportSubRankReq& req, topnext_proto::ReportSubRankRsp& rsp);
  int InnerHandleClientRequest(const topnext_proto::ReportSubRankReq& req, topnext_proto::ReportSubRankRsp& rsp,
                               int32_t image_index);
  int HandleClientRequest(const topnext_proto::ChangeSubRankReq& req, topnext_proto::ChangeSubRankRsp& rsp);
  int HandleClientRequest(const topnext_proto::GetTopSubRankReq& req, topnext_proto::GetTopSubRankRsp& rsp);
  int HandleClientRequest(const topnext_proto::GetOneUserSubRankReq& req, topnext_proto::GetOneUserSubRankRsp& rsp);
  int HandleClientRequest(const topnext_proto::ClearOneUserSubRankReq& req, topnext_proto::ClearOneUserSubRankRsp& rsp);
  int HandleClientRequest(const topnext_proto::ClearSubRankReq& req, topnext_proto::ClearSubRankRsp& rsp);
  int HandleClientRequest(const topnext_proto::GetOneUserMultiRankMultiSubRankReq& req,
                          topnext_proto::GetOneUserMultiRankMultiSubRankRsp& rsp);
  int HandleClientRequest(const topnext_proto::IncreaseScoreReq& req, topnext_proto::IncreaseScoreRsp& rsp);
  int HandleClientRequest(const topnext_proto::DecreaseScoreReq& req, topnext_proto::DecreaseScoreRsp& rsp);
  int HandleClientRequest(const topnext_proto::InnerGetSubRankListSubRankReq& req,
                          topnext_proto::InnerGetSubRankListSubRankRsp& rsp);
  int HandleClientRequest(const topnext_proto::ImageAndReduceStatReq& req, topnext_proto::ImageAndReduceStatRsp& rsp);
  int HandleClientRequest(const topnext_proto::GetAndClearOneUserSubRankReq& req,
                          topnext_proto::GetAndClearOneUserSubRankRsp& rsp);
  int HandleClientRequest(const topnext_proto::SetUserExtFieldReq& req, topnext_proto::SetUserExtFieldRsp& rsp);

  // 王者v91版本新接口
  int HandleClientRequest(const topnext_proto::GenerateSubRankImageReq& req,
                          topnext_proto::GenerateSubRankImageRsp& rsp);
  int HandleClientRequest(const topnext_proto::GetSubRankImageStatusReq& req,
                          topnext_proto::GetSubRankImageStatusRsp& rsp);
  int HandleClientRequest(const topnext_proto::OpenOrCloseSubRankImageReq& req,
                          topnext_proto::OpenOrCloseSubRankImageRsp& rsp);

  // 删除子榜实例
  int HandleClientRequest(const topnext_proto::DeleteSubRankReq& req, topnext_proto::DeleteSubRankRsp& rsp);

  // 部分接口没有SubRank，多半是一些涉及整个榜单实例（含所有子榜的）操作
  void AddSubRankOpResult(TOPNEXT_SVR_ERR code, std::vector<topnext_proto::SubRankOpResult>& op_result);
  void AddSubRankOpResult(TOPNEXT_SVR_ERR code, const topnext_app::SubRankKey& sub_rank_key,
                          std::vector<topnext_proto::SubRankOpResult>& op_result, int32_t* rank_no = NULL);
  void AddSubRankOpResult(TOPNEXT_SVR_ERR code, const topnext_proto::SubRankInfo& sub_rank_info,
                          std::vector<topnext_proto::SubRankOpResult>& op_result, int32_t* rank_no = NULL);
  void AddSubRankOpResult(TOPNEXT_SVR_ERR code, const topnext_proto::SubRankInfo& sub_rank_info,
                          UserRankInfo& user_rank_info, std::vector<topnext_proto::SubRankOpResult>& op_result);

  int CpuTimeControlForImageAndReduce();
  int CpuTimeControlForResizeSubRank();
  void DoResizeSubRank();
  void DoImageAndReduce();
  // 镜像和衰减处理相关
  void OnTickTimer();
  int OnIdle();
  bool HasDoneGenerateImageAndReduce();
  bool HasSubRankWaitImageAndReduce();

  void NeedGenerateImageForSubType(uint32_t timer_id, uint32_t sub_type);
  int GenerateImage(TopNextSubRankInstance* real);

  // 王者v91
  int GenerateImageByRequest(TopNextSubRankInstance* real);

  void NeedReduceForSubType(uint32_t timer_id);
  int Reduce(TopNextSubRankInstance* real);

 private:
  void StartPullReduceArgs();
  void StopPullReduceArgs();
  void TryPullReduceArgs();
  void PullReduceArgs();
  void ParseReduceArgs(const std::string& resp);
  void AddToReduceQueue(bool image);

  void AddSubRankImageReduceStatResult(TOPNEXT_SVR_ERR code, const topnext_proto::SubRankInfo& sub_rank_info,
                                       std::vector<topnext_proto::SubRankImageAndReduceStatResult>& stat_result,
                                       int64_t last_image_ts, int64_t last_reduce_ts);

  // 王者v91新增接口
  void AddImageStatusResult(TOPNEXT_SVR_ERR code, const topnext_app::SubRankKey& sub_rank_key, uint64_t image_sequence,
                            int32_t image_status, int64_t image_generate_ts, uint32_t image_switch,
                            std::vector<topnext_proto::SubRankImageStatusResult>& status_result);
  void AddImageStatusResult(TOPNEXT_SVR_ERR code, const topnext_app::SubRankKey& sub_rank_key,
                            TopNextSubRankInstance* image_instance,
                            std::vector<topnext_proto::SubRankImageStatusResult>& status_result);

  int GetSubRankShmGroupId(const topnext_app::SubRankKey& sub_rank_key, bool is_image, int32_t image_index,
                           uint32_t& group_id);
  uint32_t CacheOfRequestTotal() {
    return (uint32_t)(subrank_report_cache_.size() + subrank_change_cache_.size() + increase_score_cache_.size() +
                      decrease_score_cache_.size());
  }
  void GetSubRankList(bool is_image, std::vector<TopNextSubRankInstance*>& instances);
  void GetSubRankList(uint32_t sub_type, bool is_image, std::vector<TopNextSubRankInstance*>& instances);
  void GetSubRankList(uint32_t sub_type, std::vector<TopNextSubRankInstance*>& instances);

  void GetAllExtraSubRankList(std::vector<TopNextSubRankInstance*>& instances);
  void GetAllExtraSubRankList(uint32_t sub_type, std::vector<TopNextSubRankInstance*>& instances);
  void GetExtraSubRankList(int32_t index, std::vector<TopNextSubRankInstance*>& instances);

  int CastUserInfoSvr2Net(const topnext_app::UserInfo* internal, topnext_proto::UserInfo& net);
  int CastUserInfoNet2Svr(const topnext_proto::UserInfo& net, topnext_app::UserInfo& internal);

  // 删除子榜实例，子榜ID也会被回收
  bool DeleteSubRankInstance(TopNextSubRankInstance* sub_rank_instance);

 private:
  friend class TopNextRankMgr;
  // 子榜榜单
  bool is_sub_rank_shm_manager_existed_;
  shm_manager::ShmManager sub_rank_shm_manager_;
  std::map<uint32_t, TopNextSubRankShmGroupManager*> shm_group_mgr_map_;

  struct Hash {
    std::size_t operator()(const SubRankInstanceKey& k) const {
      return (k.sub_rank_key.instance_id ^ k.sub_rank_key.type) * (k.is_image ? 1 : 23);
    }
  };
  struct EqualTo {
    bool operator()(const SubRankInstanceKey& left, const SubRankInstanceKey& right) const {
      return (left.sub_rank_key.instance_id == right.sub_rank_key.instance_id &&
              left.sub_rank_key.type == right.sub_rank_key.type && left.is_image == right.is_image);
    }
  };
  std::tr1::unordered_map<SubRankInstanceKey, TopNextSubRankInstance*, Hash, EqualTo> subrank_instance_map_;

  // 通过API生成的镜像，如果指定了image_index不为0，则存储在extra_subrank_instance_map_中
  std::tr1::unordered_map<SubRankInstanceKey, std::vector<TopNextSubRankInstance*>, Hash, EqualTo>
      extra_subrank_instance_map_;

  // 每个类型，动态创建的榜单计数
  std::map<uint32_t, uint32_t> subrank_count_map_;
  // 错误消息
  std::string error_msg_;

  // 榜单配置
  TYPECFG type_cfg_;

  TopNextServer* server_;
  RankKey rank_key_;
  // 所属的主榜类型
  TypeKey type_key_;

  // cache for report and change event if image and reduce happen
  std::vector<topnext_proto::ReportSubRankReq> subrank_report_cache_;
  std::vector<topnext_proto::ChangeSubRankReq> subrank_change_cache_;

  // resize
  std::vector<TopNextSubRankInstance*> need_resize_sub_rank_;

  std::vector<topnext_proto::IncreaseScoreReq> increase_score_cache_;
  std::vector<topnext_proto::DecreaseScoreReq> decrease_score_cache_;

  // 镜像周期定时器
  apollo_service::CrontabTimer image_crontab_timer_;
  std::vector<TopNextSubRankInstance*> need_image_sub_rank_;

  // 来自客户端请求，需要镜像的子榜
  std::vector<TopNextSubRankInstance*> need_image_sub_rank_from_request_;

  // 衰减周期定时器
  apollo_service::CrontabTimer reduce_crontab_timer_;
  std::vector<TopNextSubRankInstance*> need_reduce_sub_rank_;

  bool need_pull_reduce_args_;
  uint64_t last_pull_reduce_;
};

};  // namespace topnext_app
#endif
